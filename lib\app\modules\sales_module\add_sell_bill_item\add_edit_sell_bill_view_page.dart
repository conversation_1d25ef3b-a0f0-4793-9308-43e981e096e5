import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/bill_item_response.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/item_autocomplete_textfield_with_add.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/sales_module/add_sell_bill_item/add_edit_sell_bill_item_controller.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class AddSaleBilledItemScreenView extends StatelessWidget {
  final String tag = "Sales Item Add/Edit View";
  final LineItemDetailModel? lineItemModel;

  final salesItemController = AddEditSaleBillItemController();
  AddSaleBilledItemScreenView({this.lineItemModel}) {
    if (this.lineItemModel != null) {
      salesItemController.initEdit(this.lineItemModel!);
    }
  }
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (false) {
        return Container(
            color: Colors.white,
            child: Center(child: CircularProgressIndicator()));
      } else {
        return Column(mainAxisSize: MainAxisSize.min, children: [
          Container(
            child: GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(),
              child: Container(
                child: Form(
                  key: salesItemController.formKey,
                  child: Container(
                    child: Container(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 10),
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    //===============================================Item
                                    Expanded(
                                      flex: 2,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'सामान छन्नुहोस्',
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          ItemAutoCompleteTextFieldWithAdd(
                                              itemID:
                                                  lineItemModel?.itemId ?? "",
                                              controller: salesItemController
                                                  .itemNameCtrl,
                                              onChangedFn: (value) {
                                                print("OnCangeFn Invoked");
                                                // state.selectedItem = null;
                                              },
                                              onSuggestionSelectedFn: (item) {
                                                print(
                                                    "onSuggestionSelected invoked");
                                                salesItemController
                                                    .itemOnSelectHandler(
                                                        item.itemId,
                                                        unitID: item.baseUnitId,
                                                        item: item);
                                              })
                                          // ItemAutoCompleteTextFieldWithAdd(
                                          //     controller: salesItemController
                                          //         .itemNameCtrl,
                                          //     onChangedFn: (value) {
                                          //       // state.selectedItem = null;
                                          //     },
                                          //     onSuggestionSelectedFn: (item) {
                                          //       salesItemController
                                          //           .itemOnSelectHandler(
                                          //               item.itemId,
                                          //               unitID: item.baseUnitId,
                                          //               item: item);
                                          //       salesItemController
                                          //           .selectedItem = item;
                                          //     })
                                        ],
                                      ),
                                    ),

                                    SizedBox(
                                      width: 10,
                                    ),

                                    //===============================================Unit
                                    // Expanded(
                                    //   flex: 1,
                                    //   child: Column(
                                    //     crossAxisAlignment:
                                    //         CrossAxisAlignment.start,
                                    //     children: [
                                    //       Text(
                                    //         'एकाइ',
                                    //         style: labelStyle2,
                                    //       ),
                                    //       SizedBox(height: 5.0),
                                    //       DropdownButtonFormField(
                                    //         isExpanded: true,
                                    //         value: salesItemController
                                    //             .billedItem.lineItemUnitId,
                                    //         decoration: formFieldStyle.copyWith(
                                    //           hintText: "Unit",
                                    //         ),
                                    //         items: salesItemController.unitList
                                    //             .map((billedItemUnit) {
                                    //           return DropdownMenuItem(
                                    //             value: billedItemUnit.unitId,
                                    //             child: Text(
                                    //                 "${billedItemUnit.unitName} (${billedItemUnit.unitShortName})"),
                                    //           );
                                    //         }).toList(),
                                    //         onChanged: (value) =>
                                    //             salesItemController
                                    //                 .unitOnSelectHandler(value),
                                    //       ),
                                    //     ],
                                    //   ),
                                    // ),
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'एकाइ',
                                            style: labelStyle2,
                                          ),
                                          const SizedBox(height: 5.0),
                                          DropdownButtonFormField(
                                            isExpanded: true,
                                            value: salesItemController
                                                .billedItem.lineItemUnitId,
                                            decoration: formFieldStyle.copyWith(
                                                hintText: "Unit"),
                                            items: salesItemController.unitList
                                                .map((billedItemUnit) {
                                              return DropdownMenuItem(
                                                value: billedItemUnit.unitId,
                                                child: Text(
                                                    "${billedItemUnit.unitName} (${billedItemUnit.unitShortName})"),
                                              );
                                            }).toList(),
                                            onChanged: (value) {
                                              return salesItemController
                                                  .unitOnSelectHandler(value!);
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 10,
                                ),

                                // // Row(
                                // //   mainAxisAlignment:
                                // //       MainAxisAlignment.spaceBetween,
                                // //   children: [
                                // //     //===============================================Quantity
                                // //     Expanded(
                                // //       flex: 1,
                                // //       child: Column(
                                // //         crossAxisAlignment:
                                // //             CrossAxisAlignment.start,
                                // //         children: [
                                // //           Text(
                                // //             'परिमाण',
                                // //             style: labelStyle2,
                                // //           ),
                                // //           SizedBox(height: 5.0),
                                // //           FormBuilderTextField(
                                // //             name: "qty",
                                // //             autocorrect: false,
                                // //             keyboardType:
                                // //                 TextInputType.numberWithOptions(
                                // //                     decimal: true),
                                // //             inputFormatters: [
                                // //               FilteringTextInputFormatter.allow(
                                // //                   RegExp(r'^(\d+)?\.?\d{0,2}'))
                                // //             ],
                                // //             textInputAction:
                                // //                 TextInputAction.next,
                                // //             style: formFieldTextStyle,
                                // //             decoration: formFieldStyle.copyWith(
                                // //                 labelText: "Quantity"),
                                // //             textAlign: TextAlign.end,
                                // //             controller:
                                // //                 salesItemController.qtyCtrl,
                                // //             onChanged: (value) =>
                                // //                 salesItemController
                                // //                     .qtyOnChangeHandler(value),
                                // //           ),
                                // //         ],
                                // //       ),
                                // //     ),

                                // //     SizedBox(
                                // //       width: 10,
                                // //     ),

                                // //     //===============================================Price/Rate
                                // //     Expanded(
                                // //       flex: 1,
                                // //       child: Column(
                                // //         crossAxisAlignment:
                                // //             CrossAxisAlignment.start,
                                // //         children: [
                                // //           Text(
                                // //             'मूल्य प्रति एकाइ',
                                // //             style: labelStyle2,
                                // //           ),
                                // //           SizedBox(height: 5.0),
                                // //           FormBuilderTextField(
                                // //             name: "rate",
                                // //             autocorrect: false,
                                // //             keyboardType:
                                // //                 TextInputType.numberWithOptions(
                                // //                     decimal: true),
                                // //             inputFormatters: [
                                // //               FilteringTextInputFormatter.allow(
                                // //                   RegExp(r'^(\d+)?\.?\d{0,2}'))
                                // //             ],
                                // //             textInputAction:
                                // //                 TextInputAction.done,
                                // //             style: formFieldTextStyle,
                                // //             decoration: formFieldStyle.copyWith(
                                // //                 labelText: "Price/Unit"),
                                // //             textAlign: TextAlign.end,
                                // //             controller:
                                // //                 salesItemController.rateCtrl,
                                // //             onChanged: (value) =>
                                // //                 salesItemController
                                // //                     .rateOnChangeHandler(value),
                                // //           ),
                                // //         ],
                                // //       ),
                                // //     ),
                                // //   ],
                                // // ),
                                // SizedBox(
                                //   height: 10,
                                //
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    //===============================================Quantity
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'परिमाण',
                                            style: labelStyle2,
                                          ),
                                          const SizedBox(height: 5.0),
                                          FormBuilderTextField(
                                            name: "qty",
                                            autocorrect: false,
                                            keyboardType: const TextInputType
                                                .numberWithOptions(
                                                decimal: true),
                                            inputFormatters: [
                                              FilteringTextInputFormatter.allow(
                                                  RegExp(r'^(\d+)?\.?\d{0,2}'))
                                            ],
                                            textInputAction:
                                                TextInputAction.next,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                labelText: "Quantity"),
                                            textAlign: TextAlign.end,
                                            controller:
                                                salesItemController.qtyCtrl,
                                            onChanged: (value) =>
                                                salesItemController
                                                    .qtyOnChangeHandler(value),
                                          ),
                                        ],
                                      ),
                                    ),

                                    const SizedBox(
                                      width: 10,
                                    ),

                                    //===============================================Price/Rate
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'मूल्य प्रति एकाइ',
                                            style: labelStyle2,
                                          ),
                                          const SizedBox(height: 5.0),
                                          FormBuilderTextField(
                                              name: "rate",
                                              autocorrect: false,
                                              keyboardType: const TextInputType
                                                  .numberWithOptions(
                                                  decimal: true),
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .allow(RegExp(
                                                        r'^(\d+)?\.?\d{0,2}'))
                                              ],
                                              textInputAction:
                                                  TextInputAction.next,
                                              style: formFieldTextStyle,
                                              decoration:
                                                  formFieldStyle.copyWith(
                                                      labelText: "Price/Unit"),
                                              textAlign: TextAlign.end,
                                              controller:
                                                  salesItemController.rateCtrl,
                                              onChanged: (value) {
                                                // salesItemController
                                                //         .rateCtrl.selection =
                                                //     TextSelection.fromPosition(
                                                //         TextPosition(
                                                //             offset:
                                                //                 salesItemController
                                                //                     .rateCtrl
                                                //                     .text
                                                //                     .length));
                                                salesItemController
                                                    .rateOnChangeHandler(value);
                                              }),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 15,
                                ),

                                //===============================================Amount
                                // Column(
                                //   crossAxisAlignment: CrossAxisAlignment.start,
                                //   children: [
                                //     Text(
                                //       'रकम',
                                //       style: labelStyle2,
                                //     ),
                                //     SizedBox(height: 5.0),
                                //     FormBuilderTextField(
                                //       name: "amount",
                                //       autocorrect: false,
                                //       keyboardType:
                                //           TextInputType.numberWithOptions(
                                //               decimal: true),
                                //       inputFormatters: [
                                //         FilteringTextInputFormatter.allow(
                                //             RegExp(r'^(\d+)?\.?\d{0,2}'))
                                //       ],
                                //       textInputAction: TextInputAction.done,
                                //       style: formFieldTextStyle,
                                //       decoration: formFieldStyle.copyWith(
                                //           labelText: "Amount"),
                                //       textAlign: TextAlign.end,
                                //       controller:
                                //           salesItemController.grossAmountCtrl,
                                //       onChanged: (value) => salesItemController
                                //           .amountOnChangeHandler(value),
                                //     ),
                                //   ],
                                // ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'जम्मा रकम',
                                      style: labelStyle2,
                                    ),
                                    const SizedBox(height: 5.0),
                                    FormBuilderTextField(
                                      name: "amount",
                                      autocorrect: false,
                                      keyboardType:
                                          const TextInputType.numberWithOptions(
                                              decimal: true),
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp(r'^(\d+)?\.?\d{0,2}'))
                                      ],
                                      textInputAction: TextInputAction.done,
                                      style: formFieldTextStyle,
                                      decoration: formFieldStyle.copyWith(
                                          labelText: "Amount"),
                                      textAlign: TextAlign.end,
                                      controller:
                                          salesItemController.grossAmountCtrl,
                                      onChanged: (value) {
                                        salesItemController
                                                .grossAmountCtrl.selection =
                                            TextSelection.fromPosition(
                                                TextPosition(
                                                    offset: salesItemController
                                                        .grossAmountCtrl
                                                        .text
                                                        .length));
                                        salesItemController
                                            .amountOnChangeHandler(value);
                                      },
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 15,
                                ),

                                // =============================================Discount
                                // Column(
                                //   crossAxisAlignment: CrossAxisAlignment.start,
                                //   children: [
                                //     Text(
                                //       "छुट (Discount)",
                                //       style: labelStyle2,
                                //     ),
                                //     SizedBox(height: 5.0),
                                //     Row(
                                //       mainAxisAlignment:
                                //           MainAxisAlignment.spaceBetween,
                                //       children: [
                                //         Container(
                                //           width: 80,
                                //           child: FormBuilderTextField(
                                //             name: "txn_discount_percent",
                                //             autocorrect: false,
                                //             keyboardType:
                                //                 TextInputType.numberWithOptions(
                                //                     decimal: true),
                                //             textInputAction:
                                //                 TextInputAction.done,
                                //             style: formFieldTextStyle,
                                //             decoration: formFieldStyle.copyWith(
                                //                 suffix: Text("%"),
                                //                 labelText: "%"),
                                //             textAlign: TextAlign.end,
                                //             controller: salesItemController
                                //                 .discountPercentageCtrl,
                                //             onChanged: (value) {
                                //               salesItemController
                                //                   .discountPercentOnChangeHandler(
                                //                       value);
                                //             },
                                //           ),
                                //         ),
                                //         SizedBox(
                                //           width: 20,
                                //         ),
                                //         Expanded(
                                //           child: Container(
                                //             child: FormBuilderTextField(
                                //               name: "txn_discount_amount",
                                //               autocorrect: false,
                                //               keyboardType: TextInputType
                                //                   .numberWithOptions(
                                //                       decimal: true),
                                //               textInputAction:
                                //                   TextInputAction.done,
                                //               style: formFieldTextStyle,
                                //               decoration: formFieldStyle.copyWith(
                                //                   labelText:
                                //                       "छुट रकम (Dis. Amount)"),
                                //               textAlign: TextAlign.end,
                                //               controller: salesItemController
                                //                   .discountAmountCtrl,
                                //               onChanged: (value) {
                                //                 salesItemController
                                //                     .discountAmtonChangeHandler(
                                //                         value);
                                //               },
                                //             ),
                                //           ),
                                //         ),
                                //       ],
                                //     ),
                                //   ],
                                // ),
                                // SizedBox(
                                //   height: 10,
                                // ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "छुट (Discount)",
                                      style: labelStyle2,
                                    ),
                                    const SizedBox(height: 5.0),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        SizedBox(
                                          width: 80,
                                          child: FormBuilderTextField(
                                            name: "txn_discount_percent",
                                            autocorrect: false,
                                            keyboardType: const TextInputType
                                                .numberWithOptions(
                                                decimal: true),
                                            textInputAction:
                                                TextInputAction.done,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                suffix: const Text("%"),
                                                labelText: "%"),
                                            textAlign: TextAlign.end,
                                            controller: salesItemController
                                                .discountPercentageCtrl,
                                            onChanged: (value) {
                                              salesItemController
                                                      .discountPercentageCtrl
                                                      .selection =
                                                  TextSelection.fromPosition(
                                                      TextPosition(
                                                          offset: salesItemController
                                                              .discountPercentageCtrl
                                                              .text
                                                              .length));
                                              salesItemController
                                                  .discountPercentOnChangeHandler(
                                                      value!);
                                            },
                                          ),
                                        ),
                                        const SizedBox(
                                          width: 20,
                                        ),
                                        Expanded(
                                          child: FormBuilderTextField(
                                            name: "txn_discount_amount",
                                            autocorrect: false,
                                            keyboardType: const TextInputType
                                                .numberWithOptions(
                                                decimal: true),
                                            textInputAction:
                                                TextInputAction.done,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                labelText:
                                                    "छुट रकम (Dis. Amount)"),
                                            textAlign: TextAlign.end,
                                            controller: salesItemController
                                                .discountAmountCtrl,
                                            onChanged: (value) {
                                              salesItemController
                                                      .discountAmountCtrl
                                                      .selection =
                                                  TextSelection.fromPosition(
                                                      TextPosition(
                                                          offset: salesItemController
                                                              .discountAmountCtrl
                                                              .text
                                                              .length));
                                              salesItemController
                                                  .onDiscountAmoutChange(value);
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 10,
                                ),

                                //===============================================Net Amount
                                // Column(
                                //   crossAxisAlignment: CrossAxisAlignment.start,
                                //   children: [
                                //     Text(
                                //       'खुद रकम',
                                //       style: labelStyle2,
                                //     ),
                                //     SizedBox(height: 5.0),
                                //     FormBuilderTextField(
                                //       name: "amount",
                                //       autocorrect: false,
                                //       readOnly: true,
                                //       keyboardType:
                                //           TextInputType.numberWithOptions(
                                //               decimal: true),
                                //       inputFormatters: [
                                //         FilteringTextInputFormatter.allow(
                                //             RegExp(r'^(\d+)?\.?\d{0,2}'))
                                //       ],
                                //       textInputAction: TextInputAction.done,
                                //       style: formFieldTextStyle,
                                //       decoration: formFieldStyle.copyWith(
                                //           labelText: "Net Amount"),
                                //       textAlign: TextAlign.end,
                                //       controller:
                                //           salesItemController.netAmountCtrl,
                                //     ),
                                //   ],
                                // ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'खुद रकम',
                                      style: labelStyle2,
                                    ),
                                    const SizedBox(height: 5.0),
                                    FormBuilderTextField(
                                      name: "amount",
                                      autocorrect: false,
                                      readOnly: true,
                                      keyboardType:
                                          const TextInputType.numberWithOptions(
                                              decimal: true),
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp(r'^(\d+)?\.?\d{0,2}'))
                                      ],
                                      textInputAction: TextInputAction.done,
                                      style: formFieldTextStyle,
                                      decoration: formFieldStyle.copyWith(
                                          labelText: "Net Amount"),
                                      textAlign: TextAlign.end,
                                      controller:
                                          salesItemController.netAmountCtrl,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          BottomSaveCancelButton(
            shadow: false,
            onSaveBtnPressedFn: () {
              if (salesItemController.formKey.currentState!.validate()) {
                //remove null id validation ,
                // if (null == salesItemController.selectedItem.itemId) {
                //   showToastMessage(context,
                //       message: "सामान खाली राख्न मिल्दैन |",
                //       alertType: AlertType.Normal);
                //   return;
                // }

                if (salesItemController.itemNameCtrl.text.isEmpty) {
                  showToastMessage(context,
                      message: "सामान खाली राख्न मिल्दैन |",
                      alertType: AlertType.Error);
                  return;
                }

                if (salesItemController.qtyCtrl.text.isEmpty ||
                    0 >= parseDouble(salesItemController.qtyCtrl.text)!) {
                  showToastMessage(context,
                      message:
                          "परिमाण (Quantity) खाली वा शून्य राख्न मिल्दैन |",
                      alertType: AlertType.Error);
                  return;
                }

                if (salesItemController.rateCtrl.text.isEmpty ||
                    0 >= parseDouble(salesItemController.rateCtrl.text)!) {
                  showToastMessage(context,
                      message: "मूल्य (Price) खाली वा शून्य राख्न मिल्दैन |",
                      alertType: AlertType.Error);
                  return;
                }

                salesItemController.billedItem.itemId =
                    salesItemController.selectedItem.value.itemId;
                salesItemController.billedItem.itemName =
                    strTrim(salesItemController.itemNameCtrl.text);

                salesItemController.billedItem.lineItemUnitId =
                    salesItemController.selectedUnit.unitId;
                salesItemController.billedItem.lineItemUnitName =
                    salesItemController.selectedUnit.unitShortName;

                salesItemController.billedItem.pricePerUnit =
                    parseDouble(salesItemController.rateCtrl.text);
                salesItemController.billedItem.quantity =
                    parseDouble(salesItemController.qtyCtrl.text);
                salesItemController.billedItem.discountPercent = parseDouble(
                    salesItemController.discountPercentageCtrl.text);
                salesItemController.billedItem.discountAmount =
                    parseDouble(salesItemController.discountAmountCtrl.text);
                salesItemController.billedItem.grossAmount =
                    parseDouble(salesItemController.grossAmountCtrl.text);
                salesItemController.billedItem.totalAmount =
                    parseDouble(salesItemController.netAmountCtrl.text);

                AddSaleBilledItemResponse _addSaleBilledItemResponse =
                    AddSaleBilledItemResponse(
                        newFlag: false,
                        billedItem: salesItemController.billedItem);
                Navigator.pop(context, _addSaleBilledItemResponse);
              }
            },
          ),
        ]);
      }
    });
  }
}
