import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class PackageModel {
  String? title;
  String? subTitle;
  String? displayImage;
  String? packageId;
  String? packageCode; // <-- Added field
  double? packagePrice;
  double? vatPercentage;
  double? vatAmount;
  double? packageTotalAmount;

  PackageModel({
    this.title,
    this.subTitle,
    this.displayImage,
    this.packageId,
    this.packageCode,
    this.packagePrice,
    this.vatPercentage,
    this.vatAmount,
    this.packageTotalAmount,
  });

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'sub_title': subTitle,
      'display_image': displayImage,
      'package_id': packageId,
      'package_code': packageCode, // <-- Added field
      'package_price': packagePrice,
      'vat_percentage': vatPercentage,
      'vat_amount': vatAmount,
      'package_total_amount': packageTotalAmount,
    };
  }

  factory PackageModel.fromJson(Map<String, dynamic> map) {
    return PackageModel(
      title: map['title'],
      subTitle: map['sub_title'],
      displayImage: map['display_image'],
      packageId: map['package_id'],
      packageCode: map['package_code'], // <-- Added field
      packagePrice: parseDouble(map['package_price']),
      vatPercentage: parseDouble(map['vat_percentage']),
      vatAmount: parseDouble(map['vat_amount']),
      packageTotalAmount: parseDouble(map['package_total_amount']),
    );
  }
}
