import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class CustomTextField extends FormField<dynamic> {
  CustomTextField(
      {FormFieldSetter<dynamic>? onSaved,
      FormFieldValidator<dynamic>? validator,
      final ValueChanged<dynamic>? onChange,
      dynamic initialValue,
      Color? fillColor,
      String placeholder = "",
      String hintText = "",
      String labelText = "",
      bool readOnly = false,
      String? prefixText,
      TextInputType keyboardType = TextInputType.name,
      TextEditingController? textCtrl,
      List<TextInputFormatter>? inputFormatters,
      TextCapitalization textCapitalization = TextCapitalization.none,
      TextInputAction textInputAction = TextInputAction.next})
      : super(
            onSaved: onSaved,
            validator: validator,
            initialValue: initialValue,
            builder: (FormFieldState<dynamic> state) {
              textCtrl = TextEditingController(text: initialValue);
              textCtrl!.selection = TextSelection.fromPosition(
                  TextPosition(offset: textCtrl!.text.length));

              return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    TextField(
                      inputFormatters: inputFormatters,
                      keyboardType: keyboardType,
                      textCapitalization: textCapitalization,
                      textInputAction: textInputAction,
                      readOnly: readOnly,
                      style: formFieldTextStyle,
                      decoration: formFieldStyle.copyWith(labelText: labelText),
                      controller: textCtrl,
                      onChanged: (val) {
                        state.didChange(val);
                        if (onChange != null) {
                          onChange(val);
                        }
                        // textCtrl.text = v;
                      },
                    ),
                    if (state.hasError)
                      Text(
                        state.errorText ?? "",
                        style: const TextStyle(fontSize: 12, color: Colors.red),
                      )
                  ]);
            });
}
