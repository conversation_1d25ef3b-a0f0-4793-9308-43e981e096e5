// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

// import 'package:esewa_pnp/esewa.dart';
// import 'package:esewa_pnp/esewa_pnp.dart';
// import 'package:esewa_pnp/esewa.dart';
// import 'package:esewa_pnp/esewa_pnp.dart';
import 'dart:convert';

import 'package:crypto/crypto.dart';
import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/app/model/others/package_model.dart';
import 'package:mobile_khaata_v2/app/modules/subscription_module/payment_subscription/payment_subscription_controller.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';
import 'package:uuid/uuid.dart';

import '../../web_view_modal/general_web_view.dart';
import 'payment_esewa_webview.dart';
// import 'package:flutter_khalti/flutter_khalti.dart';

// class PaymentResponse {
//   String? planId;
//   String? paymentType;
//   String? productId;
//   String? totalAmount;
//   String? referenceId;
//
//
//   PaymentResponse(
//       {this.planId,
//       this.productId,
//       this.referenceId,
//       this.totalAmount,
//       this.paymentType,
//       });
//
//   static PaymentResponse fromEsewa(result) => PaymentResponse(
//       productId: result.productId,
//       referenceId: result.referenceId,
//       totalAmount: result.totalAmount);
// }

class PaymentSubscriptionView extends StatefulWidget {
  final PackageModel? packageModel;

  const PaymentSubscriptionView({super.key, this.packageModel});

  @override
  _PaymentSubscriptionViewState createState() =>
      _PaymentSubscriptionViewState();
}

class _PaymentSubscriptionViewState extends State<PaymentSubscriptionView> {
  final paymentSubscriptionController = PaymentSubscriptionController();

  @override
  void initState() {
    super.initState();
    paymentSubscriptionController.init();
  }

  String generateEsewaSignature({
    required String secretKey,
    required Map<String, String> fields,
    required List<String> signedFieldNames,
  }) {
    // Construct the data string in the specified order
    final dataString =
        signedFieldNames.map((key) => "$key=${fields[key]}").join(',');

    // Generate HMAC-SHA256 signature
    final hmacSha256 = Hmac(sha256, utf8.encode(secretKey));
    final digest = hmacSha256.convert(utf8.encode(dataString));

    print("dataString: $dataString");
    print("Signature: $digest");
    print("Signature: ${base64Encode(digest.bytes)}");

    // Return Base64-encoded signature
    return base64Encode(digest.bytes);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      body: SafeArea(
        child: Padding(
          // height: 40,
          padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 15),

          child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "Select Payment Gateway",
                  style: labelStyle2.copyWith(
                    fontSize: 24,
                  ),
                ),
                const SizedBox(
                  height: 40,
                ),

                //button
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () async {
                          String uuid = paymentSubscriptionController
                              .generateTransactionId();
                          print("====uuid====");
                          print(uuid);
                          print("====uuid====");

                          await paymentSubscriptionController
                              .initiatEsewaPayment(
                            'live',
                            widget.packageModel!.packageCode!,
                            widget.packageModel!.packagePrice!.toString(),
                            uuid,
                            widget.packageModel!.vatAmount!.toString(),
                          );

                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) => PaymentEsewa(
                                postData: {
                                  'amount': widget.packageModel!.packagePrice!
                                      .toString(),
                                  // 'amount': "1",
                                  'tax_amount': widget.packageModel!.vatAmount!
                                      .toString(),
                                  // 'tax_amount': "0.13",
                                  'total_amount': (widget
                                              .packageModel!.packagePrice!
                                              .toDouble() +
                                          widget.packageModel!.vatAmount!
                                              .toDouble())
                                      .toString(),
                                  // 'total_amount': "1.13",
                                  'transaction_uuid': uuid,
                                  // 'product_code': 'EPAYTEST',
                                  'product_code': 'NP-ES-VTPL',
                                  'product_service_cha9843631160rge': '0',
                                  'product_delivery_charge': '0',
                                  'success_url':
                                      'https://mkapi.mobilekhaata.com/api/payment/success',
                                  'failure_url':
                                      'https://mkapi.mobilekhaata.com/api/payment/failure',
                                  // 'success_url':
                                  // 'https://mkapi.firajshankhadev.com.np/api/payment/success',
                                  // 'failure_url':
                                  // 'https://mkapi.firajshankhadev.com.np/api/payment/failure',
                                  'signed_field_names':
                                      'total_amount,transaction_uuid,product_code',
                                  'signature': generateEsewaSignature(
                                    signedFieldNames: [
                                      'total_amount',
                                      'transaction_uuid',
                                      'product_code',
                                    ],
                                    secretKey: ESEWA_MERCHANT_KEY,
                                    // secretKey: '8gBm/:&EnhH.1/q',
                                    fields: {
                                      // 'total_amount':"1.13",
                                      'total_amount': (widget
                                                  .packageModel!.packagePrice!
                                                  .toDouble() +
                                              widget.packageModel!.vatAmount!
                                                  .toDouble())
                                          .toString(),
                                      'transaction_uuid': uuid,
                                      'product_code': 'NP-ES-VTPL',
                                      // 'product_code': 'EPAYTEST',
                                    },
                                  ),
                                },
                                // url:
                                //     'https://rc-epay.esewa.com.np/api/epay/main/v2/form',
                                url:
                                    'https://epay.esewa.com.np/api/epay/main/v2/form',
                              ),
                            ),
                          );
                        },
                        child: Container(
                          clipBehavior: Clip.hardEdge,
                          decoration: BoxDecoration(
                              border: Border.all(color: esewaColor),
                              borderRadius: BorderRadius.circular(10)),
                          padding: const EdgeInsets.symmetric(vertical: 10),
                          width: 125,
                          alignment: Alignment.center,
                          child: Column(
                            children: [
                              Image.asset(
                                'images/esewa.png',
                                height: 40,
                              ),
                              const SizedBox(
                                height: 5,
                              ),
                              Text(
                                "Pay With Esewa",
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    color: esewaColor,
                                    fontSize: 15,
                                    fontWeight: FontWeight.bold),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),

                    //TODO Implemet khalti payment later
                    // Material(
                    //   color: Colors.transparent,
                    //   child: InkWell(
                    //     onTap: () async {
                    //       Log.d("clicked");
                    //       try {
                    //         var res =
                    //             await ESewaPnp(configuration: eSewaConfiguration)
                    //                 .initPayment(
                    //                     payment: ESewaPayment(
                    //                         amount: widget.price?.toInt(),
                    //                         productID: widget.id,
                    //                         // productID:pro
                    //                         productName: ESEWA_APP_NAME,
                    //                         callBackURL: ESEWA_CALLBACK_URL));
                    //         Log.d("eseswa payment resp  $res");

                    //         // if(res.)
                    //         Tuple2<bool, String> verifyResp =
                    //             await paymentSubscriptionController
                    //                 .verifyEsewaPayment(res.referenceId);

                    //         if (verifyResp.item1) {
                    //           Navigator.of(context).pop(true);
                    //         } else {
                    //           Navigator.of(context).pop(true);
                    //         }
                    //       } on ESewaPaymentException catch (e) {
                    //         Log.d("error payment " + e.toString());
                    //         Navigator.of(context).pop();
                    //       }
                    //     },
                    //     child: Container(
                    //       clipBehavior: Clip.hardEdge,
                    //       decoration: BoxDecoration(
                    //           border: Border.all(color: khaltiColor),
                    //           borderRadius: BorderRadius.circular(10)),
                    //       // height: 50,
                    //       padding: EdgeInsets.symmetric(vertical: 10),
                    //       width: 140,
                    //       alignment: Alignment.center,
                    //       child: Column(
                    //         // mainAxisAlignment: MainAxisAlignment.center,
                    //         children: [
                    //           Image.asset(
                    //             'images/khalti.png',
                    //             height: 30,
                    //           ),
                    //           SizedBox(
                    //             height: 5,
                    //           ),
                    //           Text(
                    //             "Pay With Khalti",
                    //             style: labelStyle2.copyWith(color: khaltiColor),
                    //           )
                    //         ],
                    //       ),
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
              ]),
        ),
      ),
    ));
  }
}
