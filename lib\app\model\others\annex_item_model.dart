import 'dart:convert';

class AnnexItemModel {
  String? tinNo;
  String? tinFlag;
  String? ledgerId;
  String? ledgerName;
  double? openingBalance;
  double? salesTaxableTotal;
  double? purchaseTaxableTotal;
  double? salesReturnTaxableTotal;
  double? purchaseReturnTaxableTotal;
  double? closingBalance;
  AnnexItemModel({
    this.tinNo,
    this.tinFlag,
    this.ledgerId,
    this.ledgerName,
    this.openingBalance,
    this.salesTaxableTotal,
    this.purchaseTaxableTotal,
    this.salesReturnTaxableTotal,
    this.purchaseReturnTaxableTotal,
    this.closingBalance,
  });

  Map<String, dynamic> toJson() {
    return {
      'tin_no': tinNo,
      'tin_flag': tinFlag,
      'ledger_title': ledgerName,
      'opening_balance': openingBalance,
      'sales_taxable_total': salesTaxableTotal,
      'purchase_taxable_total': purchaseTaxableTotal,
      'sales_return_taxable_total': salesReturnTaxableTotal,
      'purchase_return_taxable_total': purchaseReturnTaxableTotal,
      'closing_balance': closingBalance,
      'ledger_id': ledgerId
    };
  }

  factory AnnexItemModel.fromJson(Map<String, dynamic> map) {
    return AnnexItemModel(
      tinNo: map['tin_no'],
      tinFlag: map['tin_flag'],
      ledgerId: map['ledger_id'],
      ledgerName: map['ledger_title'],
      openingBalance: map['opening_balance'],
      salesTaxableTotal: map['sales_total_with_vat'],
      purchaseTaxableTotal: map['purchase_total_with_vat'],
      salesReturnTaxableTotal: map['sales_return_total_with_vat'],
      purchaseReturnTaxableTotal: map['purchase_return_total_with_vat'],
      closingBalance: map['closing_balance'],
    );
  }
}

class LedgerWithOpening {
  String? ledgerId;
  double? balanceAmount;
  LedgerWithOpening({this.ledgerId, this.balanceAmount});

  factory LedgerWithOpening.fromJson(Map<String, dynamic> map) {
    return LedgerWithOpening(
      ledgerId: map['ledger_id'],
      balanceAmount: map['balance_amount'],
    );
  }
}
