import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/custom_report.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/expense.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/payment_reciept.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/payment_voucher.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/purchase_invoice.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/sales_report.dart';
import 'package:open_file/open_file.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';
import 'invoice.dart';

// void main() {
//   debugDefaultTargetPlatformOverride = TargetPlatform.fuchsia;
//   runApp(MaterialApp(debugShowCheckedModeBanner: false, home: Preview()));
// }

class Preview extends StatefulWidget {
  @override
  MyAppState createState() {
    return MyAppState();
  }
}

class MyAppState extends State<Preview> with SingleTickerProviderStateMixin {
  final doc = pw.Document();
  List<Tab>? _myTabs;
  List<LayoutCallback>? _tabGen;
  List<String>? _tabUrl;
  int _tab = 0;
  TabController? _tabController;

  PrintingInfo? printingInfo;

  @override
  void initState() {
    super.initState();
    _init();
  }

  Future<void> _init() async {
    final info = await Printing.info();

    _myTabs = const <Tab>[
      Tab(text: 'INVOICE'),
      Tab(text: 'PURCHASE'),
      Tab(text: 'RECEIPT'),
      Tab(text: 'PAYMENT'),
      Tab(text: 'EXPENSE'),
      Tab(text: 'SALES'),
      Tab(text: 'CUSTOM'),
    ];

    _tabGen = const <LayoutCallback>[
      generateInvoice,
      purchaseInvoice,
      receiptVoucher,
      paymentVoucher,
      expense,
      salesReport,
      customReport,
    ];

    _tabUrl = const <String>[
      'invoice.dart',
      'payment_receipt.dart',
      'payment_voucher.dart',
      'expense.dart',
      'Purchase_invoice.dart',
      'sales_report.dart',
      'custom_report.dart'
    ];

    _tabController = TabController(
      vsync: this,
      length: _myTabs!.length,
      initialIndex: _tab,
    );
    _tabController!.addListener(() {
      setState(() {
        _tab = _tabController!.index;
      });
    });

    setState(() {
      printingInfo = info;
    });
  }

  Future<void> _saveAsFile(BuildContext context, LayoutCallback build,
      PdfPageFormat pageFormat) async {
    final bytes = await build(pageFormat);
    final appDocDir = await getApplicationDocumentsDirectory();
    final appDocPath = appDocDir.path;
    final file = File('$appDocPath/document.pdf');
    // Log.d('Save as file ${file.path} ...');
    await file.writeAsBytes(bytes);

    await OpenFile.open(file.path);
  }

  @override
  Widget build(BuildContext context) {
    pw.RichText.debug = true;

    if (_tabController == null) {
      return const Center(child: CircularProgressIndicator());
    }

    final actions = <PdfPreviewAction>[
      if (!kIsWeb)
        PdfPreviewAction(
          icon: const Icon(Icons.save),
          onPressed: _saveAsFile,
        )
    ];

    return SafeArea(
        child: Scaffold(
            appBar: AppBar(
              title: const Text(''),
              bottom: TabBar(
                isScrollable: true,
                controller: _tabController,
                tabs: _myTabs!,
              ),
            ),
            body: PdfPreview(
              maxPageWidth: 700,
              build: _tabGen![_tab],
              actions: actions,
            ),
            floatingActionButton: Container(
                margin: const EdgeInsets.only(bottom: 40),
                child: FittedBox(
                  child: FloatingActionButton.extended(
                      backgroundColor: Colors.deepOrange,
                      label: const Text('Image'),
                      icon: const Icon(Icons.share),
                      onPressed: () {
                        // Navigator.push(context,
                        //     MaterialPageRoute(builder: (context) => ExampleApp()));
                      }),
                ))));
  }
}
