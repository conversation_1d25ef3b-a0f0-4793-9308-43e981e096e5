// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/reminder_model.dart';
import 'package:mobile_khaata_v2/app/modules/reminder_module/reminder_list/reminder_list_controller.dart';
import 'package:mobile_khaata_v2/database/reminder_period.dart';
import 'package:mobile_khaata_v2/database/reminder_type.dart';
import 'package:mobile_khaata_v2/utilities/shared_pref_helper1.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class ReminderListPage extends StatefulWidget {
  const ReminderListPage({super.key});

  @override
  _ReminderListPageState createState() => _ReminderListPageState();
}

class _ReminderListPageState extends State<ReminderListPage>
    with SingleTickerProviderStateMixin {
  final String tag = "Reminder List Page";

  final reminderListController =
      Get.put(ReminderListController(), tag: "ReminderListController");

  bool showSearchBar = false;
  final FocusNode searchBoxFocus = FocusNode();
  String searchBoxPlaceholder = "Search";

  TabController? tabController;

  List<dynamic> events = [];

  @override
  void initState() {
    reminderListController.init();
    tabController = TabController(length: 2, vsync: this)
      ..addListener(tabBarOnChangeHandler);
    getEvents();
    super.initState();
  }

  tabBarOnChangeHandler() {
    if (showSearchBar) showSearchBar = false;

    if (0 == tabController!.previousIndex) {
      reminderListController.searchUpcoming("");
    } else {
      reminderListController.searchCompleted("");
    }

    setState(() {});
  }

  searchButtonOnPressedHandler() {
    showSearchBar = !showSearchBar;

    if (showSearchBar) searchBoxFocus.requestFocus();
    searchBoxPlaceholder = "Search For";
    if (0 == tabController!.index) {
      reminderListController.searchUpcoming("");
    } else {
      // searchBoxPlaceholder = "एकाइ (Unit)";
      reminderListController.searchCompleted("");
    }

    setState(() {});
  }

  searchBoxOnChangeHandler(String searchString) {
    if (0 == tabController!.index) {
      reminderListController.searchUpcoming(searchString);
    } else {
      reminderListController.searchCompleted(searchString);
    }

    setState(() {});
  }

  getEvents() async {
    String eventList = await SharedPrefHelper1().getEvents();
    events.clear();
    events.addAll((eventList).split("___"));
    setState(() {});
    // Log.d("all events $events");
  }

  @override
  void dispose() {
    reminderListController.onClose();
    tabController!.dispose();
    super.dispose();
  }

  // searchButtonOnPressedHandler() {
  //   showSearchBar = !showSearchBar;

  //   if (showSearchBar) {
  //     searchBoxFocus.requestFocus();
  //   } else {
  //     reminderListController.searchReminder("");
  //   }

  //   searchBoxPlaceholder = "Search";

  //   setState(() {});
  // }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        // resizeToAvoidBottomPadding: false,
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 0,
          leading: BackButton(
            onPressed: () => Navigator.pop(context, false),
          ),
          titleSpacing: -5.0,
          centerTitle: false,
          title: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              (showSearchBar)
                  ? SizedBox(
                      width: MediaQuery.of(context).size.width * 0.56,
                      height: 50,
                      child: FormBuilderTextField(
                        name: "searchBox",
                        autocorrect: false,
                        keyboardType: TextInputType.text,
                        textInputAction: TextInputAction.done,
                        focusNode: searchBoxFocus,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 18,
                        ),
                        decoration: InputDecoration(
                          hintText: searchBoxPlaceholder,
                          contentPadding: EdgeInsets.zero,
                          prefixStyle: formFieldTextStyle,
                          hintStyle: const TextStyle(
                            color: Colors.white60,
                            fontSize: 20,
                          ),
                          border: InputBorder.none,
                        ),
                        onChanged: (searchString) {
                          searchBoxOnChangeHandler(searchString ?? "");
                          // reminderListController.searchReminder(searchString);
                        },
                      ),
                    )
                  : const Text(
                      "रिमाइन्डर सूची (Reminder List)",
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontFamily: 'HelveticaRegular',
                        fontWeight: FontWeight.bold,
                      ),
                    ),
            ],
          ),
          actions: <Widget>[
            IconButton(
              icon: (showSearchBar)
                  ? const Icon(
                      Icons.cancel,
                      color: Colors.white,
                    )
                  : const Icon(
                      Icons.search,
                      color: Colors.white,
                    ),
              onPressed: () => searchButtonOnPressedHandler(),
            )
          ],
        ),
        body: Column(
          children: [
            Container(
              height: 50,
              decoration: BoxDecoration(color: colorPrimary),
              child: TabBar(
                controller: tabController,
                indicatorColor: Colors.white,
                labelStyle: const TextStyle(
                  fontSize: 14,
                ),
                unselectedLabelColor: Colors.white54,
                indicator: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.white,
                      width: 5,
                    ),
                  ),
                ),
                tabs: const [
                  Tab(
                    child: Text(
                      "आगामी\n(Upcoming)",
                      textAlign: TextAlign.center,
                    ),
                  ),
                  Tab(
                    child: Text(
                      "पुरानो\n(Completed)",
                      textAlign: TextAlign.center,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: tabController,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      Obx(
                        () {
                          return DropdownButton<String>(
                            hint: const Text('Select Date'),
                            value:
                                reminderListController.filterUpcomigDate.isEmpty
                                    ? null
                                    : reminderListController.filterUpcomigDate,
                            isExpanded: true,
                            onChanged: (newValue) {
                              reminderListController.updateFilteredUpcoming(
                                newValue ?? "",
                              );
                            },
                            items: const [
                              DropdownMenuItem(
                                value: 'week',
                                child: Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: Text('Next 7 Days'),
                                ),
                              ),
                              DropdownMenuItem(
                                value: 'month',
                                child: Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: Text('Next 30 days'),
                                ),
                              ),
                              DropdownMenuItem(
                                value: 'all',
                                child: Padding(
                                  padding: EdgeInsets.all(8.0),
                                  child: Text('All'),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                      Obx(
                        () {
                          if (reminderListController.isLoading) {
                            return Container(
                              color: Colors.white,
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            );
                          } else {
                            return Expanded(
                              child: Container(
                                padding: const EdgeInsets.only(top: 6),
                                color: backgroundColorShade,
                                child: _UpcomingReminderListView(
                                  reminderListController,
                                ),
                              ),
                            );
                          }
                        },
                      ),
                    ],
                  ),
                  Obx(
                    () {
                      if (reminderListController.isLoading) {
                        return Container(
                          color: Colors.white,
                          child: const Center(
                            child: CircularProgressIndicator(),
                          ),
                        );
                      } else {
                        return Container(
                          padding: const EdgeInsets.only(top: 5),
                          color: backgroundColorShade,
                          child: _CompletedReminderListView(
                            reminderListController,
                          ),
                        );
                      }
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
        floatingActionButton: SizedBox(
          width: 45,
          child: FloatingActionButton(
            onPressed: () async {
              Navigator.pushNamed(context, "/addEditReminder");

              // var pendingNotificationRequests =
              //     await flutterLocalNotificationsPlugin
              //         .pendingNotificationRequests();
              // Log.d(pendingNotificationRequests.toString());
            },
            backgroundColor: colorPrimary,
            elevation: 6,
            child: const Icon(
              Icons.add,
              size: 25,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}

class _UpcomingReminderListView extends StatelessWidget {
  final ReminderListController reminderListController;

  const _UpcomingReminderListView(this.reminderListController);

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        if (reminderListController.filteredUpcomings.isEmpty) {
          return const SizedBox(
            width: double.infinity,
            child: Center(
              child: Text(
                "No Records",
                style: TextStyle(color: Colors.black54),
              ),
            ),
          );
        } else {
          return ListView.builder(
            padding: const EdgeInsets.only(bottom: 80),
            itemCount: reminderListController.filteredUpcomings.length,
            shrinkWrap: true,
            itemBuilder: (context, int index) {
              ReminderModel reminder =
                  reminderListController.filteredUpcomings[index];

              return InkWell(
                onTap: () {
                  reminderListController.reminderOnTap(context, reminder);
                },
                child: Container(
                  margin: const EdgeInsets.only(bottom: 2, left: 2, right: 2),
                  child: Card(
                    child: Column(
                      children: [
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(6.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              _reminderDateFormat(
                                reminder.reminderCategory ?? "",
                                "${reminder.startDateBS}",
                              ),
                              _trailingIcon(reminder.reminderCategory ?? ""),
                            ],
                          ),
                        ),
                        const Divider(
                          height: 5,
                        ),
                        ListTile(
                          visualDensity: const VisualDensity(
                            horizontal: -4,
                            vertical: -4,
                          ),
                          contentPadding: const EdgeInsets.all(10),
                          leading: _leadingIcon(reminder.reminderType!),
                          title: Text(
                            "${reminder.description}",
                            style: TextStyle(color: textColor),
                          ),
                          // trailing: _trailingIcon(reminder.reminderCategory),

                          // subtitle: Container(
                          //   margin: EdgeInsets.only(top: 5),
                          //   decoration: BoxDecoration(border: Border(top: BorderSide(color: Colors.white60))),
                          //   padding: EdgeInsets.only(top: 5),
                          //   child: _reminderDateFormat(reminder.reminderCategory, "${reminder.startDateBS} ${reminder.startTime}"),
                          // ),
                        ),
                        const Divider(
                          height: 5,
                        ),
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.all(6.0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "${ReminderType.reminderTypeText[reminder.reminderType]}",
                                style: const TextStyle(fontSize: 12),
                                textAlign: TextAlign.right,
                              ),
                              Text(
                                "${ReminderPeriod.reminderPeriodText[reminder.reminderPeriod]}",
                                style: const TextStyle(fontSize: 12),
                                textAlign: TextAlign.right,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          );
        }
      },
    );
  }
}

class _CompletedReminderListView extends StatelessWidget {
  final ReminderListController reminderListController;

  const _CompletedReminderListView(this.reminderListController);

  @override
  Widget build(BuildContext context) {
    if (reminderListController.completed.isEmpty) {
      return const SizedBox(
        width: double.infinity,
        child: Center(
          child: Text(
            "No Records",
            style: TextStyle(color: Colors.black54),
          ),
        ),
      );
    } else {
      return ListView.builder(
        padding: const EdgeInsets.only(bottom: 80),
        itemCount: reminderListController.completed.length,
        shrinkWrap: true,
        itemBuilder: (context, int index) {
          ReminderModel reminder = reminderListController.completed[index];

          return InkWell(
            onTap: () {
              reminderListController.reminderOnTap(context, reminder);
            },
            child: Container(
              margin: const EdgeInsets.only(bottom: 2, left: 2, right: 2),
              child: Card(
                child: Column(
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(6.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _reminderDateFormat(
                            reminder.reminderCategory ?? "",
                            "${reminder.startDateBS}",
                          ),
                          _trailingIcon(reminder.reminderCategory ?? ""),
                        ],
                      ),
                    ),
                    const Divider(
                      height: 5,
                    ),
                    ListTile(
                      visualDensity:
                          const VisualDensity(horizontal: -4, vertical: -4),
                      contentPadding: const EdgeInsets.all(10),
                      leading: _leadingIcon(reminder.reminderType!),
                      title: Text(
                        "${reminder.description}",
                        style: TextStyle(color: textColor),
                      ),
                      // trailing: _trailingIcon(reminder.reminderCategory),

                      // subtitle: Container(
                      //   margin: EdgeInsets.only(top: 5),
                      //   decoration: BoxDecoration(border: Border(top: BorderSide(color: Colors.white60))),
                      //   padding: EdgeInsets.only(top: 5),
                      //   child: _reminderDateFormat(reminder.reminderCategory, "${reminder.startDateBS} ${reminder.startTime}"),
                      // ),
                    ),
                    const Divider(
                      height: 5,
                    ),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(6.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "${ReminderType.reminderTypeText[reminder.reminderType]}",
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.right,
                          ),
                          Text(
                            "${ReminderPeriod.reminderPeriodText[reminder.reminderPeriod]}",
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.right,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      );
    }
  }
}

class _ReminderListView extends StatelessWidget {
  final ReminderListController reminderListController;

  const _ReminderListView(this.reminderListController);

  @override
  Widget build(BuildContext context) {
    if (reminderListController.filteredReminders.isEmpty) {
      return const SizedBox(
        width: double.infinity,
        child: Center(
          child: Text(
            "No Records",
            style: TextStyle(color: Colors.black54),
          ),
        ),
      );
    } else {
      return Expanded(
        child: ListView.builder(
          padding: const EdgeInsets.only(bottom: 80),
          itemCount: reminderListController.filteredReminders.length,
          shrinkWrap: true,
          itemBuilder: (context, int index) {
            ReminderModel reminder =
                reminderListController.filteredReminders[index];

            return InkWell(
              onTap: () {
                reminderListController.reminderOnTap(context, reminder);
              },
              child: Card(
                child: Column(
                  children: [
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(6.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _reminderDateFormat(reminder.reminderCategory ?? "",
                              "${reminder.startDateBS} ${reminder.startTime}"),
                          Text(
                            "${ReminderType.reminderTypeText[reminder.reminderType]}",
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.right,
                          ),
                        ],
                      ),
                    ),
                    const Divider(
                      height: 5,
                    ),
                    ListTile(
                      visualDensity:
                          const VisualDensity(horizontal: -4, vertical: -4),
                      contentPadding: const EdgeInsets.all(10),
                      leading: CircleAvatar(
                        radius: 18,
                        backgroundColor: colorPrimary,
                        child: _leadingIcon(reminder.reminderType!),
                      ),
                      title: Text(
                        "${reminder.description} ${reminder.reminderId}",
                        style: TextStyle(color: textColor),
                      ),
                      // trailing: _trailingIcon(reminder.reminderCategory),

                      // subtitle: Container(
                      //   margin: EdgeInsets.only(top: 5),
                      //   decoration: BoxDecoration(border: Border(top: BorderSide(color: Colors.white60))),
                      //   padding: EdgeInsets.only(top: 5),
                      //   child: _reminderDateFormat(reminder.reminderCategory, "${reminder.startDateBS} ${reminder.startTime}"),
                      // ),
                    ),
                    const Divider(
                      height: 5,
                    ),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(6.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _trailingIcon(reminder.reminderCategory ?? ""),
                          Text(
                            "${ReminderPeriod.reminderPeriodText[reminder.reminderPeriod]}",
                            style: const TextStyle(fontSize: 12),
                            textAlign: TextAlign.right,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      );
    }
  }
}

Widget _leadingIcon(int reminderType) {
  if (reminderType == ReminderType.task) {
    return Icon(
      Icons.notifications,
      color: colorPrimary,
      size: 30,
    );
  } else {
    return Image.asset(
      'images/purchase-small.png',
      height: 30.0,
      fit: BoxFit.cover,
    );
  }
}

Widget _trailingIcon(String reminderCategory) {
  if ("pending" == reminderCategory) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Icon(
          Icons.pending_actions_rounded,
          color: colorRedDark,
          size: 15,
        ),
        Text(
          "Pending",
          style: TextStyle(
            fontSize: 10,
            color: colorRedDark,
          ),
        )
      ],
    );
  } else if ("upcoming" == reminderCategory) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: const [
        Icon(
          Icons.notifications_rounded,
          color: Colors.orange,
          size: 15,
        ),
        Text(
          "Upcoming",
          style: TextStyle(
            fontSize: 10,
            color: Colors.orange,
          ),
        )
      ],
    );
  } else {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: const [
        Icon(
          Icons.check_box_sharp,
          color: Colors.green,
          size: 15,
        ),
        Text(
          "Completed",
          style: TextStyle(
            fontSize: 10,
            color: Colors.green,
          ),
        )
      ],
    );
  }
}

Widget _reminderDateFormat(String reminderCategory, String textValue) {
  if ("pending" == reminderCategory) {
    return Text(
      textValue,
      style: TextStyle(color: colorRedDark),
    );
  } else if ("upcoming" == reminderCategory) {
    return Text(
      textValue,
      style: const TextStyle(color: Colors.orange),
    );
  } else {
    return Text(
      textValue,
      style: const TextStyle(color: Colors.green),
    );
  }
}
