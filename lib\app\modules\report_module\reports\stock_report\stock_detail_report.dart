import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
import 'package:mobile_khaata_v2/app/model/others/item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/stock_detail_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/stock_report/report_stock_transaction_controller.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import 'package:nepali_date_picker/nepali_date_picker.dart';

// ignore: must_be_immutable
class StockDetailReport extends StatelessWidget {
  // ignore: prefer_final_fields
  ReportStockTransactionController _controller =
      ReportStockTransactionController();
  String startDate = currentDate;
  String endDate = currentDate;

  StockDetailReport({super.key}) {
    generate();
  }

  generate() {
    _controller.generateStockDetailReport(
        startDate: startDate, endDate: endDate);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        elevation: 0,
        titleSpacing: -5.0,
        backgroundColor: colorPrimary,
        title: const Text(
          "स्टक डिटेल रिपोर्ट\n(Stock Detail Report)",
          style: TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
        actions: [
          PrintButton(
            onPressed: () {
              Navigator.of(context).pushNamed('/printStockDetailReport',
                  arguments: StockDetailPrintPage(
                    transactions: _controller.itemDetailList,
                    pageTitle: "Stock Detail Report",
                    startDate: startDate,
                    endDate: endDate,
                  ));
            },
          )
        ],
      ),
      body: Container(
        color: Colors.black12,
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Expanded(
                    flex: 2,
                    child: ReportCustomDatePickerTextField(
                      initialValue: toDateBS(DateTime.parse(startDate)),
                      hintText: "From Date",
                      onChange: (selectedDate) {
                        startDate =
                            toDateAD(NepaliDateTime.parse(selectedDate));
                        generate();
                      },
                    ),
                  ),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: Text(
                        "TO",
                        style: labelStyle2,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: ReportCustomDatePickerTextField(
                      initialValue: toDateBS(DateTime.parse(endDate)),
                      hintText: "To Date",
                      onChange: (selectedDate) {
                        endDate = toDateAD(NepaliDateTime.parse(selectedDate));
                        generate();
                      },
                    ),
                  ),
                ],
              ),
            ),
            const Divider(
              height: 0,
              color: Colors.black54,
            ),
            DefaultTextStyle(
              style: TextStyle(
                  fontSize: 12, color: textColor, fontWeight: FontWeight.bold),
              child: Container(
                color: Colors.white,
                padding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    //====================================1st Column
                    Expanded(
                      flex: 1,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: const [
                          Text(
                            "Item Name",
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          ),
                        ],
                      ),
                    ),

                    //====================================2nd Column
                    const Expanded(
                      flex: 1,
                      child: Text(
                        "Opening",
                        textAlign: TextAlign.right,
                      ),
                    ),

                    //====================================3rd Column
                    const Expanded(
                      flex: 1,
                      child: Text(
                        "In Qty",
                        textAlign: TextAlign.right,
                      ),
                    ),

                    //====================================4th Column
                    const Expanded(
                      flex: 1,
                      child: Text(
                        "Out Qty",
                        textAlign: TextAlign.right,
                      ),
                    ),

                    //====================================5th Column
                    const Expanded(
                      flex: 1,
                      child: Text(
                        "Closing",
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const Divider(
              height: 0,
              color: Colors.black54,
            ),
            Obx(() {
              if (_controller.txnLoading) {
                return Container(
                    color: Colors.white,
                    child: const Center(child: CircularProgressIndicator()));
              }

              if (_controller.itemDetailList.isEmpty) {
                return Container(
                    color: Colors.white,
                    width: double.infinity,
                    child: const Center(
                        child: Text(
                      "No Records",
                      style: TextStyle(color: Colors.black54),
                    )));
              } else {
                return Expanded(
                    child: _TxnListView(_controller.itemDetailList));
              }
            }),
          ],
        ),
      ),
    ));
    // return SafeArea(child: Scaffold(
    //   // resizeToAvoidBottomPadding: true,
    //   resizeToAvoidBottomInset: true,

    //   bottomNavigationBar: Container(
    //     height: 45,
    //     padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
    //     color: colorPrimary,
    //     child: SingleChildScrollView(
    //       child: Obx(() {
    //         return DefaultTextStyle(
    //           style: const TextStyle(
    //             color: Colors.white,
    //             fontSize: 16,
    //           ),
    //           child: Column(
    //             crossAxisAlignment: CrossAxisAlignment.start,
    //             children: [
    //               Text(
    //                 "Total Qty: ${formatCurrencyAmount(_controller.totalQty, false)}",
    //                 textAlign: TextAlign.right,
    //               ),
    //             ],
    //           ),
    //         );
    //       }),
    //     ),
    //   ),
    // );
  }
}

class _TxnListView extends StatelessWidget {
  final List<ItemDetailModel> _itemDetailList;

  const _TxnListView(this._itemDetailList);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: _itemDetailList.length,
      itemBuilder: (context, int index) {
        ItemDetailModel txn = _itemDetailList[index];

        return InkWell(
          // onTap: () => TransactionHelper.gotoTransactionEditPage(
          //     context, txn.txnId, txn.txnType),
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                DefaultTextStyle(
                  style: TextStyle(fontSize: 12, color: colorPrimary),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        //====================================1st Column
                        Expanded(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${txn.itemName}",
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                            ],
                          ),
                        ),

                        //====================================2nd Column
                        Expanded(
                          flex: 1,
                          child: Text(
                            (txn.openingQuantity)!.toStringAsFixed(2),
                            textAlign: TextAlign.right,
                          ),
                        ),

                        //====================================3rd Column
                        Expanded(
                          flex: 1,
                          child: Text(
                            (txn.inQuantity)!.toStringAsFixed(2),
                            textAlign: TextAlign.right,
                          ),
                        ),

                        //====================================4th Column
                        Expanded(
                          flex: 1,
                          child: Text(
                            (txn.outQuantity)!.toStringAsFixed(2),
                            textAlign: TextAlign.right,
                          ),
                        ),

                        //====================================5th Column
                        Expanded(
                          flex: 1,
                          child: Text(
                            (txn.balanceQuantity)!.toStringAsFixed(2),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                //Add space if last element
                if (_itemDetailList.length - 1 == index) ...{
                  const SizedBox(
                      // height: 100,
                      )
                },
              ],
            ),
          ),
        );
      },
    );
  }
}
