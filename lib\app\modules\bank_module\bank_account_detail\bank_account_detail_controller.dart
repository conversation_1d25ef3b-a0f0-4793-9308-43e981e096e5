import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/payment_type_model.dart';
import 'package:mobile_khaata_v2/app/model/others/bank_transaction_model.dart';
import 'package:mobile_khaata_v2/app/repository/bank_adjustment_repository.dart';
import 'package:mobile_khaata_v2/app/repository/payment_type_repository.dart';

class BankAccountDetailController extends GetxController {
  final String tag = "BankAccountDetailController";
  var _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  var _txnLoading = true.obs;
  bool get txnLoading => _txnLoading.value;

  var _isSearching = true.obs;
  bool get isSearching => _isSearching.value;

  PaymentTypeModel? _bank;
  PaymentTypeModel? get bank => _bank;

  final PaymentTypeRepository _paymentTypeRepository = PaymentTypeRepository();
  final BankAdjustmentRepository _bankAdjustmentRepository =
      BankAdjustmentRepository();

  List<BankTransactionModel> transactions = [];

  List<BankTransactionModel> _filteredTransactions = [];
  List<BankTransactionModel> get filteredTransactions => _filteredTransactions;

  reload() async {
    await init(bank!.pmtTypeId ?? "");
  }

  init(String id) async {
    getBankAccount(id);
    loadTransaction(id);
  }

  loadTransaction(String id) async {
    _txnLoading(true);
    List<BankTransactionModel> txn =
        await _bankAdjustmentRepository.getTransactionsForBank(id);
    transactions = txn;
    _filteredTransactions.clear();
    _filteredTransactions.addAll(transactions);
    _txnLoading(false);
  }

  getBankAccount(String id) async {
    _isLoading(true);
    _bank = await _paymentTypeRepository.getBankById(id);
    _bank!.pmtTypeCurrentBalance =
        await _bankAdjustmentRepository.getCurrentBankBalance(id);
    _isLoading(false);
  }

  searchTransaction(String searchString) {
    _isSearching(true);
    _filteredTransactions.clear();
    for (var item in transactions) {
      _filteredTransactions.addIf(
          item
              .toString()
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          item);
    }
    _isSearching(false);
    update();
  }
}
