// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/add_edit_bank_account/add_edit_bank_account_controller.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/repository/payment_type_repository.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

class AddEditBankAccountPage extends StatelessWidget {
  final String tag = "AddEditBankAccountPage";

  final String? bankId;
  final controller = AddEditBankAccountController();

  AddEditBankAccountPage({super.key, this.bankId}) {
    controller.bank.pmtTypeId = bankId;
    controller.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
        // resizeToAvoidBottomPadding: true,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          leading: BackButton(
            onPressed: () => Navigator.pop(context, false),
          ),
          centerTitle: false,
          backgroundColor: colorPrimary,
          titleSpacing: -10.0,
          title: ListTile(
            contentPadding: const EdgeInsets.only(right: 15),
            title: Text(
              (!controller.editFlag)
                  ? "नयाँ बैंक/सहकारीहरू खाता\n(New Bank/Co-operatives Account)"
                  : "बैंक/सहकारीहरू खाता\n(Edit Bank/Co-operatives Account)",
              style: const TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontFamily: 'HelveticaRegular',
                  fontWeight: FontWeight.bold),
            ),
          ),
          actions: [
            if (controller.editFlag) ...{
              InkWell(
                  onTap: () async {
                    Tuple2<bool, String> checkResponse = await checkPermission(
                        context: context,
                        forPermission: PermissionManager.bankEdit);
                    if (checkResponse.item1) {
                      controller.readOnlyFlag = !controller.readOnlyFlag;
                    } else {
                      //no  permission
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "",
                          message: checkResponse.item2);
                    }
                  },
                  splashColor: colorPrimaryLighter,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 15),
                    child: (controller.readOnlyFlag)
                        ? Column(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(
                                Icons.mode_edit,
                                color: Colors.white,
                              ),
                              Text(
                                "Edit",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          )
                        : Column(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(
                                Icons.close,
                                color: Colors.white,
                              ),
                              Text(
                                "Cancel",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          ),
                  )),
            }
          ],
        ),
        body: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            child: Form(
              key: controller.formKey,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    //===============================================Bank Name
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "बैंक/सहकारी को नाम",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "bank_name",
                          autocorrect: false,
                          keyboardType: TextInputType.text,
                          textInputAction: TextInputAction.next,
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              labelText: "Bank/Co-operative Name"),
                          readOnly: controller.readOnlyFlag,
                          initialValue: controller.bank.pmtTypeBankName,
                          onChanged: (value) {
                            controller.bank.pmtTypeBankName = strTrim(value!);
                          },
                          validator: (value) {
                            if (null == value || value.isEmpty) {
                              return "बैंक/सहकारी नाम राख्नुहोस् (Fill Bank's Name)";
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),

                    //===============================================Account Name
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "खातावालाको नाम",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "bank_acc_name",
                          autocorrect: false,
                          keyboardType: TextInputType.text,
                          textInputAction: TextInputAction.next,
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              labelText: "Account Person's Name"),
                          readOnly: controller.readOnlyFlag,
                          initialValue: controller.bank.pmtTypeBankAccountName,
                          onChanged: (value) {
                            controller.bank.pmtTypeBankAccountName =
                                strTrim(value!);
                          },
                          validator: (value) {
                            if (null == value || value.isEmpty) {
                              return "खातावालाको नाम राख्नुहोस् (Account Person's Name)";
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),

                    //===============================================Account Number
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "खाता नम्बर",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "bank_acc_no",
                          autocorrect: false,
                          keyboardType: TextInputType.text,
                          textInputAction: TextInputAction.next,
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              labelText: "Account's Number"),
                          readOnly: controller.readOnlyFlag,
                          initialValue:
                              controller.bank.pmtTypeBankAccountNumber,
                          onChanged: (value) {
                            controller.bank.pmtTypeBankAccountNumber =
                                strTrim(value!);
                          },
                          validator: (value) {
                            if (null == value || value.isEmpty) {
                              return "खाता नम्बर राख्नुहोस् (Fill Account's Number)";
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),

                    //===============================================Account's Branch
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "खाताको शाखा",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "bank_acc_branch",
                          autocorrect: false,
                          keyboardType: TextInputType.text,
                          textInputAction: TextInputAction.next,
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              labelText: "Account's Branch"),
                          readOnly: controller.readOnlyFlag,
                          initialValue:
                              controller.bank.pmtTypeBankAccountBranch,
                          onChanged: (value) {
                            controller.bank.pmtTypeBankAccountBranch =
                                strTrim(value!);
                          },
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),

                    //===============================================Short Name
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "देखाउने नाम (बैंक/सहकारीको)",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "short_name",
                          autocorrect: false,
                          keyboardType: TextInputType.text,
                          textInputAction: TextInputAction.next,
                          style: formFieldTextStyle,
                          maxLength: 30,
                          decoration: formFieldStyle.copyWith(
                              labelText: "Bank's Short Display Name"),
                          readOnly: controller.readOnlyFlag,
                          initialValue: controller.bank.pmtTypeShortName,
                          onChanged: (value) {
                            controller.bank.pmtTypeShortName = strTrim(value!);
                          },
                          validator: (value) {
                            if (null == value || value.isEmpty) {
                              return "देखाउने नाम (बैंक/सहकारीको) राख्नुहोस् (Fill Bank's Short Display Name)";
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),

                    Row(
                      children: [
                        //=======================================Opening Balance Field
                        Expanded(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "सुरु मौज्दात",
                                style: labelStyle2,
                              ),
                              const SizedBox(height: 5.0),
                              FormBuilderTextField(
                                name: "opening_bal",
                                autocorrect: false,
                                keyboardType:
                                    const TextInputType.numberWithOptions(
                                        decimal: true),
                                textInputAction: TextInputAction.next,
                                style: formFieldTextStyle,
                                maxLength: 10,
                                decoration: formFieldStyle.copyWith(
                                    labelText: "Opening Balance",
                                    counterText: ''),
                                readOnly: controller.readOnlyFlag,
                                initialValue: controller
                                    .bank.pmtTypeOpeningBalance
                                    ?.toString(),
                                onChanged: (value) {
                                  controller.bank.pmtTypeOpeningBalance =
                                      parseDouble(value);
                                },
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(width: 10.0),

                        //=================================Opening Balance Date Field
                        Expanded(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "सुरु मौज्दात मिति",
                                style: labelStyle2,
                              ),
                              const SizedBox(height: 5.0),
                              CustomDatePickerTextField(
                                readOnly: controller.readOnlyFlag,
                                maxBSDate: NepaliDateTime.now(),
                                initialValue:
                                    controller.bank.pmtTypeOpeningDateBS,
                                onChange: (selectedDate) {
                                  controller.bank.pmtTypeOpeningDateBS =
                                      selectedDate;
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20.0),
                  ],
                ),
              ),
            ),
          ),
        ),
        bottomNavigationBar: BottomSaveCancelButton(
          shadow: false,
          hasDelete:
              (controller.editFlag && !controller.readOnlyFlag) ? true : false,
          onDeleteBtnPressedFn: () async {
            showAlertDialog(context,
                okText: "YES",
                hasCancel: true,
                cancelText: "NO",
                alertType: AlertType.Error,
                alertTitle: "Confirm Delete", onCloseButtonPressed: () async {
              // Navigator.of(_).pop();
              ProgressDialog progressDialog = ProgressDialog(context,
                  type: ProgressDialogType.normal, isDismissible: false);
              progressDialog.update(
                  message: "Checking Permission. Please wait....");
              await progressDialog.show();
              Tuple2<bool, String> checkResp =
                  await PermissionWrapperController().requestForPermissionCheck(
                      forPermission: PermissionManager.bankDelete);
              if (checkResp.item1) {
                //has  permission
                progressDialog.update(
                    message: "Deleting Data. Please wait....");
                Tuple2<bool, String> deleteResp =
                    await PaymentTypeRepository().delete(bankId ?? "");
                await progressDialog.hide();
                if (deleteResp.item1) {
                  //  data deleted
                  TransactionHelper.refreshPreviousPages();
                  showAlertDialog(context,
                      barrierDismissible: false,
                      alertType: AlertType.Success,
                      alertTitle: "", onCloseButtonPressed: () {
                    Navigator.of(context).pop();
                    Navigator.of(context).pop();
                  }, message: deleteResp.item2);
                } else {
                  //cannot  delete  data
                  showAlertDialog(context,
                      alertType: AlertType.Error,
                      alertTitle: "",
                      message: deleteResp.item2);
                }
              } else {
                await progressDialog.hide();
                showAlertDialog(context,
                    alertType: AlertType.Error,
                    alertTitle: "",
                    message: checkResp.item2);
              }
            }, message: "Are you sure you  want to  delete this bank?");
          },
          enableFlag: !controller.readOnlyFlag,
          onSaveBtnPressedFn: (controller.readOnlyFlag)
              ? null
              : () async {
                  FocusScope.of(context).unfocus();

                  if (controller.bank.pmtTypeBankAccountNumber != null &&
                      controller.bank.pmtTypeBankAccountNumber!.length > 7 &&
                      controller.bank.pmtTypeBankAccountNumber!.length < 17) {
                    if (controller.formKey.currentState!.validate()) {
                      ProgressDialog progressDialog = ProgressDialog(context,
                          type: ProgressDialogType.normal,
                          isDismissible: false);
                      progressDialog.update(
                          message: "Saving data. Please wait....");
                      await progressDialog.show();

                      bool status = false;
                      try {
                        if (!await controller.checkUniqueShortName()) {
                          throw CustomException(
                              "देखाउने नाम पहिल्यै प्रयोगमा छ\n(Display name already in used.)");
                        }

                        if (!controller.editFlag) {
                          status = await controller.createBank();
                        } else {
                          status = await controller.updateBank();
                        }
                      } on CustomException catch (e) {
                        await progressDialog.hide();
                        showAlertDialog(context,
                            alertType: AlertType.Error,
                            alertTitle: "Error",
                            message: e.toString());
                        return;
                      } catch (e, trace) {
                        // Log.e(tag, e.toString() + trace.toString());
                      }

                      await progressDialog.hide();

                      if (status) {
                        Navigator.pop(context, true);

                        TransactionHelper.refreshPreviousPages();

                        String message = (controller.editFlag)
                            ? "Bank Account Updated Successfully."
                            : "Bank Account Created Successfully.";
                        showToastMessage(context,
                            message: message, duration: 2);
                      } else {
                        showToastMessage(context,
                            alertType: AlertType.Error,
                            message: "Failed to process operation",
                            duration: 2);
                      }
                    }
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "Error",
                        message:
                            "Account's Number must be between 8 to 16 digits");
                    return;
                  }
                },
        ),
      ));
    });
  }
}
