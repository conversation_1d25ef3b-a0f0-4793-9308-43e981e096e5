import 'dart:convert';

import 'package:mobile_khaata_v2/app/model/others/batched_group_model.dart';
import 'package:mobile_khaata_v2/app/model/others/synced_batched_model.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/app/repository/synced_batched_repository.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/notification_helper.dart';
import 'package:mobile_khaata_v2/utilities/shared_pref_helper1.dart';
import 'package:tuple/tuple.dart';

// TODO: CRITICAL - This file implements UNIDIRECTIONAL sync (push-only)
// TODO: Major Issue: Pull sync is completely disabled, causing web-mobile sync issues
// TODO: Priority 1: Re-enable bidirectional sync to fix:
//   1. App doesn't work if transactions are performed in web
//   2. Companies fail to load after doing any activity in web
//   3. Backup/sync fails after doing transactions from web

// TODO: Current sync flow: Mobile → Server (MISSING: Server → Mobile)
// TODO: Required: Implement Server → Mobile sync for web transaction support

Future<bool> pushPendingQueries(
    {bool showNotificationAfterResult = false,
    String source = "BACKGROUND",
    bool all = false,
    String? singleBatchId,
    dynamic dbClient}) async {
  bool status = false;

  List<BatchedGroupModel> pendingBatchQueries = await QueryRepository()
      .getPendingQueries(
          all: all, dbClient: dbClient, singleBatchId: singleBatchId);

  List<String?> syncedBatchIDs = [];
  List<SyncedBatchModel> syncedBatchModels = [];
  syncedBatchModels =
      await SyncedBatchRepository().getSyncedBatchIDs(dbClient: dbClient);

  syncedBatchIDs = syncedBatchModels.map((e) => e.batchID).toList();

  // Log.d("queries to push ${pendingBatchQueries.toString()}");

  if (pendingBatchQueries.length > 0 || syncedBatchIDs.length > 0) {
    ApiBaseHelper apiBaseHelper = new ApiBaseHelper();
    ApiResponse apiResponse = await apiBaseHelper.post(
        apiBaseHelper.PUSH_PENDING_QUERY,
        ({
          "pending_queries": jsonEncode(pendingBatchQueries),
          "synced_batch_id": syncedBatchIDs
        }),
        accessToken: true);

    // Log.d("api response ${apiResponse.status}");
    if (apiResponse.status) {
      status = true;
      var data = apiResponse.data ?? {};
      List<dynamic> successBatchIds = data['success_batch_id'] ?? [];

      //setting expired data to pref
      bool isExpired = data['is_account_expired'];

      // save backup information
      await SharedPrefHelper1()
          .updateBackupInfo({'last_synced_at': DateTime.now().toString()});

      try {
        int? isActive = int.tryParse(data['is_user_active']);
        await setActiveUser(active: isActive!);
        if (isActive == 0) {
          // push all pending queried upto now for inactive user
          Future.delayed(Duration(seconds: 10), () {
            pushPendingQueries(showNotificationAfterResult: false, all: true);
          });
        }
      } catch (e) {}

      await setExpiredUser(flag: isExpired);

      await SyncedBatchRepository().deleteSyncedBatches(syncedBatchModels);

      if (successBatchIds.length > 0) {
        await QueryRepository().deleteSyncedQueries(successBatchIds);
        if (showNotificationAfterResult) {
          //show notification for success of push
          showNotification(flutterLocalNotificationsPlugin,
              id: 1,
              body: "${successBatchIds.length} Records synced to server");
        }
      } else {}
    } else {
      // Log.d("erroe message" + apiResponse.msg!);
    }
  } else {
    status = true;
    //no any pending queries to push to server
  }

  return status;
}

Future<Tuple2<bool, String>> pushPendingQueriesWithMessage(
    {bool showNotificationAfterResult = false,
    String source = "BACKGROUND",
    bool all = false,
    String? singleBatchId,
    dynamic dbClient}) async {
  bool status = false;
  String message = "";

  List<BatchedGroupModel> pendingBatchQueries = await QueryRepository()
      .getPendingQueries(
          all: all, dbClient: dbClient, singleBatchId: singleBatchId);

  List<String?> syncedBatchIDs = [];
  List<SyncedBatchModel> syncedBatchModels = [];
  syncedBatchModels =
      await SyncedBatchRepository().getSyncedBatchIDs(dbClient: dbClient);

  syncedBatchIDs = syncedBatchModels.map((e) => e.batchID).toList();

  // Log.d("queries to push ${pendingBatchQueries.toString()}");

  // if (pendingBatchQueries.length > 0 || syncedBatchIDs.length > 0) {
  ApiBaseHelper apiBaseHelper = new ApiBaseHelper();
  ApiResponse apiResponse = await apiBaseHelper.post(
      apiBaseHelper.PUSH_PENDING_QUERY,
      ({
        "pending_queries": jsonEncode(pendingBatchQueries),
        "synced_batch_id": syncedBatchIDs
      }),
      accessToken: true);

  // Log.d("api response ${apiResponse.status}");
  if (apiResponse.status) {
    status = true;
    message = apiResponse.msg ?? "";
    var data = apiResponse.data ?? {};
    List<dynamic> successBatchIds = data['success_batch_id'] ?? [];

    //setting expired data to pref
    bool isExpired = data['is_account_expired'];

    // save backup information
    await SharedPrefHelper1()
        .updateBackupInfo({'last_synced_at': DateTime.now().toString()});

    try {
      int isActive = int.tryParse(data['is_user_active'])!;
      await setActiveUser(active: isActive);
      if (isActive == 0) {
        // push all pending queried upto now for inactive user
        Future.delayed(Duration(seconds: 10), () {
          pushPendingQueries(showNotificationAfterResult: false, all: true);
        });
      }
    } catch (e) {}

    await setExpiredUser(flag: isExpired);

    await SyncedBatchRepository().deleteSyncedBatches(syncedBatchModels);

    if (successBatchIds.length > 0) {
      await QueryRepository().deleteSyncedQueries(successBatchIds);
      if (showNotificationAfterResult) {
        //show notification for success of push
        showNotification(flutterLocalNotificationsPlugin,
            id: 1, body: "${successBatchIds.length} Records synced to server");
      }
    } else {}
  } else {
    status = false;
    message = apiResponse.msg ?? "";
    // Log.d("erroe message" + apiResponse.msg!);
  }

  return Tuple2(status, message);
}

// TODO: CRITICAL ISSUE - Pull sync functionality is COMPLETELY DISABLED
// TODO: This is the ROOT CAUSE of all web-mobile sync problems
// TODO: URGENTLY NEEDED: Re-enable and fix pullPendingQueries functions
// TODO: Current state: All pull sync code is commented out, making sync unidirectional
// TODO: Impact: Web transactions never reach mobile app, causing data inconsistency

// TODO: PRIORITY 1 - Uncomment and implement proper pull sync
// TODO: Required fixes for pullPendingQueries:
//   1. Add conflict resolution for when both web and mobile modify same data
//   2. Add data validation before applying server changes to local DB
//   3. Handle foreign key constraints properly
//   4. Add rollback mechanism for failed pull operations
//   5. Implement proper error handling and logging

// Future<bool> pullPendingQueries(
//     {bool showNotificationAfterResult = false,
//     String source = "BACKGROUND",
//     // String singleBatchId,
//     int pullCount = 10}) async {
//   bool status = true;
//   List<String?> syncedBatchIDs = [];
//   List<SyncedBatchModel> syncedBatchModels = [];
//   // if (null != singleBatchId) {
//   // syncedBatchModels = [SyncedBatchModel(batchID: singleBatchId)];
//   // } else {
//
//   syncedBatchModels = await SyncedBatchRepository().getSyncedBatchIDs();
//   // }
//   syncedBatchIDs = syncedBatchModels.map((e) => e.batchID).toList();
//
//   ApiBaseHelper apiBaseHelper = new ApiBaseHelper();
//
//   ApiResponse apiResponse = await apiBaseHelper.post(
//       apiBaseHelper.PULL_PENDING_QUERY,
//       {"synced_batch_id": syncedBatchIDs, "pull_query_count": pullCount},
//       accessToken: true);
//
//   if (apiResponse.status) {
//     dynamic data = apiResponse.data ?? {};
//     List<dynamic> pendingQueries = data['pending_queries'] ?? [];
//     // Log.d("apiresponse ${apiResponse.data}");
//     //setting expired data to pref
//     bool isExpired = data['is_account_expired'];
//
//     // save backup information
//     await SharedPrefHelper1()
//         .updateBackupInfo({'last_synced_at': DateTime.now().toString()});
//     try {
//       int? isActive = int.tryParse(data['is_user_active']);
//       await setActiveUser(active: isActive!);
//       if (isActive == 0) {
//         // push all pending queried upto now for inactive user
//         Future.delayed(Duration(seconds: 10), () {
//           pushPendingQueries(showNotificationAfterResult: false, all: true);
//         });
//       }
//     } catch (e) {}
//     await setExpiredUser(flag: isExpired);
//
//     await SyncedBatchRepository().deleteSyncedBatches(syncedBatchModels);
//
//     await Future.wait(pendingQueries.map((e) async {
//       bool status = await QueryRepository().executeSingleSyncedQueries(e);
//     }).toList());
//     if (showNotificationAfterResult && pendingQueries.length > 0) {
//       //show notification for success of push
//       showNotification(flutterLocalNotificationsPlugin,
//           id: 2, body: "${pendingQueries.length} Records synced from server");
//     }
//   } else {
//     // Log.d("erroe message" + apiResponse.msg!);
//   }
//
//   return status;
// }

// Future<Tuple2<bool, String>> pullPendingQueriesWithMessage(
//     {bool showNotificationAfterResult = false,
//     String source = "BACKGROUND",
//     // String singleBatchId,
//     int pullCount = 10}) async {
//   bool status = false;
//   String message = "";
//   List<String?> syncedBatchIDs = [];
//   List<SyncedBatchModel> syncedBatchModels = [];
//
//   syncedBatchModels = await SyncedBatchRepository().getSyncedBatchIDs();
//   // }
//
//   syncedBatchIDs = syncedBatchModels.map((e) => e.batchID).toList();
//
//   ApiBaseHelper apiBaseHelper = new ApiBaseHelper();
//
//   ApiResponse apiResponse = await apiBaseHelper.post(
//       apiBaseHelper.PULL_PENDING_QUERY,
//       {"synced_batch_id": syncedBatchIDs, "pull_query_count": pullCount},
//       accessToken: true);
//
//   if (apiResponse.status) {
//     status = true;
//     // Log.d("sennding message ${apiResponse.msg}");
//     message = apiResponse.msg ?? "";
//
//     dynamic data = apiResponse.data ?? {};
//     List<dynamic> pendingQueries = data['pending_queries'] ?? [];
//     // Log.d("apiresponse ${apiResponse.data}");
//     bool isExpired = data['is_account_expired'];
//
//     // save backup information
//     await SharedPrefHelper1()
//         .updateBackupInfo({'last_synced_at': DateTime.now().toString()});
//     try {
//       int isActive = int.tryParse(data['is_user_active'])!;
//       await setActiveUser(active: isActive);
//       if (isActive == 0) {
//         // push all pending queried upto now for inactive user
//         Future.delayed(Duration(seconds: 10), () {
//           pushPendingQueries(showNotificationAfterResult: false, all: true);
//         });
//       }
//     } catch (e) {}
//     await setExpiredUser(flag: isExpired);
//
//     await SyncedBatchRepository().deleteSyncedBatches(syncedBatchModels);
//
//     await Future.wait(pendingQueries.map((e) async {
//       bool status = await QueryRepository().executeSingleSyncedQueries(e);
//     }).toList());
//     if (showNotificationAfterResult && pendingQueries.length > 0) {
//       //show notification for success of push
//       showNotification(flutterLocalNotificationsPlugin,
//           id: 2, body: "${pendingQueries.length} Records synced from server");
//     }
//   } else {
//     status = false;
//     message = apiResponse.msg ?? "";
//     // Log.d("erroe message" + apiResponse.msg!);
//   }
//
//   return Tuple2(status, message);
// }
