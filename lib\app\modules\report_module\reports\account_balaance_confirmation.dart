import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/components/custom_dropdown.dart';
import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/party_list/party_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/account_balance_confirmation_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/report_controllers/vat_report_controller.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import 'package:nepali_date_picker/nepali_date_picker.dart';

// ignore: must_be_immutable
class AccountBalanceConfirmation extends StatefulWidget {
  AccountBalanceConfirmation();

  @override
  _AccountBalanceConfirmationState createState() =>
      _AccountBalanceConfirmationState();
}

class _AccountBalanceConfirmationState
    extends State<AccountBalanceConfirmation> {
  VatReportController _controller = VatReportController();
  final partyListController = PartyListController();

  String startDate = currentDate;
  String endDate = currentDate;
  String? ledgerID;
  List<LedgerDetailModel> ledgers = [];

  @override
  void initState() {
    super.initState();
    getPartyList();
    // generate();
  }

  getPartyList() async {
    await partyListController.init();
    this.setState(() {
      ledgers = partyListController.filteredLedgers;
    });
  }

  generate() async {
    if (null == ledgerID) {
      return;
    }
    _controller.generateReport(
        startDate: startDate, endDate: endDate, ledgerID: ledgerID);
  }

  @override
  void dispose() {
    super.dispose();
    partyListController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: colorPrimary,
        titleSpacing: -5.0,
        title: Text(
          "करोबार प्रमाणिकरण पत्र\n(Letter of Confirmation)",
          style: TextStyle(
              fontSize: 17,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
        actions: [],
      ),
      body: Obx(
        () {
          if (_controller.txnLoading)
            return const Center(
              child: CircularProgressIndicator(),
            );
          else
            return Container(
              color: Colors.black12,
              child: Column(
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 10),
                    child: Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: ReportCustomDatePickerTextField(
                            initialValue: toDateBS(DateTime.parse(startDate)),
                            hintText: "From Date",
                            onChange: (selectedDate) {
                              startDate =
                                  toDateAD(NepaliDateTime.parse(selectedDate));
                              this.setState(() {});
                              generate();
                            },
                          ),
                        ),
                        Expanded(
                          child: Container(
                            padding: EdgeInsets.symmetric(horizontal: 10),
                            child: Text(
                              "TO",
                              style: labelStyle2,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                        Expanded(
                          flex: 2,
                          child: ReportCustomDatePickerTextField(
                            initialValue: toDateBS(DateTime.parse(endDate)),
                            hintText: "To Date",
                            onChange: (selectedDate) {
                              setState(() {
                                endDate = toDateAD(
                                    NepaliDateTime.parse(selectedDate));
                              });
                              generate();
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  Divider(
                    height: 4,
                    color: Colors.black54,
                  ),
                  Obx(() {
                    return Container(
                      padding: EdgeInsets.symmetric(horizontal: 10),
                      child: CustomDropdown(
                          borderless: true,
                          style: formFieldTextStyle,
                          decoration: formFieldStyle,
                          value: ledgerID,
                          allowClear: false,
                          placeholder: "Select Party",
                          options: [
                            ...partyListController.filteredLedgers.map((e) {
                              return {
                                'key': e.ledgerId,
                                'value': e.ledgerTitle
                              };
                            }).toList(),
                          ],
                          onChange: (value) {
                            this.setState(() {
                              ledgerID = value;
                            });
                            generate();
                          }),
                    );
                  }),
                  Divider(
                    height: 4,
                    color: Colors.black54,
                  ),
                  Expanded(
                    child: Container(
                      color: Colors.white,
                      child: ListView(
                        padding: EdgeInsets.only(left: 20, right: 20),
                        children: [
                          SizedBox(
                            height: 20,
                          ),
                          Row(
                            children: [
                              Container(
                                width: 20,
                                height: 20,
                                child: Checkbox(
                                  activeColor: colorPrimary,
                                  checkColor: Colors.white,
                                  value: _controller.showHeader.value,
                                  onChanged: (value) {
                                    _controller.showHeader.value = value!;
                                    _controller.showHeader.refresh();
                                  },
                                ),
                              ),
                              Text("  Show Header", style: labelStyle2)
                            ],
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Text(
                            "पार्टीको नाम",
                            style: labelStyle2,
                          ),
                          SizedBox(height: 5.0),
                          Obx(
                            () {
                              return FormBuilderTextField(
                                name: 'party_name',
                                initialValue: _controller.partyName.value,
                                textInputAction: TextInputAction.next,
                                textCapitalization:
                                    TextCapitalization.sentences,
                                onChanged: (value) {
                                  _controller.partyName.value = value ?? "";
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return "Party Name cannot be empty";
                                  }
                                  return null;
                                },
                              );
                            },
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Text(
                            "पार्टीको ठेगाना",
                            style: labelStyle2,
                          ),
                          SizedBox(height: 5.0),
                          FormBuilderTextField(
                            name: 'party_address',
                            initialValue: _controller.partyAddress.value,
                            textInputAction: TextInputAction.next,
                            textCapitalization: TextCapitalization.sentences,
                            onChanged: (value) {
                              _controller.partyAddress.value = value ?? "";
                            },
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "पान/मु. अ. कर",
                                      style: labelStyle2,
                                    ),
                                    SizedBox(height: 5.0),
                                    FormBuilderDropdown(
                                      key: _controller.vatPanDropdownKey,
                                      name: 'tin_flag',
                                      style: formFieldTextStyle,
                                      decoration: formFieldStyle.copyWith(
                                          labelText: "PAN/VAT"),
                                      items: [
                                        DropdownMenuItem(
                                            value: "PAN", child: Text("PAN")),
                                        DropdownMenuItem(
                                            value: "VAT", child: Text("VAT"))
                                      ],
                                      initialValue:
                                          "" == _controller.tinType.value
                                              ? null
                                              : _controller.tinType.value,
                                      onChanged: (value) {
                                        _controller.tinType.value = value ?? "";
                                      },
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "पान/मु. अ. कर न.",
                                      style: labelStyle2,
                                    ),
                                    SizedBox(height: 5.0),
                                    FormBuilderTextField(
                                      name: 'tin_no',
                                      initialValue: _controller.tinNo.value,
                                      textInputAction: TextInputAction.next,
                                      decoration: InputDecoration(
                                          hintText: "PAN/VAT No"),
                                      inputFormatters: [
                                        FilteringTextInputFormatter.digitsOnly
                                      ],
                                      textCapitalization:
                                          TextCapitalization.sentences,
                                      onChanged: (value) {
                                        _controller.tinNo.value = value ?? "";
                                      },
                                      validator: (value) {
                                        if (value == null || value.isEmpty) {
                                          return "PAN/VAT No is required!";
                                        }
                                        return null;
                                      },
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Text(
                            "आर्थिक वर्ष",
                            style: labelStyle2,
                          ),
                          SizedBox(height: 5.0),
                          FormBuilderTextField(
                            name: 'fiscal_year',
                            initialValue: _controller.fiscalYear.value,
                            textInputAction: TextInputAction.next,
                            onChanged: (value) {
                              _controller.fiscalYear.value = value ?? "";
                            },
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "सुरु मौज्दात",
                                      style: labelStyle2,
                                    ),
                                    SizedBox(height: 5.0),
                                    FormBuilderTextField(
                                      name: 'opening_balance',
                                      initialValue: _controller
                                          .openingBalance.value
                                          .toString(),

                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp(r'^\-?(\d+)?\.?\d{0,2}')),
                                      ],
                                      keyboardType:
                                          TextInputType.numberWithOptions(
                                              decimal: true, signed: true),
                                      textInputAction: TextInputAction.next,
                                      onChanged: (value) {
                                        _controller.openingBalance.value =
                                            (value ?? "0.0");
                                      },
                                      //   keyboardType:
                                      //       TextInputType.numberWithOptions(
                                      //           decimal: true, signed: true),

                                      //   onChange: (newValue) {
                                      //     Log.d("change name $newValue");

                                      //   },
                                      // ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "अन्तिम मौज्दात",
                                      style: labelStyle2,
                                    ),
                                    SizedBox(height: 5.0),
                                    FormBuilderTextField(
                                      name: 'closing_balance',
                                      decoration: InputDecoration(
                                        hintText: "Closing Balance",
                                      ),
                                      initialValue: _controller
                                          .closingBalance.value
                                          .toString(),
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp(r'^\-?(\d+)?\.?\d{0,2}'))
                                      ],

                                      keyboardType:
                                          TextInputType.numberWithOptions(
                                              decimal: true, signed: true),

                                      textInputAction: TextInputAction.next,
                                      onChanged: (value) {
                                        _controller.closingBalance.value =
                                            (value ?? "0.00");
                                      },
                                      //   onChange: (newValue) {
                                      //     Log.d("change name $newValue");

                                      //   },
                                      // ),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Text(
                            "खरीद (Purchase)",
                            style: labelStyle2,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  FormBuilderTextField(
                                    name: 'purchase_taxable_total',
                                    // labelText: "",
                                    decoration: InputDecoration(
                                      hintText: "Taxable Amt.",
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'^\-?(\d+)?\.?\d{0,2}'))
                                    ],
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            decimal: true, signed: true),

                                    initialValue:
                                        _controller.purchaseTaxableTotal.value,
                                    textInputAction: TextInputAction.next,
                                    onChanged: (value) {
                                      _controller.purchaseTaxableTotal.value =
                                          value ?? "0.00";
                                    },
                                  ),
                                ],
                              )),
                              SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  FormBuilderTextField(
                                    name: 'purchase_tax_total',
                                    decoration:
                                        InputDecoration(hintText: "VAT Amt."),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'^\-?(\d+)?\.?\d{0,2}'))
                                    ],
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            decimal: true, signed: true),
                                    initialValue:
                                        _controller.purchaseTaxTotal.value,
                                    textInputAction: TextInputAction.next,
                                    onChanged: (value) {
                                      _controller.purchaseTaxTotal.value =
                                          value ?? "";
                                    },
                                  ),
                                ],
                              )),
                            ],
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Text(
                            "बिक्री (Sales)",
                            style: labelStyle2,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  FormBuilderTextField(
                                    name: 'sales_taxable_total',
                                    decoration: InputDecoration(
                                      hintText: "Taxable Amt.",
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'^\-?(\d+)?\.?\d{0,2}'))
                                    ],
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            decimal: true, signed: true),
                                    initialValue:
                                        _controller.salesTaxableTotal.value,
                                    textInputAction: TextInputAction.next,
                                    onChanged: (value) {
                                      _controller.salesTaxableTotal.value =
                                          value ?? "";
                                    },
                                  ),
                                ],
                              )),
                              SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  FormBuilderTextField(
                                    name: 'sales_tax_total',
                                    decoration: InputDecoration(
                                      hintText: "VAT Amt.",
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'^\-?(\d+)?\.?\d{0,2}'))
                                    ],
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            decimal: true, signed: true),
                                    initialValue:
                                        _controller.salesTaxTotal.value,
                                    textInputAction: TextInputAction.next,
                                    onChanged: (value) {
                                      _controller.salesTaxTotal.value =
                                          value ?? "";
                                    },
                                  ),
                                ],
                              )),
                            ],
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Text(
                            "खरीद फिर्ता (Purchase Return)",
                            style: labelStyle2,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  FormBuilderTextField(
                                    name: 'purchase_return_taxable_total',
                                    decoration: InputDecoration(
                                      hintText: "Taxable Amt.",
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'^\-?(\d+)?\.?\d{0,2}'))
                                    ],
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            decimal: true, signed: true),
                                    initialValue: _controller
                                        .purchaseReturnTaxableTotal.value,
                                    textInputAction: TextInputAction.next,
                                    onChanged: (value) {
                                      _controller.purchaseReturnTaxableTotal
                                          .value = value ?? "";
                                    },
                                    //   onChange: (newValue) {
                                    //     Log.d("change name $newValue");

                                    //   },
                                    // ),
                                  ),
                                ],
                              )),
                              SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  FormBuilderTextField(
                                    name: 'purchase_return_tax_total',
                                    decoration: InputDecoration(
                                      hintText: "VAT Amt.",
                                    ),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'^\-?(\d+)?\.?\d{0,2}'))
                                    ],
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            decimal: true, signed: true),
                                    initialValue: _controller
                                        .purchaseReturnTaxTotal.value,
                                    textInputAction: TextInputAction.next,
                                    onChanged: (value) {
                                      _controller.purchaseReturnTaxTotal.value =
                                          value ?? "";
                                    },
                                  ),
                                ],
                              )),
                            ],
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Text(
                            "बिक्री फिर्ता (Sales Return)",
                            style: labelStyle2,
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Row(
                            children: [
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  FormBuilderTextField(
                                    name: 'sales_return_taxable_total',
                                    decoration: InputDecoration(
                                      hintText: "Taxable Amt.",
                                    ),

                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'^\-?(\d+)?\.?\d{0,2}'))
                                    ],
                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            decimal: true, signed: true),
                                    initialValue: _controller
                                        .salesReturnTaxableTotal.value,
                                    textInputAction: TextInputAction.next,
                                    onChanged: (value) {
                                      _controller.salesReturnTaxableTotal
                                          .value = value ?? "";
                                    },
                                    //   onChange: (newValue) {
                                    //     // Log.d("change name $newValue");

                                    //   },
                                    // ),
                                  ),
                                ],
                              )),
                              SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  FormBuilderTextField(
                                    name: 'sales_return_tax_total',
                                    decoration:
                                        InputDecoration(hintText: "VAT Amt."),
                                    inputFormatters: [
                                      FilteringTextInputFormatter.allow(
                                          RegExp(r'^\-?(\d+)?\.?\d{0,2}'))
                                    ],

                                    keyboardType:
                                        TextInputType.numberWithOptions(
                                            decimal: true, signed: true),
                                    initialValue:
                                        _controller.salesReturnTaxTotal.value,
                                    textInputAction: TextInputAction.next,
                                    onChanged: (value) {
                                      _controller.salesReturnTaxTotal.value =
                                          value ?? "0.00";
                                    },
                                    //   onChange: (newValue) {
                                    //     // Log.d("change name $newValue");

                                    //   },
                                    // ),
                                  ),
                                ],
                              )),
                            ],
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          SizedBox(
                            height: 20,
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            );
        },
      ),
      bottomNavigationBar: Container(
        height: 55,
        child: ElevatedButton(
          style: ElevatedButton.styleFrom(
            backgroundColor: colorPrimary,
          ),
          child: Text(
            "Generate",
            style: TextStyle(color: Colors.white, fontSize: 18),
          ),
          onPressed: null == ledgerID
              ? null
              : () {
                  var partyText = "All Party";
                  if (null != ledgerID) {
                    LedgerDetailModel l = ledgers
                        .firstWhere((element) => element.ledgerId == ledgerID);
                    partyText = l.ledgerTitle ?? "";
                  }
                  Navigator.pushNamed(context, '/printLOC',
                      arguments: AccountBalanceConfirmationPrintPage(
                        startDate: startDate,
                        endDate: endDate,
                        locData: LOCModal(
                            showHeader: _controller.showHeader.value,
                            partyName: _controller.partyName.value,
                            partyAddress: _controller.partyAddress.value,
                            tinNo: _controller.tinNo.value,
                            tinType: _controller.tinType.value,
                            fiscalYear: _controller.fiscalYear.value,
                            openingBalance:
                                parseDouble(_controller.openingBalance),
                            closingBalance:
                                parseDouble(_controller.closingBalance),
                            salesTaxableTotal:
                                parseDouble(_controller.salesTaxableTotal),
                            salesTaxTotal:
                                parseDouble(_controller.salesTaxTotal),
                            purchaseTaxableTotal:
                                parseDouble(_controller.purchaseTaxableTotal),
                            purchaseTaxTotal:
                                parseDouble(_controller.purchaseTaxTotal),
                            salesReturnTaxableTotal: parseDouble(
                                _controller.salesReturnTaxableTotal),
                            salesReturnTaxTotal:
                                parseDouble(_controller.salesReturnTaxTotal),
                            purchaseReturnTaxableTotal: parseDouble(
                                _controller.purchaseReturnTaxableTotal),
                            purchaseReturnTaxTotal: parseDouble(
                                _controller.purchaseReturnTaxTotal)),
                      ));
                },
        ),
      ),
    ));
  }
}
