// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_dropdown.dart';
import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/party_list/party_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/report_controllers/report_transaction_controller.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';

// ignore: must_be_immutable
class AllTransactionReport extends StatefulWidget {
  const AllTransactionReport({super.key});

  @override
  _AllTransactionReportState createState() => _AllTransactionReportState();
}

class _AllTransactionReportState extends State<AllTransactionReport> {
  final ReportTransactionController _controller = ReportTransactionController();
  var partyListController = PartyListController();

  List<int> types = [];

  String startDate = currentDate;
  String endDate = currentDate;
  String? ledgerID;

  @override
  void initState() {
    super.initState();
    _initData();
  }

  Future<void> _initData() async {
    await partyListController.init();
    generate();
  }

  @override
  void dispose() {
    partyListController.dispose();
    super.dispose();
  }

  generate() {
    _controller.generateAllTransactionReport(
      startDate: startDate,
      endDate: endDate,
      types: (types.isEmpty) ? TxnType.financialTxnTypeList : types,
      ledgerID: ledgerID,
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        // resizeToAvoidBottomPadding: true,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          elevation: 0,
          titleSpacing: -5.0,
          backgroundColor: colorPrimary,
          title: const Text(
            "सबै लेनदेन रिपोर्ट\n(All Transaction Report)",
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold,
            ),
          ),
          actions: [
            PrintButton(
              onPressed: () {
                var partyText = "All Party";
                if (null != ledgerID) {
                  LedgerDetailModel l =
                      partyListController.filteredLedgers.firstWhere(
                    (element) => element.ledgerId == ledgerID,
                  );
                  partyText = l.ledgerTitle!;
                }
                TransactionHelper.goToTransactionPrintPage(
                  context,
                  txnTypeText: types.isNotEmpty
                      ? TxnType.txnTypeText[types[0]]
                      : "All Transaction",
                  pageTitle: "Transaction Report",
                  partyText: partyText,
                  transactions: _controller.transactions,
                  startDate: startDate,
                  endDate: endDate,
                  totalSale: _controller.totalSale.value,
                  totalSaleReturn: _controller.totalSaleReturn.value,
                  totalPurchase: _controller.totalPurchase.value,
                  totalPurchaseReturn: _controller.totalPurchaseReturn.value,
                  totalExpense: _controller.totalExpense.value,
                  totalPaymentIn: _controller.totalPaymentIn.value,
                  totalPaymentOut: _controller.totalPaymentOut.value,
                );
              },
            )
          ],
        ),
        body: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: Container(
            color: Colors.black12,
            child: Column(
              children: [
                //=============================transaction date filter
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: ReportCustomDatePickerTextField(
                          initialValue: toDateBS(DateTime.parse(startDate)),
                          hintText: "From Date",
                          onChange: (selectedDate) {
                            startDate =
                                toDateAD(NepaliDateTime.parse(selectedDate));
                            setState(() {});
                            generate();
                          },
                        ),
                      ),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 10),
                          child: Text(
                            "TO",
                            style: labelStyle2,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: ReportCustomDatePickerTextField(
                          initialValue: toDateBS(DateTime.parse(endDate)),
                          hintText: "To Date",
                          onChange: (selectedDate) {
                            endDate =
                                toDateAD(NepaliDateTime.parse(selectedDate));
                            setState(() {});
                            generate();
                          },
                        ),
                      ),
                    ],
                  ),
                ),
                const Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: CustomDropdown(
                      borderless: true,
                      style: formFieldTextStyle,
                      decoration: formFieldStyle,
                      value: types.isNotEmpty ? types[0] : null,
                      allowClear: false,
                      placeholder: "Select Txn Type",
                      options: [
                        const {'key': null, 'value': "All Transaction"},
                        ...TxnType.financialTxnTypeList.map((e) {
                          return {'key': e, 'value': TxnType.txnTypeText[e]};
                        }).toList(),
                      ],
                      onChange: (value) {
                        // Log.d("on change $value");
                        if (null == value) {
                          types = [];
                        } else {
                          types = [value];
                        }
                        setState(() {});
                        generate();
                      }),
                ),
                const Divider(
                  height: 4,
                  color: Colors.black54,
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: Obx(() {
                    if (partyListController.partyLoading) {
                      return Container(
                        height: 48,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const Center(
                          child: CircularProgressIndicator(),
                        ),
                      );
                    }
                    return CustomDropdown(
                        borderless: true,
                        style: formFieldTextStyle,
                        decoration: formFieldStyle,
                        value: ledgerID,
                        allowClear: false,
                        placeholder: "Select Party",
                        options: [
                          const {'key': null, 'value': "All Party"},
                          ...partyListController.filteredLedgers.map((e) {
                            return {'key': e.ledgerId, 'value': e.ledgerTitle};
                          }).toList(),
                        ],
                        onChange: (value) {
                          // Log.d("on change $value");
                          ledgerID = value;
                          setState(() {});
                          generate();
                        });
                  }),
                ),
                const Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                Obx(() {
                  if (_controller.txnLoading) {
                    return Container(
                        color: Colors.white,
                        child:
                            const Center(child: CircularProgressIndicator()));
                  }

                  if (_controller.transactions.isEmpty) {
                    return Container(
                        color: Colors.white,
                        width: double.infinity,
                        child: const Center(
                            child: Text(
                          "No Records",
                          style: TextStyle(color: Colors.black54),
                        )));
                  } else {
                    return Expanded(
                        child: _TxnListView(_controller.transactions));
                  }
                }),
              ],
            ),
          ),
        ),
        // extendBody: true,
        bottomNavigationBar: Container(
          height: 105,
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          color: colorPrimary,
          child: SingleChildScrollView(
            child: Obx(
              () {
                return DefaultTextStyle(
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // FIXED: Use pre-calculated controller totals instead of manual calculations
                      // This ensures consistency and reduces errors
                      Text(
                        "Total Amount: ${formatCurrencyAmount(_controller.totalAmount.value, true)}",
                        textAlign: TextAlign.right,
                      ),
                      const SizedBox(height: 5),

                      // FIXED: Use controller totals for all transaction types
                      // These values are properly calculated in the controller with correct logic
                      Text(
                        "Total Sales: ${formatCurrencyAmount(_controller.totalSale.value, true)}",
                        textAlign: TextAlign.right,
                      ),
                      const SizedBox(height: 5),

                      Text(
                        "Total Sales Return: ${formatCurrencyAmount(_controller.totalSaleReturn.value, true)}",
                        textAlign: TextAlign.right,
                      ),
                      const SizedBox(height: 5),

                      Text(
                        "Total Purchase: ${formatCurrencyAmount(_controller.totalPurchase.value, true)}",
                        textAlign: TextAlign.right,
                      ),
                      const SizedBox(height: 5),

                      Text(
                        "Total Purchase Return: ${formatCurrencyAmount(_controller.totalPurchaseReturn.value, true)}",
                        textAlign: TextAlign.right,
                      ),
                      const SizedBox(height: 5),

                      Text(
                        "Total Expense: ${formatCurrencyAmount(_controller.totalExpense.value, true)}",
                        textAlign: TextAlign.right,
                      ),
                      const SizedBox(height: 5),

                      Text(
                        "Total Receipt: ${formatCurrencyAmount(_controller.totalPaymentIn.value, true)}",
                        textAlign: TextAlign.right,
                      ),
                      const SizedBox(height: 5),

                      Text(
                        "Total Payment: ${formatCurrencyAmount(_controller.totalPaymentOut.value, true)}",
                        textAlign: TextAlign.right,
                      ),
                      const SizedBox(height: 5),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class _TxnListView extends StatelessWidget {
  final List<AllTransactionModel> _transactionList;

  const _TxnListView(this._transactionList);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: _transactionList.length,
      // shrinkWrap: true,
      itemBuilder: (context, int index) {
        AllTransactionModel txn = _transactionList[index];

        return InkWell(
          // onTap: () => TransactionHelper.gotoTransactionEditPage(
          //     context, txn.txnId, txn.txnType),
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                DefaultTextStyle(
                  style: TextStyle(fontSize: 14, color: colorPrimary),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        //====================================1st Column
                        Flexible(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // FIXED: Display name concatenation bug - was showing duplicate text
                              // OLD: "${txn.txnDisplayName != null && txn.txnDisplayName!.isNotEmpty ? txn.txnDisplayName : txn.ledgerTitle}${txn.txnDisplayName ?? txn.ledgerTitle}"
                              // NEW: Show txnDisplayName if available, otherwise show ledgerTitle (no duplication)
                              Text(
                                (txn.txnDisplayName != null &&
                                        txn.txnDisplayName!.isNotEmpty)
                                    ? txn.txnDisplayName!
                                    : (txn.ledgerTitle ?? ""),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                                style: const TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 5),
                              Text(
                                "${txn.txnDateBS}",
                                style: const TextStyle(
                                  fontSize: 12,
                                  color: Colors.black54,
                                ),
                              ),
                            ],
                          ),
                        ),

                        //====================================2nd Column
                        Flexible(
                          child: Text(
                            (null != txn.txnRefNumberChar)
                                ? "${txn.txnTypeText}: #${txn.txnRefNumberChar}"
                                : "${txn.txnTypeText}",
                            textAlign: TextAlign.left,
                          ),
                        ),

                        //====================================3rd Column - FIXED AMOUNT DISPLAY
                        // SIMPLIFIED: Use txnTotalAmount for all transaction types for consistency
                        // This eliminates complex discount calculations and potential errors
                        // ENSURE: Always show "0.00" for null or blank values
                        Flexible(
                          child: Text(
                            formatCurrencyAmount(
                              (txn.txnTotalAmount != null &&
                                      txn.txnTotalAmount! > 0)
                                  ? txn.txnTotalAmount!
                                  : 0.0,
                              false,
                            ),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                //Add space if last element
                if (_transactionList.length - 1 == index) ...{const SizedBox()},
              ],
            ),
          ),
        );
      },
    );
  }
}
