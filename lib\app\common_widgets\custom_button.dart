import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

// ignore: must_be_immutable
class GradientButtonBlue extends StatelessWidget {
  String buttonText;
  VoidCallback onClick;
  FontWeight? fontWeight;
  double? fontSize;
  double? radius;
  double? height;
  double? width;
  Color? btnTextColor;

  GradientButtonBlue(
      {Key? key,
      required this.buttonText,
      required this.onClick,
      this.fontWeight,
      this.fontSize,
      this.radius,
      this.height,
      this.width,
      this.btnTextColor})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    fontWeight = fontWeight ?? FontWeight.bold;
    fontSize = fontSize != null ? fontSize!.toDouble() : 18;
    radius = radius != null ? radius!.toDouble() : 6.0;
    height = (height != null) ? height!.toDouble() : 40.0;
    width = (width != null) ? width!.toDouble() : 250.0;
    btnTextColor = (btnTextColor != null) ? btnTextColor : Colors.white;

    return ElevatedButton(
      onPressed: onClick,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.transparent,
        foregroundColor: colorPrimaryLightest,
        shape: RoundedRectangleBorder(
          borderRadius: new BorderRadius.circular(6.0),
          // side: BorderSide(color: Colors.black),
        ),
      ),
      child: Ink(
        decoration: BoxDecoration(
            gradient: const LinearGradient(colors: <Color>[
              Color(0xFF3560AF),
              Color(0xFF4F73B7),
            ], begin: Alignment.centerLeft, end: Alignment.centerRight),
            borderRadius: BorderRadius.circular(radius!)),
        child: Container(
          height: height,
          width: width,
          constraints: BoxConstraints(maxWidth: width!, maxHeight: height!),
          alignment: Alignment.center,
          child: Text(
            buttonText,
            textAlign: TextAlign.center,
            style: TextStyle(
                color: btnTextColor,
                fontWeight: fontWeight,
                fontSize: fontSize),
          ),
        ),
      ),
    );
  }
}

class PrintButton extends StatelessWidget {
  final VoidCallback? onPressed;

  const PrintButton({Key? key, required this.onPressed}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return IconButton(
        icon: const Icon(FontAwesomeIcons.filePdf), onPressed: onPressed);
  }
}

// class GradientButtonGreen extends StatelessWidget{
//   String buttonText;
//   Function onClick;
//   FontWeight fontWeight;
//   double fontSize;
//   double radius;
//   double height;
//   double width;
//
//   GradientButtonGreen({this.buttonText, @required this.onClick, this.fontWeight, this.fontSize, this.radius, this.height, this.width});
//
//   @override
//   Widget build(BuildContext context) {
//     fontWeight = (fontWeight!=null) ? fontWeight : FontWeight.bold;
//     fontSize = (fontSize!=null) ? fontSize.toDouble() :18;
//     radius = (radius!=null) ? radius.toDouble() : 6.0;
//     height = (height!=null) ? height.toDouble() : 45.0;
//     width = (width!=null) ? width.toDouble() : 250.0;
//
//     return MaterialButton(
//       padding: EdgeInsets.all(0),
//       onPressed: onClick,
//       child: Container(
//         height: height,
//         width: width,
//         child: Container(
//           decoration: BoxDecoration(
//               boxShadow: downShadow,
//               gradient: LinearGradient(
//                   colors: <Color>[
//                     Color(0xFF00A219),
//                     Color(0xFF5BBC6A),
//                   ],
//                   begin: Alignment.centerLeft,
//                   end: Alignment.centerRight
//               ),
//               borderRadius: BorderRadius.circular(radius)
//           ),
//           child: Container(
//             constraints: BoxConstraints(maxWidth: width, maxHeight: height),
//             alignment: Alignment.center,
//             child: Text(buttonText, textAlign: TextAlign.center, style: TextStyle(color: Colors.white, fontWeight: fontWeight, fontSize: fontSize),),
//           ),
//         ),
//       ),
//     );
//   }
//
// }
//
//
// class GradientButtonOrange extends StatelessWidget{
//   String buttonText;
//   Function onClick;
//   FontWeight fontWeight;
//   double fontSize;
//   double radius;
//   double height;
//   double width;
//
//   GradientButtonOrange({this.buttonText, @required this.onClick, this.fontWeight, this.fontSize, this.radius, this.height, this.width});
//
//   @override
//   Widget build(BuildContext context) {
//     fontWeight = (fontWeight!=null) ? fontWeight : FontWeight.bold;
//     fontSize = (fontSize!=null) ? fontSize.toDouble() :18;
//     radius = (radius!=null) ? radius.toDouble() : 6.0;
//     height = (height!=null) ? height.toDouble() : 45.0;
//     width = (width!=null) ? width.toDouble() : 250.0;
//
//     return RaisedButton(
//       padding: EdgeInsets.all(0),
//       onPressed: onClick,
//       child: Container(
//         height: height,
//         width: width,
//         child: Container(
//           decoration: BoxDecoration(
//               boxShadow: downShadow,
//               gradient: LinearGradient(
//                   colors: <Color>[
//                     Color(0xFFEB7A27),
//                     Color(0xFFF6AA74),
//                   ],
//                   begin: Alignment.centerLeft,
//                   end: Alignment.centerRight
//               ),
//               borderRadius: BorderRadius.circular(radius)
//           ),
//           child: Container(
//             constraints: BoxConstraints(maxWidth: width, maxHeight: height),
//             alignment: Alignment.center,
//             child: Row(
//               mainAxisAlignment: MainAxisAlignment.center,
//               children: [
//                 Text(buttonText, textAlign: TextAlign.center, style: TextStyle(color: Colors.white, fontWeight: fontWeight, fontSize: fontSize),),
//               ],
//             ),
//           ),
//         ),
//       ),
//     );
//   }
//
// }
//
//
// class GradientButtonRed extends StatelessWidget{
//   String buttonText;
//   Function onClick;
//   FontWeight fontWeight;
//   double fontSize;
//   double radius;
//   double height;
//   double width;
//
//   GradientButtonRed({this.buttonText, @required this.onClick, this.fontWeight, this.fontSize, this.radius, this.height, this.width});
//
//   @override
//   Widget build(BuildContext context) {
//     fontWeight = (fontWeight!=null) ? fontWeight : FontWeight.bold;
//     fontSize = (fontSize!=null) ? fontSize.toDouble() :18;
//     radius = (radius!=null) ? radius.toDouble() : 6.0;
//     height = (height!=null) ? height.toDouble() : 45.0;
//     width = (width!=null) ? width.toDouble() : 250.0;
//
//     return MaterialButton(
//       padding: EdgeInsets.all(0),
//       onPressed: onClick,
//       child: Container(
//         height: height,
//         width: width,
//         child: Container(
//           decoration: BoxDecoration(
//               boxShadow: downShadow,
//               gradient: LinearGradient(
//                   colors: <Color>[
//                     Color(0xFFC90101),
//                     Color(0xFFD14545),
//                   ],
//                   begin: Alignment.centerLeft,
//                   end: Alignment.centerRight
//               ),
//               borderRadius: BorderRadius.circular(radius)
//           ),
//           child: Container(
//             constraints: BoxConstraints(maxWidth: width, maxHeight: height),
//             alignment: Alignment.center,
//             child: Text(buttonText, textAlign: TextAlign.center, style: TextStyle(color: Colors.white, fontWeight: fontWeight, fontSize: fontSize),),
//           ),
//         ),
//       ),
//     );
//   }
//
// }
