import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/model/database/txn_image_model.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:sqflite/sqflite.dart';

class TxnImageRepository {
  final String tag = "TxnImageRepository";
  final String tableName = 'mk_txn_images';
  DatabaseHelper databaseHelper = DatabaseHelper();

  QueryRepository queryRepository = QueryRepository();
  //========================================================================================= SYNCING ACTIONS
  Future<bool> insert(TxnImageModel txnImage,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    await dbClient.insert(tableName, txnImage.toJson());

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.insert,
      data: txnImage.toJson(),
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    status = true;

    return status;
  }

  Future<bool> deleteImagesForTransaction(String txnID,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    String whereClause = "txn_id = ?";
    List<dynamic> whereArgs = [txnID];

    await dbClient.delete(tableName, where: whereClause, whereArgs: whereArgs);

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.delete,
      whereArgs: whereArgs,
      whereClause: whereClause,
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    status = true;

    return status;
  }

  //=========================================================================================NON SYNCING ACTIONS
  Future<bool> setImagesForTransaction(
      String txnID, List<TxnImageModel>? images,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    int sNo = 0;
    if (images != null) {
      await Future.wait(images.map((imageItem) async {
        imageItem.sno = sNo;
        sNo += 1;
        imageItem.txnId = txnID;
        await insert(imageItem, dbClient: dbClient, batchID: batchID);
      }).toList());
    }

    status = true;

    return status;
  }

  Future<List<TxnImageModel>> getImagesForTransaction(String txnID) async {
    List<TxnImageModel> items = [];
    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> listJson = (await dbClient!
          .rawQuery('SELECT * FROM $tableName WHERE txn_id=?', [txnID]));

      items =
          listJson.map((itemData) => TxnImageModel.fromJson(itemData)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return items;
  }
}
