// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/components/expense_category_autocomplete.dart';
import 'package:mobile_khaata_v2/app/components/image_grid_gallery.dart';
import 'package:mobile_khaata_v2/app/components/ledger_autocomplete%20_%20textfield_with_add.dart';
import 'package:mobile_khaata_v2/app/components/payment_mode_selector.dart';
import 'package:mobile_khaata_v2/app/model/database/expanse_category_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_module/detail_expense/detail_express_controller.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

extension ExtendedIterable<E> on Iterable<E> {
  /// Like Iterable<T>.map but callback have index as second argument
  Iterable<T> mapIndex<T>(T f(E e, int i)) {
    var i = 0;
    return this.map((e) => f(e, i++));
  }

  void forEachIndex(void f(E e, int i)) {
    var i = 0;
    this.forEach((e) => f(e, i++));
  }
}

class DetailExpensesPage extends StatelessWidget {
  final String tag = "Expense Detail Page";

  final String? expensID;
  final bool? reaOnlyFlag;

  final expenseController = DetailExpensesController();

  DetailExpensesPage({super.key, this.expensID, this.reaOnlyFlag}) {
    expenseController.onInit();

    if (null != expensID) {
      // initiate edit functionality
      expenseController.initEdit(expensID, reaOnlyFlag ?? true);
    } else {
      expenseController.initialize();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      bool iscashExpenses = expenseController.transaction.value.ledgerId ==
          CASH_EXPENSE_LEDGER_ID;
      bool hasSubTotal =
          (expenseController.transaction.value.txnSubTotalAmount != null &&
              expenseController.transaction.value.txnSubTotalAmount! > 0.0);

      if (expenseController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      } else {
        return SafeArea(
            child: Scaffold(
                resizeToAvoidBottomInset: true,
                appBar: AppBar(
                  toolbarHeight: 60,
                  elevation: 4,
                  backgroundColor: colorPrimary,
                  leading: BackButton(
                    onPressed: () => Navigator.pop(context, false),
                  ),
                  centerTitle: false,
                  titleSpacing: -5.0,
                  title: const Text(
                    "खर्च (Expense Detail)",
                    style: TextStyle(
                        fontSize: 20,
                        color: Colors.white,
                        fontFamily: 'HelveticaRegular',
                        fontWeight: FontWeight.bold),
                  ),
                  actions: [
                    if (expenseController.editFlag) ...{
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(10)),
                              backgroundColor: colorPrimary,
                              foregroundColor: colorPrimaryLightest,
                            ),
                            onPressed: () {
                              TransactionHelper.gotoTransactionEditPage(
                                  context, expensID ?? "", TxnType.expense,
                                  forEdit: true);
                            },
                            child: (expenseController.readOnlyFlag)
                                ? Column(
                                    children: const [
                                      Icon(
                                        Icons.mode_edit,
                                        color: Colors.white,
                                      ),
                                      Text(
                                        "Click here to Edit",
                                        style: TextStyle(
                                            color: Colors.white, fontSize: 10),
                                      ),
                                    ],
                                  )
                                : Column(
                                    children: const [
                                      Icon(
                                        Icons.close,
                                        color: Colors.white,
                                      ),
                                      Text(
                                        "Cancel",
                                        style: TextStyle(
                                            color: Colors.white, fontSize: 10),
                                      ),
                                    ],
                                  )),
                      ),
                    }
                  ],
                ),

                //===========================================================================Body Part
                body: Center(
                  child: Container(
                    color: backgroundColorShade,
                    child: GestureDetector(
                      onTap: () => FocusScope.of(context).unfocus(),
                      child: Form(
                        key: expenseController.formKey,
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              Card(
                                elevation: 2,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 15),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      //===========================Bill No.
                                      Expanded(
                                        flex: 1,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "बिल/भौचर न.",
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            FormBuilderTextField(
                                              name: "bill_no",
                                              readOnly: expenseController
                                                  .readOnlyFlag,
                                              autocorrect: false,
                                              keyboardType: TextInputType.text,
                                              textInputAction:
                                                  TextInputAction.done,
                                              textAlign: TextAlign.right,
                                              style: formFieldTextStyle,
                                              decoration:
                                                  formFieldStyle.copyWith(
                                                      labelText:
                                                          "Bill/Voucher No."),
                                              controller:
                                                  expenseController.billNoCtrl,
                                              onChanged: (value) {
                                                expenseController
                                                    .transaction
                                                    .value
                                                    .txnRefNumberChar = value;
                                                expenseController.transaction
                                                    .refresh();
                                              },
                                            ),
                                          ],
                                        ),
                                      ),

                                      const SizedBox(
                                        width: 20,
                                      ),

                                      //===========================Transaction Date
                                      Expanded(
                                        flex: 1,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "मिति",
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            CustomDatePickerTextField(
                                              readOnly: expenseController
                                                  .readOnlyFlag,
                                              maxBSDate: NepaliDateTime.now(),
                                              initialValue: expenseController
                                                  .transaction.value.txnDateBS,
                                              onChange: (selectedDate) {
                                                expenseController
                                                    .transaction
                                                    .value
                                                    .txnDateBS = selectedDate;
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              Card(
                                elevation: 2,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 10),
                                  child: Column(
                                    children: [
                                      //===============================================Expense Category
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'खर्च शीर्षक',
                                            style: labelStyle2,
                                          ),
                                          const SizedBox(height: 5.0),
                                          ExpensesCategoryAutoComplete(
                                              enableFlag: !expenseController
                                                  .readOnlyFlag,
                                              labelText: "Expense Title",
                                              controller: expenseController
                                                  .expenseCategoryCtrl,
                                              onChangedFn: (value) {
                                                // Log.d(
                                                //     "called i text change $value");
                                                expenseController
                                                    .onChangeCategory(null);
                                              },
                                              categoryID: expenseController
                                                  .transaction
                                                  .value
                                                  .expenseCategoryId,
                                              onSuggestionSelectedFn:
                                                  (ExpenseCategoryModel
                                                      category) {
                                                expenseController
                                                    .onChangeCategory(category);
                                                FocusScope.of(context)
                                                    .unfocus();
                                              }),
                                          const SizedBox(
                                            height: 25,
                                          ),
                                        ],
                                      ),

                                      //===============================================Party Balance

                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Row(
                                            children: [
                                              SizedBox(
                                                width: 20,
                                                height: 20,
                                                child: Checkbox(
                                                  activeColor: colorPrimary,
                                                  checkColor: Colors.white,
                                                  value: expenseController
                                                      .isCashExpensesSelected,
                                                  onChanged:
                                                      expenseController
                                                              .readOnlyFlag
                                                          ? null
                                                          : (value) {
                                                              expenseController
                                                                      .setisCashExpensesSelected =
                                                                  value!;
                                                              if (value) {
                                                                expenseController.onChangeParty(LedgerDetailModel(
                                                                    ledgerId:
                                                                        CASH_EXPENSE_LEDGER_ID,
                                                                    ledgerTitle:
                                                                        CASH_EXPENSE_LEDGER_NAME));
                                                                expenseController
                                                                    .onToggleVat(
                                                                        false);
                                                              } else {
                                                                expenseController.onChangeParty(
                                                                    LedgerDetailModel(
                                                                        ledgerId:
                                                                            null));
                                                              }
                                                            },
                                                ),
                                              ),
                                              Text("  खुद्रा खर्च",
                                                  style: labelStyle2)
                                            ],
                                          ),
                                          RichText(
                                            textAlign: TextAlign.right,
                                            text: TextSpan(
                                                text: "पुरानो बाँकी: ",
                                                style:
                                                    TextStyle(color: textColor),
                                                children: [
                                                  if (null !=
                                                      expenseController
                                                          .transaction
                                                          .value
                                                          .ledgerId) ...{
                                                    TextSpan(
                                                      text:
                                                          "${expenseController.selectedLedger.balanceAmount ?? 0}",
                                                      style: TextStyle(
                                                          color: ((expenseController
                                                                          .selectedLedger
                                                                          .balanceAmount ??
                                                                      0) >=
                                                                  0.0)
                                                              ? colorGreenDark
                                                              : colorRedLight),
                                                    )
                                                  }
                                                ]),
                                          )
                                        ],
                                      ),

                                      Container(
                                        height: 15,
                                      ),
                                      //===============================================Party Field
                                      if (!expenseController
                                          .isCashExpensesSelected)
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'विक्रेता / पाउने को नाम',
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            LedgerAutoCompleteTextFieldWithAdd(
                                                excludedIDS: const [
                                                  CASH_SALES_LEDGER_ID
                                                ],
                                                enableFlag: !expenseController
                                                    .readOnlyFlag,
                                                labelText:
                                                    "Vendor / Receiver Name",
                                                controller: expenseController
                                                    .partyNameCtrl,
                                                onChangedFn: (value) {
                                                  // Log.d("called i text change");
                                                },
                                                ledgetID: expenseController
                                                    .transaction.value.ledgerId,
                                                onSuggestionSelectedFn:
                                                    (LedgerDetailModel ledger) {
                                                  expenseController
                                                      .onChangeParty(ledger);
                                                })
                                          ],
                                        ),
                                      ...iscashExpenses
                                          ? [
                                              const SizedBox(
                                                height: 10,
                                              ),
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    "खुद्रा विक्रेता / पाउने को नाम",
                                                    style: labelStyle2,
                                                  ),
                                                  const SizedBox(height: 5.0),
                                                  FormBuilderTextField(
                                                    name: "display_text",
                                                    // readOnly:
                                                    //     (null == state.selectedLedger.ledgerId)
                                                    //         ? false
                                                    //         : true,
                                                    readOnly: expenseController
                                                        .readOnlyFlag,
                                                    autocorrect: false,
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            labelText:
                                                                "Vendor / Receiver Name"),
                                                    controller:
                                                        expenseController
                                                            .displayTextCtrl,
                                                    onChanged: (value) {
                                                      expenseController
                                                              .transaction
                                                              .value
                                                              .txnDisplayName =
                                                          value;
                                                      expenseController
                                                          .transaction
                                                          .refresh();
                                                    },
                                                  ),
                                                ],
                                              )
                                            ]
                                          : [],

                                      const SizedBox(
                                        height: 10,
                                      ),
                                      if (!iscashExpenses)
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            //===================================Mobile
                                            SizedBox(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.3,
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'फोन नम्बर',
                                                    style: labelStyle2,
                                                  ),
                                                  const SizedBox(height: 5.0),
                                                  FormBuilderTextField(
                                                      name: "mobile",
                                                      readOnly: true,
                                                      autocorrect: false,
                                                      keyboardType:
                                                          TextInputType.number,
                                                      textInputAction:
                                                          TextInputAction.done,
                                                      inputFormatters: [
                                                        FilteringTextInputFormatter
                                                            .digitsOnly
                                                      ],
                                                      style: formFieldTextStyle,
                                                      decoration: formFieldStyle
                                                          .copyWith(
                                                              labelText:
                                                                  "Contact no.",
                                                              hintText:
                                                                  "Contact no"),
                                                      controller:
                                                          expenseController
                                                              .mobileCtrl),
                                                ],
                                              ),
                                            ),

                                            //===================================Address
                                            SizedBox(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.5,
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'ठेगाना',
                                                    style: labelStyle2,
                                                  ),
                                                  const SizedBox(height: 5.0),
                                                  FormBuilderTextField(
                                                    name: "address",
                                                    readOnly: true,
                                                    autocorrect: false,
                                                    keyboardType:
                                                        TextInputType.text,
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            labelText:
                                                                "Address"),
                                                    controller:
                                                        expenseController
                                                            .addressCtrl,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      if (!iscashExpenses)
                                        const SizedBox(height: 10.0),

                                      //=====================================PAN No Field
                                      if (!iscashExpenses)
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "पान / मु. अ. कर नम्बर",
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            FormBuilderTextField(
                                                name: "pan_no",
                                                readOnly: true,
                                                autocorrect: false,
                                                keyboardType:
                                                    TextInputType.number,
                                                inputFormatters: [
                                                  FilteringTextInputFormatter
                                                      .digitsOnly
                                                ],
                                                textInputAction:
                                                    TextInputAction.done,
                                                style: formFieldTextStyle,
                                                decoration:
                                                    formFieldStyle.copyWith(
                                                        labelText:
                                                            "PAN/VAT No."),
                                                controller: expenseController
                                                    .panNoCtrl),
                                          ],
                                        ),
                                    ],
                                  ),
                                ),
                              ),

                              //===============================================Description
                              Card(
                                elevation: 2,
                                child: Container(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 10, vertical: 10),
                                    child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "खर्च विवरण",
                                            style: labelStyle2,
                                          ),
                                          const SizedBox(height: 5.0),
                                          FormBuilderTextField(
                                            name: "description",
                                            readOnly:
                                                expenseController.readOnlyFlag,
                                            autocorrect: false,
                                            textAlign: TextAlign.start,
                                            textInputAction:
                                                TextInputAction.newline,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                labelText:
                                                    "Expense Description"),
                                            minLines: 4,
                                            maxLines: 4,
                                            controller:
                                                expenseController.descCtrl,
                                            onChanged: (value) {
                                              expenseController.transaction
                                                  .value.txnDescription = value;
                                              expenseController.transaction
                                                  .refresh();
                                            },
                                            // validators: [],
                                          ),
                                        ])),
                              ),

                              //===============================================Total Amount
                              Card(
                                elevation: 2,
                                child: Column(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 6, horizontal: 8),
                                      decoration: BoxDecoration(
                                          color: colorPrimaryLight,
                                          borderRadius: const BorderRadius.only(
                                              topLeft: Radius.circular(4),
                                              topRight: Radius.circular(4))),
                                      child: const Center(
                                          child: Text(
                                        "Bill Totals (जम्मा बिल)",
                                        style: TextStyle(
                                            color: Colors.white, fontSize: 16),
                                      )),
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 10,
                                      ),
                                      child: Column(
                                        children: [
                                          // =============================================Sub Total
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                "रकम",
                                                style: labelStyle2,
                                              ),
                                              const SizedBox(height: 5.0),
                                              FormBuilderTextField(
                                                name: "txn_subtotal",
                                                readOnly: expenseController
                                                    .readOnlyFlag,
                                                autocorrect: false,
                                                keyboardType:
                                                    const TextInputType
                                                        .numberWithOptions(
                                                        decimal: true),
                                                textInputAction:
                                                    TextInputAction.done,
                                                style: formFieldTextStyle,
                                                inputFormatters: [
                                                  FilteringTextInputFormatter
                                                      .allow(RegExp(
                                                          r'^(\d+)?\.?\d{0,2}'))
                                                ],
                                                maxLength: 10,
                                                decoration:
                                                    formFieldStyle.copyWith(
                                                        hintText: "Amount",
                                                        counterText: ''),
                                                textAlign: TextAlign.end,
                                                controller: expenseController
                                                    .subTotalAmountCtrl,
                                                onChanged: (value) {
                                                  expenseController
                                                      .onSubTotalIndividualChange(
                                                          value ?? "",
                                                          editorTag:
                                                              'txn_subtotal');
                                                  expenseController.transaction
                                                      .refresh();
                                                },
                                              ),
                                            ],
                                          ),
                                          const SizedBox(
                                            height: 20,
                                          ),

                                          // =============================================Discount
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                "छुट (Discount)",
                                                style: labelStyle2,
                                              ),
                                              const SizedBox(height: 5.0),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Container(
                                                    width: 80,
                                                    child: FormBuilderTextField(
                                                      name:
                                                          "txn_discount_percent",
                                                      readOnly: expenseController
                                                              .readOnlyFlag ||
                                                          (!hasSubTotal),
                                                      autocorrect: false,
                                                      keyboardType:
                                                          const TextInputType
                                                              .numberWithOptions(
                                                              decimal: true),
                                                      textInputAction:
                                                          TextInputAction.done,
                                                      style: formFieldTextStyle,
                                                      decoration: formFieldStyle
                                                          .copyWith(
                                                              suffix:
                                                                  const Text(
                                                                      "%"),
                                                              labelText: "%"),
                                                      textAlign: TextAlign.end,
                                                      controller: expenseController
                                                          .discountPercentageCtrl,
                                                      onChanged: (value) {
                                                        expenseController
                                                            .updateDiscountPercentage(
                                                                value ?? "",
                                                                editorTag:
                                                                    'txn_discount_percent');
                                                      },
                                                    ),
                                                  ),
                                                  const SizedBox(
                                                    width: 20,
                                                  ),
                                                  Expanded(
                                                    child: FormBuilderTextField(
                                                        name:
                                                            "txn_discount_amount",
                                                        readOnly: expenseController
                                                                .readOnlyFlag ||
                                                            (!hasSubTotal),
                                                        autocorrect: false,
                                                        keyboardType:
                                                            const TextInputType.numberWithOptions(
                                                                decimal: true),
                                                        textInputAction:
                                                            TextInputAction
                                                                .done,
                                                        style:
                                                            formFieldTextStyle,
                                                        decoration: formFieldStyle
                                                            .copyWith(
                                                                labelText:
                                                                    "छुट रकम (Dis. Amount)"),
                                                        textAlign:
                                                            TextAlign.end,
                                                        controller:
                                                            expenseController
                                                                .discountAmountCtrl,
                                                        onChanged: (value) {
                                                          expenseController
                                                              .updateDiscountAmount(
                                                                  value ?? "",
                                                                  editorTag:
                                                                      'txn_discount_amount');
                                                        }),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                          const SizedBox(
                                            height: 25,
                                          ),

                                          //====================================================VAT
                                          ...iscashExpenses
                                              ? []
                                              : [
                                                  Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Row(
                                                        children: [
                                                          SizedBox(
                                                            width: 20,
                                                            height: 20,
                                                            child: Checkbox(
                                                              activeColor:
                                                                  colorPrimary,
                                                              checkColor:
                                                                  Colors.white,
                                                              value: expenseController
                                                                  .isVatEnabled,
                                                              onChanged: (expenseController
                                                                          .readOnlyFlag ||
                                                                      (!hasSubTotal))
                                                                  ? null
                                                                  : (value) {
                                                                      expenseController
                                                                          .onToggleVat(
                                                                              value!);
                                                                    },
                                                            ),
                                                          ),
                                                          Text(
                                                              "  मु.अ. कर (VAT)",
                                                              style:
                                                                  labelStyle2)
                                                        ],
                                                      ),
                                                      const SizedBox(
                                                          height: 10.0),
                                                      Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          Container(
                                                            width: 80,
                                                            child:
                                                                FormBuilderTextField(
                                                              name:
                                                                  "txn_tax_percent",
                                                              readOnly: true,
                                                              autocorrect:
                                                                  false,
                                                              keyboardType:
                                                                  const TextInputType
                                                                      .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                              textInputAction:
                                                                  TextInputAction
                                                                      .done,
                                                              style:
                                                                  formFieldTextStyle,
                                                              decoration: formFieldStyle
                                                                  .copyWith(
                                                                      suffix:
                                                                          const Text(
                                                                              "%"),
                                                                      labelText:
                                                                          "%"),
                                                              textAlign:
                                                                  TextAlign.end,
                                                              controller:
                                                                  expenseController
                                                                      .vatPercentCtrl,
                                                              onChanged:
                                                                  (value) {
                                                                expenseController
                                                                    .onvatPercentChange(
                                                                        value ??
                                                                            "",
                                                                        editorTag:
                                                                            'txn_tax_percent');
                                                              },
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                            width: 20,
                                                          ),
                                                          Expanded(
                                                            child:
                                                                FormBuilderTextField(
                                                              name:
                                                                  "txn_tax_amount",
                                                              readOnly: true,
                                                              autocorrect:
                                                                  false,
                                                              keyboardType:
                                                                  const TextInputType
                                                                      .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                              textInputAction:
                                                                  TextInputAction
                                                                      .done,
                                                              style:
                                                                  formFieldTextStyle,
                                                              decoration: formFieldStyle
                                                                  .copyWith(
                                                                      labelText:
                                                                          "मु.अ. कर रकम (VAT Amount) "),
                                                              textAlign:
                                                                  TextAlign.end,
                                                              controller:
                                                                  expenseController
                                                                      .vatAmountCtrl,
                                                              onChanged:
                                                                  (value) {
                                                                expenseController
                                                                    .onvatAmountChange(
                                                                        value ??
                                                                            "",
                                                                        editorTag:
                                                                            'txn_tax_amount');
                                                              },
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                  const SizedBox(
                                                    height: 5,
                                                  ),
                                                ],

                                          const Divider(
                                            height: 5,
                                          ),
                                          const Divider(
                                            height: 0,
                                          ),
                                          const SizedBox(
                                            height: 15,
                                          ),

                                          // =============================================Total Amount
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                "कुल रकम",
                                                style: labelStyle2,
                                              ),
                                              const SizedBox(
                                                height: 5,
                                              ),
                                              FormBuilderTextField(
                                                  name: "txn_total",
                                                  readOnly: true,
                                                  autocorrect: false,
                                                  keyboardType:
                                                      const TextInputType
                                                          .numberWithOptions(
                                                          decimal: true),
                                                  textInputAction:
                                                      TextInputAction.done,
                                                  style: formFieldTextStyle,
                                                  decoration:
                                                      formFieldStyle.copyWith(
                                                          labelText:
                                                              "Total Amount"),
                                                  textAlign: TextAlign.end,
                                                  controller: expenseController
                                                      .totalAmountCtrl),
                                            ],
                                          ),
                                          const SizedBox(
                                            height: 25,
                                          ),

                                          // =============================================Received Amount
                                          Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Row(
                                                        children: [
                                                          SizedBox(
                                                            width: 20,
                                                            height: 20,
                                                            child: Checkbox(
                                                              activeColor:
                                                                  colorPrimary,
                                                              checkColor:
                                                                  Colors.white,
                                                              value: iscashExpenses
                                                                  ? true
                                                                  : (expenseController
                                                                      .isReceived),
                                                              onChanged: (expenseController
                                                                          .readOnlyFlag ||
                                                                      iscashExpenses ||
                                                                      (!hasSubTotal))
                                                                  ? null
                                                                  : (value) {
                                                                      expenseController
                                                                              .setIsReceived =
                                                                          value!;
                                                                      if (value) {
                                                                        expenseController
                                                                            .transaction
                                                                            .value
                                                                            .txnCashAmount = expenseController
                                                                                .transaction.value.txnTotalAmount ??
                                                                            0.0;
                                                                        expenseController
                                                                            .transaction
                                                                            .value
                                                                            .txnBalanceAmount = 0.00;
                                                                      } else {
                                                                        expenseController
                                                                            .transaction
                                                                            .value
                                                                            .txnCashAmount = 0.0;

                                                                        expenseController
                                                                            .transaction
                                                                            .value
                                                                            .txnBalanceAmount = expenseController
                                                                                .transaction.value.txnTotalAmount ??
                                                                            0.0;
                                                                      }
                                                                      expenseController
                                                                          .assignTransactionToTextFields();
                                                                    },
                                                            ),
                                                          ),
                                                          Text(" भुक्तानी रकम",
                                                              style:
                                                                  labelStyle2)
                                                        ],
                                                      ),
                                                      const SizedBox(
                                                        height: 10,
                                                      ),
                                                      PaymentModeSelector(
                                                        onChangedFn: (v) {
                                                          expenseController
                                                              .transaction
                                                              .value
                                                              .txnPaymentTypeId = v;

                                                          expenseController
                                                                  .transaction
                                                                  .value
                                                                  .txnPaymentReference =
                                                              null;
                                                          expenseController
                                                                  .transaction
                                                                  .value
                                                                  .chequeIssueDateBS =
                                                              null;
                                                          expenseController
                                                              .transaction
                                                              .refresh();
                                                        },
                                                        paymentModeID:
                                                            expenseController
                                                                .transaction
                                                                .value
                                                                .txnPaymentTypeId,
                                                        enableFlag:
                                                            expenseController
                                                                .readOnlyFlag,
                                                      )
                                                    ],
                                                  ),
                                                ),
                                                const SizedBox(
                                                  width: 10,
                                                ),
                                                Expanded(
                                                  child: FormBuilderTextField(
                                                    name: "txn_cash_amount",
                                                    readOnly: (expenseController
                                                            .readOnlyFlag ||
                                                        !hasSubTotal ||
                                                        iscashExpenses),
                                                    autocorrect: false,
                                                    keyboardType:
                                                        const TextInputType
                                                            .numberWithOptions(
                                                            decimal: true),
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    inputFormatters: [
                                                      FilteringTextInputFormatter
                                                          .digitsOnly
                                                    ],
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            labelText:
                                                                "Paid Amount"),
                                                    textAlign: TextAlign.end,
                                                    controller:
                                                        expenseController
                                                            .receivedAmountCtrl,
                                                    onChanged: (value) {
                                                      expenseController
                                                          .changeReceivedAmount(
                                                              value ?? "",
                                                              editorTag:
                                                                  'txn_cash_amount');
                                                    },
                                                  ),
                                                )
                                              ]),
                                          ...(expenseController.transaction
                                                      .value.txnPaymentTypeId !=
                                                  PAYMENT_MODE_CASH_ID)
                                              ? [
                                                  const SizedBox(
                                                    height: 25,
                                                  ),
                                                  Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text("चेक/भौचर न.",
                                                          style: labelStyle2),
                                                      const SizedBox(
                                                        height: 10,
                                                      ),
                                                      TextField(
                                                          autocorrect: false,
                                                          readOnly:
                                                              expenseController
                                                                  .readOnlyFlag,
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration: formFieldStyle
                                                              .copyWith(
                                                                  labelText:
                                                                      "Cheque/Voucher No."),
                                                          controller:
                                                              expenseController
                                                                  .paymentRefCtrl,
                                                          onChanged: (v) {
                                                            expenseController
                                                                .transaction
                                                                .value
                                                                .txnPaymentReference = v;
                                                            expenseController
                                                                .transaction
                                                                .refresh();
                                                          }),
                                                    ],
                                                  ),
                                                ]
                                              : [],
                                          if (expenseController.transaction
                                                  .value.txnPaymentTypeId ==
                                              PAYMENT_MODE_CHEQUE_ID) ...[
                                            const SizedBox(
                                              height: 25,
                                            ),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text("चेक मिति",
                                                    style: labelStyle2),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                CustomDatePickerTextField(
                                                  labelText: "Cheque Date",
                                                  readOnly: expenseController
                                                      .readOnlyFlag,
                                                  // maxBSDate: NepaliDateTime.now(),
                                                  initialValue:
                                                      expenseController
                                                          .transaction
                                                          .value
                                                          .chequeIssueDateBS,
                                                  onChange: (selectedDate) {
                                                    expenseController
                                                            .transaction
                                                            .value
                                                            .chequeIssueDateBS =
                                                        selectedDate;
                                                  },
                                                ),
                                              ],
                                            )
                                          ],

                                          const SizedBox(
                                            height: 25,
                                          ),

                                          // =============================================Balance Amount
                                          ...iscashExpenses
                                              ? []
                                              : [
                                                  Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text("बाँकी रहेको रकम",
                                                          style: labelStyle2),
                                                      const SizedBox(
                                                        height: 10,
                                                      ),
                                                      FormBuilderTextField(
                                                          name:
                                                              "txn_balance_amount",
                                                          readOnly: true,
                                                          autocorrect: false,
                                                          keyboardType:
                                                              const TextInputType.numberWithOptions(
                                                                  decimal:
                                                                      true),
                                                          textInputAction:
                                                              TextInputAction
                                                                  .done,
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration: formFieldStyle
                                                              .copyWith(
                                                                  labelText:
                                                                      "Balance Due"),
                                                          textAlign:
                                                              TextAlign.end,
                                                          controller:
                                                              expenseController
                                                                  .dueAmountCtrl),
                                                    ],
                                                  ),
                                                  const SizedBox(
                                                    height: 20,
                                                  ),
                                                ],
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              //===============================================Image
                              // Container(
                              //   width: double.infinity,
                              //   child: Card(
                              //     elevation: 2,
                              //     child: Container(
                              //       child: Container(
                              //           // color: Colors.red,
                              //           height: 140,
                              //           width: 100,
                              //           // width: ,
                              //           // child: (null==state.selectImage)?
                              //           child: FormBuilderImagePicker(
                              //               attribute: "image_picker",
                              //               bottomSheetPadding:
                              //                   EdgeInsets.all(0),
                              //               imageHeight: 100,
                              //               imageWidth: 100,
                              //
                              //               // maxHeight: 100,
                              //               // maxWidth: 100,
                              //               imageMargin: EdgeInsets.symmetric(
                              //                   horizontal: 10),
                              //               readOnly:
                              //                   expenseController.readOnlyFlag,
                              //               decoration: InputDecoration(
                              //                 border: InputBorder.none,
                              //               ),
                              //               maxImages: 2,
                              //               iconColor: colorPrimaryLight,
                              //               validators: [],
                              //               initialValue:
                              //                   expenseController.files.value,
                              //               onChanged: (_fls) async {
                              //                 expenseController.files.value =
                              //                     _fls.cast<File>();
                              //                 expenseController.files.refresh();
                              //
                              //                 bool isLargeFile =
                              //                     await expenseController
                              //                         .checkLargeImage(_fls);
                              //                 if (isLargeFile) {
                              //                   showToastMessage(context,
                              //                       message:
                              //                           MAX_IMAGE_SIZE_MESSAGE,
                              //                       alertType: AlertType.Error);
                              //                   return;
                              //                 }
                              //               })
                              //
                              //           //     :customImageBox(
                              //           //     state.selectImage,
                              //           //     onCancel: (expenseController.readOnlyFlag)?  null : () => state.imageCancelButtonOnClickHandler()
                              //           // ),
                              //           ),
                              //     ),
                              //   ),
                              // ),
                              //===============================================Image Preview Grid
                              SizedBox(
                                width: double.infinity,
                                child: Card(
                                  elevation: 2,
                                  child: SizedBox(
                                      height: 140,
                                      width: 100,
                                      child: ImageGalleryGrid(
                                          images: expenseController.files)),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                //=================================================Save button
                bottomNavigationBar: BottomSaveCancelButton(
                  shadow: false,
                  hasDelete: true,
                  onDeleteBtnPressedFn: () async {
                    showAlertDialog(context,
                        okText: "YES",
                        hasCancel: true,
                        cancelText: "NO",
                        alertType: AlertType.Error,
                        alertTitle: "Confirm Delete",
                        onCloseButtonPressed: () async {
                      Navigator.of(context).pop();
                      debugPrint("hello there delete gar na");
                      ProgressDialog progressDialog = ProgressDialog(context,
                          type: ProgressDialogType.normal,
                          isDismissible: false);
                      progressDialog.update(
                          message: "Checking Permission. Please wait....");
                      await progressDialog.show();
                      Tuple2<bool, String> checkResp =
                          await PermissionWrapperController()
                              .requestForPermissionCheck(
                                  forPermission:
                                      PermissionManager.expenseDelete);
                      if (checkResp.item1) {
                        //has  permission
                        progressDialog.update(
                            message: "Deleting Data. Please wait....");
                        Tuple2<bool, String> deleteResp =
                            await TransactionRepository()
                                .delete(expensID ?? "");
                        await progressDialog.hide();
                        if (deleteResp.item1) {
                          //  data deleted
                          TransactionHelper.refreshPreviousPages();
                          showAlertDialog(context,
                              barrierDismissible: false,
                              alertType: AlertType.Success,
                              alertTitle: "", onCloseButtonPressed: () {
                            // Navigator.of(_).pop();
                            Navigator.of(context).pop();
                          }, message: deleteResp.item2);
                        } else {
                          //cannot  delete  data
                          showAlertDialog(context,
                              alertType: AlertType.Error,
                              alertTitle: "",
                              message: deleteResp.item2);
                        }
                      } else {
                        await progressDialog.hide();
                        showAlertDialog(context,
                            alertType: AlertType.Error,
                            alertTitle: "",
                            message: checkResp.item2);
                      }
                    },
                        message:
                            "Are you sure you  want to  delete this expense record?");
                  },
                  enableFlag: true,
                  saveText: "Share",
                  onSaveBtnPressedFn: () {
                    TransactionHelper.goToPrintPage(
                        context, expensID ?? "", TxnType.expense);
                  },
                )));
      }
    });
  }
}
