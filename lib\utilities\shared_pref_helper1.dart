import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';

import 'package:shared_preferences/shared_preferences.dart';

class SharedPrefHelper1 {
  static final SharedPrefHelper1 _instance = SharedPrefHelper1._internal();

  SharedPrefHelper1._internal();

  factory SharedPrefHelper1() => _instance;

  SharedPreferences? _prefs;

  Future<bool> get isLoggedIn async {
    _prefs = await SharedPreferences.getInstance();

    bool status = (_prefs!.getBool(LoginStatus) ?? false);

    return status;
  }

  Future<bool> get isUnauthorized async {
    _prefs = await SharedPreferences.getInstance();
    bool status = (_prefs!.getBool(UnauthorizedKey) ?? false);
    return status;
  }

  Future<bool> setUnauthorized(bool flag) async {
    _prefs = await SharedPreferences.getInstance();
    bool status = await _prefs!.setBool(Unauthorized<PERSON>ey, flag);
    // await refreshUnAuthorized();
    return status;
  }

  Future<bool> get isExpired async {
    _prefs = await SharedPreferences.getInstance();
    bool status = (_prefs!.getBool(ExpiredKey) ?? false);
    return status;
  }

  Future<dynamic> get getSubsDetail async {
    _prefs = await SharedPreferences.getInstance();
    String sub_detail_string = _prefs!.getString(SubDetailKey) ?? "{}";
    return jsonDecode(sub_detail_string);
  }

  Future<bool> setSubsDetail(dynamic d) async {
    // Log.d("saving in shared $d ${jsonEncode(d)}");
    _prefs = await SharedPreferences.getInstance();
    bool status = await _prefs!.setString(SubDetailKey, jsonEncode(d ?? {}));
    return status;
  }

  // Future<String> get accountExpiryDate

  Future<int> get isActive async {
    _prefs = await SharedPreferences.getInstance();
    int status = (_prefs!.getInt(ActiveKey) ?? 1);
    return status;
  }

  Future<int> get isMultiUser async {
    _prefs = await SharedPreferences.getInstance();
    int status = (_prefs!.getInt(MultiUserKey) ?? 1);
    return status;
  }

  Future<bool> setExpired(bool flag) async {
    _prefs = await SharedPreferences.getInstance();
    bool status = await _prefs!.setBool(ExpiredKey, flag);
    return status;
  }

  Future<bool> setActive(int v) async {
    _prefs = await SharedPreferences.getInstance();
    bool status = await _prefs!.setInt(ActiveKey, v);
    return status;
  }

  Future<bool> setMultiUser(int v) async {
    _prefs = await SharedPreferences.getInstance();
    bool status = await _prefs!.setInt(MultiUserKey, v);
    return status;
  }

  Future<bool> setWebEnabled(bool v) async {
    _prefs = await SharedPreferences.getInstance();
    bool status = await _prefs!.setBool(WebEnabledKey, v);
    return status;
  }

  Future<bool> createLoginSession(
      {required int userId, required int registerDeviceId}) async {
    _prefs = await SharedPreferences.getInstance();

    _prefs!.setBool(LoginStatus, true);
    // _prefs.setInt(UserIdKey, userId);
    // _prefs.setInt(RegisteredDeviceId, registerDeviceId);

    return true;
  }

  Future<bool> logout() async {
    _prefs = await SharedPreferences.getInstance();
    _prefs!.remove(LoginStatus);
    // _prefs.remove(UserIdKey);
    // _prefs.remove(RegisteredDeviceId);

    return true;
  }

  Future<bool> get isFreshInstall async {
    _prefs = await SharedPreferences.getInstance();

    bool status = (_prefs!.getBool(FreshInstallStatus) ?? true);

    return status;
  }

  Future<bool> updateFreshInstallStatus() async {
    _prefs = await SharedPreferences.getInstance();

    _prefs!.setBool(FreshInstallStatus, false);

    return true;
  }

  Future<bool> updateDbStatus(bool status) async {
    _prefs = await SharedPreferences.getInstance();

    if (status) {
      _prefs!.setBool('db', status);
    } else {}

    return true;
  }

  Future<bool> checkPermission(String permission) async {
    _prefs = await SharedPreferences.getInstance();
    List<String> permissions = _prefs!.getStringList(Permissions) ?? [];
    return permissions.contains(permission);
  }

  Future<bool> get getDbStatus async {
    _prefs = await SharedPreferences.getInstance();

    bool status = (_prefs!.getBool('db') ?? false);

    return status;
  }

  Future<bool> updateEvent(String str) async {
    _prefs = await SharedPreferences.getInstance();
    bool status = await _prefs!.setString('events', str);
    return status;
  }

  Future<bool> addEvent(String str) async {
    _prefs = await SharedPreferences.getInstance();
    bool status = await _prefs!
        .setString('events', (_prefs!.getString('events') ?? "") + str);
    return status;
  }

  Future<String> getEvents() async {
    _prefs = await SharedPreferences.getInstance();
    String status = _prefs!.getString('events') ?? "";
    return status;
  }

  Future<Map<String, dynamic>> getBackupInfo() async {
    _prefs = await SharedPreferences.getInstance();
    String back_info_string = _prefs!.getString('backup_info') ?? "{}";
    return jsonDecode(back_info_string);
  }

  Future<bool> updateBackupInfo(Map<String, dynamic> newInfo) async {
    _prefs = await SharedPreferences.getInstance();
    bool status = await _prefs!.setString('backup_info', jsonEncode(newInfo));
    return status;
  }

  //batch ID operations
  Future<bool> addBatchIDs(String batchIds) async {
    _prefs = await SharedPreferences.getInstance();
    List<String> existingBatchIds = _prefs!.getStringList('batch_ids') ?? [];
    if (!existingBatchIds.contains(batchIds)) {
      existingBatchIds.add(batchIds);
    }
    bool status = await _prefs!.setStringList('batch_ids', existingBatchIds);
    return status;
  }

  Future<List<String>> getBatchIds() async {
    _prefs = await SharedPreferences.getInstance();
    List<String> existingBatchIds = _prefs!.getStringList('batch_ids') ?? [];
    return existingBatchIds;
  }

  Future<bool> removeBatchIds(String batchIds) async {
    _prefs = await SharedPreferences.getInstance();
    List<String> existingBatchIds = _prefs!.getStringList('batch_ids') ?? [];
    if (existingBatchIds.contains(batchIds)) {
      existingBatchIds.remove(batchIds);
    }
    bool status = await _prefs!.setStringList('batch_ids', existingBatchIds);
    return status;
  }

  Future<bool> containsBatchIds(String batchIds) async {
    _prefs = await SharedPreferences.getInstance();
    List<String> existingBatchIds = _prefs!.getStringList('batch_ids') ?? [];
    return existingBatchIds.contains(batchIds);
  }

  Future<bool> clearBatchIds() async {
    _prefs = await SharedPreferences.getInstance();
    bool status = await _prefs!.remove('batch_ids');
    return status;
  }
}
