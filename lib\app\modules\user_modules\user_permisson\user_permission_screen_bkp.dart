// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/model/others/user_modal.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/user_list/user_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/user_permisson/user_permission_controller.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:tuple/tuple.dart';

class UserPermissionScreen extends StatelessWidget {
  final UserModel? user;
  final userPermissionController = UserPermissionController();
  UserPermissionScreen({super.key, this.user}) {
    userPermissionController.init(user!);
  }
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (userPermissionController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
              appBar: AppBar(
                toolbarHeight: 60,
                elevation: 4,
                centerTitle: false,
                titleSpacing: -5.0,
                title: const Text("User Permissions"),
                actions: [
                  // if (userPermissionController.editFlag) ...{
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: ElevatedButton(
                        // elevation: 0,
                        // textColor: Colors.black54,
                        // color: Colors.transparent,
                        // padding: EdgeInsets.zero,
                        // splashColor: colorPrimaryLight,
                        // shape: RoundedRectangleBorder(
                        //     borderRadius: BorderRadius.circular(10)),
                        onPressed: () => userPermissionController.readOnlyFlag =
                            !userPermissionController.readOnlyFlag,
                        child: (userPermissionController.readOnlyFlag)
                            ? Column(
                                children: const [
                                  Icon(
                                    Icons.mode_edit,
                                    color: Colors.white,
                                  ),
                                  Text(
                                    "Click here to Edit",
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 10),
                                  ),
                                ],
                              )
                            : Column(
                                children: const [
                                  Icon(
                                    Icons.close,
                                    color: Colors.white,
                                  ),
                                  Text(
                                    "Cancel",
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 10),
                                  ),
                                ],
                              )),
                  ),
                ],
              ),
              body: ListView(
                padding:
                    const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
                children: [
                  Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: userPermissionController.readOnlyFlag
                          ? null
                          : () {
                              if (userPermissionController.permissions.length ==
                                  PermissionManager.permissionTypeList.length) {
                                //remove  all
                                userPermissionController.clearAllPermission();
                              } else {
                                //select all
                                userPermissionController.onSelectAllPermission(
                                    PermissionManager.getAllPermissionValues());
                              }
                            },
                      child: Container(
                        color: colorPrimary,
                        child: Row(
                          children: [
                            Theme(
                              data: ThemeData(
                                primarySwatch: Colors.blue,
                                unselectedWidgetColor:
                                    Colors.white, // Your color
                              ),
                              child: AbsorbPointer(
                                child: Checkbox(
                                  value: userPermissionController
                                          .permissions.length ==
                                      PermissionManager
                                          .permissionTypeList.length,
                                  onChanged: (value) {},
                                  activeColor: Colors.white,
                                  checkColor: colorPrimary,
                                ),
                              ),
                            ),
                            const SizedBox(
                              width: 0,
                            ),
                            Text(
                              "All",
                              style: labelStyle2.copyWith(color: Colors.white),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  ...PermissionManager.permissionGroupName.entries.map((entry) {
                    int groupID = entry.key;
                    String groupName = entry.value;
                    List<dynamic> groupChilds =
                        PermissionManager.permissionsForGroup(groupID);

                    if (groupChilds.isEmpty) {
                      return const SizedBox(
                        height: 0,
                        width: 0,
                      );
                    }
                    return Container(
                      decoration: BoxDecoration(
                          border:
                              Border.all(color: Colors.black12, width: 0.5)),
                      child: Column(children: [
                        Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: userPermissionController.readOnlyFlag
                                ? null
                                : () {
                                    userPermissionController
                                        .onSelectGroupPermission(groupID);
                                  },
                            child: Container(
                              color: colorPrimaryLightest,
                              child: Row(
                                children: [
                                  AbsorbPointer(
                                    child: Checkbox(
                                      value: userPermissionController
                                          .isAllExistForGroup(groupID),
                                      onChanged: (value) {},
                                      activeColor: Colors.white,
                                      checkColor: colorPrimary,
                                    ),
                                  ),
                                  Text(
                                    groupName,
                                    style: labelStyle2.copyWith(
                                        color: Colors.white),
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                        ...groupChilds.map((groupChildItem) {
                          return Material(
                            child: InkWell(
                              onTap: userPermissionController.readOnlyFlag
                                  ? null
                                  : () {
                                      userPermissionController
                                          .onSelectSinglePermission(
                                              groupChildItem['value']
                                                  .toString());
                                    },
                              child: Container(
                                padding: const EdgeInsets.only(left: 30),
                                child: Row(
                                  children: [
                                    AbsorbPointer(
                                      child: Checkbox(
                                        value: userPermissionController
                                            .permissions
                                            .contains(groupChildItem['value']),
                                        onChanged: (value) {},
                                        activeColor: colorPrimary,
                                      ),
                                    ),
                                    Text(
                                      groupChildItem['text'],
                                      style: labelStyle2,
                                    )
                                  ],
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ]),
                    );
                  }),
                  Text(userPermissionController.permissions.toString())
                ],
              ),
              bottomNavigationBar: BottomSaveCancelButton(
                  shadow: false,
                  enableFlag: !userPermissionController.readOnlyFlag,
                  onSaveBtnPressedFn: (userPermissionController.readOnlyFlag)
                      ? null
                      : () async {
                          ProgressDialog progressDialog = ProgressDialog(
                              context,
                              type: ProgressDialogType.normal,
                              isDismissible: false);
                          progressDialog.update(
                              message:
                                  "Updating user's permission. Please wait....");
                          await progressDialog.show();

                          Tuple2<bool, String> toggleResp =
                              await userPermissionController
                                  .updateUserPermission();
                          await progressDialog.hide();
                          if (toggleResp.item1) {
                            Navigator.of(context).pop();
                            //update user list
                            if (Get.isRegistered<UserListController>(
                                tag: "UserListController")) {
                              UserListController userListController =
                                  Get.find(tag: "UserListController");
                              userListController.init();
                            }
                            showToastMessage(context,
                                message: toggleResp.item2, duration: 2);
                          } else {
                            showToastMessage(context,
                                alertType: AlertType.Error,
                                message: toggleResp.item2,
                                duration: 2);
                          }
                        })));
    });
  }
}
