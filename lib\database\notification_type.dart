// ignore_for_file: constant_identifier_names

class NotificationType {
  static const String server = "SERVER";
  static const String payment = "PAYMENT";
  static const String local = "LOCAL";
  static const String lowStock = "LOW_STOCK";

  static final Map<String, String> notificationTypeText = ({
    NotificationType.payment: "Payment",
    NotificationType.server: "Server",
    NotificationType.local: "Local",
    NotificationType.lowStock: "Low Stock"
  });
}

class NotificationLinkType {
  static const String EXTERNAL = "EXTERNAL";
  static const String INTERNAL = "INTERNAL";

  static final Map<String, String> notificationLinkTypeText = ({
    NotificationLinkType.EXTERNAL: "External",
    NotificationLinkType.INTERNAL: "Internal",
  });
}
