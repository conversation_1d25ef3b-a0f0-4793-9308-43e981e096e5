import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/controllers/registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/registration_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';

import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:nepali_utils/nepali_utils.dart';

Future<Uint8List> generateExpensePrint(
  PdfPageFormat pageFormat, {
  String billTitle = "Expense",
  AllTransactionModel? transactionModel,
  LedgerDetailModel? ledgerDetailModel,
}) async {
  RegistrationDetailController _registrationDetailController =
      Get.find(tag: "RegistrationDetailController");
  ImageModel sellerImageModel = _registrationDetailController.logo;

  RegistrationDetailModel myDetail =
      _registrationDetailController.registrationDetail;

  final invoice = Invoice(
    myDetail: myDetail,
    partyModel: ledgerDetailModel!,
    sellerImage: sellerImageModel,
    transaction: transactionModel!,
    invoiceTitle: billTitle,
    baseColor: PdfColors.lightBlue,
    accentColor: PdfColors.blueGrey900,
  );

  return await invoice.buildPdf(pageFormat);
}

class Invoice {
  Invoice({
    this.myDetail,
    this.transaction,
    this.partyModel,
    this.sellerImage,
    this.invoiceTitle,
    this.baseColor,
    this.accentColor,
  });

  final String? invoiceTitle;
  final RegistrationDetailModel? myDetail;
  final ImageModel? sellerImage;
  final AllTransactionModel? transaction;
  final LedgerDetailModel? partyModel;
  final PdfColor? baseColor;
  final PdfColor? accentColor;

  static const _darkColor = PdfColors.blueGrey800;
  static const _lightColor = PdfColors.black;
  static const _VedColor = PdfColor.fromInt(0xFF3560AF);

  PdfColor get _baseTextColor =>
      baseColor!.luminance < 0.5 ? _lightColor : _darkColor;

  // double get _total => (transaction!.txnDiscountAmount! * 100)/transaction!.txnDiscountPercent!;
  double get _total => transaction!.txnDiscountPercent.toString() != "0.0"
      ? (transaction!.txnDiscountAmount! * 100) /
          transaction!.txnDiscountPercent!
      : transaction!.txnTaxPercent.toString() != "0.0"
          ? (transaction!.txnTaxAmount! * 100) / transaction!.txnTaxPercent!
          : transaction!.txnCashAmount! + transaction!.txnBalanceAmount!;

  // double get _total => transaction!.txnBalanceAmount!;

  double get _grandTotal => transaction!.txnTotalAmount!;

  var currencyInWords = NepaliNumberFormat(
    inWords: true,
    language: Language.english,
    isMonetory: true,
    decimalDigits: 2,
  );

  String? _logo;

  Future<Uint8List> buildPdf(PdfPageFormat pageFormat) async {
    // Create a PDF document.
    final doc = pw.Document();

    final font1 = await rootBundle.load('assets/roboto1.ttf');
    final font2 = await rootBundle.load('assets/roboto1.ttf');
    final font3 = await rootBundle.load('assets/roboto1.ttf');

    _logo = await rootBundle.loadString('assets/mobilekhata.svg');

    // Add page to the PDF
    doc.addPage(
      pw.MultiPage(
        header: _buildHeader,
        build: (context) => [
          _header(context),
          _newHeader(context),
          pw.SizedBox(height: 20),
          _contentTable(context),
          // _content(context),
          pw.SizedBox(height: 20),
          _contentFooter(context),
          _terms(context),
          pw.SizedBox(height: 20),
        ],
      ),
    );

    // Return the PDF file content
    return doc.save();
  }

  pw.Widget _buildHeader(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Expanded(
              flex: 1,
              child: pw.Column(
                mainAxisSize: pw.MainAxisSize.min,
                children: [
                  if (null != sellerImage!.imageBitmap) ...{
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.center,
                        children: [
                          pw.Container(
                            margin: pw.EdgeInsets.only(top: -20),
                            alignment: pw.Alignment.center,
                            height: 60,
                            width: 60,
                            child: sellerImage!.imageBitmap != null
                                ? pw.Image(pw.MemoryImage(Uint8List.fromList(
                                    sellerImage!.imageBitmap!)))
                                : pw.PdfLogo(),
                          ),
                        ])
                  },
                  // pw.Container(
                  //   color: baseColor,
                  //   padding: pw.EdgeInsets.only(top: 3),
                  // ),
                ],
              ),
            ),
          ],
        ),
        if (context.pageNumber > 1) pw.SizedBox(height: 20)
      ],
    );
  }

  pw.PageTheme _buildTheme(
      PdfPageFormat pageFormat, pw.Font base, pw.Font bold, pw.Font italic) {
    return pw.PageTheme(
      pageFormat: pageFormat,
      theme: pw.ThemeData.withFont(
        base: base,
        bold: bold,
        italic: italic,
      ),
      buildBackground: (context) => pw.FullPage(
        ignoreMargins: true,
      ),
    );
  }

  pw.Widget _header(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Expanded(
              child: pw.Column(
                children: [
                  pw.Container(
                    height: 30,
                    padding:
                        const pw.EdgeInsets.only(left: 0, top: 0, bottom: 0),
                    alignment: pw.Alignment.center,
                    child: pw.Text(
                      myDetail!.businessName!,
                      style: pw.TextStyle(
                        color: _VedColor,
                        fontWeight: pw.FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  pw.Container(
                      margin: pw.EdgeInsets.only(),
                      child: pw.Text(
                        myDetail!.businessAddress ?? "N/A",
                        style: pw.TextStyle(
                          color: accentColor,
                          fontSize: 8,
                        ),
                      )),
                  if (null != myDetail!.tinNo && "" != myDetail!.tinNo)
                    pw.Container(
                        margin: pw.EdgeInsets.only(),
                        child: pw.Text(
                          "${myDetail!.tinFlag ?? ''}" +
                              " No. :" +
                              "${myDetail!.tinNo ?? ''}",
                          style: pw.TextStyle(
                            color: accentColor,
                            fontSize: 8,
                          ),
                        )),
                  pw.Container(
                    margin: pw.EdgeInsets.only(bottom: 20, top: 10),
                    alignment: pw.Alignment.center,
                    child: pw.Text(invoiceTitle!,
                        style: pw.TextStyle(
                            color: PdfColors.red,
                            fontSize: 15,
                            fontWeight: pw.FontWeight.bold)),
                  )
                ],
              ),
            ),
          ],
        ),
        pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _contentTable(pw.Context context) {
    const tableHeaders = ['Expense Title', 'Total'];

    return pw.Table.fromTextArray(
      border: null,
      cellAlignment: pw.Alignment.centerLeft,
      headerDecoration: pw.BoxDecoration(
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(2)),
        color: PdfColors.blue100,
      ),
      headerHeight: 25,
      cellHeight: 40,
      cellAlignments: {
        0: pw.Alignment.centerLeft,
        1: pw.Alignment.centerRight,
      },
      headerStyle: pw.TextStyle(
        color: _baseTextColor,
        fontSize: 11,
        fontWeight: pw.FontWeight.bold,
      ),
      cellStyle: const pw.TextStyle(
        color: _darkColor,
        fontSize: 9,
      ),
      rowDecoration: pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: accentColor!,
            width: .5,
          ),
        ),
      ),
      headers: List<String>.generate(
        tableHeaders.length,
        (col) => tableHeaders[col],
      ),
      data: [
        [transaction!.expenseCategoryName!, _total]
      ],
    );
  }

  pw.Widget _content(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Container(
                margin: const pw.EdgeInsets.only(top: 10, bottom: 2),
                child: pw.Text(
                  'Description:',
                  style: pw.TextStyle(
                      color: _VedColor,
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 12),
                ),
              ),
              pw.Text(
                transaction!.expenseCategoryName!,
                style: pw.TextStyle(
                  fontSize: 9,
                  // lineSpacing: 5,
                  fontStyle: pw.FontStyle.italic,
                  fontWeight: pw.FontWeight.bold,
                  color: _darkColor,
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  pw.Widget _newHeader(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Container(
                child: pw.Text(
                  'Expense For:',
                  style: pw.TextStyle(
                    color: PdfColors.black,
                    lineSpacing: 10,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.Text(
                // '${(null != transaction.txnDisplayName && "" != transaction.txnDisplayName) ? transaction.txnDisplayName : partyModel.ledgerTitle}',
                transaction!.expenseCategoryName!,
                style: pw.TextStyle(
                    fontSize: 10,
                    color: _darkColor,
                    fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
        ),
        pw.Expanded(
          flex: 1,
          child: pw.DefaultTextStyle(
            style: const pw.TextStyle(
              fontSize: 10,
              color: _darkColor,
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.end,
                  children: [
                    pw.Text(
                      'Voucher No.:\r',
                      style: pw.TextStyle(
                        color: PdfColors.black,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      '${transaction!.txnRefNumberChar ?? "N/A"}',
                      style: pw.TextStyle(
                        color: PdfColors.black,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 5),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.end,
                  children: [
                    pw.Text(
                      'Date:\r',
                      style: pw.TextStyle(
                        color: PdfColors.black,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      transaction!.txnDateBS!,
                      style: pw.TextStyle(
                        color: PdfColors.black,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  pw.Widget _contentFooter(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
                pw.Text(
                  'AMOUNT IN WORDS:',
                  style: pw.TextStyle(
                    color: _VedColor,
                    fontSize: 9,
                  ),
                ),
              ]),
              pw.Row(children: [
                pw.Text(
                  '\r\r${currencyInWords.format(transaction!.txnCashAmount! + transaction!.txnBalanceAmount!)}',
                  style: pw.TextStyle(
                    color: accentColor,
                    fontStyle: pw.FontStyle.italic,
                    fontSize: 9,
                  ),
                ),
              ]),
              pw.Container(
                margin: const pw.EdgeInsets.only(top: 30, bottom: 0),
              ),
            ],
          ),
        ),
        pw.Expanded(
          flex: 1,
          child: pw.DefaultTextStyle(
            style: const pw.TextStyle(
              fontSize: 10,
              color: _darkColor,
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text('Sub Total:'),
                    // pw.Text(transaction!.txnDiscountPercent.toString()),
                    // pw.Text('Sub Total:'),
                    pw.Text(_formatCurrency(_total)),
                  ],
                ),
                pw.SizedBox(height: 5),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text('Discount:'),
                    pw.Text('${(transaction!.txnDiscountAmount)}'),
                  ],
                ),
                if (transaction!.txnTaxAmount! > 0.0)
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text('${(transaction!.txnTaxPercent)}%\rTax:'),
                      pw.Text('${(transaction!.txnTaxAmount)}'),
                    ],
                  ),
                pw.Divider(color: accentColor),
                pw.DefaultTextStyle(
                  style: pw.TextStyle(
                    color: _VedColor,
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text('Total:'),
                      pw.Text(_formatCurrency(transaction!.txnCashAmount! +
                          transaction!.txnBalanceAmount!)),
                    ],
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text('Paid:'),
                    pw.Text('${(transaction!.txnCashAmount)}'),
                  ],
                ),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text('Due:'),
                    pw.Text('${(transaction!.txnBalanceAmount)}'),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  pw.Widget _terms(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 1,
          child: pw.DefaultTextStyle(
              style: pw.TextStyle(
                fontSize: 10,
                color: PdfColors.black,
              ),
              child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.SizedBox(height: 80),
                    pw.Divider(color: accentColor, endIndent: 70, indent: 70),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.center,
                        children: [
                          pw.Text('Paid By'),
                        ])
                  ])),
        ),
        pw.Expanded(
          flex: 1,
          child: pw.DefaultTextStyle(
              style: pw.TextStyle(
                fontSize: 10,
                color: PdfColors.black,
              ),
              child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.SizedBox(height: 80),
                    pw.Divider(color: accentColor, endIndent: 70, indent: 70),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.center,
                        children: [
                          pw.Text('Received By'),
                        ])
                  ])),
        ),
      ],
    );
  }

  String _formatCurrency(double amount) {
    return '${amount.toStringAsFixed(2)}';
  }

  String _formatDate(DateTime date) {
    final format = DateFormat.yMMMd('en_US');
    return format.format(date);
  }
}
