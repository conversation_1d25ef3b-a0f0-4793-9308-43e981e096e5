// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/modules/cheque_module/cheque_deposit/cheque_deposit_controller.dart';
import 'package:mobile_khaata_v2/app/modules/cheque_module/cheque_list/cheque_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:tuple/tuple.dart';

import '../../bank_module/bank_account_list/bank_account_list_controller.dart';

class ChequeDepositPage extends StatelessWidget {
  final String tag = "ChequeDepositPage";

  final String chequeTxnId;
  final controller = ChequeDepositController();
  final bankController =
      Get.put(BankAccountListController(), tag: "BankAccountListController");

  ChequeDepositPage({super.key, required this.chequeTxnId}) {
    controller.init(chequeTxnId, notIncludeCash: true);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Log.d(controller.cheque.txnId);
      if (controller.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
        resizeToAvoidBottomInset: true,
        // resizeToAvoidBottomPadding: true,
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          leading: BackButton(
            onPressed: () => Navigator.pop(context, false),
          ),
          centerTitle: false,
          titleSpacing: -10.0,
          backgroundColor: colorPrimary,
          title: const ListTile(
            contentPadding: EdgeInsets.only(right: 15),
            title: Text(
              "Transfer Cheque",
              style: TextStyle(
                  fontSize: 18,
                  color: Colors.white,
                  fontFamily: 'HelveticaRegular',
                  fontWeight: FontWeight.bold),
            ),
          ),
          actions: [
            if (controller.editFlag) ...{
              InkWell(
                  onTap: () =>
                      controller.readOnlyFlag = !controller.readOnlyFlag,
                  splashColor: colorPrimaryLighter,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 15),
                    child: (controller.readOnlyFlag)
                        ? Column(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(
                                Icons.mode_edit,
                                color: Colors.white,
                              ),
                              Text(
                                "Edit",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          )
                        : Column(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(
                                Icons.close,
                                color: Colors.white,
                              ),
                              Text(
                                "Cancel",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          ),
                  )),
            }
          ],
        ),
        body: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
            child: Form(
              key: controller.formKey,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const SizedBox(
                      height: 10,
                    ),

                    Row(
                      children: [
                        Expanded(
                            flex: 1,
                            child: Text(
                                ('Deposit' == controller.cheque.chequeTxnType)
                                    ? "Received From: "
                                    : "Paid To: ",
                                style: const TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold))),
                        Expanded(
                            flex: 1,
                            child: Text(
                              "${controller.cheque.txnPartyName}",
                              style: const TextStyle(fontSize: 16),
                            )),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),

                    Row(
                      children: [
                        const Expanded(
                            flex: 1,
                            child: Text(
                              "Cheque No:",
                              style: TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.bold),
                            )),
                        Expanded(
                            flex: 1,
                            child: Text(
                                controller.cheque.txnPaymentReference ?? '',
                                style: const TextStyle(fontSize: 16))),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),

                    Row(
                      children: [
                        const Expanded(
                            flex: 1,
                            child: Text(
                              "Cheque Date:",
                              style: TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.bold),
                            )),
                        Expanded(
                            flex: 1,
                            child: Text(
                                controller.cheque.chequeIssueDateBS ?? '',
                                style: const TextStyle(fontSize: 16))),
                      ],
                    ),
                    const SizedBox(
                      height: 10,
                    ),

                    Row(
                      children: [
                        const Expanded(
                            flex: 1,
                            child: Text(
                              "Cheque Amount:",
                              style: TextStyle(
                                  fontSize: 16, fontWeight: FontWeight.bold),
                            )),
                        Expanded(
                            flex: 1,
                            child: Text("${controller.cheque.txnCashAmount}",
                                style: const TextStyle(fontSize: 16))),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),

                    const Divider(),

                    const SizedBox(
                      height: 20,
                    ),

                    //===================================Deposit To
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          ('Deposit' == controller.cheque.chequeTxnType)
                              ? "Deposit To"
                              : "Withdraw From",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderDropdown(
                          name: 'deposit_to',
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                            hintText: 'Please select bank account',
                          ),
                          items: controller.banks.map((row) {
                            return DropdownMenuItem(
                                value: row.pmtTypeId,
                                child: Text(row.pmtTypeShortName ?? ""));
                          }).toList(),
                          // readOnly: controller.readOnlyFlag,
                          initialValue: null,
                          onChanged: (value) {
                            print(value);
                            print(controller.banks
                                .where((element) => element.pmtTypeId == value)
                                .first
                                .pmtTypeCurrentBalance);
                            // print(controller.banks.where((element) => element.pmtTypeId == value).first.pmtTypeCurrentBalance! < controller.cheque.txnCashAmount!);
                            print(controller.cheque.txnCashAmount);

                            controller.cheque.chequeTransferredToAccId = value;
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Deposit' ==
                                      controller.cheque.chequeTxnType
                                  ? "Please Select Deposit To"
                                  : "Please Select Withdraw From";
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),

                    //===================================Deposit Date
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "मिति",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        CustomDatePickerTextField(
                          labelText: "Transfer Date",
                          readOnly: controller.readOnlyFlag,
                          initialValue: controller.cheque.chequeTransferDateBS,
                          maxBSDate: NepaliDateTime.now(),
                          onChange: (selectedDate) {
                            controller.cheque.chequeTransferDateBS =
                                selectedDate;

                            print(selectedDate);
                            print(controller.cheque.chequeTransferDate);
                          },
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),

                    if (controller.editFlag && controller.readOnlyFlag) ...{
                      const SizedBox(
                        height: 40,
                      ),
                      Center(
                        child: ElevatedButton(
                          // color: colorPrimary,
                          child: const Text(
                            "Re-Open",
                            style: TextStyle(color: Colors.white, fontSize: 16),
                          ),
                          onPressed: () {
                            showAlertDialog(context,
                                okText: "YES",
                                hasCancel: true,
                                cancelText: "NO",
                                alertType: AlertType.Error,
                                alertTitle: "Confirm Re-open cheque",
                                onCloseButtonPressed: () async {
                              // Navigator.of(_).pop();
                              ProgressDialog progressDialog = ProgressDialog(
                                  context,
                                  type: ProgressDialogType.normal,
                                  isDismissible: false);
                              progressDialog.update(
                                  message:
                                      "Checking Permission. Please wait....");
                              await progressDialog.show();

                              Tuple2<bool, String> checkResponse =
                                  await PermissionWrapperController()
                                      .requestForPermissionCheck(
                                          forPermission:
                                              PermissionManager.chequeReopen);

                              if (checkResponse.item1) {
                                //has  permission
                                progressDialog.update(
                                    message:
                                        "Re-opening cheque. Please wait....");

                                bool status = await ChequeListController()
                                    .reOpenCheque(controller.cheque);
                                await progressDialog.hide();

                                if (status) {
                                  showAlertDialog(context,
                                      barrierDismissible: false,
                                      alertType: AlertType.Success,
                                      alertTitle: "", onCloseButtonPressed: () {
                                    // Navigator.of(_).pop();
                                    // Navigator.of(context).pop();

                                    //reload cheque list
                                    TransactionHelper.refreshPreviousPages();
                                  }, message: "Cheque Re-opened Successfully.");
                                } else {
                                  showAlertDialog(context,
                                      alertType: AlertType.Error,
                                      alertTitle: "",
                                      message: "Failed.");
                                }
                              } else {
                                //no  permission
                                await progressDialog.hide();
                                showAlertDialog(context,
                                    alertType: AlertType.Error,
                                    alertTitle: "",
                                    message: checkResponse.item2);
                              }
                            },
                                message:
                                    "Are you sure you want to re-open this cheque?");
                          },
                        ),
                      )
                    }
                  ],
                ),
              ),
            ),
          ),
        ),

        //=================================================Save button
        bottomNavigationBar: BottomSaveCancelButton(
          shadow: false,
          enableFlag: !controller.readOnlyFlag,
          onSaveBtnPressedFn: () async {
            FocusScope.of(context).unfocus();
            if (controller.formKey.currentState!.validate()) {
              ProgressDialog progressDialog = ProgressDialog(context,
                  type: ProgressDialogType.normal, isDismissible: false);
              progressDialog.update(message: "Saving data. Please wait....");

              await progressDialog.show();

              bool status = false;
              String errorMsg = "Failed to process operation";
              print("controller.cheque.chequeTransferredToAccId");
              print(controller.cheque.chequeTransferredToAccId);
              print(controller.cheque.chequeTransferredToAccId == "1");

              if (controller.cheque.chequeTxnType == "Withdraw" &&
                  controller.cheque.chequeTransferredToAccId != "1" &&
                  controller.banks
                          .where((element) =>
                              element.pmtTypeId ==
                              controller.cheque.chequeTransferredToAccId)
                          .first
                          .pmtTypeCurrentBalance! <
                      controller.cheque.txnCashAmount!) {
                status = false;
                errorMsg =
                    "चयन गरिएको बैंक खातामा अपर्याप्त मौज्दात रकम। कृपया अर्को बैंक खाता चयन गर्नुहोस् वा भुक्तानी मोड परिवर्तन गर्नुहोस्। | \nInsufficient balance in selected bank account. Please select another bank account or change payment mode.";
              } else {
                try {
                  print(controller.cheque.chequeIssueDateBS!);
                  print(controller.cheque.chequeTransferDateBS!);
                  print(DateTime.parse(controller.cheque.chequeIssueDateBS!)
                      .isAfter(DateTime.parse(
                          controller.cheque.chequeTransferDateBS!)));

                  if (DateTime.parse(controller.cheque.chequeIssueDateBS!)
                      .isAfter(DateTime.parse(
                          controller.cheque.chequeTransferDateBS!))) {
                    status = false;
                    print("date mismatch");
                    errorMsg = "Cannot transfer cheque before issue date.";
                  } else {
                    status = await controller.closeCheque();
                  }
                } catch (e, trace) {
                  status = false;
                  errorMsg = "Failed to process operation";
                }
              }

              await progressDialog.hide();

              if (status) {
                // WidgetsBinding.instance.addPostFrameCallback((_) {
                Navigator.pop(context, true);
                // });

                TransactionHelper.refreshPreviousPages();

                showToastMessage(context,
                    message: "Cheque Transferred Successfully.", duration: 2);
              } else {
                showToastMessage(
                  context,
                  alertType: AlertType.Error,
                  message: errorMsg,
                  duration: 2,
                );
              }
            }
          },
        ),
      ));
    });
  }
}
