// ignore_for_file: use_build_context_synchronously

import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_image_picker/form_builder_image_picker.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/components/payment_mode_selector.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/payment_module/add_edit_payment_controller.dart';
import 'package:mobile_khaata_v2/app/modules/payment_module/detail_payment/detail_payment_screen.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';

import 'package:nepali_utils/nepali_utils.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

import '../bank_module/bank_account_list/bank_account_list_controller.dart';

// ignore: must_be_immutable
class AddEditPaymentPage extends StatelessWidget {
  final String tag = "AddEditPaymentPage";

  final String? ledgerId;
  final String? txnId;

  final addEditPaymentController = AddEditPaymentController();
  final bankController =
      Get.put(BankAccountListController(), tag: "BankAccountListController");

  AddEditPaymentPage({super.key, this.ledgerId, this.txnId}) {
    if (null != txnId) {
      addEditPaymentController.initEdit(txnId ?? "");
    } else {
      addEditPaymentController.initNew(ledgerId ?? "");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (addEditPaymentController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          backgroundColor: colorRedLight,
          leading: BackButton(
            onPressed: () => Navigator.pop(context),
          ),
          centerTitle: false,
          titleSpacing: -5.0,
          title: Text(
            (!addEditPaymentController.editFlag)
                ? "भुक्तानी (Pay) - Debit Party"
                : "भुक्तानी (Pay) - Debit Party",
            style: const TextStyle(
                fontSize: 20,
                color: Colors.white,
                fontFamily: 'HelveticaRegular',
                fontWeight: FontWeight.bold),
          ),
          // actions: [
          //   if (addEditPaymentController.editFlag) ...{
          //     InkWell(
          //         onTap: () => addEditPaymentController.readOnlyFlag =
          //             !addEditPaymentController.readOnlyFlag,
          //         splashColor: colorPrimaryLighter,
          //         child: Container(
          //           padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
          //           child: (addEditPaymentController.readOnlyFlag)
          //               ? Column(
          //                   mainAxisSize: MainAxisSize.min,
          //                   children: [
          //                     Icon(
          //                       Icons.mode_edit,
          //                       color: Colors.white,
          //                     ),
          //                     Text(
          //                       "Edit",
          //                       style: TextStyle(
          //                           color: Colors.white, fontSize: 10),
          //                     ),
          //                   ],
          //                 )
          //               : Column(
          //                   mainAxisSize: MainAxisSize.min,
          //                   children: [
          //                     Icon(
          //                       Icons.close,
          //                       color: Colors.white,
          //                     ),
          //                     Text(
          //                       "Cancel",
          //                       style: TextStyle(
          //                           color: Colors.white, fontSize: 10),
          //                     ),
          //                   ],
          //                 ),
          //         )),
          //   }
          // ],
        ),

        //===========================================================================Body Part
        body: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: SingleChildScrollView(
            child: Container(
              decoration: BoxDecoration(
                color: backgroundColorShade,
              ),
              child: Column(
                children: [
                  //===============================Party detail
                  Card(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      width: double.infinity,
                      child: Column(
                        children: [
                          SizedBox(
                            width: double.infinity,
                            child: Text(
                              "${addEditPaymentController.ledger.ledgerTitle}",
                              textAlign: TextAlign.left,
                              style: const TextStyle(
                                  fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                          ),
                          const Divider(
                            thickness: 0.5,
                          ),
                          if (null !=
                              addEditPaymentController.ledger.mobileNo) ...{
                            Row(
                              children: [
                                const Icon(
                                  Icons.call,
                                  size: 14,
                                  color: Colors.black54,
                                ),
                                Text(
                                  " ${addEditPaymentController.ledger.mobileNo}",
                                  textAlign: TextAlign.left,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                          },
                          if (null !=
                              addEditPaymentController.ledger.address) ...{
                            Row(
                              children: [
                                Text(
                                  "Address: ${addEditPaymentController.ledger.address}",
                                  textAlign: TextAlign.left,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                          },
                          if (null !=
                              addEditPaymentController.ledger.tinNo) ...{
                            Row(
                              children: [
                                Text(
                                  "PAN/VAT: ${addEditPaymentController.ledger.tinNo}",
                                  textAlign: TextAlign.left,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                          },
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              //=================================Balance Amount
                              Expanded(
                                child: _balanceAmountWidget(
                                    addEditPaymentController.ledger),
                              ),

                              //=======================================Profile Image Ledger
                              ClipRRect(
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(10)),
                                child: Container(
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                      color: colorPrimaryLightest),
                                  child: (null ==
                                          addEditPaymentController
                                              .image.imageBitmap)
                                      ? const Icon(
                                          Icons.person,
                                          color: Colors.white,
                                          size: 60,
                                        )
                                      : Image(
                                          image: MemoryImage(Uint8List.fromList(
                                              addEditPaymentController
                                                  .image.imageBitmap!)),
                                          fit: BoxFit.cover,
                                        ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  Card(
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      child: Form(
                        key: addEditPaymentController.formKey,
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                //===========================Txn No.
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "भौचर न. ",
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      FormBuilderTextField(
                                        name: "txn_no",
                                        readOnly: addEditPaymentController
                                            .readOnlyFlag,
                                        autocorrect: false,
                                        keyboardType: TextInputType.number,
                                        textInputAction: TextInputAction.done,
                                        textAlign: TextAlign.right,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Voucher No"),
                                        initialValue: addEditPaymentController
                                            .transaction.txnRefNumberChar,
                                        onChanged: (value) {
                                          addEditPaymentController.transaction
                                              .txnRefNumberChar = value;
                                        },
                                      ),
                                    ],
                                  ),
                                ),

                                const SizedBox(
                                  width: 20,
                                ),

                                //===========================Transaction Date
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "मिति",
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      CustomDatePickerTextField(
                                        readOnly: addEditPaymentController
                                            .readOnlyFlag,
                                        maxBSDate: NepaliDateTime.now(),
                                        initialValue: addEditPaymentController
                                            .transaction.txnDateBS,
                                        onChange: (selectedDate) {
                                          addEditPaymentController.transaction
                                              .txnDateBS = selectedDate;
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20.0),

                            Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "भुक्तानी रकम",
                                          style: labelStyle2,
                                        ),
                                        const SizedBox(height: 5.0),
                                        PaymentModeSelector(
                                            onChangedFn: (v) {
                                              print('Payment mode selctor');
                                              print(v);
                                              // if(v != "1" && v != '2'){
                                              //   print(bankController.bankList.where((element) => element.pmtTypeId == v).toList()[0].pmtTypeShortName);
                                              // }
                                              addEditPaymentController
                                                  .transaction
                                                  .txnPaymentTypeId = v;

                                              addEditPaymentController
                                                  .transaction
                                                  .txnPaymentReference = null;
                                              addEditPaymentController
                                                  .transaction
                                                  .chequeIssueDateBS = null;
                                              addEditPaymentController
                                                  .paymentRefCtrl
                                                  .clear();
                                              addEditPaymentController
                                                  .refreshTransaction();
                                            },
                                            paymentModeID:
                                                addEditPaymentController
                                                    .transaction
                                                    .txnPaymentTypeId,
                                            enableFlag:
                                                (addEditPaymentController
                                                    .readOnlyFlag))
                                      ],
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 10,
                                  ),
                                  Expanded(
                                    child: FormBuilderTextField(
                                        name: "paid_amt",
                                        autocorrect: false,
                                        readOnly: addEditPaymentController
                                            .readOnlyFlag,
                                        keyboardType: const TextInputType
                                            .numberWithOptions(decimal: true),
                                        textInputAction: TextInputAction.next,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Paid Amount"),
                                        textAlign: TextAlign.end,
                                        initialValue: addEditPaymentController
                                            .transaction.txnCashAmount
                                            ?.toString(),
                                        onChanged: (value) {
                                          addEditPaymentController
                                                  .transaction.txnCashAmount =
                                              parseDouble(value);
                                          addEditPaymentController
                                              .remainingBalanceAmount
                                              .text = (addEditPaymentController
                                                      .ledger.balanceAmount! +
                                                  (addEditPaymentController
                                                          .transaction
                                                          .txnCashAmount ??
                                                      0.00))
                                              .toStringAsFixed(2);
                                        }),
                                  )
                                ]),

                            ...(addEditPaymentController
                                        .transaction.txnPaymentTypeId ==
                                    PAYMENT_MODE_CHEQUE_ID)
                                ? [
                                    const SizedBox(
                                      height: 25,
                                    ),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text("चेक/भौचर न.", style: labelStyle2),
                                        const SizedBox(
                                          height: 10,
                                        ),
                                        TextField(
                                            autocorrect: false,
                                            readOnly: addEditPaymentController
                                                .readOnlyFlag,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                labelText:
                                                    "Cheque/Voucher No."),
                                            controller: addEditPaymentController
                                                .paymentRefCtrl,
                                            onChanged: (v) {
                                              addEditPaymentController
                                                  .transaction
                                                  .txnPaymentReference = v;
                                            }),
                                      ],
                                    ),
                                  ]
                                : [],
                            if (addEditPaymentController
                                    .transaction.txnPaymentTypeId ==
                                PAYMENT_MODE_CHEQUE_ID) ...[
                              const SizedBox(
                                height: 25,
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text("चेक मिति", style: labelStyle2),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  CustomDatePickerTextField(
                                    labelText: "Cheque Date",
                                    readOnly:
                                        addEditPaymentController.readOnlyFlag,
                                    // maxBSDate: NepaliDateTime.now(),
                                    initialValue: addEditPaymentController
                                        .transaction.chequeIssueDateBS,
                                    onChange: (selectedDate) {
                                      addEditPaymentController.transaction
                                          .chequeIssueDateBS = selectedDate;
                                    },
                                  ),
                                ],
                              )
                            ],

                            const SizedBox(height: 20.0),

                            // Column(
                            //   crossAxisAlignment: CrossAxisAlignment.start,
                            //   children: [
                            //     Text(
                            //       "बाँकी रकम",
                            //       style: labelStyle2,
                            //     ),
                            //     SizedBox(height: 5.0),
                            //     FormBuilderTextField(
                            //       attribute: "remaining_amt",
                            //       autocorrect: false,
                            //       keyboardType: TextInputType.numberWithOptions(decimal: true),
                            //       textInputAction: TextInputAction.done,
                            //       style: formFieldTextStyle,
                            //       decoration: formFieldStyle.copyWith(labelText: "Remaining Amount"),
                            //       textAlign: TextAlign.end,
                            //       readOnly: true,
                            //       controller: addEditPaymentController.remainingBalanceAmount,
                            //     ),
                            //   ],
                            // ),
                            // SizedBox(
                            //   height: 25,
                            // ),
                          ],
                        ),
                      ),
                    ),
                  ),

                  //===============================================Description
                  Card(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                            flex: 5,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "कैफियत",
                                  style: labelStyle2,
                                ),
                                const SizedBox(height: 5.0),
                                FormBuilderTextField(
                                  name: "description",
                                  readOnly:
                                      addEditPaymentController.readOnlyFlag,
                                  autocorrect: false,
                                  textAlign: TextAlign.start,
                                  textInputAction: TextInputAction.newline,
                                  style: formFieldTextStyle,
                                  decoration: formFieldStyle.copyWith(
                                      labelText: "Remarks"),
                                  minLines: 4,
                                  maxLines: 4,
                                  initialValue: addEditPaymentController
                                      .transaction.txnDescription,
                                  onChanged: (value) {
                                    addEditPaymentController
                                        .transaction.txnDescription = value;
                                  },
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            width: 20,
                          ),
                          Expanded(
                            flex: 1,
                            child: FormBuilderImagePicker(
                                name: "image_picker",
                                decoration: const InputDecoration(
                                  border: InputBorder.none,
                                ),
                                maxImages: 1,
                                iconColor: colorPrimaryLight,
                                initialValue: addEditPaymentController
                                            .txnImageFile ==
                                        null
                                    ? []
                                    : [addEditPaymentController.txnImageFile],
                                onChanged: (value) => addEditPaymentController
                                    .imagePickerOnChangeHandler(value)),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        //=================================================Save button
        bottomNavigationBar: BottomSaveCancelButton(
          shadow: false,
          hasDelete: (addEditPaymentController.editFlag &&
                  !addEditPaymentController.readOnlyFlag)
              ? true
              : false,
          onDeleteBtnPressedFn: () async {
            showAlertDialog(context,
                okText: "YES",
                hasCancel: true,
                cancelText: "NO",
                alertType: AlertType.Error,
                alertTitle: "Confirm Delete", onCloseButtonPressed: () async {
              // Navigator.of(_).pop();
              ProgressDialog progressDialog = ProgressDialog(context,
                  type: ProgressDialogType.normal, isDismissible: false);
              progressDialog.update(
                  message: "Checking Permission. Please wait....");
              await progressDialog.show();
              Tuple2<bool, String> checkResp =
                  await PermissionWrapperController().requestForPermissionCheck(
                      forPermission: PermissionManager.paymentDelete);
              if (checkResp.item1) {
                //has  permission
                progressDialog.update(
                    message: "Deleting Data. Please wait....");
                Tuple2<bool, String> deleteResp =
                    await TransactionRepository().delete(txnId ?? "");
                await progressDialog.hide();
                if (deleteResp.item1) {
                  //  data deleted
                  TransactionHelper.refreshPreviousPages();
                  showAlertDialog(context,
                      barrierDismissible: false,
                      alertType: AlertType.Success,
                      alertTitle: "", onCloseButtonPressed: () {
                    // Navigator.of(_).pop();
                    Navigator.of(context).pop();
                  }, message: deleteResp.item2);
                } else {
                  //cannot  delete  data
                  showAlertDialog(context,
                      alertType: AlertType.Error,
                      alertTitle: "",
                      message: deleteResp.item2);
                }
              } else {
                await progressDialog.hide();
                showAlertDialog(context,
                    alertType: AlertType.Error,
                    alertTitle: "",
                    message: checkResp.item2);
              }
            },
                message:
                    "Are you sure you  want to  delete this payment record?");
          },
          enableFlag: !addEditPaymentController.readOnlyFlag,
          onSaveBtnPressedFn: (addEditPaymentController.readOnlyFlag)
              ? null
              : () async {
                  FocusScope.of(context).unfocus();
                  if (addEditPaymentController.formKey.currentState!
                      .validate()) {
                    ProgressDialog progressDialog = ProgressDialog(context,
                        type: ProgressDialogType.normal, isDismissible: false);
                    progressDialog.update(
                        message: "Saving data. Please wait....");
                    await progressDialog.show();

                    bool status = false;
                    try {
                      if (null ==
                              addEditPaymentController.transaction.txnDateBS ||
                          addEditPaymentController
                              .transaction.txnDateBS!.isEmpty) {
                        throw CustomException(
                            "मिति खाली राख्न मिल्दैन\n(Fill Date.)");
                      }

                      if (null ==
                              addEditPaymentController
                                  .transaction.txnCashAmount ||
                          0 >=
                              addEditPaymentController
                                  .transaction.txnCashAmount!) {
                        throw CustomException(
                            "भुक्तानी रकम खाली वा जिरो राख्न मिल्दैन\n(Fill Paid Amount.)");
                      }

                      if (addEditPaymentController
                                  .transaction.txnPaymentTypeId ==
                              PAYMENT_MODE_CHEQUE_ID &&
                          (null ==
                                  addEditPaymentController
                                      .transaction.txnPaymentReference ||
                              "" ==
                                  addEditPaymentController
                                      .transaction.txnPaymentReference)) {
                        throw CustomException(
                            "चेक/भौचर न. खाली राख्न मिल्दैन | \nPlease fill the cheque/voucher no.");
                      }
                      if (addEditPaymentController
                                  .transaction.txnPaymentTypeId ==
                              PAYMENT_MODE_CHEQUE_ID &&
                          (null ==
                                  addEditPaymentController
                                      .transaction.chequeIssueDateBS ||
                              "" ==
                                  addEditPaymentController
                                      .transaction.chequeIssueDateBS)) {
                        throw CustomException(
                            "चेक मिति खाली राख्न मिल्दैन | \nPlease fill the cheque date");
                      }

                      //Eta haii etaa
                      if (addEditPaymentController
                                  .transaction.txnPaymentTypeId !=
                              PAYMENT_MODE_CASH_ID &&
                          addEditPaymentController
                                  .transaction.txnPaymentTypeId !=
                              PAYMENT_MODE_CHEQUE_ID) {
                        if (bankController.bankList
                                .where((element) =>
                                    element.pmtTypeId ==
                                    addEditPaymentController
                                        .transaction.txnPaymentTypeId)
                                .first
                                .pmtTypeCurrentBalance! <
                            addEditPaymentController
                                .transaction.txnCashAmount!) {
                          print(
                              "चयन गरिएको बैंक खातामा अपर्याप्त मौज्दात रकम। कृपया अर्को बैंक खाता चयन गर्नुहोस् वा भुक्तानी मोड परिवर्तन गर्नुहोस्। | \nInsufficient balance in selected bank account. Please select another bank account or change payment mode.");
                          throw CustomException(
                              "चयन गरिएको बैंक खातामा अपर्याप्त मौज्दात रकम। कृपया अर्को बैंक खाता चयन गर्नुहोस् वा भुक्तानी मोड परिवर्तन गर्नुहोस्। | \nInsufficient balance in selected bank account. Please select another bank account or change payment mode.");
                        }
                      }

                      bool isLargeFile =
                          addEditPaymentController.checkIfLargeImage();
                      if (isLargeFile) {
                        throw CustomException(MAX_IMAGE_SIZE_MESSAGE);
                      }

                      if (!addEditPaymentController.editFlag) {
                        status =
                            await addEditPaymentController.createTransaction();
                      } else {
                        status =
                            await addEditPaymentController.updateTransaction();
                      }
                    } on CustomException catch (e) {
                      await progressDialog.hide();
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "Error",
                          message: e.toString());
                      return;
                    } catch (e, trace) {
                      // Log.e(tag, e.toString() + trace.toString());
                    }
                    await progressDialog.hide();

                    if (status) {
                      Navigator.pop(context, true);
                      if (addEditPaymentController.editFlag) {
                        Navigator.of(context)
                            .pushReplacementNamed('/detailcreditPay',
                                arguments: DetailPaymentPage(
                                  txnId: txnId,
                                  ledgerId: ledgerId,
                                ));
                      }

                      TransactionHelper.refreshPreviousPages();

                      String message = (addEditPaymentController.editFlag)
                          ? "Payment updated successfully."
                          : "Payment created successfully.";
                      showToastMessage(context, message: message, duration: 2);
                    } else {
                      showToastMessage(context,
                          alertType: AlertType.Error,
                          message: "Failed to process operation",
                          duration: 2);
                    }
                  }
                },
        ),
      ));
    });
  }

  Widget _balanceAmountWidget(LedgerDetailModel selectedLedger) {
    bool isReceivable = (selectedLedger.balanceAmount! >= 0) ? true : false;

    return Row(
      children: [
        Transform.rotate(
          angle: (isReceivable) ? (3.14 / 1.3) : (-3.14 / 4),
          alignment: Alignment.center,
          child: CircleAvatar(
            radius: 10,
            backgroundColor: (isReceivable) ? colorGreenDark : colorRedLight,
            child: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
              size: 15,
            ),
          ),
        ),
        const SizedBox(
          width: 10,
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                (isReceivable)
                    ? "लिनुपर्ने (Receivable)"
                    : "तिर्नुपर्ने (Payable)",
                style: const TextStyle(color: Colors.black54, fontSize: 12),
              ),
              Text(
                formatCurrencyAmount(selectedLedger.balanceAmount!.abs()),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: (isReceivable) ? colorGreenDark : colorRedLight,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
