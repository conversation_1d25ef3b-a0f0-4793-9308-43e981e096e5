// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/model/database/reminder_model.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/modules/reminder_module/add_edit_reminder/add_edit_reminder_page.dart';
import 'package:mobile_khaata_v2/app/repository/reminder_repository.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/database/reminder_period.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:tuple/tuple.dart';

class ReminderListController extends GetxController {
  final _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  final _isSearching = true.obs;
  bool get isSearching => _isSearching.value;

  final List<ReminderModel> _reminders = [];
  List<ReminderModel> _filteredReminders = [];

  final RxList<ReminderModel> _upcomings = <ReminderModel>[].obs;
  final RxList<ReminderModel> _filteredUpcomings = <ReminderModel>[].obs;
  List<ReminderModel> get filteredUpcomings => _filteredUpcomings;

  List<ReminderModel> get upcomings => _filteredUpcomings;

  final List<ReminderModel> _completed = [];
  List<ReminderModel> _filteredCompleted = [];
  List<ReminderModel> get completed => _filteredCompleted;

  List<ReminderModel> get filteredReminders => _filteredReminders;

  final ReminderRepository _reminderRepository = ReminderRepository();

  final _filterUpcomigDate = 'week'.obs;

  String get filterUpcomigDate => _filterUpcomigDate.value;

  String filterDate =
      DateFormat('y-MM-dd').format(DateTime.now().add(const Duration(days: 7)));

  updateFilteredUpcoming(String val) {
    _filterUpcomigDate.value = val;
    _filterUpcomigDate.refresh();
    switch (val) {
      case 'week':
        filterDate = DateFormat('y-MM-dd')
            .format(DateTime.now().add(const Duration(days: 7)));
        break;

      case 'month':
        filterDate = DateFormat('y-MM-dd')
            .format(DateTime.now().add(const Duration(days: 30)));
        break;
      default:
        filterDate = "";
        break;
    }
    getUpcomings();
  }

  init() async {
    _isLoading(true);

    try {
      await updateUpcomingsList();

      // Fetch completed reminders
      _completed.clear();
      _filteredCompleted.clear();
      final completedData = await _reminderRepository.getCompletedReminders();
      _completed.assignAll(completedData);
      _filteredCompleted.assignAll(completedData);
    } catch (e) {
      print('Error initializing reminders: $e');
    } finally {
      _isLoading(false);
    }
  }

  getUpcomings() async {
    _isLoading(true);
    _upcomings.clear();
    _upcomings
        .addAll(await _reminderRepository.getUpcomingReminders(filterDate));
    _filteredUpcomings.clear();
    _filteredUpcomings.addAll(_upcomings);
    update();

    _isLoading(false);
  }

  searchReminder(String searchString) {
    _isLoading(true);
    _filteredReminders.clear();

    for (var reminder in _reminders) {
      _filteredReminders.addIf(
          reminder.description!
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          reminder);
    }

    _isLoading(false);
  }

  searchUpcoming(String searchString) {
    _isSearching(true);
    _filteredUpcomings.clear();

    for (var reminder in _upcomings) {
      _filteredUpcomings.addIf(
          reminder.description!
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          reminder);
    }

    _isSearching(false);
  }

  searchCompleted(String searchString) {
    _isSearching(true);
    _filteredCompleted.clear();

    for (var reminder in _completed) {
      _filteredCompleted.addIf(
          reminder.description!
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          reminder);
    }
    _isSearching(false);
  }

  reminderOnTap(
    BuildContext context,
    ReminderModel reminder,
  ) {
    showDialog(
      context: context,
      builder: (_) {
        return Dialog(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                constraints: const BoxConstraints(minHeight: 50),
                width: double.infinity,
                color: colorPrimary,
                padding: const EdgeInsets.all(10),
                child: Text(
                  "${reminder.description}",
                  style: const TextStyle(fontSize: 18, color: Colors.white),
                  maxLines: 3,
                  overflow: TextOverflow.fade,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 15),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // if ("completed" != reminder.reminderCategory) ...{
                    //   InkWell(
                    //     child: ListTile(
                    //       contentPadding: EdgeInsets.all(0),
                    //       visualDensity:
                    //           VisualDensity(horizontal: -4, vertical: -4),
                    //       leading: Icon(
                    //         Icons.check_box_sharp,
                    //         size: 20,
                    //         color: colorPrimary,
                    //       ),
                    //       title: Text(
                    //         " Mark as completed",
                    //         style: TextStyle(fontSize: 16),
                    //       ),
                    //     ),
                    //     onTap: () async {
                    //       Navigator.pop(context);
                    //       // bool status = await markAsComplete(reminder);
                    //       // if(status){
                    //       //   showToastMessage(context, message: "Reminder Completed Successfully.", duration: 2);
                    //       //   fetchAll();
                    //       // }
                    //       markAsComplete(reminder);
                    //     },
                    //   ),
                    //   Divider(),
                    // },
                    if ("pending" == reminder.reminderCategory &&
                        ReminderPeriod.once == reminder.reminderPeriod) ...{
                      InkWell(
                        child: ListTile(
                          contentPadding: const EdgeInsets.all(0),
                          visualDensity:
                              const VisualDensity(horizontal: -4, vertical: -4),
                          leading: Icon(
                            Icons.replay,
                            size: 20,
                            color: colorPrimary,
                          ),
                          title: const Text(
                            " Re-Remind",
                            style: TextStyle(fontSize: 16),
                          ),
                        ),
                        onTap: () async {
                          Navigator.pop(context);
                          Navigator.pushNamed(
                            context,
                            "/addEditReminder",
                            arguments: AddEditReminderPage(
                              reminderId: reminder.reminderId,
                              reaOnlyFlag: false,
                            ),
                          );
                        },
                      ),
                      const Divider(),
                    },
                    InkWell(
                      child: ListTile(
                        contentPadding: const EdgeInsets.all(0),
                        visualDensity:
                            const VisualDensity(horizontal: -4, vertical: -4),
                        leading: Icon(
                          Icons.edit,
                          size: 20,
                          color: colorPrimary,
                        ),
                        title: const Text(
                          " Edit",
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                      onTap: () async {
                        Navigator.pop(context); // Close dialog
                        final returnData = await Navigator.pushNamed(
                          context,
                          "/addEditReminder",
                          arguments: AddEditReminderPage(
                            reminderId: reminder.reminderId,
                          ),
                        );

                        if (returnData == true) {
                          showToastMessage(
                            context,
                            message: "Reminder Edited Successfully.",
                            duration: 2,
                          );

                          // Clear and update lists
                          _upcomings.clear();
                          _filteredUpcomings.clear();
                          await updateUpcomingsList();
                        }
                      },
                    ),
                    const Divider(),
                    InkWell(
                      child: ListTile(
                        contentPadding: const EdgeInsets.all(0),
                        visualDensity:
                            const VisualDensity(horizontal: -4, vertical: -4),
                        leading: Icon(
                          Icons.delete,
                          size: 20,
                          color: colorPrimary,
                        ),
                        title: const Text(
                          " Delete",
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                      onTap: () async {
                        Navigator.pop(context);

                        showAlertDialog(context,
                            okText: "YES",
                            hasCancel: true,
                            cancelText: "NO",
                            alertType: AlertType.Error,
                            alertTitle: "Confirm Delete",
                            onCloseButtonPressed: () async {
                          // Navigator.of(_).pop();
                          ProgressDialog progressDialog = ProgressDialog(
                              context,
                              type: ProgressDialogType.normal,
                              isDismissible: false);
                          progressDialog.update(
                              message: "Checking Permission. Please wait....");
                          await progressDialog.show();
                          Tuple2<bool, String> checkResp =
                              await PermissionWrapperController()
                                  .requestForPermissionCheck(
                                      forPermission:
                                          PermissionManager.reminderDelete);
                          if (checkResp.item1) {
                            //has  permission
                            progressDialog.update(
                              message: "Deleting Data. Please wait....",
                            );
                            Tuple2<bool, String> deleteResp =
                                await ReminderRepository().delete(
                              reminder.reminderId ?? "",
                            );

                            await progressDialog.hide();
                            if (deleteResp.item1) {
                              //  data deleted
                              // TransactionHelper.refreshPreviousPages();

                              showAlertDialog(
                                context,
                                barrierDismissible: false,
                                alertType: AlertType.Success,
                                alertTitle: "",
                                onCloseButtonPressed: () {
                                  // Navigator.of(_).pop();
                                  init();
                                  // Navigator.of(context).pop();
                                },
                                message: deleteResp.item2,
                              );
                            } else {
                              //cannot  delete  data
                              showAlertDialog(
                                context,
                                alertType: AlertType.Error,
                                alertTitle: "",
                                message: deleteResp.item2,
                              );
                            }
                          } else {
                            await progressDialog.hide();
                            showAlertDialog(
                              context,
                              alertType: AlertType.Error,
                              alertTitle: "",
                              message: checkResp.item2,
                            );
                          }
                        },
                            message:
                                "Are you sure you  want to  delete this Reminder?");

                        // bool status = await deleteReminder(reminder);
                        // if (status) {
                        //   showToastMessage(context,
                        //       message: "Reminder Deleted Successfully.",
                        //       duration: 2);
                        //   init();
                        // }
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<bool> deleteReminder(ReminderModel reminder) async {
    Tuple2<bool, String> deleteResp =
        await _reminderRepository.delete(reminder.reminderId ?? "");
    return deleteResp.item1;
  }

  void markAsComplete(ReminderModel reminder) {
    DateTime dateTime = DateTime.parse(reminder.startDatetime ?? "");

    if (ReminderPeriod.once == reminder.reminderPeriod) {
      // reminder.scheduledId = 1;
    } else if (ReminderPeriod.daily == reminder.reminderPeriod) {
      reminder.startDatetime = dateTime.add(const Duration(days: 1)).toString();
    } else if (ReminderPeriod.weekly == reminder.reminderPeriod) {
      reminder.startDatetime = dateTime.add(const Duration(days: 7)).toString();
    } else if (ReminderPeriod.monthly == reminder.reminderPeriod) {
      reminder.startDatetime = DateTime(dateTime.year, dateTime.month + 1,
              dateTime.day, dateTime.hour, dateTime.minute, dateTime.second)
          .toString();
    } else if (ReminderPeriod.yearly == reminder.reminderPeriod) {
      reminder.startDatetime = DateTime(dateTime.year + 1, dateTime.month,
              dateTime.day, dateTime.hour, dateTime.minute, dateTime.second)
          .toString();
    }

    _reminderRepository.update(reminder);
  }

  updateUpcomingsList() async {
    // Clear lists using clear()
    _upcomings.clear();
    _filteredUpcomings.clear();

    try {
      // Get fresh data
      final upcomingData =
          await _reminderRepository.getUpcomingReminders(filterDate);

      // Use assignAll instead of addAll for RxList
      _upcomings.assignAll(upcomingData);
      _filteredUpcomings.assignAll(upcomingData);
    } catch (e) {
      print('Error updating upcomings list: $e');
    }
  }
}
