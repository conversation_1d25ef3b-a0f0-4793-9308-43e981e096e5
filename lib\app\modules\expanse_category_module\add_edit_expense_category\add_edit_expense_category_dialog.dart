// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';

import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/model/database/expanse_category_model.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_category_module/add_edit_expense_category/add_edit_expense_category_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

Future<ExpenseCategoryModel?> displayAddEditExpenseCategoryDialog(
    BuildContext context,
    {String? categoryId}) async {
  ExpenseCategoryModel? returnedData = await showDialog(
      context: context,
      useRootNavigator: true,
      barrierDismissible: false,
      builder: (_) {
        return AlertDialog(
          insetPadding:
              const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          contentPadding: EdgeInsets.zero,
          clipBehavior: Clip.hardEdge,
          content: SizedBox(
            width: MediaQuery.of(context).size.width - 20,
            child: AddEditExpenseCategoryDialog(
              categoryId: categoryId ?? "",
            ),
          ),
        );
      });
  return returnedData;
}

class AddEditExpenseCategoryDialog extends StatelessWidget {
  final String tag = "AddEditExpenseCategoryDialog";
  final String? categoryId;

  final addEditExpenseCategoryController = AddEditExpenseCategoryController();

  AddEditExpenseCategoryDialog({super.key, this.categoryId}) {
    if (categoryId!.isEmpty) return;
    addEditExpenseCategoryController.initEdit(categoryId!);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
              padding: const EdgeInsets.only(left: 15, right: 15, top: 15),
              child: Container(
                padding: const EdgeInsets.only(bottom: 10),
                child: Text(
                  (addEditExpenseCategoryController.editFlag == true)
                      ? "खर्च शीर्षक (Edit Expense Category)${addEditExpenseCategoryController.editFlag == true}"
                      : "नयाँ खर्च शीर्षक (New Expense Category) ",
                  style: TextStyle(
                      color: textColor,
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                ),
              )),
          if (addEditExpenseCategoryController.isLoading) ...{
            Container(
                color: Colors.white,
                child: const Center(child: CircularProgressIndicator()))
          } else ...{
            GestureDetector(
              onTap: () {
                FocusScope.of(context).unfocus();
              },
              child: Form(
                key: addEditExpenseCategoryController.formKey,
                child: SingleChildScrollView(
                  child: Container(
                    margin: const EdgeInsets.only(top: 10),
                    padding: const EdgeInsets.only(
                      left: 15,
                      right: 15,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(height: 5.0),

                        //===============================================Item Name
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "शीर्षकको नाम",
                              style: labelStyle2,
                            ),
                            const SizedBox(height: 5.0),
                            FormBuilderTextField(
                              name: "title_name",
                              autocorrect: false,
                              controller: addEditExpenseCategoryController
                                  .newExpenseTitleController,
                              keyboardType: TextInputType.text,
                              textInputAction: TextInputAction.next,
                              style: formFieldTextStyle,
                              decoration: formFieldStyle.copyWith(
                                  labelText: "Expense Category Title"),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return "शीर्षकको नाम राख्नुहोस् (Fill Expense Title)";
                                }
                                return null;
                              },
                            ),
                          ],
                        ),

                        const SizedBox(height: 25.0),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          },
          BottomSaveCancelButton(
            shadow: false,
            onSaveBtnPressedFn: () async {
              FocusScope.of(context).unfocus();
              if (addEditExpenseCategoryController.formKey.currentState!
                  .validate()) {
                ProgressDialog progressDialog = ProgressDialog(context,
                    type: ProgressDialogType.normal, isDismissible: false);
                progressDialog.update(message: "Saving data. Please wait....");
                await progressDialog.show();

                bool status = false;
                try {
                  if (!addEditExpenseCategoryController.editFlag) {
                    status =
                        await addEditExpenseCategoryController.createCategory();
                  } else {
                    status =
                        await addEditExpenseCategoryController.updateCategory();
                  }
                } catch (e, trace) {
                  // Log.e(tag, e.toString() + trace.toString());
                }
                await progressDialog.hide();

                if (status) {
                  Navigator.pop(
                      context, addEditExpenseCategoryController.category);

                  TransactionHelper.refreshPreviousPages();

                  String message = (addEditExpenseCategoryController.editFlag)
                      ? "Expense Title Updated Successfully."
                      : "Expense Title Created Successfully.";
                  showToastMessage(context, message: message, duration: 2);
                } else {
                  showToastMessage(context,
                      alertType: AlertType.Error,
                      message: "Failed to process operation",
                      duration: 2);
                }
              }
            },
          ),
        ],
      );
    });
  }
}
