import 'package:flutter/material.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class PhoneContactRepository {
  Future<List> getContactByTitle(String title) async {
    List<dynamic> contactList = [];
    bool isPermitted = await _getContactPermission();
    if (isPermitted) {
      final contacts = await FlutterContacts.getContacts(
          withProperties: true, withThumbnail: false, withPhoto: false);
      // Filter contacts by title/name
      contactList = contacts.where((contact) {
        return contact.displayName.toLowerCase().contains(title.toLowerCase());
      }).toList();
      print("list ho $contactList");
    }
    return contactList;
  }

  //Check contacts permission
  Future<bool> _getContactPermission() async {
    // flutter_contacts has built-in permission handling
    return await FlutterContacts.requestPermission();
  }
}

class PhoneContactListController extends GetxController {
  var _isLoading = true.obs;
  final _contacts = [].obs;
  bool get isLoading => _isLoading.value;
  List get contacts => _contacts;

  final PhoneContactRepository _phoneContactRepository =
      PhoneContactRepository();

  searchByTitle(String title) async {
    _isLoading(true);
    _contacts.clear();
    _contacts.addAll(await _phoneContactRepository.getContactByTitle(title));
    _isLoading(false);
  }
}

// ignore: must_be_immutable
class PhoneContactTextInput extends StatelessWidget {
  bool enableFlag;
  String labelText;
  TextEditingController? controller;
  Function? onChangedFn;
  Function? onSuggestionSelectedFn;

  final _phoneContactTextInputController =
      Get.put(PhoneContactListController());

  PhoneContactTextInput(
      {super.key,
      this.enableFlag = true,
      this.labelText = "Select Person/Firm",
      this.controller,
      this.onChangedFn,
      this.onSuggestionSelectedFn});

  @override
  Widget build(BuildContext context) {
    return TypeAheadField(
      textFieldConfiguration: TextFieldConfiguration(
        enabled: enableFlag,
        controller: controller,
        decoration: formFieldStyle.copyWith(labelText: labelText),
        onChanged: (value) => onChangedFn!(value),
      ),
      suggestionsCallback: (pattern) async {
        await _phoneContactTextInputController
            .searchByTitle(pattern.toLowerCase());
        return _phoneContactTextInputController.contacts;
      },
      hideOnEmpty: true,
      transitionBuilder: (context, suggestionsBox, controller) {
        return suggestionsBox;
      },
      itemBuilder: (context, contact) {
        return Column(
          children: [
            ListTile(
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
              title: Text(
                "${contact.displayName}",
                style:
                    const TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
              ),
            ),
            const Divider(height: 0, thickness: 1),
          ],
        );
      },
      onSuggestionSelected: (contact) => onSuggestionSelectedFn!(contact),
    );
  }
}
