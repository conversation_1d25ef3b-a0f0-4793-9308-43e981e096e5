import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/single_transaction_print.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import 'package:nepali_utils/nepali_utils.dart';

import 'package:printing/printing.dart';

class SingleTransactionPrintPage extends StatelessWidget {
  final String? pageTitle;
  final List<AllTransactionModel>? transactions;
  final String? partyText;
  final String? txnTypeText;
  final String? endDate;
  final String? startDate;

  const SingleTransactionPrintPage(
      {super.key,
      this.pageTitle = "Print Preview",
      this.transactions,
      this.partyText = "All Party",
      this.txnTypeText = "All Transaction",
      this.endDate,
      this.startDate});

  @override
  Widget build(BuildContext context) {
    String startDate =
        NepaliDateTime.parse(toDateBS(DateTime.parse(this.startDate ?? "")))
            .format("y-MM-dd");
    String endDate =
        NepaliDateTime.parse(toDateBS(DateTime.parse(this.endDate ?? "")))
            .format("y-MM-dd");

    // Log.d("start and end $startDate $endDate");
    return SafeArea(
        child: Scaffold(
            // resizeToAvoidBottomPadding: true,
            resizeToAvoidBottomInset: true,
            appBar: AppBar(
              toolbarHeight: 60,
              backgroundColor: colorPrimary,
              elevation: 4,
              leading: BackButton(
                onPressed: () => Navigator.pop(context, false),
              ),
              centerTitle: false,
              titleSpacing: -5.0,
              title: Text(
                pageTitle ?? "",
                style: const TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontFamily: 'HelveticaRegular',
                    fontWeight: FontWeight.bold),
              ),
              actions: [
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10)),
                        backgroundColor: colorPrimary,
                        foregroundColor: colorPrimaryLightest,
                      ),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Column(
                        children: const [
                          Icon(
                            Icons.close,
                            color: Colors.white,
                          ),
                          Text(
                            "Cancel",
                            style: TextStyle(color: Colors.white, fontSize: 10),
                          ),
                        ],
                      )),
                ),
              ],
            ),
            body: PdfPreview(
                useActions: true,
                canChangePageFormat: false,
                initialPageFormat: defaultPdfPageFormat,
                // allowPrinting: false,
                // allowSharing: false,
                pdfFileName: "$pageTitle From $startDate To $endDate .pdf",
                // maxPageWidth: 700,
                // actions: actions,
                build: (format) {
                  return generateSingleTransactionReport(defaultPdfPageFormat,
                      transactions: transactions,
                      partyText: partyText ?? "",
                      billTitle: pageTitle ?? "",
                      txnTypeText: txnTypeText ?? "",
                      startDate: this.startDate,
                      endDate: this.endDate);
                })));
  }
}
