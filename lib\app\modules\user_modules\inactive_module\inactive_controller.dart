import 'package:mobile_khaata_v2/http/api_base_helper.dart';

import 'package:tuple/tuple.dart';
import 'package:get/get.dart';

class InactivePageController extends GetxController {
  final _isChecking = false.obs;
  bool get isChecking => _isChecking.value;

  Future<Tuple2<int, String>> recheckActiveFromNetwork() async {
    int? active = 0;
    String message = "";
    _isChecking(true);
    try {
      ApiBaseHelper apiBaseHelper = ApiBaseHelper();
      ApiResponse apiResponse = await apiBaseHelper
          .get(apiBaseHelper.RECHECK_ACTIVE, accessToken: true);

      // Log.d("resp:    " + apiResponse.data.toString());
      if (apiResponse.status) {
        // Log.d(apiResponse.data);
        active = int.tryParse(apiResponse.data['is_user_active'].toString());
        message = apiResponse.msg ?? "";
      } else {
        message = apiResponse.msg ?? "";
      }
    } catch (e) {
      // Log.d(e);
      message =
          "Cannot check active status at this moment. Please try again later";
    }
    _isChecking(false);

    return Tuple2(active!, message);
  }
}
