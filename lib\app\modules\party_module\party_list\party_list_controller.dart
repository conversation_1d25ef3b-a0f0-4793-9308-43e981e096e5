import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/add_party/add_edit_party_page.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';

class PartyListController extends GetxController {
  var _partyLoading = true.obs;
  bool get partyLoading => _partyLoading.value;

  var _ledgers = <LedgerDetailModel>[].obs;
  var _filteredLedgers = <LedgerDetailModel>[].obs;
  List<LedgerDetailModel> get filteredLedgers => _filteredLedgers;

  LedgerRepository _ledgerRepository = new LedgerRepository();

  Future<void> init() async {
    _partyLoading.value = true;
    _ledgers(await _ledgerRepository.getAllGeneralLedgersWithBalance());

    _filteredLedgers.clear();
    _filteredLedgers.addAll(_ledgers);

    _partyLoading.value = false;
  }

  searchLedger(String searchString) {
    _partyLoading.value = true;

    _filteredLedgers.clear();
    _ledgers.forEach((ledger) {
      _filteredLedgers.addIf(
          (ledger.ledgerTitle ?? "")
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          ledger);
    });

    _partyLoading.value = false;
  }

  addButtonOnPressedHandler(BuildContext context) async {
    Navigator.pushNamed(context, "/addPartyLedger");
  }

  editBtnOnPressedHandler(BuildContext context, String ledgerId) {
    Navigator.pushNamed(context, "/addPartyLedger",
        arguments: AddEditPartyPage(partyID: ledgerId));
  }
}
