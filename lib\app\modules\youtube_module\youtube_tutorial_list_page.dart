// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:mobile_khaata_v2/app/components/youtube_video_item.dart';
import 'package:mobile_khaata_v2/app/modules/youtube_module/youtube_tutorial_list_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import '../../model/others/youtube_turorial_model.dart';

class YoutubeTutorialPage extends StatefulWidget {
  const YoutubeTutorialPage({Key? key}) : super(key: key);

  @override
  _YoutubeTutorialPageState createState() => _YoutubeTutorialPageState();
}

class _YoutubeTutorialPageState extends State<YoutubeTutorialPage> {
  final youtubeTutorialListController =
      Get.put(YoutubeTutorialListController());

  @override
  void initState() {
    super.initState();
    youtubeTutorialListController.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        titleSpacing: -5.0,
        backgroundColor: colorPrimary,
        title: const Text(
          "How to use mobile खाता ?",
          style: TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
      ),
      body: FutureBuilder<List<YoutubeTutorialModal>>(
        future: youtubeTutorialListController.listAllTutorials(),
        builder: (BuildContext context,
            AsyncSnapshot<List<YoutubeTutorialModal>> snapshot) {
          if (snapshot.hasData) {
            return Container(
              child: youtubeTutorialListController.isError
                  ? Center(
                      child: Column(
                        children: [
                          Text(youtubeTutorialListController.errorMessage),
                          const SizedBox(
                            height: 20,
                          ),
                          ElevatedButton(
                              onPressed: () {
                                setState(() {
                                  youtubeTutorialListController.onInit();
                                });
                              },
                              child: const Text("Re-try"))
                        ],
                      ),
                    )
                  : ListView(
                      padding: const EdgeInsets.all(10),
                      children: [
                        ...youtubeTutorialListController.tutorials
                            .map((e) => YoutubeVideoItem(
                                  tutorialModal: e,
                                ))
                            .toList()
                      ],
                    ),
            );
          } else {
            return Center(
              child: CircularProgressIndicator(),
            );
          }
        },
      ),
      // body: GetBuilder<YoutubeTutorialListController>(
      //   builder: (_) {
      //     return Container(
      //       child: youtubeTutorialListController.isError || youtubeTutorialListController.tutorials.isEmpty
      //           ? Center(
      //               child: Column(
      //                 children: [
      //                   Text(youtubeTutorialListController.errorMessage),
      //                   const SizedBox(
      //                     height: 20,
      //                   ),
      //                   ElevatedButton(
      //                       onPressed: () {
      //                         setState(() {
      //                           youtubeTutorialListController.onInit();
      //                         });
      //                       },
      //                       child: const Text("Re-try"))
      //                 ],
      //               ),
      //             )
      //           : ListView(
      //               padding: const EdgeInsets.all(10),
      //               children: [
      //                 ...youtubeTutorialListController.tutorials
      //                     .map((e) => YoutubeVideoItem(
      //                           tutorialModal: e,
      //                         ))
      //                     .toList()
      //               ],
      //             ),
      //     );
      //   },
      // ),
    ));
  }
}
