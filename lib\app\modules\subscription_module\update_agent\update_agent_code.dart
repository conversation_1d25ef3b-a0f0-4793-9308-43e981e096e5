// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/modules/subscription_module/subscription_page/subscription_page_controller.dart';
import 'package:mobile_khaata_v2/app/modules/subscription_module/update_agent/update_agent_code_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import 'package:tuple/tuple.dart';

class UpdateAgentDialogContent extends StatefulWidget {
  final BuildContext? builderContext;

  const UpdateAgentDialogContent({
    super.key,
    required this.builderContext,
  });

  @override
  _UpdateAgentDialogContentState createState() =>
      _UpdateAgentDialogContentState();
}

class _UpdateAgentDialogContentState extends State<UpdateAgentDialogContent> {
  final updateAgentCodeController = UpdateAgentCodeController();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
        child: GestureDetector(
          onTap: () {
            FocusScope.of(widget.builderContext!).unfocus();
          },
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: <Widget>[
              Text(
                "Update Representative",
                style: labelStyle2.copyWith(
                  fontSize: 24,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 20.0,
              ),
              FormBuilder(
                key: updateAgentCodeController.formKey,
                child: Padding(
                    padding: EdgeInsets.only(
                        bottom: MediaQuery.of(widget.builderContext!)
                            .viewInsets
                            .bottom),
                    child: Column(mainAxisSize: MainAxisSize.min, children: [
                      FormBuilderTextField(
                        name: "agent_code",
                        readOnly: false,
                        autocorrect: false,
                        // autofocus: true,
                        keyboardType: TextInputType.text,
                        // textInputAction: TextInputAction.done,
                        style: formFieldTextStyle,
                        maxLength: 10,
                        controller: updateAgentCodeController.agentCodeCtrl,
                        decoration: formFieldStyle.copyWith(
                            counterText: "", labelText: "Representative Code"),

                        inputFormatters: [
                          FilteringTextInputFormatter.singleLineFormatter
                        ],
                        textAlign: TextAlign.end,
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                            // color: colorPrimary,
                            // elevation: 10,
                            // shape: RoundedRectangleBorder(
                            //   borderRadius: BorderRadius.circular(10.0),
                            // ),
                            // splashColor: colorPrimaryLightest,
                            onPressed: updateAgentCodeController.isSubmitting
                                ? null
                                : () async {
                                    if (updateAgentCodeController
                                        .formKey.currentState!
                                        .saveAndValidate()) {
                                      Tuple3<bool, String, dynamic>
                                          checkResponse =
                                          await updateAgentCodeController
                                              .verifyAgent();
                                      // Log.d("got  vverify response" +
                                      //     checkResponse.item1.toString() +
                                      //     checkResponse.item2);
                                      if (!checkResponse.item1) {
                                        showAlertDialog(context,
                                            alertType: AlertType.Error,
                                            alertTitle: "",
                                            message: checkResponse.item2);
                                      } else {
                                        dynamic data =
                                            checkResponse.item3 ?? {};

                                        showAlertDialog(context,
                                            alertType: AlertType.Normal,
                                            alertTitle:
                                                "Confirm Representative",
                                            hasCancel: true,
                                            cancelText: "Cancel",
                                            okText: "Update",
                                            onCloseButtonPressed: () async {
                                          // Navigator.of(_).pop();
                                          Tuple2<bool, String>
                                              checkApplyresponse =
                                              await updateAgentCodeController
                                                  .updateAgent();

                                          if (!checkApplyresponse.item1) {
                                            showAlertDialog(context,
                                                alertType: AlertType.Error,
                                                alertTitle: "",
                                                message:
                                                    checkApplyresponse.item2);
                                          } else {
                                            await SubscriptionPageController()
                                                .recheckExpiryFromNetwork();

                                            showAlertDialog(context,
                                                alertType: AlertType.Success,
                                                alertTitle:
                                                    "Representative Updated",
                                                message:
                                                    checkApplyresponse.item2,
                                                onCloseButtonPressed: () {
                                              // Navigator.of(_).pop();
                                              Navigator.of(
                                                      widget.builderContext!)
                                                  .pop(true);
                                            });
                                          }
                                        },
                                            message:
                                                "Name: ${data['name'] ?? ""} \nMobile No: ${data['mobile_no'] ?? ""}");
                                      }
                                    }
                                  },
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 12, horizontal: 40),
                              child: Text(
                                updateAgentCodeController.isSubmitting
                                    ? "Submitting.."
                                    : "Submit",
                                style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 15,
                                    fontWeight: FontWeight.bold),
                              ),
                            )),
                      ),
                    ])),
              ),
              const SizedBox(height: 10),
            ],
          ),
        ),
      );
    });
  }
}
