class LastActivityType {
  static final int New = 1;
  static final int Edit = 2;
  static final int Delete = 3;

  static int getLastActivity(int syncFlag, int previousActivity) {
    if (1 == syncFlag) {
      return LastActivityType.Edit;
    } else {
      if (LastActivityType.Edit == previousActivity) {
        return LastActivityType.Edit;
      } else {
        return LastActivityType.New;
      }
    }
  }
}
