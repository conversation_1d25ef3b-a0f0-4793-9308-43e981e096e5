library constants;

import 'package:pdf/pdf.dart';

const App_Debug = true;

//==============================================ONE SIGNAL
const ONE_SIGNAL_APP_ID = "2f9118ed-e88a-477d-8c59-fb056264ecc0";

//==============================================Payment Gateway
const ESEWA_APP_NAME = "MOBILE KHAATA";
const KHALTI_APP_NAME = "MOBILE KHAATA";

const ESEWA_MERCHANT_KEY =
    'MxYBFg8HCkUtABANGQ4fBAIQAABFJxcHRUU1ERdfOTFeLjZUMyc1Ow==';
const ESEWA_MERCHANT_SECRET = 'WCUx';
//==============================================End Payment Gateway

const MobileSettings = "mobilekhaata_settings.json";
const FreshInstallStatus = "isFreshInstall"; //For checking fresh install

//=================================================Shared Preference keys
const LoginStatus = "isLoggedIn";
const AccessToken = "accessToken";
const UserNameKey = 'userNameKey';
const FullNameKey = 'fullNameKey';
const ExpiredKey = "isExpired";
const UnauthorizedKey = "isUnauthorized";
const SubDetailKey = 'subDetailKey';
const Permissions = 'permissions';
const ActiveKey = "isActive";
const MultiUserKey = "isMultiUser";
const WebEnabledKey = "isWebEnabled";
const CurrentDatabase = "currentDB";
const databaseSuffix = "-mobilekhaata.db";
const Companies = "companies";

// "dashboard-widgets",
// "purchase-add",
// "purchase-edit",
// "item-adjustment-delete"
// "purchase-delete",
// "item-adjustment-add",
// "item-adjustment-edit",
// "item-adjustment-delete"

//==========================================================API Configs
const ApiBaseUrl = "https://mkapi.mobilekhaata.com/api/";
// const ApiBaseUrl = "https://digitalrecord-api.hamroaccount.com/api/";
// const ApiBaseUrl = "https://mkapi.firajshankhadev.com.np/api/";
const ApiKeySecret = "mSFeDMbk4DqrZ0S9p8wdMY9D8qA6rFQW2hGs";
const ApiKeyHeader = "mobilekhaata-app-key";
const ESEWA_CALLBACK_URL =
    "https://mkapi.mobilekhaata.com/payment/esewa-confirmation";

//========================================================
const CASH_SALES_LEDGER_ID = "S-1";
const CASH_SALES_LEDGER_NAME = "Cash Sales";
const CASH_PURCHASE_LEDGER_ID = "S-2";
const CASH_PURCHASE_LEDGER_NAME = "Cash Purchase";
const CASH_EXPENSE_LEDGER_ID = "S-3";
const CASH_EXPENSE_LEDGER_NAME = "Cash Expense";

const APP_NAME = "MobileKhata";
const VAT_PERCENTAGE = 13.00;

const MAX_IMAGE_SIZE = 307200;
// const MAX_IMAGE_SIZE = 82000;
const MAX_IMAGE_SIZE_MESSAGE =
    "फाईल आकार 80 Kb भन्दा बढि हुन सक्दैन। कृपया ठूला फाईलहरू हटाउनुहोस् ।\n(File Size cann't be greater than 80 Kb. Please remove large file(s))";

const PULL_TASK_ID = "com.transistorsoft.pullPendingQueries";

const PUSH_TASK_ID = "com.transistorsoft.pushPendingQueries";

const CHECK_REMINDER_TASK_ID = "com.transistorsoft.checkReminder";

const SHARE_APP_MSG =
    'Namaste,\n"Byabasaya ra sambhanda dubai ma sudhaar aauchha dhukka hunuhos".\nMobileKhaata, Nepal\'s first business App.\n\nhttps://play.google.com/store/apps/details?id=com.techvedanta.mobile_khaata';

//SMS Message
const DUE_RECEIVE_MSG =
    "Namaste,\nHajurko bhuktani huna baki Rs.{Amount} raheko chha.\nSamaya mai bhuktani gardinuhola.\n--{shopName}.\n--MobileKhaata";

const DUE_PAY_MSG =
    "Namaste,\nHajurle bhuktani lina baki Rs.{Amount} raheko chha.\nBhuktani linako laagi sampark garnuhola.\n--{shopName}.\n--MobileKhaata";

const APP_STORE_ID = 'com.techvedanta.mobile_khaata';

const PLAY_STORE_ID = 'com.techvedanta.mobile_khaata';

const RATE_APP_TITLE = "Rate mobile खाता";

const RATE_APP_DESCRIPTION =
    "You like this app ? Then take a little bit of your time to leave a rating";
const SUBSCRIPTION_EXPIRY_MESSAGE =
    "तपाईंको सेवाको समयावधि सकिएको छ । सेवा पुनः सुचारु गर्नका लागि नवीकरण गर्नुहोस् ।\n सेवा अवरुद्ध भएकोमा क्षमाप्रार्थी छौ ।";
const SUBSCRIPTION_EXPIRY_MESSAGE_EN =
    "Your service has been expired. Please renew to continue your service.\nSorry for the inconvenience.\nFor more detail contact us at www.mobilekhaata.com";

const APP_VERSION = "1.0";

const FAILED_OPERATION_ERROR = "Cannot perform this operation at this moment";

const PAYMENT_MODE_CASH_ID = "1";
const PAYMENT_MODE_CHEQUE_ID = "2";

// const FOOTER_PRINT_TEXT = 'Generated by: MobileKhaata';
const FOOTER_PRINT_TEXT = '';

PdfPageFormat defaultPdfPageFormat = PdfPageFormat.a4.copyWith(
  marginLeft: 0.5 * PdfPageFormat.inch,
  marginRight: 0.5 * PdfPageFormat.inch,
  marginTop: 1 * PdfPageFormat.inch,
  marginBottom: 0.5 * PdfPageFormat.inch,
);

const ABOUT_US_LINK = 'https://mobilekhaata.com/about-us/';
