import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/txn_type_filter_dialog.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/modules/transaction_list_module/all_transaction_list_controller.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';

class AllTransactionListPage extends StatelessWidget {
  final String tag = "AllTransaactionListPage";

  final allTransactionListController = Get.put(AllTransactionListController(),
      tag: "AllTransactionListController");

  AllTransactionListPage() {
    allTransactionListController.init();
  }

  searchBoxOnChangeHandler(String searchString) {
    allTransactionListController.searchTransaction(searchString);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (allTransactionListController.txnLoading) {
        return Container(
            color: Colors.white,
            child: Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
        // resizeToAvoidBottomPadding: false,
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          backgroundColor: colorPrimary,
          leading: BackButton(
            onPressed: () => Navigator.pop(context, false),
          ),
          centerTitle: false,
          titleSpacing: -10.0,
          title: ListTile(
            contentPadding: EdgeInsets.only(right: 15),
            title: Text(
              "सबै लेनदेन\n(All Transactions)",
              style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontFamily: 'HelveticaRegular',
                  fontWeight: FontWeight.bold),
            ),
          ),
        ),
        body: allTransactionListController.transactions.length == 0
            ? Center(
                child: Text(
                "No Records",
                style: TextStyle(color: Colors.black54),
              ))
            : Column(children: [
                Container(
                    width: MediaQuery.of(context).size.width,
                    padding: EdgeInsets.only(left: 10, top: 10, bottom: 5),
                    color: Colors.white,
                    height: 55,
                    child: Row(
                      children: [
                        Expanded(
                          child: FormBuilderTextField(
                            name: "searchBox",
                            autocorrect: false,
                            keyboardType: TextInputType.text,
                            textInputAction: TextInputAction.done,

                            style: TextStyle(
                              // color: Colors.white,
                              fontSize: 18,
                            ),
                            // controller: searchController,
                            decoration: formFieldStyle.copyWith(
                                // labelText: "Search For",
                                hintText: "Search For",
                                prefixIcon: Icon(Icons.search),
                                alignLabelWithHint: true),
                            onChanged: (searchString) =>
                                searchBoxOnChangeHandler(searchString ?? ""),
                          ),
                        ),
                        // SizedBox(
                        //   width: 10,
                        // ),
                        Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: () async {
                              var returned = await showModalBottomSheet(
                                  context: context,
                                  builder: (_) {
                                    return TxnTypeFilterDialog(
                                      initialSelectedTypes:
                                          allTransactionListController
                                              .selectedTypes,
                                      onChange: (List<int> types) {
                                        // onChangeSelection(types);
                                        Navigator.of(context).pop(types);
                                      },
                                    );
                                  });
                              if (null != returned) {
                                allTransactionListController.selectedTypes =
                                    returned.cast<int>();
                                allTransactionListController.getTransactionsFor(
                                    types: returned.cast<int>());
                              }
                              // Log.d("selected returnedd $returned");
                            },
                            child: Container(
                              padding: EdgeInsets.only(left: 10, right: 10),
                              height: double.infinity,
                              // color: Colors.red,
                              child: Icon(
                                Icons.filter_alt,
                                color: Colors.black54,
                              ),
                            ),
                          ),
                        )
                      ],
                    )),
                Expanded(child: _TxnListView(allTransactionListController)),
              ]),
        // floatingActionButton: Container(
        // width: 45,
        // child: FloatingActionButton(
        //   onPressed: () =>
        //       Navigator.pushNamed(context, "/cashInHandAdjustment"),
        //   backgroundColor: colorPrimary,
        //   elevation: 6,
        //   child: Icon(
        //     Icons.add,
        //     size: 25,
        //     color: Colors.white,
        //   ),
        // ),
        // )
      ));
    });
  }
}

class _TxnListView extends StatelessWidget {
  final AllTransactionListController allTransactionListController;

  _TxnListView(this.allTransactionListController);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (allTransactionListController.txnLoading) {
        return Container(
            color: Colors.white,
            child: Center(child: CircularProgressIndicator()));
      }
      if (allTransactionListController.filteredTransactions.isEmpty) {
        return Container(
            width: double.infinity,
            child: Center(
                child: Text(
              "No Records",
              style: TextStyle(color: Colors.black54),
            )));
      } else {
        return ListView.builder(
          itemCount: allTransactionListController.filteredTransactions.length,
          // shrinkWrap: true,
          padding: EdgeInsets.only(bottom: 80),
          itemBuilder: (context, int index) {
            AllTransactionModel txn =
                allTransactionListController.filteredTransactions[index];

            // Log.d(txn.txnDisplayName);
            // Log.d(txn.ledgerTitle);

            return InkWell(
              onTap: () {
                TransactionHelper.gotoTransactionEditPage(
                    context, txn.txnId ?? "", txn.txnType ?? 0);
              },
              child: Column(
                children: [
                  if (0 == index) ...{
                    SizedBox(
                      height: 5,
                    )
                  },

                  DefaultTextStyle(
                    style: TextStyle(
                      fontSize: 14,
                      color: colorPrimary,
                    ),
                    child: Container(
                      padding:
                          EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          //============1st Column
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  // "${txn.ledgerTitle ?? txn.txnDisplayName}",
                                  txn.ledgerTitle == null ||
                                          txn.ledgerTitle!.isEmpty
                                      ? "${txn.txnDisplayName ?? ""}"
                                      // :"NO DISPLAY NAME"
                                      : txn.ledgerTitle == 'Cash Sale' &&
                                              txn.txnTypeText == "Sales Return"
                                          ? "Cash Sales Return"
                                          : txn.ledgerTitle ==
                                                      'Cash Purchase' &&
                                                  txn.txnTypeText ==
                                                      "Purchase Return"
                                              ? 'Cash Purchase Return'
                                              : '${txn.ledgerTitle}',
                                  style: TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold),
                                ),
                                SizedBox(
                                  height: 8,
                                ),
                                Container(
                                  padding: EdgeInsets.all(4),
                                  color: getTxnColor(txn.txnType ?? 0),
                                  child: Text(
                                    "${txn.txnTypeText}",
                                    style: TextStyle(
                                        fontSize: 12, color: Colors.white),
                                  ),
                                ),
                                SizedBox(
                                  height: 8,
                                ),
                                // FIXED: Use pre-calculated txnTotalAmount instead of manual calculation
                                // OLD CODE (INCORRECT): "Total: " + "${txn.txnCashAmount! + txn.txnBalanceAmount!}"
                                // PROBLEM: This was manually adding cash and balance amounts which could have rounding errors
                                // SOLUTION: Use the properly calculated txnTotalAmount from the model
                                Text(
                                  "Total: ${txn.txnTotalAmount?.toStringAsFixed(2) ?? '0.00'}",
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight
                                        .w600, // Make total amount more prominent
                                  ),
                                ),

                                // OPTIONAL: Show breakdown for transparency (uncomment if needed)
                                /*
                                if (txn.txnCashAmount! > 0 && txn.txnBalanceAmount! > 0) ...{
                                  SizedBox(height: 4),
                                  Text(
                                    "Cash: ${txn.txnCashAmount?.toStringAsFixed(2)} | Credit: ${txn.txnBalanceAmount?.toStringAsFixed(2)}",
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                }
                                */
                              ],
                            ),
                          ),

                          SizedBox(
                            width: 20,
                          ),

                          //============2nd Column
                          Column(
                            children: [
                              if (null != txn.txnRefNumberChar)
                                Container(
                                    margin: EdgeInsets.only(bottom: 5),
                                    child: Text(
                                      "Sale: #${txn.txnRefNumberChar}",
                                      textAlign: TextAlign.left,
                                    )),
                              Text(
                                "${txn.txnDateBS}",
                              ),
                            ],
                          ),
                          PrintButton(
                            onPressed: () {
                              TransactionHelper.goToPrintPage(
                                  context, txn.txnId ?? "", txn.txnType ?? 0);
                            },
                          )
                        ],
                      ),
                    ),
                  ),
                  Divider(
                    height: 0,
                  ),

                  //Add space if last element
                  // if (allTransactionListController.filteredTransactions.length -
                  //         1 ==
                  //     index) ...{
                  //   SizedBox(
                  //     height: 100,
                  //   )
                  // },
                ],
              ),
            );
          },
        );
      }
    });
  }

  Color getTxnColor(int txnType) {
    Color amtTextColor = colorOrangeDark;

    if (txnType == TxnType.addCash)
      amtTextColor = colorGreen;
    else if (txnType == TxnType.sales)
      amtTextColor = colorGreen;
    else if (txnType == TxnType.purchaseReturn)
      amtTextColor = colorGreen;
    else if (txnType == TxnType.paymentIn) amtTextColor = colorGreen;

    return amtTextColor;
  }
}
