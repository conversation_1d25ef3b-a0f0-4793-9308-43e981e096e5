// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/model/others/bank_transaction_model.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/add_edit_bank_account/add_edit_bank_account_page.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_account_detail/bank_account_detail_controller.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_adjustment/add_edit_bank_adjustment_page.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_deposit/add_edit_bank_desposit_page.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_to_bank_transfer/add_edit_bank_to_bank_transfer_page.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_withdrawl/add_edit_bank_withdrawl_page.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';

import 'package:sticky_headers/sticky_headers/widget.dart';
import 'package:tuple/tuple.dart';

class BankAccountDetailPage extends StatelessWidget {
  final String tag = "DetailBankAccountPage";

  final String? bankId;
  final controller = Get.put(BankAccountDetailController(),
      tag: "BankAccountDetailController");

  BankAccountDetailPage({super.key, this.bankId}) {
    controller.init(bankId ?? "");
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      } else {
        return SafeArea(
            child: Scaffold(
          // resizeToAvoidBottomPadding: true,
          resizeToAvoidBottomInset: true,
          appBar: AppBar(
            toolbarHeight: 60,
            elevation: 4,
            leading: BackButton(
              onPressed: () => Navigator.pop(context, false),
            ),
            centerTitle: false,
            titleSpacing: -10.0,
            title: const ListTile(
              contentPadding: EdgeInsets.only(right: 15),
              title: Text(
                "बैंक खाता ( Bank Account Detail)",
                style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                    fontFamily: 'HelveticaRegular',
                    fontWeight: FontWeight.bold),
              ),
            ),
            actions: [
              Container(
                padding: const EdgeInsets.all(6.0),
                child: InkWell(
                  child: const Icon(
                    Icons.edit,
                    size: 25,
                  ),
                  onTap: () async {
                    Tuple2<bool, String> checkResp = await checkPermission(
                        context: context,
                        forPermission: PermissionManager.bankEdit);
                    if (checkResp.item1) {
                      Navigator.pushNamed(context, "/addEditBank",
                          arguments: AddEditBankAccountPage(
                            bankId: controller.bank!.pmtTypeId,
                          ));
                    } else {
                      //no  permission
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "",
                          message: checkResp.item2);
                    }
                  },
                ),
              ),
            ],
          ),
          body:
              ListView(padding: const EdgeInsets.only(bottom: 100), children: [
            Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                decoration: BoxDecoration(
                  color: colorPrimaryLight,
                ),
                child: DefaultTextStyle(
                    style: const TextStyle(color: Colors.white, fontSize: 14),
                    child: Column(
                      children: [
                        Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text("A/C Name",
                                          style: TextStyle(fontSize: 12)),
                                      Text(controller
                                              .bank?.pmtTypeBankAccountName ??
                                          ""),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text("Opening Bal.",
                                          style: TextStyle(fontSize: 12)),
                                      Text(formatCurrencyAmount(controller
                                          .bank!.pmtTypeOpeningBalance!)),
                                    ],
                                  ),
                                ],
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      const Text("A/C No",
                                          style: TextStyle(fontSize: 12)),
                                      Text(controller
                                              .bank!.pmtTypeBankAccountNumber ??
                                          ""),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 15,
                                  ),
                                  Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      const Text("Short Name",
                                          style: TextStyle(fontSize: 12)),
                                      Text(controller.bank!.pmtTypeShortName ??
                                          ""),
                                    ],
                                  ),
                                ],
                              ),
                            ]),
                        const Divider(
                          color: Colors.white,
                        ),
                        Text(
                            "Current Balance: ${formatCurrencyAmount(controller.bank!.pmtTypeCurrentBalance!)}"),
                      ],
                    ))),
            Container(
              height: 2,
              color: Colors.black26,
            ),
            Container(
              width: double.infinity,
              decoration: const BoxDecoration(color: Colors.white),
              padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
              child: _BankTxnListView(controller),
            ),
          ]),
          floatingActionButton: SpeedDial(
            elevation: 8,
            backgroundColor: colorPrimary,
            overlayColor: colorPrimaryLightest,
            children: [
              SpeedDialChild(
                  child: const Icon(
                    Icons.add,
                    size: 24,
                  ),
                  label: "Cash To Bank Transfer",
                  backgroundColor: colorGreen,
                  onTap: () => Navigator.pushNamed(context, "/bankDeposit",
                      arguments: AddEditBankDepositPage(
                        bankID: bankId,
                      ))),
              SpeedDialChild(
                  child: const Icon(
                    Icons.add,
                    size: 24,
                  ),
                  label: "Bank To Cash Transfer",
                  backgroundColor: colorRedDark,
                  onTap: () => Navigator.pushNamed(context, "/bankWithdrawal",
                      arguments: AddEditBankWithdrawalPage(bankID: bankId))),
              SpeedDialChild(
                  child: const Icon(
                    FontAwesomeIcons.rightLeft,
                    size: 24,
                  ),
                  label: "Bank to Bank Transfer",
                  backgroundColor: colorOrangeLight,
                  onTap: () =>
                      Navigator.pushNamed(context, "/bankToBankTransfer",
                          arguments: AddEditBankToBankTransferPage(
                            bankID: bankId,
                          ))),
              SpeedDialChild(
                  child: const Icon(
                    Icons.tune,
                    size: 24,
                  ),
                  label: "Adjust Bank Balance",
                  backgroundColor: colorOrangeLight,
                  onTap: () => Navigator.pushNamed(context, "/bankAdjustment",
                      arguments: AddEditBankAdjustmentPage(bankID: bankId))),
            ].reversed.toList(),
            child: const Icon(
              Icons.tune,
              size: 30,
            ),
          ),
        ));
      }
    });
  }
}

class _BankTxnListView extends StatelessWidget {
  final BankAccountDetailController controller;

  const _BankTxnListView(this.controller);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }
      if (controller.filteredTransactions.isEmpty) {
        return const SizedBox(
            width: double.infinity,
            child: Center(
                child: Text(
              "No Records",
              style: TextStyle(color: Colors.black54),
            )));
      } else {
        return StickyHeader(
            header: Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.only(
                  left: 10, right: 10, top: 10, bottom: 5),
              color: Colors.white,
              height: 55,
              child: FormBuilderTextField(
                name: "searchBox",
                autocorrect: false,
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.done,

                style: const TextStyle(
                  // color: Colors.white,
                  fontSize: 18,
                ),
                // controller: searchController,
                decoration: formFieldStyle.copyWith(
                    hintText: "Search For",
                    prefixIcon: const Icon(Icons.search),
                    alignLabelWithHint: true),
                onChanged: (searchString) =>
                    controller.searchTransaction(searchString ?? ""),
              ),
            ),
            content: Column(children: [
              if (controller.filteredTransactions.isEmpty)
                Container(
                    padding: const EdgeInsets.all(40),
                    width: double.infinity,
                    child: const Center(
                        child: Text(
                      "No Records for given query",
                      style: TextStyle(color: Colors.black54),
                    ))),
              ...controller.filteredTransactions
                  .map((BankTransactionModel txn) {
                return InkWell(
                  onTap: () => TransactionHelper.gotoTransactionEditPage(
                      context,
                      txn.txnId!,
                      ('cheque' == txn.txnMode)
                          ? TxnType.chequeTransfer
                          : txn.txnType!),
                  child: Column(
                    children: [
                      DefaultTextStyle(
                        style: TextStyle(
                          fontSize: 14,
                          color: colorPrimary,
                        ),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 10, horizontal: 15),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              //====================================1st Column
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "${txn.txnTypeText}",
                                      style: const TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.bold),
                                    ),
                                    const SizedBox(
                                      height: 5,
                                    ),
                                    Text(
                                      "${txn.txnDateBS}",
                                      style: const TextStyle(
                                        fontSize: 12,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const SizedBox(
                                width: 6,
                              ),

                              //============2nd Column
                              Expanded(
                                flex: 1,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      txn.bankTransactionDesc ?? '',
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 2,
                                      style: const TextStyle(),
                                    ),
                                  ],
                                ),
                              ),

                              //====================================3rd Column
                              Expanded(
                                flex: 2,
                                child: Text(
                                  formatCurrencyAmount(txn.txnAmount ?? 0),
                                  textAlign: TextAlign.right,
                                  style: TextStyle(
                                      color: getTxnColor(
                                          txn.bankTransactionType ?? 0)),
                                ),
                              ),
                              // PrintButton(
                              //   onPressed: () {
                              //     TransactionHelper.goToPrintPage(
                              //         context, txn.txnId, txn.txnType);
                              //   },
                              // )
                            ],
                          ),
                        ),
                      ),
                      const Divider(
                        height: 0,
                      ),
                    ],
                  ),
                );
              }).toList(),
            ]));
      }
    });
  }

  Color getTxnColor(int txnType) {
    Color amtTextColor = textColor;

    if (TxnType.increaseBankBalance == txnType) {
      amtTextColor = colorGreen;
    } else if (TxnType.decreaseBankBalance == txnType) {
      amtTextColor = colorOrangeDark;
    }

    return amtTextColor;
  }
}
