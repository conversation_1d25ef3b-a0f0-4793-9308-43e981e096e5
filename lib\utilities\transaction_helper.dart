import 'package:flutter/material.dart';
import 'package:get/instance_manager.dart';
import 'package:mobile_khaata_v2/app/controllers/dashboard_item_controller.dart';
import 'package:mobile_khaata_v2/app/controllers/registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_account_detail/bank_account_detail_controller.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_account_list/bank_account_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_adjustment/add_edit_bank_adjustment_page.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_deposit/add_edit_bank_desposit_page.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_to_bank_transfer/add_edit_bank_to_bank_transfer_page.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_withdrawl/add_edit_bank_withdrawl_page.dart';
import 'package:mobile_khaata_v2/app/modules/cash_in_hand_module/add_edit_cash_adjustment/add_edit_cash_adjustment_page.dart';
import 'package:mobile_khaata_v2/app/modules/cash_in_hand_module/cash_adjustment_detail/cash_adjustment_detail_controller.dart';
import 'package:mobile_khaata_v2/app/modules/cheque_module/cheque_deposit/cheque_deposit_page.dart';
import 'package:mobile_khaata_v2/app/modules/cheque_module/cheque_list/cheque_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/credit_list_module/credit_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_module/add_edit_expense/add_edit_expense_page.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_module/detail_expense/detail_express_page.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_module/expense_list/expense_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/item_adjustment/item_adjustment_page.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/item_detail/item_detail_controller.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/item_list/item_unit_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/party_detail/party_detail_controller.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/party_list/party_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/payment_module/add_edit_payment_screen.dart';
import 'package:mobile_khaata_v2/app/modules/payment_module/detail_payment/detail_payment_screen.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/all_transaction_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/general_transaction_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/single_transaction_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_module.dart/add_edit_purchase/add_edit_purchase_page.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_module.dart/detail_purchase/detail_purchase_page.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_return_module/add_purchase_return/add_edit_purchase_return_view.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_return_module/detail_purchase_return/detail_purchase_return_page.dart';
import 'package:mobile_khaata_v2/app/modules/receipt_module/add_edit_receipt_page.dart';
import 'package:mobile_khaata_v2/app/modules/receipt_module/detailed_receipt/detailed_receipt_screen.dart';
import 'package:mobile_khaata_v2/app/modules/reminder_module/reminder_list/reminder_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/sales_module/add_sale/add_edit_sale_view.dart';
import 'package:mobile_khaata_v2/app/modules/sales_module/detail_sale/detail_sale_page.dart';
import 'package:mobile_khaata_v2/app/modules/sales_return_module/add_sale_return/add_edit_sale_return_view.dart';
import 'package:mobile_khaata_v2/app/modules/sales_return_module/detail_sale_return/detail_sale_return_page.dart';
import 'package:mobile_khaata_v2/app/modules/transaction_list_module/all_transaction_list_controller.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';

abstract class TransactionHelper {
  static goToPrintPage(BuildContext context, String txnId, int txnType) {
    Navigator.pushNamed(context, "/print",
        arguments: GeneralTransactionPrintPage(
          txnID: txnId,
          txnType: txnType,
        ));
  }

  static gotoSingleTransactionPrintPage(
    BuildContext context, {
    String pageTitle = "Print Preview",
    List<AllTransactionModel>? transactions,
    String partyText = "All Party",
    String? txnTypeText,
    String? startDate,
    String? endDate,
  }) {
    Navigator.pushNamed(
      context,
      "/printSingleTransactions",
      arguments: SingleTransactionPrintPage(
        pageTitle: pageTitle,
        partyText: partyText,
        txnTypeText: txnTypeText,
        transactions: transactions,
        startDate: startDate,
        endDate: endDate,
        // reaOnlyFlag: optArgs?.reaOnlyFlag,
      ),
    );
  }

  static goToTransactionPrintPage(
    BuildContext context, {
    String pageTitle = "Print Preview",
    List<AllTransactionModel>? transactions,
    String partyText = "All Party",
    String? txnTypeText,
    String? startDate,
    String? endDate,
    double? totalSale,
    double? totalSaleReturn,
    double? totalPurchase,
    double? totalPurchaseReturn,
    double? totalExpense,
    double? totalPaymentIn,
    double? totalPaymentOut,
  }) {
    Navigator.pushNamed(
      context,
      "/printTransactions",
      arguments: AllTransactionPrintPage(
        pageTitle: pageTitle,
        partyText: partyText,
        txnTypeText: txnTypeText,
        transactions: transactions,
        startDate: startDate,
        endDate: endDate,
        totalSale: totalSale,
        totalSaleReturn: totalSaleReturn,
        totalPurchase: totalPurchase,
        totalPurchaseReturn: totalPurchaseReturn,
        totalExpense: totalExpense,
        totalPaymentIn: totalPaymentIn,
        totalPaymentOut: totalPaymentOut,
        // reaOnlyFlag: optArgs?.reaOnlyFlag,
      ),
    );
  }

  static gotoTransactionEditPage(
      BuildContext context, String txnId, int txnType,
      {bool forEdit = false}) {
    if (TxnType.addCash == txnType || TxnType.reduceCash == txnType) {
      Navigator.pushNamed(context, "/cashInHandAdjustment",
          arguments: AddEditCashAdjustmentPage(
            adjustmentId: txnId,
          ));
    } else if (TxnType.sales == txnType) {
      if (forEdit) {
        Navigator.pushNamed(context, "/addSale",
            arguments: AddEditSalePage(
              saleID: txnId,
              reaOnlyFlag: false,
            ));
      } else {
        Navigator.pushNamed(context, "/detailSale",
            arguments: DetailSalePage(
              saleID: txnId,
            ));
      }
    } else if (TxnType.purchase == txnType) {
      if (forEdit) {
        Navigator.pushNamed(context, "/addPurchase",
            arguments: AddEditPurchasePage(
              purchaseID: txnId,
              reaOnlyFlag: false,
            ));
      } else {
        Navigator.pushNamed(context, "/detailPurchase",
            arguments: DetailPurchasePage(
              purchaseID: txnId,
            ));
      }
    } else if (TxnType.paymentOut == txnType) {
      if (forEdit) {
        Navigator.pushNamed(context, "/creditPay",
            arguments: AddEditPaymentPage(
              txnId: txnId,
            ));
      } else {
        Navigator.pushNamed(context, "/detailcreditPay",
            arguments: DetailPaymentPage(
              txnId: txnId,
            ));
      }
    } else if (TxnType.paymentIn == txnType) {
      if (forEdit) {
        Navigator.pushNamed(context, "/creditReceive",
            arguments: AddEditReceiptPage(
              txnId: txnId,
            ));
      } else {
        Navigator.pushNamed(context, "/detailcreditReceive",
            arguments: DetailReceiptPage(
              txnId: txnId,
            ));
      }
    } else if (TxnType.salesReturn == txnType) {
      if (forEdit) {
        Navigator.pushNamed(context, "/addSaleReturn",
            arguments: AddEditSaleReturnPage(
              salesReturnID: txnId,
            ));
      } else {
        Navigator.pushNamed(context, "/detailSaleReturn",
            arguments: DetailSaleReturnPage(
              salesReturnID: txnId,
            ));
      }
    } else if (TxnType.purchaseReturn == txnType) {
      if (forEdit) {
        Navigator.pushNamed(context, "/addPurchaseReturn",
            arguments: AddEditPurchaseReturnPage(
              purchaseReturnID: txnId,
            ));
      } else {
        Navigator.pushNamed(context, "/detailPurchaseReturn",
            arguments: DetailPurchaseReturnPage(
              purchaseReturnID: txnId,
            ));
      }
    } else if (TxnType.expense == txnType) {
      if (forEdit) {
        Navigator.pushNamed(context, "/addExpenses",
            arguments: AddEditExpensesPage(
              expensID: txnId,
            ));
      } else {
        Navigator.pushNamed(context, "/detailExpenses",
            arguments: DetailExpensesPage(
              expensID: txnId,
            ));
      }
    } else if (TxnType.addStock == txnType || TxnType.reduceStock == txnType) {
      Navigator.pushNamed(context, "/itemAdjustment",
          arguments: ItemAdjustmentPage(
            adjustmentId: txnId,
          ));
    }

    //===============================================
    else if (TxnType.bankDeposit == txnType) {
      Navigator.pushNamed(context, "/bankDeposit",
          arguments: AddEditBankDepositPage(
            adjustmentId: txnId,
          ));
    } else if (TxnType.bankWithdrawn == txnType) {
      Navigator.pushNamed(context, "/bankWithdrawal",
          arguments: AddEditBankWithdrawalPage(
            adjustmentId: txnId,
          ));
    } else if (TxnType.increaseBankBalance == txnType ||
        TxnType.decreaseBankBalance == txnType) {
      Navigator.pushNamed(context, "/bankAdjustment",
          arguments: AddEditBankAdjustmentPage(
            adjustmentId: txnId,
          ));
    } else if (TxnType.bankToBankTransfer == txnType) {
      Navigator.pushNamed(context, "/bankToBankTransfer",
          arguments: AddEditBankToBankTransferPage(
            adjustmentId: txnId,
          ));
    } else if (TxnType.chequeTransfer == txnType) {
      Navigator.pushNamed(context, "/chequeDeposit",
          arguments: ChequeDepositPage(
            chequeTxnId: txnId,
          ));
    }
  }

  static refreshPreviousPages() async {
    //=====================ItemDetailController
    if (Get.isRegistered<RegistrationDetailController>(
        tag: "RegistrationDetailController")) {
      RegistrationDetailController registrationDetailController =
          Get.find(tag: "RegistrationDetailController");
      registrationDetailController.onInit();
    }

    //=====================ItemDetailController
    if (Get.isRegistered<ItemDetailController>(tag: "ItemDetailController")) {
      ItemDetailController itemDetailController =
          Get.find(tag: "ItemDetailController");
      itemDetailController.reload();
    }

    // //=====================ItemUnitListController
    if (Get.isRegistered<ItemUnitListController>(
        tag: "ItemUnitListController")) {
      ItemUnitListController itemUnitListController =
          Get.find(tag: "ItemUnitListController");
      itemUnitListController.initItem();
    }

    // //=====================CashAdjustmentDetailController
    if (Get.isRegistered<CashAdjustmentDetailController>(
        tag: "CashAdjustmentDetailController")) {
      CashAdjustmentDetailController cashAdjController =
          Get.find(tag: "CashAdjustmentDetailController");
      cashAdjController.init();
    }

    // //=====================PartyDetailController
    if (Get.isRegistered<PartyDetailController>(tag: "PartyDetailController")) {
      PartyDetailController partyDetailController =
          Get.find(tag: "PartyDetailController");
      partyDetailController.reload();
    }

    // //=====================PartyListController
    if (Get.isRegistered<PartyListController>(tag: "PartyListController")) {
      PartyListController partyListController =
          Get.find(tag: "PartyListController");
      partyListController.init();
    }

    // //=====================ReminderListController
    if (Get.isRegistered<ReminderListController>(
        tag: "ReminderListController")) {
      ReminderListController controller =
          Get.find(tag: "ReminderListController");
      controller.init();
    }

    // //=====================DashboardItemController
    if (Get.isRegistered<DashboardItemController>(
        tag: "DashboardItemController")) {
      DashboardItemController controller =
          Get.find(tag: "DashboardItemController");
      controller.init();
    }
    // //=====================ExpensesListController
    if (Get.isRegistered<ExpensesListController>(
        tag: "ExpensesListController")) {
      ExpensesListController controller =
          Get.find(tag: "ExpensesListController");
      controller.init();
    }

    // //=====================CreditListController
    if (Get.isRegistered<CreditListController>(tag: "CreditListController")) {
      CreditListController controller = Get.find(tag: "CreditListController");
      controller.init();
    }

    // //=====================AllTransactionListController
    if (Get.isRegistered<AllTransactionListController>(
        tag: "AllTransactionListController")) {
      AllTransactionListController controller =
          Get.find(tag: "AllTransactionListController");
      controller.init();
    }

    // //=====================BankAccountListController
    if (Get.isRegistered<BankAccountListController>(
        tag: "BankAccountListController")) {
      BankAccountListController controller =
          Get.find(tag: "BankAccountListController");
      controller.init();
    }

    // //=====================BankAccountListController
    if (Get.isRegistered<BankAccountDetailController>(
        tag: "BankAccountDetailController")) {
      BankAccountDetailController controller =
          Get.find(tag: "BankAccountDetailController");
      controller.reload();
    }

    // //=====================ChequeListController
    if (Get.isRegistered<ChequeListController>(tag: "ChequeListController")) {
      ChequeListController controller = Get.find(tag: "ChequeListController");
      controller.init();
    }
  }
}
