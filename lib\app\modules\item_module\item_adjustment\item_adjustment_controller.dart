import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/item_adjustment_model.dart';
import 'package:mobile_khaata_v2/app/model/database/item_modal.dart';
import 'package:mobile_khaata_v2/app/repository/item_adjustment_repository.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

import 'package:nepali_utils/nepali_utils.dart';

class ItemAdjustmentController extends GetxController {
  final String tag = "ItemAdjustmentController";

  var _isLoading = false.obs;
  var _editFlag = false.obs;
  var _readOnlyFlag = false.obs;

  bool get isLoading => _isLoading.value;
  bool get editFlag => _editFlag.value;

  bool get readOnlyFlag => _readOnlyFlag.value;
  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  set editFlag(bool flag) {
    _editFlag.value = flag;
  }

  ItemAdjustmentModel _itemAdj = ItemAdjustmentModel();
  ItemAdjustmentModel? get itemAdj => _itemAdj;

  List<ItemModel> _items = [];
  List<ItemModel> get items => _items;

  void itemAdjRefresh() {}

  // ignore: prefer_final_fields
  ItemAdjustmentRepository _itemAdjustmentRepository =
      ItemAdjustmentRepository();
  final ItemRepository _itemRepository = ItemRepository();

  final formKey = GlobalKey<FormState>();
  final TextEditingController itemNameController = TextEditingController();

  init(String itemId) async {
    _isLoading(true);
    _items.clear();
    _items.addAll(await _itemRepository.getAllItems('active'));
    itemOnSelectHandler(items.firstWhere(
        (element) => element.itemId == itemAdj?.itemAdjItemId,
        orElse: () => ItemModel()));

    _isLoading(false);

    _isLoading(false);
  }

  @override
  void onClose() {
    _items.clear();
    itemNameController.dispose();
    super.onClose();
  }

  itemOnSelectHandler(ItemModel item) {
    itemAdj?.itemAdjItemId = item.itemId;
    itemNameController.text = item.itemName ?? "";
    itemAdjRefresh();
  }

  itemOnClearHandler() {
    itemAdj?.itemAdjItemId = null;
    itemNameController.text = "";
    itemAdjRefresh();
    itemAdjRefresh();
  }

  Future<bool> createAdjustment() async {
    bool status = false;
    try {
      itemAdj?.itemAdjDate =
          toDateAD(NepaliDateTime.parse(itemAdj!.itemAdjDateBS!));
      itemAdj?.itemAdjAtprice = parseDouble(itemAdj!.itemAdjAtprice!);

      String itemAdjId = await _itemAdjustmentRepository.insert(itemAdj!);

      if (null != itemAdjId) status = true;
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<bool> updateAdjustment() async {
    bool status = false;
    try {
      itemAdj!.itemAdjDate =
          toDateAD(NepaliDateTime.parse(itemAdj!.itemAdjDateBS!));
      itemAdj!.itemAdjAtprice = parseDouble(itemAdj!.itemAdjAtprice);

      status = await _itemAdjustmentRepository.update(itemAdj!);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }
}
