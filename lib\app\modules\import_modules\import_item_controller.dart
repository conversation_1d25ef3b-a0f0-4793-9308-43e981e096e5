import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'dart:io';
import 'package:path/path.dart';
import 'package:excel/excel.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/model/database/item_modal.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:path_provider/path_provider.dart';

import 'package:permission_handler/permission_handler.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

import '../../../database/item_type.dart';

extension ExtendedIterable<E> on Iterable<E> {
  /// Like Iterable<T>.map but callback have index as second argument
  Iterable<T> mapIndex<T>(T Function(E e, int i) f) {
    var i = 0;
    return map((e) => f(e, i++));
  }

  void forEachIndex(void Function(E e, int i) f) {
    var i = 0;
    for (var e in this) {
      f(e, i++);
    }
  }
}

class ImportItemController extends GetxController {
  var _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  String sheetName = "Sheet1";

  Future<String?> getDownloadPath() async {
    Directory? directory;
    try {
      if (Platform.isIOS) {
        directory = await getApplicationDocumentsDirectory();
      } else {
        directory = Directory('/storage/emulated/0/Download');
        // Put file in global download folder, if for an unknown reason it didn't exist, we fallback
        // ignore: avoid_slow_async_io
        if (!await directory.exists())
          directory = await getExternalStorageDirectory();
      }
    } catch (err, stack) {
      // print("Cannot get download folder path");
    }
    return directory?.path;
  }

  Future<Tuple2<bool, String>> saveTemplate(BuildContext context) async {
    bool status = false;
    String msg = "";

    ProgressDialog progressDialog = ProgressDialog(context,
        type: ProgressDialogType.normal, isDismissible: false);
    progressDialog.update(message: "Generating Template. Please wait....");
    await progressDialog.show();

    var excel = Excel.createExcel();
    excel.rename('Sheet1', sheetName);

    Sheet sheetObject = excel[sheetName];
    [
      "Item Name",
      "Item Code",
      "Stock Quantity",
      "Item Location",
      "Sale Price",
      "Purchase Price",
      "Min Stock Quantity"
    ].mapIndex((e, i) {
      // Log.d("alphabet id  ${String.fromCharCode(i + 65)}");
      sheetObject
          .cell(CellIndex.indexByString("${String.fromCharCode(i + 65)}1"))
          .value = e;
    }).toList();

    var granted = await Permission.storage.status;
    if (!granted.isGranted) {
      await Permission.storage.request();
    }

    try {
      final path = await getDownloadPath();
      if (path == null) {
        return Tuple2(status, msg);
      }
      excel.encode();
      var fileBytes = excel.save();

      File(join("$path/MKhaata_Import_Item.xlsx"))
        ..createSync(recursive: true)
        ..writeAsBytesSync(fileBytes!);

      await progressDialog.hide();

      showAlertDialog(context,
          alertType: AlertType.Success,
          alertTitle: "Sample File",
          message:
              "A sample excel file has been saved to\nDownload/MKhaata_Import_Item.xlsx");
    } catch (e) {
      await progressDialog.hide();
      // Log.d(e.toString());
    }

    return Tuple2(status, msg);
  }

  Future<Tuple2<bool, String>> importData(BuildContext context) async {
    bool status = false;
    String msg = "";

    FilePickerResult? pickedFile = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['xlsx', 'xls'],
      allowMultiple: false,
    );
    ProgressDialog progressDialog = ProgressDialog(context,
        type: ProgressDialogType.normal, isDismissible: false);

    /// file might be picked

    if (pickedFile != null) {
      progressDialog.update(message: "Importing Data. Please wait....");
      await progressDialog.show();

      final file = pickedFile.files.first.path;
      var bytes = File(file ?? "").readAsBytesSync();
      var excel = Excel.decodeBytes(bytes);

      List<ItemModel> items = [];
      bool isAllValid = true;
      // var table = excel.tables[sheetName];

      await Future.wait(excel.tables.keys.map((table) async {
        int i = 0;
        // await Future.wait((excel.tables[table].rows.map((row) async {
        for (var row in excel.tables[table]!.rows) {
          // Log.d("for row $i");
          if (i != 0) {
            // ignore first row,
            // Log.d("checking for row $row");
            String? name =
                toBeginningOfSentenceCase(row[0]!.value.toString()) ?? "";
            String code = (row[1]!.value.toString()).toString();
            dynamic openingStockBalance = (row[2]!.value.toString()).toString();
            String itemLocation = row[3]!.value.toString();
            double? salePrice =
                parseDouble((row[4]!.value.toString()).toString());
            double? purchasePrice =
                parseDouble((row[5]!.value.toString()).toString());
            double? minStockQuantity =
                parseDouble((row[6]!.value.toString()).toString());
            // int itemType = ("service" == (row[7] ?? ""))
            //     ? ItemType.service
            //     : ItemType.product;
            int itemType = ItemType.product;
            double openingAmount = 0.0;

            //validate and add in ledger list
            if ([null, "", " "].contains(name)) {
              isAllValid = false;
              msg = "Item Name is missing in row $i";
            }
            //check uniqueness of name in db and previous  list
            // Log.d("prev items are ${items.toString()}");
            ItemModel? prevItem = items.firstWhere((element) {
              // Log.d("validating ${element.itemName} and $name");
              return element.itemName == name;
            }, orElse: () {
              return ItemModel();
            });
            // Log.d("prevItem is  ${prevItem?.toJson()}");
            // await Future.delayed(Duration(seconds: 1));
            // Log.d("after 1 sec for index $i");

            bool isPresentInDatabase =
                await ItemRepository().checkUniqueItemName(name);

            isPresentInDatabase = !isPresentInDatabase;
            // Log.d("checking for uniquness $isPresentInDatabase");

            if (null != prevItem.itemName) {
              // name already exist in previous list
              isAllValid = false;
              msg = "Item name must be unique of row $i";
            }
            if (isPresentInDatabase) {
              isAllValid = false;
              msg = "Item name already exist in database of row $i";
            }

            if (code.isNotEmpty) {
              //check for  uniquness  in previous list and database
              ItemModel? prevCodeItem = items.firstWhere((element) {
                return element.itemCode == code;
              }, orElse: () {
                return ItemModel();
              });
              bool isCodeUnique =
                  await ItemRepository().checkUniqueItemCode(code);

              if (null != prevCodeItem.itemCode) {
                isAllValid = false;
                msg = "Item code must be unique of row $i";
              }
              if (!isCodeUnique) {
                isAllValid = false;
                msg = "Item code already exist in database of row $i";
              }
            }

            if (openingStockBalance.length > 0) {
              openingAmount = parseDouble(openingStockBalance ?? 0) ?? 0;
            }
            // Log.d("is alll valid ${isAllValid} for row $row");

            if (isAllValid) {
              var newItem = ItemModel(
                  itemName: name,
                  itemCode: code,
                  itemLocation: itemLocation,
                  itemSaleUnitPrice: salePrice,
                  itemPurchaseUnitPrice: purchasePrice,
                  itemMinStockQuantity: minStockQuantity,
                  itemType: itemType,
                  openingStock: openingAmount,
                  openingDate: currentDate,
                  unitConversionFactor: 0);

              items.add(newItem);
              // Log.d("item  is ${newItem.toJson()} ${items.toString()}");
            } else {
              //
            }
          }
          i++;
        }
      }).toList());

      if (isAllValid) {
        status = true;

        status = await ItemRepository().insertMultipleItem(items);
        //insert all ledger to database;
        if (!status) {
          msg = "Failed to perform operation";
        }
      }

      if (isAllValid & status) {
        //save in db and pushupdate
        msg = "Imported ${items.length} records";
      }
    } else {
      msg = "Supported File Not Picked";
    }

    await progressDialog.hide();

    if (status) {
      showAlertDialog(context,
          alertType: AlertType.Success,
          alertTitle: "Item Imported Successfully",
          message: msg);
    } else {
      showAlertDialog(context,
          alertType: AlertType.Error, alertTitle: "Error", message: msg);
    }

    return Tuple2(status, msg);
  }
}
