import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/cash_txn_model.dart';
import 'package:mobile_khaata_v2/app/modules/cash_in_hand_module/cash_adjustment_detail/cash_adjustment_detail_controller.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';

import '../../../../utilities/styles.dart';

class CashAdjustmentDetailPage extends StatelessWidget {
  final String tag = "CashAdjustmentDetailPage";

  final cashAdjustmentDetailController = Get.put(
      CashAdjustmentDetailController(),
      tag: "CashAdjustmentDetailController");

  CashAdjustmentDetailPage({super.key}) {
    cashAdjustmentDetailController.init();
  }

  searchBoxOnChangeHandler(String searchString) {
    cashAdjustmentDetailController.searchTransaction(searchString);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (cashAdjustmentDetailController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
        // resizeToAvoidBottomPadding: false,
        resizeToAvoidBottomInset: false,
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          backgroundColor: colorPrimary,
          leading: BackButton(
            onPressed: () => Navigator.pop(context, false),
          ),
          centerTitle: false,
          titleSpacing: -10.0,
          title: ListTile(
            contentPadding: const EdgeInsets.only(right: 15),
            title: const Text(
              "नगद मौज्दात\n(Cash in hand) ",
              style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontFamily: 'HelveticaRegular',
                  fontWeight: FontWeight.bold),
            ),
            trailing: Text(
              formatCurrencyAmount(
                  cashAdjustmentDetailController.totalCashInHand),
              style: const TextStyle(
                fontSize: 14,
                color: Colors.white,
              ),
            ),
          ),
        ),
        body: Column(children: [
          Container(
            width: MediaQuery.of(context).size.width,
            padding:
                const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 5),
            color: Colors.white,
            height: 55,
            child: FormBuilderTextField(
              name: "searchBox",
              autocorrect: false,
              keyboardType: TextInputType.text,
              textInputAction: TextInputAction.done,

              style: const TextStyle(
                // color: Colors.white,
                fontSize: 18,
              ),
              // controller: searchController,
              decoration: formFieldStyle.copyWith(
                  // labelText: "Search For",
                  hintText: "Search For",
                  prefixIcon: const Icon(Icons.search),
                  alignLabelWithHint: true),
              onChanged: (searchString) =>
                  searchBoxOnChangeHandler(searchString ?? ""),
            ),
          ),
          Expanded(child: _TxnListView(cashAdjustmentDetailController)),
        ]),
        floatingActionButton: SpeedDial(
          // marginBottom: 15,
          elevation: 8,
          backgroundColor: colorPrimary,
          overlayColor: colorPrimaryLightest,
          children: [
            SpeedDialChild(
                child: const Icon(
                  Icons.account_balance,
                  size: 20,
                ),
                label: "बैंक जम्मा(Bank Deposit)",
                backgroundColor: colorGreen,
                onTap: () => Navigator.pushNamed(context, "/bankDeposit")),
            SpeedDialChild(
                child: const Icon(
                  Icons.account_balance,
                  size: 20,
                ),
                label: "बैंक निकासी (Bank Withdrawal)",
                backgroundColor: colorRedDark,
                onTap: () => Navigator.pushNamed(context, "/bankWithdrawal")),
            SpeedDialChild(
                child: const Icon(
                  Icons.account_balance_wallet,
                  size: 20,
                ),
                label: "नगद समायोजन(Cash Adjustment)",
                backgroundColor: colorOrangeLight,
                onTap: () =>
                    Navigator.pushNamed(context, "/cashInHandAdjustment")),
          ].reversed.toList(),
          child: const Icon(
            Icons.add,
            size: 35,
          ),
        ),
      ));
    });
  }
}

class _TxnListView extends StatelessWidget {
  final CashAdjustmentDetailController cashAdjustmentDetailController;

  const _TxnListView(this.cashAdjustmentDetailController);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (cashAdjustmentDetailController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }
      if (cashAdjustmentDetailController.filteredCashTxnList.isEmpty) {
        return const SizedBox(
            width: double.infinity,
            child: Center(
                child: Text(
              "No Records",
              style: TextStyle(color: Colors.black54),
            )));
      } else {
        return ListView.builder(
          itemCount: cashAdjustmentDetailController.filteredCashTxnList.length,
          shrinkWrap: true,
          itemBuilder: (context, int index) {
            CashTxnModel txn =
                cashAdjustmentDetailController.filteredCashTxnList[index];

            return InkWell(
              onTap: () {
                TransactionHelper.gotoTransactionEditPage(
                    context, txn.txnId ?? "", txn.txnType!,
                    forEdit: false);
              },
              child: Column(
                children: [
                  if (0 == index) ...{
                    const SizedBox(
                      height: 5,
                    )
                  },

                  DefaultTextStyle(
                    style: TextStyle(
                      fontSize: 14,
                      color: colorPrimary,
                    ),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 10, horizontal: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          //====================================1st Column
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "${txn.txnTypeText}",
                                  style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.bold),
                                ),
                                const SizedBox(
                                  height: 5,
                                ),
                                Text(
                                  "${txn.txnDateBS}",
                                  style: const TextStyle(
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            width: 6,
                          ),

                          //============2nd Column
                          Expanded(
                            flex: 2,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  txn.cashTransactionDesc ?? '',
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                  style: const TextStyle(),
                                ),
                              ],
                            ),
                          ),

                          //====================================3rd Column
                          Expanded(
                            flex: 2,
                            child: Text(
                              formatCurrencyAmount(parseDouble(
                                  (txn.txnAmount ?? 0.0).toStringAsFixed(2))!),
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                  color: getTxnColor(txn.cashTransactionType!)),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  const Divider(
                    height: 0,
                  ),

                  //Add space if last element
                  if (cashAdjustmentDetailController
                              .filteredCashTxnList.length -
                          1 ==
                      index) ...{
                    const SizedBox(
                      height: 100,
                    )
                  },
                ],
              ),
            );
          },
        );
      }
    });
  }

  Color getTxnColor(int txnType) {
    Color amtTextColor = textColor;

    if (TxnType.addCash == txnType) {
      amtTextColor = colorGreen;
    } else if (TxnType.reduceCash == txnType) {
      amtTextColor = colorOrangeDark;
    }

    return amtTextColor;
  }
}
