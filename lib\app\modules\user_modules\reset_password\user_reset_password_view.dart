// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/reset_password/user_reset_password_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:tuple/tuple.dart';

class UserResetPasswordView extends StatefulWidget {
  final BuildContext builderContext;
  final int userId;

  const UserResetPasswordView(this.builderContext, this.userId, {super.key});

  @override
  _UserResetPasswordViewState createState() => _UserResetPasswordViewState();
}

class _UserResetPasswordViewState extends State<UserResetPasswordView> {
  final userresetPasswordController = UserResetPasswordController();

  bool newPassObs = true;
  bool verifyPassObs = true;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            "Reset Password",
            style: labelStyle2.copyWith(
              fontSize: 24,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(
            height: 20.0,
          ),
          FormBuilder(
            key: userresetPasswordController.formKey,
            child: Padding(
                padding: EdgeInsets.only(
                    bottom:
                        MediaQuery.of(widget.builderContext).viewInsets.bottom),
                child: GestureDetector(
                  onTap: () {
                    // Log.d("tapped");
                    FocusScope.of(widget.builderContext).unfocus();
                    // userresetPasswordController.formKey.
                  },
                  child: Column(mainAxisSize: MainAxisSize.min, children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'नया पासवर्ड',
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                            name: "new_password",
                            autocorrect: false,
                            obscureText: newPassObs,
                            keyboardType: TextInputType.text,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return "New Password field is required";
                              }
                              return null;
                            },
                            textInputAction: TextInputAction.next,
                            inputFormatters: [
                              FilteringTextInputFormatter.deny(
                                  RegExp(r"\s\b|\b\s")),
                            ],
                            style: formFieldTextStyle,
                            decoration: formFieldStyle.copyWith(
                                suffixIcon: IconButton(
                                    icon: Icon(newPassObs
                                        ? Icons.visibility
                                        : Icons.visibility_off),
                                    onPressed: () {
                                      setState(() {
                                        newPassObs = !newPassObs;
                                      });
                                    }),
                                labelText: "New Password",
                                hintText: "New Password"),
                            controller:
                                userresetPasswordController.newPasswordCtrl
                            // controller: state.mobileCtrl,
                            ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'पासवर्ड प्रमाणित गर्नुहोस्',
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                            name: "verify_password",
                            autocorrect: false,
                            obscureText: verifyPassObs,
                            keyboardType: TextInputType.text,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return "Verify Password field is required";
                              }
                              return null;
                            },
                            textInputAction: TextInputAction.done,
                            inputFormatters: [
                              FilteringTextInputFormatter.deny(
                                  RegExp(r"\s\b|\b\s")),
                            ],
                            style: formFieldTextStyle,
                            decoration: formFieldStyle.copyWith(
                                suffixIcon: IconButton(
                                    icon: Icon(verifyPassObs
                                        ? Icons.visibility
                                        : Icons.visibility_off),
                                    onPressed: () {
                                      setState(() {
                                        verifyPassObs = !verifyPassObs;
                                      });
                                    }),
                                labelText: "Verify Password",
                                hintText: "Verify Password"),
                            controller: userresetPasswordController
                                .verifyNewPasswordCtrl
                            // controller: state.mobileCtrl,
                            ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton(
                          // color: colorPrimary,
                          // elevation: 10,
                          // shape: RoundedRectangleBorder(
                          //   borderRadius: BorderRadius.circular(10.0),
                          // ),
                          // splashColor: colorPrimaryLightest,
                          onPressed: userresetPasswordController.isSubmitting
                              ? null
                              : () async {
                                  if (userresetPasswordController
                                      .formKey.currentState!
                                      .saveAndValidate()) {
                                    Tuple2<bool, String> checkResponse =
                                        await userresetPasswordController
                                            .resetPassword(
                                                userId: widget.userId);
                                    // Log.d("got  check response" +
                                    //     checkResponse.item1.toString() +
                                    //     checkResponse.item2);

                                    if (!checkResponse.item1) {
                                      showAlertDialog(context,
                                          alertType: AlertType.Error,
                                          alertTitle: "",
                                          message: checkResponse.item2);
                                    } else {
                                      // number verified, so send to next screen

                                      Navigator.of(widget.builderContext).pop();
                                      showToastMessage(context,
                                          alertType: AlertType.Success,
                                          message: checkResponse.item2);
                                    }
                                  }
                                },
                          // color: colorPrimary,
                          // elevation: 10,
                          // shape: RoundedRectangleBorder(
                          //   borderRadius: BorderRadius.circular(10.0),
                          // ),
                          // splashColor: colorPrimaryLightest,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                                vertical: 12, horizontal: 40),
                            child: Text(
                              userresetPasswordController.isSubmitting
                                  ? "Submitting.."
                                  : "Submit",
                              style: const TextStyle(
                                  color: Colors.white,
                                  fontSize: 15,
                                  fontWeight: FontWeight.bold),
                            ),
                          )),
                    ),
                  ]),
                )),
          ),
          const SizedBox(height: 10),
        ],
      ),
    );
    // return Obx(() {
    //   return Container(
    //     padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 10),
    //     child: GestureDetector(
    //       onTap: () {
    //         // Log.d("tapped");
    //         FocusScope.of(widget.builderContext).unfocus();
    //         // userresetPasswordController.formKey.
    //       },
    //       child: Column(
    //         crossAxisAlignment: CrossAxisAlignment.center,
    //         mainAxisSize: MainAxisSize.min,
    //         children: <Widget>[
    //           Text(
    //             "Reset Password",
    //             style: labelStyle2.copyWith(
    //               fontSize: 24,
    //             ),
    //             textAlign: TextAlign.center,
    //           ),
    //           const SizedBox(
    //             height: 20.0,
    //           ),
    //           FormBuilder(
    //             key: userresetPasswordController.formKey,
    //             child: Padding(
    //                 padding: EdgeInsets.only(
    //                     bottom: MediaQuery.of(widget.builderContext)
    //                         .viewInsets
    //                         .bottom),
    //                 child: GestureDetector(
    //                   onTap: () {
    //                     // Log.d("tapped");
    //                     FocusScope.of(widget.builderContext).unfocus();
    //                     // userresetPasswordController.formKey.
    //                   },
    //                   child: Column(mainAxisSize: MainAxisSize.min, children: [
    //                     Column(
    //                       crossAxisAlignment: CrossAxisAlignment.start,
    //                       children: [
    //                         Text(
    //                           'नया पासवर्ड',
    //                           style: labelStyle2,
    //                         ),
    //                         const SizedBox(height: 5.0),
    //                         FormBuilderTextField(
    //                             name: "new_password",
    //                             autocorrect: false,
    //                             keyboardType: TextInputType.text,
    //                             validator: (value) {
    //                               if (value == null || value.isEmpty) {
    //                                 return "New Password field is required";
    //                               }
    //                               return null;
    //                             },
    //                             textInputAction: TextInputAction.next,
    //                             inputFormatters: [
    //                               FilteringTextInputFormatter.deny(
    //                                   RegExp(r"\s\b|\b\s")),
    //                             ],
    //                             style: formFieldTextStyle,
    //                             decoration: formFieldStyle.copyWith(
    //                                 labelText: "New Password",
    //                                 hintText: "New Password"),
    //                             controller:
    //                                 userresetPasswordController.newPasswordCtrl
    //                             // controller: state.mobileCtrl,
    //                             ),
    //                       ],
    //                     ),
    //                     const SizedBox(
    //                       height: 20,
    //                     ),
    //                     Column(
    //                       crossAxisAlignment: CrossAxisAlignment.start,
    //                       children: [
    //                         Text(
    //                           'पासवर्ड प्रमाणित गर्नुहोस्',
    //                           style: labelStyle2,
    //                         ),
    //                         const SizedBox(height: 5.0),
    //                         FormBuilderTextField(
    //                             name: "verify_password",
    //                             autocorrect: false,
    //                             keyboardType: TextInputType.text,
    //                             validator: (value) {
    //                               if (value == null || value.isEmpty) {
    //                                 return "Verify Password field is required";
    //                               }
    //                               return null;
    //                             },
    //                             textInputAction: TextInputAction.done,
    //                             inputFormatters: [
    //                               FilteringTextInputFormatter.deny(
    //                                   RegExp(r"\s\b|\b\s")),
    //                             ],
    //                             style: formFieldTextStyle,
    //                             decoration: formFieldStyle.copyWith(
    //                                 labelText: "Verify Password",
    //                                 hintText: "Verify Password"),
    //                             controller: userresetPasswordController
    //                                 .verifyNewPasswordCtrl
    //                             // controller: state.mobileCtrl,
    //                             ),
    //                       ],
    //                     ),
    //                     const SizedBox(
    //                       height: 20,
    //                     ),
    //                     SizedBox(
    //                       width: double.infinity,
    //                       child: ElevatedButton(
    //                           // color: colorPrimary,
    //                           // elevation: 10,
    //                           // shape: RoundedRectangleBorder(
    //                           //   borderRadius: BorderRadius.circular(10.0),
    //                           // ),
    //                           // splashColor: colorPrimaryLightest,
    //                           onPressed: userresetPasswordController
    //                                   .isSubmitting
    //                               ? null
    //                               : () async {
    //                                   if (userresetPasswordController
    //                                       .formKey.currentState!
    //                                       .saveAndValidate()) {
    //                                     Tuple2<bool, String> checkResponse =
    //                                         await userresetPasswordController
    //                                             .resetPassword(
    //                                                 userId: widget.userId);
    //                                     // Log.d("got  check response" +
    //                                     //     checkResponse.item1.toString() +
    //                                     //     checkResponse.item2);

    //                                     if (!checkResponse.item1) {
    //                                       showAlertDialog(context,
    //                                           alertType: AlertType.Error,
    //                                           alertTitle: "",
    //                                           message: checkResponse.item2);
    //                                     } else {
    //                                       // number verified, so send to next screen

    //                                       Navigator.of(widget.builderContext)
    //                                           .pop();
    //                                       showToastMessage(context,
    //                                           alertType: AlertType.Success,
    //                                           message: checkResponse.item2);
    //                                     }
    //                                   }
    //                                 },
    //                           // color: colorPrimary,
    //                           // elevation: 10,
    //                           // shape: RoundedRectangleBorder(
    //                           //   borderRadius: BorderRadius.circular(10.0),
    //                           // ),
    //                           // splashColor: colorPrimaryLightest,
    //                           child: Padding(
    //                             padding: const EdgeInsets.symmetric(
    //                                 vertical: 12, horizontal: 40),
    //                             child: Text(
    //                               userresetPasswordController.isSubmitting
    //                                   ? "Submitting.."
    //                                   : "Submit",
    //                               style: const TextStyle(
    //                                   color: Colors.white,
    //                                   fontSize: 15,
    //                                   fontWeight: FontWeight.bold),
    //                             ),
    //                           )),
    //                     ),
    //                   ]),
    //                 )),
    //           ),
    //           const SizedBox(height: 10),
    //         ],
    //       ),
    //     ),
    //   );
    // });
  }
}
