import 'dart:convert';

class YoutubeTutorialModal {
  String? title;
  String? link;
  YoutubeTutorialModal({
    this.title,
    this.link,
  });

  YoutubeTutorialModal copyWith({
    String? title,
    String? link,
  }) {
    return YoutubeTutorialModal(
      title: title ?? this.title,
      link: link ?? this.link,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'title': title,
      'link': link,
    };
  }

  factory YoutubeTutorialModal.fromJson(Map<String, dynamic> map) {
    return YoutubeTutorialModal(
      title: map['title'],
      link: map['link'],
    );
  }
}
