linter:
  rules:
    # STYLE
    - camel_case_types
    - library_names
    - file_names
    - library_prefixes
    - non_constant_identifier_names
    - constant_identifier_names # prefer
    - directives_ordering
    #- lines_longer_than_80_chars # avoid
    - curly_braces_in_flow_control_structures

    # DOCUMENTATION
    - slash_for_doc_comments
    - package_api_docs # prefer # ?
    - public_member_api_docs # prefer # ?
    #- comment_references # Unused because https://github.com/dart-lang/sdk/issues/36974

    # USAGE
    - avoid_relative_lib_imports # prefer
    - prefer_adjacent_string_concatenation
    - prefer_interpolation_to_compose_strings # prefer
    - unnecessary_brace_in_string_interps # avoid
    - prefer_collection_literals
    - avoid_function_literals_in_foreach_calls # avoid
    - prefer_iterable_whereType
    - prefer_function_declarations_over_variables
    - unnecessary_lambdas
    - avoid_init_to_null
    - unnecessary_getters_setters
    #- unnecessary_getters # prefer # Disabled pending fix: https://github.com/dart-lang/linter/issues/23
    #- prefer_expression_function_bodies # consider
    - unnecessary_this
    - prefer_initializing_formals
    - type_init_formals
    - empty_constructor_bodies
    - unnecessary_new
    - unnecessary_const
    - avoid_catches_without_on_clauses # avoid
    - use_rethrow_when_possible

    # DESIGN
    - use_to_and_as_if_applicable # prefer
    - one_member_abstracts # avoid
    - avoid_classes_with_only_static_members # avoid
    - prefer_final_fields # prefer
    - use_setters_to_change_properties
    - avoid_setters_without_getters
    - avoid_returning_null # avoid
    - avoid_returning_this # avoid
    - type_annotate_public_apis # prefer
    #- prefer_typing_uninitialized_variables # consider
    - omit_local_variable_types # avoid
    - avoid_return_types_on_setters
    - prefer_generic_function_type_aliases
    - avoid_private_typedef_functions # prefer
    #- use_function_type_syntax_for_parameters # consider
    - avoid_positional_boolean_parameters # avoid
    - hash_and_equals
    - avoid_null_checks_in_equality_operators