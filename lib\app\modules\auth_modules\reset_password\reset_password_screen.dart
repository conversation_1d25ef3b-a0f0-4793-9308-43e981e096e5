import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/timer_button.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/reset_password/reset_password_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/ui_helper.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:tuple/tuple.dart';

class ResetPasswordScreen extends StatefulWidget {
  final String? username;
  final String? mobileNo;
  final String? token;
  const ResetPasswordScreen(
      {super.key, this.username, required this.token, this.mobileNo});
  @override
  _ResetPasswordScreenState createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final passwordResetController = PasswordResetController();
  bool hasError = false;
  bool sendingOTP = false;

  bool newPwObs = true;
  bool verifyPwObs = true;

  resendOtp() async {
    setState(() {
      sendingOTP = true;
      print("Mah yaha xu ");
    });
    FocusScope.of(context).unfocus();

    ProgressDialog progressDialog = ProgressDialog(context,
        type: ProgressDialogType.normal, isDismissible: false);
    progressDialog.update(message: "Please wait....");
    await progressDialog.show();

    Tuple2<bool, String> checkResponse =
        await passwordResetController.resendOTP(tk: widget.token ?? "");
    setState(() {
      sendingOTP = false;
    });
    await progressDialog.hide();
    if (!checkResponse.item1) {
      showAlertDialog(context,
          alertType: AlertType.Error,
          alertTitle: "",
          message: checkResponse.item2);
    } else {
      // success in reseting password
      showAlertDialog(context,
          alertTitle: "OTP sent",
          message: checkResponse.item2,
          alertType: AlertType.Success);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        backgroundColor: colorPrimary,
        titleSpacing: -5.0,
        title: const Text("Reset Password"),
      ),
      body: Obx(
        () {
          if (passwordResetController.isLoading) {
            return Container(
              color: Colors.white,
              child: const Center(
                child: CircularProgressIndicator(),
              ),
            );
          }
          return Padding(
            padding: sPagePadding,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                RichText(
                    textAlign: TextAlign.center,
                    text: TextSpan(
                        text: 'Enter the OTP sent to your registered number ',
                        style: kLabelStyle.copyWith(fontSize: 16, height: 1.4),
                        children: <TextSpan>[
                          TextSpan(
                              text: widget.mobileNo,
                              style: TextStyle(color: colorPrimary))
                        ])),
                const SizedBox(height: 10.0),
                PinCodeTextField(
                  onChanged: (value) {},
                  appContext: context,
                  pastedTextStyle: TextStyle(
                    color: Colors.green.shade600,
                    fontWeight: FontWeight.bold,
                  ),
                  length: 6,
                  obscureText: false,
                  controller: passwordResetController.otpController,
                  errorAnimationController:
                      passwordResetController.errorController,
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  animationType: AnimationType.fade,
                  pinTheme: PinTheme(
                    shape: PinCodeFieldShape.box,
                    borderRadius: BorderRadius.circular(5),
                    fieldHeight: 45,
                    fieldWidth: 45,
                    inactiveColor: hasError ? Colors.orange : colorPrimary,
                  ),
                ),
                const SizedBox(height: 15.0),
                Text(
                  'नया पासवर्ड',
                  style: labelStyle2,
                ),
                const SizedBox(height: 5.0),
                TextFormField(
                  // attribute: "new_password",
                  autocorrect: false,
                  keyboardType: TextInputType.text,
                  // validators: [
                  //   FormBuilderValidators.required(
                  //       errorText: "New Password field is required")
                  // ],
                  textInputAction: TextInputAction.next,
                  inputFormatters: [
                    FilteringTextInputFormatter.deny(RegExp(r"\s\b|\b\s")),
                  ],
                  style: formFieldTextStyle,
                  decoration: formFieldStyle.copyWith(
                      suffixIcon: IconButton(
                        icon: Icon(
                          newPwObs ? Icons.visibility : Icons.visibility_off,
                          color: Colors.grey,
                        ),
                        onPressed: () {
                          setState(() {
                            newPwObs = !newPwObs;
                          });
                          // changePasswordcontroller.update();
                        },
                      ),
                      labelText: "New Password",
                      hintText: "New Password"),
                  controller: passwordResetController.newPasswordCtrl,
                  obscureText: newPwObs,

                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "New Password field is required";
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 15.0),
                Text(
                  'पासवर्ड प्रमाणित गर्नुहोस्',
                  style: labelStyle2,
                ),
                const SizedBox(height: 5.0),
                TextFormField(
                  // attribute: "verify_password",
                  autocorrect: false,
                  keyboardType: TextInputType.text,

                  textInputAction: TextInputAction.done,
                  inputFormatters: [
                    FilteringTextInputFormatter.deny(RegExp(r"\s\b|\b\s")),
                  ],
                  style: formFieldTextStyle,
                  decoration: formFieldStyle.copyWith(
                      suffixIcon: IconButton(
                        icon: Icon(
                          verifyPwObs ? Icons.visibility : Icons.visibility_off,
                          color: Colors.grey,
                        ),
                        onPressed: () {
                          setState(() {
                            verifyPwObs = !verifyPwObs;
                          });
                          // changePasswordcontroller.update();
                        },
                      ),
                      labelText: "Verify Password",
                      hintText: "Verify Password"),
                  controller: passwordResetController.verifyNewPasswordCtrl,
                  obscureText: verifyPwObs,

                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "Verify Password field is required";
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 35.0),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorPrimary,
                      elevation: 10,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                      disabledBackgroundColor: colorPrimaryLightest,
                    ),
                    onPressed: passwordResetController.isSubmitting
                        ? null
                        : () async {
                            // Log.d("this is token ${widget.token} ");
                            Tuple2<bool, String> checkResponse =
                                await passwordResetController.resetPassword(
                                    tk: widget.token);
                            if (!checkResponse.item1) {
                              showAlertDialog(context,
                                  alertType: AlertType.Error,
                                  alertTitle: "",
                                  message: checkResponse.item2);
                            } else {
                              // success in reseting password
                              showAlertDialog(context,
                                  alertTitle: "Password Reset Complete",
                                  message: checkResponse.item2,
                                  alertType: AlertType.Success,
                                  onCloseButtonPressed: () {
                                Navigator.of(context).pushNamedAndRemoveUntil(
                                    '/login', (route) => false);
                              });
                            }
                          },
                    child: const Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: 12, horizontal: 40),
                      child: Text(
                        "Submit",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 15,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 35.0),
                const SizedBox(height: 35.0),
                Text(
                  "OTP successfully sent to your registered mobile number. ",
                  style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: colorGreen),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20.0),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Text("Didn't receive a code ? "),
                    sendingOTP
                        ? const CircularProgressIndicator(
                            strokeWidth: 1,
                          )
                        : DTTimerButton(
                            label: "Resend",
                            onPressed: () {
                              resendOtp();
                            },
                            color: Colors.red,
                            timeOutInSeconds: 120,
                          ),
                  ],
                ),
                const SizedBox(height: 25.0),
              ],
            ),
          );
        },
      ),
    ));
  }
}

class ResetPasswordDialogContent extends StatelessWidget {
  final BuildContext builderContext;
  ResetPasswordDialogContent(this.builderContext);

  final passwordResetController =
      Get.put<PasswordResetController>(PasswordResetController());

  @override
  Widget build(BuildContext context) {
    // Clear all previous data when dialog is opened
    passwordResetController.clearAllData();

    return Padding(
      padding:
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
      child: SingleChildScrollView(
        physics: const BouncingScrollPhysics(),
        keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
        child: Container(
          padding: sPagePadding,
          height: MediaQuery.of(context).size.height * .28,
          child: Column(
            mainAxisSize: MainAxisSize.min, // Important to keep height dynamic
            children: [
              Text(
                "Reset Password",
                style: labelStyle2.copyWith(
                  fontSize: 24,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 20.0),
              TextFormField(
                onChanged: (value) {
                  debugPrint("Mobile no is $value");
                  debugPrint(passwordResetController.mobileNoCtrl.text);
                },
                readOnly: false,
                autocorrect: false,
                autofocus: true,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.done,
                style: formFieldTextStyle,
                maxLength: 10,
                controller: passwordResetController.mobileNoCtrl,
                decoration: formFieldStyle.copyWith(
                  counterText: "",
                  labelText: "Registered Mobile No",
                ),
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                textAlign: TextAlign.end,
              ),
              const SizedBox(height: 20),
              Obx(() {
                return Container(
                  margin: const EdgeInsets.symmetric(horizontal: 10),
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorPrimary,
                      foregroundColor: colorPrimaryLightest,
                    ),
                    onPressed: passwordResetController.isChecking
                        ? null
                        : () async {
                            String mobileNo =
                                passwordResetController.mobileNoCtrl.text;
                            Tuple2<bool, String> checkResponse =
                                await passwordResetController
                                    .checkUserExistForReset();

                            if (!checkResponse.item1) {
                              showAlertDialog(context,
                                  alertType: AlertType.Error,
                                  alertTitle: "",
                                  message: checkResponse.item2);
                            } else {
                              Navigator.of(builderContext).pop();
                              Navigator.of(builderContext).pushNamed(
                                '/resetPassword',
                                arguments: ResetPasswordScreen(
                                  username: mobileNo,
                                  token: passwordResetController.token,
                                  mobileNo: passwordResetController.mobileNo,
                                ),
                              );
                            }
                          },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 40),
                      child: Text(
                        passwordResetController.isChecking
                            ? "Submitting.."
                            : "Submit",
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 15,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                );
              })
            ],
          ),
        ),
      ),
    );
  }
}
