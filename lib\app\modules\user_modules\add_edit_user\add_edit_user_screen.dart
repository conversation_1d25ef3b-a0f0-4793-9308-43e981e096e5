// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/model/others/user_create_model.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/add_edit_user/add_edit_user_controller.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/user_list/user_list_controller.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

// ignore: must_be_immutable
class AddEditUser extends StatefulWidget {
  final UserCreateModel? userData;
  final int? userId;

  AddEditUser({super.key, this.userData, this.userId});

  @override
  State<AddEditUser> createState() => _AddEditUserState();
}

class _AddEditUserState extends State<AddEditUser> {
  final String tag = "AddEditUser";

  @override
  void initState() {
    if (null == widget.userData) {
      addEditUserController.initAdd();
    } else {
      addEditUserController.initEdit(widget.userData!);
    }
    // TODO: implement initState
    super.initState();
  }

  var addEditUserController = AddEditUserController();

  bool passObs = true;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (addEditUserController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
        // resizeToAvoidBottomPadding: true,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          leading: BackButton(
            onPressed: () => Navigator.pop(context),
          ),
          centerTitle: false,
          titleSpacing: -5.0,
          title: Text(
            (!addEditUserController.editFlag) ? "Add User" : "Edit User",
            style: const TextStyle(
                fontSize: 20,
                color: Colors.white,
                fontFamily: 'HelveticaRegular',
                fontWeight: FontWeight.bold),
          ),
          actions: [
            if (addEditUserController.editFlag) ...{
              InkWell(
                  onTap: () => addEditUserController.readOnlyFlag =
                      !addEditUserController.readOnlyFlag,
                  splashColor: colorPrimaryLighter,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 15),
                    child: (addEditUserController.readOnlyFlag)
                        ? Column(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(
                                Icons.mode_edit,
                                color: Colors.white,
                              ),
                              Text(
                                "Edit",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          )
                        : Column(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(
                                Icons.close,
                                color: Colors.white,
                              ),
                              Text(
                                "Cancel",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          ),
                  )),
            }
          ],
        ),
        body: GestureDetector(
            onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
            child: Container(
                height: MediaQuery.of(context).size.height,
                padding: const EdgeInsets.all(20),
                child: Form(
                    key: addEditUserController.formKey,
                    child: Container(
                        decoration: const BoxDecoration(color: Colors.white),
                        child: SingleChildScrollView(
                            child: Column(children: [
                          //====================================FullName
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Text(
                                'प्रयोगकर्ताको नाम  (User’s Full Name) ',
                                style: RegformLabelStyle,
                              ),
                              const SizedBox(height: 10.0),
                              FormBuilderTextField(
                                name: 'full_name',
                                autocorrect: false,
                                readOnly: addEditUserController.readOnlyFlag,
                                keyboardType: TextInputType.text,
                                textInputAction: TextInputAction.next,
                                style: formFieldTextStyle,
                                decoration: formFieldStyle.copyWith(
                                  hintText: 'User Full Name',
                                ),
                                initialValue:
                                    addEditUserController.user.userFullName,
                                onChanged: (value) {
                                  addEditUserController.user.userFullName =
                                      toBeginningOfSentenceCase(
                                          strTrim(value!));
                                },
                                validator: (value) {
                                  if (value != null) {
                                    value = value.trim();
                                    if (value.isEmpty) {
                                      return 'प्रयोगकर्ताको नाम भर्नुहोस् (Fill User’s Name)';
                                    }
                                    return null;
                                  }
                                  return null;
                                },
                                // validators: [
                                //   FormBuilderValidators.required(
                                //       errorText:
                                //           "प्रयोगकर्ताको नाम भर्नुहोस् (Fill User’s Name)"),

                                // ],
                              ),
                            ],
                          ),

                          //====================================Mobile No
                          const SizedBox(height: 20.0),
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Text(
                                'प्रयोगकर्ताको मोबाईल नम्बर (User’s Mobile No.)',
                                style: RegformLabelStyle,
                              ),
                              const SizedBox(height: 10.0),
                              FormBuilderTextField(
                                name: 'mobile',
                                autocorrect: false,
                                readOnly: addEditUserController.readOnlyFlag,
                                keyboardType: TextInputType.number,
                                inputFormatters: [
                                  FilteringTextInputFormatter.digitsOnly
                                ],
                                textInputAction: TextInputAction.next,
                                maxLength: 10,
                                style: formFieldTextStyle,
                                decoration: formFieldStyle.copyWith(
                                    hintText: "Mobile No.", counterText: ''),
                                initialValue:
                                    addEditUserController.user.userName,
                                onChanged: (value) {
                                  addEditUserController.user.userName =
                                      strTrim(value!);
                                },
                                validator: (value) {
                                  if (value == null || value.isEmpty) {
                                    return 'मोबाईल नम्बर भर्नुहोस् (Fill Mobile No.)';
                                  }

                                  if (10 != value.length) {
                                    return 'अवैध मोबाइल नम्बर (Invalid Mobile No.)';
                                  }

                                  if ("98" != value.substring(0, 2)) {
                                    return 'अवैध मोबाइल नम्बर (Invalid Mobile No.)';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ),
                          if (!addEditUserController.editFlag) ...[
                            //====================================Password
                            const SizedBox(height: 20.0),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  'पासवर्ड (Password)',
                                  style: RegformLabelStyle,
                                ),
                                const SizedBox(height: 10.0),
                                FormBuilderTextField(
                                  name: 'password',
                                  autocorrect: false,
                                  keyboardType: TextInputType.text,
                                  textInputAction: TextInputAction.done,
                                  style: formFieldTextStyle,
                                  obscureText: passObs,
                                  decoration: formFieldStyle.copyWith(
                                      suffixIcon: IconButton(
                                        icon: Icon(
                                          passObs
                                              ? Icons.visibility
                                              : Icons.visibility_off,
                                          color: Colors.grey,
                                        ),
                                        onPressed: () {
                                          setState(() {
                                            passObs = !passObs;
                                          });
                                        },
                                      ),
                                      hintText: "Password.",
                                      counterText: ''),
                                  initialValue:
                                      addEditUserController.user.password,
                                  onChanged: (value) {
                                    addEditUserController.user.password =
                                        strTrim(value!);
                                  },
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'पासवर्ड भर्नुहोस् (Fill Password)';
                                    }

                                    return null;
                                  },
                                ),
                              ],
                            ),
                          ]
                        ])))))),
        //=================================================Save button
        bottomNavigationBar: BottomSaveCancelButton(
          shadow: false,
          enableFlag: !addEditUserController.readOnlyFlag,
          onSaveBtnPressedFn: (addEditUserController.readOnlyFlag)
              ? null
              : () async {
                  FocusScope.of(context).unfocus();
                  if (addEditUserController.formKey.currentState!.validate()) {
                    ProgressDialog progressDialog = ProgressDialog(context,
                        type: ProgressDialogType.normal, isDismissible: false);
                    progressDialog.update(
                        message: "Saving data. Please wait....");
                    await progressDialog.show();

                    Tuple2<bool, String> ctrlResp = const Tuple2(false, "");
                    try {
                      // if (null != addEditItemController.item.baseUnitId &&
                      //     null != addEditItemController.item.alternateUnitId) {
                      //   if (0.00 ==
                      //       (addEditItemController.item.unitConversionFactor ??
                      //           0.00)) {
                      //     throw new CustomException(
                      //         "रूपान्तरण दर भर्नुहोस्\n(Fill Conversion Rate)");
                      //   }
                      // }

                      if (!addEditUserController.editFlag) {
                        ctrlResp = await addEditUserController.createUser();
                      } else {
                        ctrlResp = await addEditUserController
                            .updateUser(widget.userId!);
                      }
                    } on CustomException catch (e) {
                      await progressDialog.hide();
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "Error",
                          message: e.toString());
                      return;
                    } catch (e, trace) {
                      // Log.e(tag, e.toString() + trace.toString());
                    }
                    await progressDialog.hide();

                    if (ctrlResp.item1) {
                      Navigator.pop(context, true);

                      //update user list
                      if (Get.isRegistered<UserListController>(
                          tag: "UserListController")) {
                        UserListController userListController =
                            Get.find(tag: "UserListController");
                        userListController.init();
                      }

                      String message = (addEditUserController.editFlag)
                          ? "User Updated Successfully."
                          : "User Created Successfully.";
                      showToastMessage(context, message: message, duration: 2);
                    } else {
                      showToastMessage(context,
                          alertType: AlertType.Error,
                          message: ctrlResp.item2,
                          duration: 2);
                    }
                  }
                },
        ),
      ));
    });
  }
}
