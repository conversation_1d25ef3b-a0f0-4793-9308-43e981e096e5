import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/pdf_view.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:printing/printing.dart';
// import 'package:new_pdf/preview.dart';

class PdfDemoPage extends StatelessWidget {
  const PdfDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        title: const Text('Pdf Demo'),
      ),
      body: SizedBox(
        width: double.infinity,
        height: double.infinity,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            ElevatedButton(
              child: const Icon(Icons.print),
              onPressed: () {
                _printPdf();
              },
            ),
            ElevatedButton(
              child: const Icon(Icons.share),
              onPressed: () {
                _sharePdf();
              },
            ),
            ElevatedButton(
              child: const Icon(Icons.picture_as_pdf),
              onPressed: () async {
                Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const Preview(),
                    ));
              },
            ),
          ],
        ),
      ),
    ));
  }

  Future<void> _sharePdf() async {
    final appDocDir = await getApplicationDocumentsDirectory();
    final appDocPath = appDocDir.path;
    final file = File('$appDocPath/document.pdf');
    Uint8List bytes = await file.readAsBytes();

    await Printing.sharePdf(bytes: bytes, filename: 'document.pdf');
  }

  Future<void> _printPdf() async {
    // final bytes = await build(pageFormat);

    final appDocDir = await getApplicationDocumentsDirectory();
    final appDocPath = appDocDir.path;
    final file = File('$appDocPath/document.pdf');
    Uint8List bytes = await file.readAsBytes();
    await Printing.layoutPdf(onLayout: (PdfPageFormat format) async => bytes);
  }
}
