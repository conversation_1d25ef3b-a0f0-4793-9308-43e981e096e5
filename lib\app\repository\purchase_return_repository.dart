import 'package:mobile_khaata_v2/app/model/database/transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/database/txn_image_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/purchase_return_model.dart';
import 'package:mobile_khaata_v2/app/repository/line_item_repository.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';
import 'package:mobile_khaata_v2/app/repository/txn_image_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:sqflite/sqflite.dart';

import 'package:tuple/tuple.dart';

class PurchaseReturnRepository {
  final String tag = "PurchaseReturnRepository";
  DatabaseHelper databaseHelper = DatabaseHelper();

  //get from transaction repository and return from here
  Future<List<PurchaseReturnModal>> getAllPurchaseReturn() async {
    List<PurchaseReturnModal> purchaseReturns = [];
    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> txnDataListJson = (await dbClient!.rawQuery(
          'SELECT * FROM mk_transactions WHERE txn_type=? AND last_activity_type!=?',
          [TxnType.purchaseReturn, LastActivityType.Delete]));
      purchaseReturns = txnDataListJson.map((txnData) {
        return PurchaseReturnModal.fromJson(txnData);
      }).toList();
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return purchaseReturns;
  }

  Future<
      Tuple3<PurchaseReturnModal, List<LineItemDetailModel>,
          List<TxnImageModel>>> getPurchaseReturnById(String? txnID) async {
    TransactionModel? txnData = TransactionModel();
    List<LineItemDetailModel> items = [];
    List<TxnImageModel> images = [];
    TransactionRepository transactionRepository = TransactionRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();
    LineItemRepository lineItemRepository = LineItemRepository();

    try {
      if (null != txnID) {
        txnData = await transactionRepository.getTransactionByTxnId(txnID);
        items =
            await lineItemRepository.getLineDetailItemsForTransaction(txnID);
        images = await txnImageRepository.getImagesForTransaction(txnID);
      } else {
        // Throw error for null id
      }
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return Tuple3(
        PurchaseReturnModal.fromJson(txnData!.toJson()), items, images);
  }

  Future<String?> addPurchaseReturn(PurchaseReturnModal purchaseReturnModal,
      List<LineItemDetailModel> listItem, List<TxnImageModel> images) async {
    String? status;
    TransactionRepository transactionRepository = TransactionRepository();
    LineItemRepository lineItemRepository = LineItemRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();

    try {
      Database? dbClient = await databaseHelper.database;
      await dbClient!.transaction((batch) async {
        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        String txnID = await transactionRepository.insert(
            TransactionModel.fromJson(purchaseReturnModal.toJson()),
            dbClient: batch,
            batchID: batchID);

        await lineItemRepository.setLineDetailItemsForTransaction(
            txnID, listItem,
            dbClient: batch, batchID: batchID);

        await txnImageRepository.setImagesForTransaction(txnID, images,
            dbClient: batch, batchID: batchID);

        // bool isSuccess = await pushPendingQueries(
        //     singleBatchId: batchID, source: "TRIGGER", dbClient: batch);

        // if (!isSuccess) {
        // throw CustomException("No Net");
        pushPendingQueries(
            singleBatchId: batchID, source: "TRIGGER", dbClient: batch);
        // }

        status = txnID;
      });
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }

  Future<bool> updatePurchaseReturn(PurchaseReturnModal purchaseReturnModal,
      List<LineItemDetailModel> listItem, List<TxnImageModel> images) async {
    bool status = false;
    TransactionRepository transactionRepository = TransactionRepository();
    LineItemRepository lineItemRepository = LineItemRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();

    try {
      Database? dbClient = await databaseHelper.database;

      await dbClient!.transaction((batch) async {
        // var batch = txn.batch();
        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        await transactionRepository.update(
            TransactionModel.fromJson(purchaseReturnModal.toJson()),
            dbClient: batch,
            batchID: batchID);

        await lineItemRepository.deleteLineItemsForTransaction(
            purchaseReturnModal.txnId ?? "",
            dbClient: batch,
            batchID: batchID);

        await lineItemRepository.setLineDetailItemsForTransaction(
            purchaseReturnModal.txnId ?? "", listItem,
            dbClient: batch, batchID: batchID);

        await txnImageRepository.deleteImagesForTransaction(
            purchaseReturnModal.txnId ?? "",
            dbClient: batch,
            batchID: batchID);

        await txnImageRepository.setImagesForTransaction(
            purchaseReturnModal.txnId ?? "", images,
            dbClient: batch, batchID: batchID);

        // bool isSuccess = await pushPendingQueries(
        //     singleBatchId: batchID, source: "TRIGGER", dbClient: batch);
        // if (!isSuccess) {
        // throw CustomException("No Net");
        pushPendingQueries(
            singleBatchId: batchID, source: "TRIGGER", dbClient: batch);
        // }
        // await batch.commit(continueOnError: false, noResult: true);

        status = true;
      });
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }

  Future<bool> isBillDuplicate(String? billID) async {
    bool status = false;
    try {
      Database? dbClient = await databaseHelper.database;

      int count = 0;

      if (null != billID && "" != billID) {
        count = Sqflite.firstIntValue(await dbClient!.rawQuery(
            'SELECT COUNT(txn_id) AS total_txn FROM mk_transactions WHERE txn_type=? AND last_activity_type!=3 AND txn_ref_number_char=? limit 1',
            [TxnType.purchaseReturn, billID]))!;
      }

      if (0 < count) {
        // Bill id exist
        status = true;
      } else {
        status = false;
      }
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }
}
