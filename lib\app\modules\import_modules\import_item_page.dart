import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/app/modules/import_modules/import_item_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class ImportItemPage extends StatelessWidget {
  final importController = ImportItemController();

  ImportItemPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        titleSpacing: -5.0,
        centerTitle: false,
        backgroundColor: colorPrimary,
        elevation: 0,
        title: const Text(
          "सामानहरु ईम्पोर्ट गर्नुस् \nImport Products/Items",
          // textAlign: TextAlign.center,
          style: TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
      ),
      body: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                "सामानहरु ईम्पोर्ट गर्ने तरिका ।",
                style: labelStyle2.copyWith(fontSize: 22),
                textAlign: TextAlign.center,
              ),
              Text(
                "Steps to Import Products/Items",
                style: labelStyle2.copyWith(fontSize: 20),
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 40,
              ),
              Text(
                "नमूना फाइल डाउनलोड गर्नुहोस् र छविमा देखाइएको अनुसार डाटा भर्नुहोस् ।\nकृपया फाइल प्रकार सुनिश्चित गर्नुहोस् । (.xlsx, .xls)",
                style: TextStyle(color: textColor, fontSize: 14),
                textAlign: TextAlign.center,
              ),
              Text(
                "\nDownload the sample file and fill the data as shown in image. \nPlease note the file extension. (.xlsx, .xls)",
                style: TextStyle(color: textColor, fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 20,
              ),
              Text(
                "**Please note that item name and code must be unique.**",
                textAlign: TextAlign.center,
                style: TextStyle(color: colorRedDark),
              ),
              const SizedBox(
                height: 20,
              ),
              Image.asset('images/itemExcelTemplate.png'),
              const SizedBox(
                height: 20,
              ),
              ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorPrimary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                  foregroundColor: colorPrimaryLightest,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 50,
                    vertical: 15,
                  ),
                ),
                child: Text(
                  "नमूना फाइल डाउनलोड गर्नुहोस्\nDownload Sample File",
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 15,
                      decoration: TextDecoration.underline),
                  textAlign: TextAlign.center,
                ),
                onPressed: () {
                  importController.saveTemplate(context);
                },
              ),
            ],
          )),
      bottomNavigationBar: ElevatedButton(
        // color: colorPrimary,
        // elevation: 10,
        // splashColor: colorPrimaryLightest,
        style: ElevatedButton.styleFrom(
          elevation: 10,
          backgroundColor: colorPrimary,
          foregroundColor: colorPrimaryLightest,
        ),
        child: const Padding(
          padding: EdgeInsets.symmetric(vertical: 10, horizontal: 40),
          child: Text(
            "SELECT FILE \n(.xlsx, .xls)",
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        onPressed: () {
          importController.importData(context);
        },
      ),
    ));
  }
}
