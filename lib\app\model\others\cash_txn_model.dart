import 'dart:convert';

import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class CashTxnModel {
  String? txnId;
  String? txnDate;
  String? txnDateBS;
  int? txnType;
  String? txnTypeText;
  String? txnDesc;
  double? txnAmount;
  int? cashTransactionType;
  String? cashTransactionDesc;
  String? cashTxnMode;

  CashTxnModel({
    this.txnId,
    this.txnDate,
    this.txnDateBS,
    this.txnType,
    this.txnTypeText,
    this.txnDesc,
    this.txnAmount,
    this.cashTransactionType,
    this.cashTransactionDesc,
    this.cashTxnMode,
  });

  factory CashTxnModel.fromJson(Map<String, dynamic> json) {
    DateTime txnDateTime = DateTime.parse(json["txn_date"]);
    String txnDate = DateFormat('y-MM-dd').format(txnDateTime);
    String txnDateBS = toDateBS(txnDateTime);

    int cashTransactionType =
        (0 <= json['txn_amount']) ? TxnType.addCash : TxnType.reduceCash;
    String txtTypeText = "";
    String? cashTransactionDesc = json["txn_desc"];

    if ('cheque' == json["mode"]) {
      txtTypeText = TxnType.txnTypeText[TxnType.chequeTransfer] ?? "";
    } else {
      txtTypeText = TxnType.txnTypeText[json["txn_type"]] ?? "";
      if ('bank' == json["mode"]) {
        cashTransactionDesc = json['bank_name'];
      }
    }

    return CashTxnModel(
      txnId: json["txn_id"],
      txnDate: txnDate,
      txnDateBS: txnDateBS,
      txnType: json["txn_type"],
      txnTypeText: txtTypeText,
      txnDesc: json["txn_desc"],
      txnAmount: json["txn_amount"],
      cashTransactionType: cashTransactionType,
      cashTransactionDesc: cashTransactionDesc,
      cashTxnMode: json["mode"],
    );
  }

  Map<String, dynamic> toJson() => {
        "txn_id": txnId,
        "txn_date": txnDate,
        "txn_date_bs": txnDateBS,
        "txn_type": txnType,
        "txn_type_text": txnTypeText,
        "txn_desc": txnDesc,
        "txn_amount": txnAmount,
      };

  String toString() {
    return jsonEncode(this.toJson());
  }
}
