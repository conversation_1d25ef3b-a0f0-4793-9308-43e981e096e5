import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/ledger_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';

class LedgerListController extends GetxController {
  var _isLoading = true.obs;
  var _isLoadingWithBalance = true.obs;

  List<LedgerModel> _ledgers = [];

  List<LedgerDetailModel> _ledgersWithBalance = [];

  final LedgerRepository _ledgerRepository = LedgerRepository();

  bool get isLoading => _isLoading.value;
  bool get isLoadingWithBalance => _isLoadingWithBalance.value;

  List<LedgerModel> get ledgers => _ledgers;
  List<LedgerDetailModel> get ledgerWithBalance => _ledgersWithBalance;

  fetchAll() async {
    _isLoading(true);
    _ledgers.clear();
    _ledgers.addAll(await _ledgerRepository.getAllLedgers());
    _isLoading(false);
  }

  fetchAllWithBalance() async {
    _isLoadingWithBalance(true);
    _ledgersWithBalance.clear();
    _ledgersWithBalance
        .addAll(await _ledgerRepository.getAllLedgersWithBalance());
    _isLoadingWithBalance(false);
  }

  searchByTitle(String title) async {
    _isLoading(true);
    _ledgers.clear();
    _ledgers.addAll(await _ledgerRepository.getLedgersByTitle(title));
    _isLoading(false);
  }

  searchByTitleWithBalance(String title, {List<String>? included}) async {
    _isLoading(true);
    _ledgersWithBalance.clear();
    _ledgersWithBalance.addAll(await _ledgerRepository
        .getAllGeneralLedgersWithBalanceByTitle(title, includedIDs: included));
    _isLoading(false);
  }
}
