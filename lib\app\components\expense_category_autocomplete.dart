// ignore_for_file: library_private_types_in_public_api
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/expense_category_list_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/expanse_category_model.dart';
import 'package:mobile_khaata_v2/app/repository/expense_category_repository.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

// ignore: must_be_immutable
class ExpensesCategoryAutoComplete extends StatefulWidget {
  bool enableFlag;
  String? labelText;
  TextEditingController? controller;
  Function? onChangedFn;
  Function? onSuggestionSelectedFn;
  String? categoryID;

  ExpensesCategoryAutoComplete(
      {super.key,
      this.enableFlag = true,
      this.labelText = "Select Category",
      this.controller,
      this.onChangedFn,
      this.onSuggestionSelectedFn,
      this.categoryID});

  @override
  _ExpensesCategoryAutoCompleteState createState() =>
      _ExpensesCategoryAutoCompleteState();
}

class _ExpensesCategoryAutoCompleteState
    extends State<ExpensesCategoryAutoComplete> {
  var expensesCategoryListController =
      Get.put(ExpensesCategoryListController());
  // ExpensesCategoryRepository expensesCategoryRepository = new ExpensesCategoryRepository();

  @override
  void initState() {
    expensesCategoryListController.fetchAll();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return TypeAheadField(
      // suggestionsBoxController: _suggestionsBoxController,
      textFieldConfiguration: TextFieldConfiguration(
        enabled: widget.enableFlag,
        controller: widget.controller,
        decoration: formFieldStyle.copyWith(labelText: widget.labelText),
        onChanged: (value) {
          widget.onChangedFn!(value);
          print("this is val $value");
        },
      ),
      suggestionsCallback: (pattern) async {
        List<ExpenseCategoryModel> results = expensesCategoryListController
            .categories
            .where((element) => element.expenseTitle!
                .toLowerCase()
                .contains(pattern.toLowerCase()))
            .toList();

        print("this is result $results");

        return results;
      },
      transitionBuilder: (context, suggestionsBox, controller) {
        return suggestionsBox;
      },
      noItemsFoundBuilder: (noItemCotext) {
        if (widget.controller!.text.isEmpty) {
          return const Padding(
            padding: EdgeInsets.all(8.0),
            child: Text("No Items Found"),
          );
        }
        return InkWell(
          onTap: () async {
            String categoryID = await ExpensesCategoryRepository()
                .createIfNotExistByCategoryName(widget.controller!.text);
            expensesCategoryListController.fetchAll();

            widget.onSuggestionSelectedFn!(ExpenseCategoryModel(
                expenseCategoryId: categoryID,
                expenseTitle: widget.controller!.text));
          },
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Text(
              "Add \"${widget.controller?.text}\"",
              style: labelStyle2,
            ),
          ),
        );
      },
      itemBuilder: (context, item) {
        return Column(
          children: [
            ListTile(
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
              title: Text(
                "${item.expenseTitle ?? ""}",
                style:
                    const TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
              ),
            ),
            const Divider(
              height: 0,
              thickness: 1,
            ),
          ],
        );
      },
      onSuggestionSelected: (item) => widget.onSuggestionSelectedFn!(item),
    );
  }
}
