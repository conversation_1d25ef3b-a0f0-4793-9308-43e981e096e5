import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/model/others/cheque_modal.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:sqflite/sqflite.dart';

class ChequeRepository {
  final String tag = "ChequeRepository";
  final String tableName = "mk_transactions";
  DatabaseHelper databaseHelper = DatabaseHelper();
  QueryRepository queryRepository = QueryRepository();

  //========================================================================================= SYNCING ACTIONS
  Future<bool> updateChequeStatus(ChequeModel chequeModel,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    chequeModel.lastActivityAt = currentDateTime;
    chequeModel.lastActivityBy = await getLastActivityBy();
    chequeModel.lastActivityType = LastActivityType.Edit;

    String whereClause = "txn_id = ?";
    List<dynamic> whereArgs = [chequeModel.txnId];

    Map<String, dynamic> updateData = {
      "cheque_current_status": chequeModel.chequeCurrentStatus,
      "cheque_transfer_date": chequeModel.chequeTransferDate,
      "cheque_transferred_to_acc_id": chequeModel.chequeTransferredToAccId,
      "last_activity_type": chequeModel.lastActivityType,
      "last_activity_at": chequeModel.lastActivityAt,
      "last_activity_by": chequeModel.lastActivityBy,
    };

    await dbClient.update(tableName, updateData,
        where: whereClause, whereArgs: whereArgs);

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.update,
      whereArgs: whereArgs,
      whereClause: whereClause,
      data: updateData,
    );
    await queryRepository.pushQuery(newQueryModel,
        dbClient: dbClient, batchID: batchID);

    pushPendingQueries(
        singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

    status = true;

    return status;
  }

  //========================================================================================= NON SYNCING ACTIONS
  Future<List<ChequeModel>> getAllCheque(int chequeStatus,
      {String? orderBy}) async {
    List<ChequeModel> chequeList = [];
    try {
      Database? dbClient = await databaseHelper.database;

      String chequeStatusType = " ";
      if (0 == chequeStatus) {
        chequeStatusType = " mk_transactions.cheque_current_status=0 AND ";
      } else if (1 == chequeStatus) {
        chequeStatusType = " mk_transactions.cheque_current_status=1 AND ";
      }

      String orderByQuery = " ORDER BY mk_transactions.cheque_issue_date ASC ";
      if ("amount" == orderBy) {
        orderByQuery = " ORDER BY mk_transactions.txn_cash_amount ASC ";
      }

      String query = "SELECT mk_transactions.*, "
          "mk_ledger_master.ledger_title, mk_expense_category.expense_title,  "
          "mk_payment_types.pmt_type_short_name AS cheque_transferred_to_acc "
          "FROM mk_transactions "
          "LEFT JOIN mk_ledger_master ON mk_ledger_master.ledger_id=mk_transactions.ledger_id "
          "LEFT JOIN mk_expense_category ON mk_expense_category.expense_category_id=mk_transactions.expense_category_id "
          "LEFT JOIN mk_payment_types ON mk_payment_types.pmt_type_id=mk_transactions.cheque_transferred_to_acc_id "
          "WHERE mk_transactions.txn_payment_type_id='$PAYMENT_MODE_CHEQUE_ID' AND mk_transactions.txn_cash_amount>0 AND $chequeStatusType mk_transactions.last_activity_type<>${LastActivityType.Delete} "
          " $orderByQuery ";

      List<Map<String, dynamic>> json = await dbClient!.rawQuery(query);
      chequeList = json.map((row) => ChequeModel.fromJson(row)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return chequeList;
  }

  Future<ChequeModel?> getChequeById(String chequeTxnId) async {
    ChequeModel? cheque;
    try {
      Database? dbClient = await databaseHelper.database;

      if (null != chequeTxnId) {
        String query = "SELECT mk_transactions.*, "
            "mk_ledger_master.ledger_title, mk_expense_category.expense_title "
            "FROM mk_transactions "
            "LEFT JOIN mk_ledger_master ON mk_ledger_master.ledger_id=mk_transactions.ledger_id "
            "LEFT JOIN mk_expense_category ON mk_expense_category.expense_category_id=mk_transactions.expense_category_id "
            "WHERE mk_transactions.txn_id=? AND mk_transactions.last_activity_type<>${LastActivityType.Delete} ";

        Map<String, dynamic> json =
            (await dbClient!.rawQuery(query, [chequeTxnId])).first;
        cheque = ChequeModel.fromJson(json);
      }
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return cheque;
  }
}
