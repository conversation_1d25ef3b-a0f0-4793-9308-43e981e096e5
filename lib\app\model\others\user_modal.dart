// ignore_for_file: unnecessary_brace_in_string_interps

import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class UserModel {
  String? userFullName;
  String? userName;
  int? userId;
  String? deviceName;
  String? deviceModel;
  String? lastActivityAt;
  String? lastActivityAtBS;
  int? isActive;
  List<String>? userPermissions;

  UserModel(
      {this.userFullName,
      this.userName,
      this.deviceName,
      this.deviceModel,
      this.isActive,
      this.lastActivityAt,
      this.lastActivityAtBS,
      this.userPermissions,
      this.userId});

  Map<String, dynamic> toJson() {
    return {
      'id': userId,
      'full_name': userFullName,
      'user_name': userName,
      'device_name': deviceName,
      'user_last_activity_at': lastActivityAt,
      'is_active': isActive,
      'device_model': deviceModel,
    };
  }

  factory UserModel.fromJson(Map<String, dynamic> json) {
    String? bsDateTime;
    if (null != json['user_last_activity_at']) {
      DateTime lastActivityDateTime =
          DateTime.parse(json["user_last_activity_at"]);
      bsDateTime = toDateTimeBS(lastActivityDateTime);
    }

    // Log.d(
    //     "user json $json ${json['id'].runtimeType} ${json['is_active'].runtimeType}");
    return UserModel(
        userId: json['id'],
        userFullName: json['full_name'].toString(),
        userName: json['user_name'].toString(),
        deviceName: json['device_name'].toString(),
        deviceModel: json['device_model'].toString(),
        lastActivityAt: json['user_last_activity_at'].toString(),
        userPermissions: (json['permissions'] ?? []).cast<String>(),
        lastActivityAtBS: bsDateTime,
        isActive: int.tryParse(json['is_active'].toString()));
  }
  @override
  String toString() {
    return "${userFullName} ${userName}";
  }
}
