// ignore_for_file: dead_code

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/item_autocomplete_textfield_with_add.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/sales_return_module/add_sale_return_bill_item/add_edit_sale_return_bill_item_controller.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class AddEditSaleReturnBilledItemScreenView extends StatelessWidget {
  final String tag = "Sales Item Add/Edit View";
  final LineItemDetailModel? lineItemModel;

  final salesReturnItemController = AddEditSaleReturnBillItemController();
  AddEditSaleReturnBilledItemScreenView({super.key, this.lineItemModel}) {
    if (lineItemModel != null) {
      salesReturnItemController.initEdit(lineItemModel!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (false) {
      } else {
        return Column(mainAxisSize: MainAxisSize.min, children: [
          Container(
            child: GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(),
              child: Container(
                child: Form(
                  key: salesReturnItemController.formKey,
                  child: Container(
                    child: Container(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 10),
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    //===============================================Item
                                    Expanded(
                                      flex: 2,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'सामान छन्नुहोस्',
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          ItemAutoCompleteTextFieldWithAdd(
                                              controller:
                                                  salesReturnItemController
                                                      .itemNameCtrl,
                                              onChangedFn: (value) {
                                                // state.selectedItem = null;
                                              },
                                              onSuggestionSelectedFn: (item) {
                                                salesReturnItemController
                                                    .itemOnSelectHandler(
                                                        item.itemId,
                                                        unitID: item.baseUnitId,
                                                        item: item);
                                                salesReturnItemController
                                                    .selectedItem = item;
                                              })
                                        ],
                                      ),
                                    ),

                                    SizedBox(
                                      width: 10,
                                    ),

                                    //===============================================Unit
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'एकाइ',
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          DropdownButtonFormField(
                                            isExpanded: true,
                                            value: salesReturnItemController
                                                .billedItem.lineItemUnitId,
                                            decoration: formFieldStyle.copyWith(
                                              hintText: "Unit",
                                            ),
                                            items: salesReturnItemController
                                                .unitList
                                                .map((billedItemUnit) {
                                              return DropdownMenuItem(
                                                value: billedItemUnit.unitId,
                                                child: Text(
                                                    "${billedItemUnit.unitName} (${billedItemUnit.unitShortName})"),
                                              );
                                            }).toList(),
                                            onChanged: (value) =>
                                                salesReturnItemController
                                                    .unitOnSelectHandler(
                                                        value!),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 10,
                                ),

                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    //===============================================Quantity
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'परिमाण',
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          FormBuilderTextField(
                                            name: "qty",
                                            autocorrect: false,
                                            keyboardType:
                                                TextInputType.numberWithOptions(
                                                    decimal: true),
                                            inputFormatters: [
                                              FilteringTextInputFormatter.allow(
                                                  RegExp(r'^(\d+)?\.?\d{0,2}'))
                                            ],
                                            textInputAction:
                                                TextInputAction.next,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                labelText: "Quantity"),
                                            textAlign: TextAlign.end,
                                            controller:
                                                salesReturnItemController
                                                    .qtyCtrl,
                                            onChanged: (value) =>
                                                salesReturnItemController
                                                    .qtyOnChangeHandler(value),
                                          ),
                                        ],
                                      ),
                                    ),

                                    SizedBox(
                                      width: 10,
                                    ),

                                    //===============================================Price/Rate
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'मूल्य प्रति एकाइ',
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          FormBuilderTextField(
                                              name: "rate",
                                              autocorrect: false,
                                              keyboardType: TextInputType
                                                  .numberWithOptions(
                                                      decimal: true),
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .allow(RegExp(
                                                        r'^(\d+)?\.?\d{0,2}'))
                                              ],
                                              textInputAction:
                                                  TextInputAction.done,
                                              style: formFieldTextStyle,
                                              decoration:
                                                  formFieldStyle.copyWith(
                                                      labelText: "Price/Unit"),
                                              textAlign: TextAlign.end,
                                              controller:
                                                  salesReturnItemController
                                                      .rateCtrl,
                                              onChanged: (value) {
                                                // salesReturnItemController
                                                //         .rateCtrl.selection =
                                                //     TextSelection.fromPosition(
                                                //         TextPosition(
                                                //             offset:
                                                //                 salesReturnItemController
                                                //                     .rateCtrl
                                                //                     .text
                                                //                     .length));salesReturnItemController
                                                //         .rateCtrl.selection =
                                                //     TextSelection.fromPosition(
                                                //         TextPosition(
                                                //             offset:
                                                //                 salesReturnItemController
                                                //                     .rateCtrl
                                                //                     .text
                                                //                     .length));

                                                salesReturnItemController
                                                    .rateOnChangeHandler(value);
                                              }),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 10,
                                ),

                                //===============================================Amount
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'रकम',
                                      style: labelStyle2,
                                    ),
                                    SizedBox(height: 5.0),
                                    FormBuilderTextField(
                                        name: "amount",
                                        autocorrect: false,
                                        keyboardType:
                                            TextInputType.numberWithOptions(
                                                decimal: true),
                                        inputFormatters: [
                                          FilteringTextInputFormatter.allow(
                                              RegExp(r'^(\d+)?\.?\d{0,2}'))
                                        ],
                                        textInputAction: TextInputAction.done,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Amount"),
                                        textAlign: TextAlign.end,
                                        controller: salesReturnItemController
                                            .grossAmountCtrl,
                                        onChanged: (value) {
                                          salesReturnItemController
                                                  .grossAmountCtrl.selection =
                                              TextSelection.fromPosition(
                                                  TextPosition(
                                                      offset:
                                                          salesReturnItemController
                                                              .grossAmountCtrl
                                                              .text
                                                              .length));
                                          if (value != null &&
                                              value.isNotEmpty) {
                                            salesReturnItemController
                                                .amountOnChangeHandler(value);
                                          }
                                        }),
                                  ],
                                ),
                                SizedBox(
                                  height: 15,
                                ),

                                // =============================================Discount
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "छुट (Discount)",
                                      style: labelStyle2,
                                    ),
                                    SizedBox(height: 5.0),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Container(
                                          width: 80,
                                          child: FormBuilderTextField(
                                            name: "txn_discount_percent",
                                            readOnly:
                                                ((salesReturnItemController
                                                                .billedItem
                                                                .grossAmount ??
                                                            0.00) >
                                                        0.00)
                                                    ? false
                                                    : true,
                                            autocorrect: false,
                                            keyboardType:
                                                TextInputType.numberWithOptions(
                                                    decimal: true),
                                            textInputAction:
                                                TextInputAction.done,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                suffix: Text("%"),
                                                labelText: "%"),
                                            textAlign: TextAlign.end,
                                            controller:
                                                salesReturnItemController
                                                    .discountPercentageCtrl,
                                            onChanged: (value) {
                                              salesReturnItemController
                                                      .discountPercentageCtrl
                                                      .selection =
                                                  TextSelection.fromPosition(
                                                      TextPosition(
                                                          offset: salesReturnItemController
                                                              .discountPercentageCtrl
                                                              .text
                                                              .length));
                                              if (value != null &&
                                                  value.isNotEmpty) {
                                                salesReturnItemController
                                                    .discountPercentOnChangeHandler(
                                                        value);
                                              }
                                            },
                                          ),
                                        ),
                                        SizedBox(
                                          width: 20,
                                        ),
                                        Expanded(
                                          child: Container(
                                            child: FormBuilderTextField(
                                              name: "txn_discount_amount",
                                              autocorrect: false,
                                              readOnly:
                                                  ((salesReturnItemController
                                                                  .billedItem
                                                                  .grossAmount ??
                                                              0.00) >
                                                          0.00)
                                                      ? false
                                                      : true,
                                              keyboardType: TextInputType
                                                  .numberWithOptions(
                                                      decimal: true),
                                              textInputAction:
                                                  TextInputAction.done,
                                              style: formFieldTextStyle,
                                              decoration: formFieldStyle.copyWith(
                                                  labelText:
                                                      "छुट रकम (Dis. Amount)"),
                                              textAlign: TextAlign.end,
                                              controller:
                                                  salesReturnItemController
                                                      .discountAmountCtrl,
                                              onChanged: (value) {
                                                salesReturnItemController
                                                        .discountAmountCtrl
                                                        .selection =
                                                    TextSelection.fromPosition(
                                                        TextPosition(
                                                            offset: salesReturnItemController
                                                                .discountAmountCtrl
                                                                .text
                                                                .length));
                                                if (value != null &&
                                                    value.isNotEmpty) {
                                                  salesReturnItemController
                                                      .onDiscountAmoutChange(
                                                          value);
                                                }
                                              },
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 10,
                                ),

                                //===============================================Net Amount
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'खुद रकम',
                                      style: labelStyle2,
                                    ),
                                    SizedBox(height: 5.0),
                                    FormBuilderTextField(
                                      name: "amount",
                                      autocorrect: false,
                                      readOnly: true,
                                      keyboardType:
                                          TextInputType.numberWithOptions(
                                              decimal: true),
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp(r'^(\d+)?\.?\d{0,2}'))
                                      ],
                                      textInputAction: TextInputAction.done,
                                      style: formFieldTextStyle,
                                      decoration: formFieldStyle.copyWith(
                                          labelText: "Net Amount"),
                                      textAlign: TextAlign.end,
                                      controller: salesReturnItemController
                                          .netAmountCtrl,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          BottomSaveCancelButton(
            shadow: false,
            onSaveBtnPressedFn: () {
              if (salesReturnItemController.formKey.currentState!.validate()) {
                //remove null id validation ,
                // if (null == salesReturnItemController.selectedItem.itemId) {
                //   showToastMessage(context,
                //       message: "सामान खाली राख्न मिल्दैन |",
                //       alertType: AlertType.Normal);
                //   return;
                // }

                if (salesReturnItemController.itemNameCtrl.text.isEmpty) {
                  showToastMessage(context,
                      message: "सामान खाली राख्न मिल्दैन |",
                      alertType: AlertType.Error);
                  return;
                }

                if (salesReturnItemController.qtyCtrl.text.isEmpty ||
                    0 >= parseDouble(salesReturnItemController.qtyCtrl.text)!) {
                  showToastMessage(context,
                      message:
                          "परिमाण (Quantity) खाली वा शून्य राख्न मिल्दैन |",
                      alertType: AlertType.Error);
                  return;
                }

                if (salesReturnItemController.rateCtrl.text.isEmpty ||
                    0 >=
                        parseDouble(salesReturnItemController.rateCtrl.text)!) {
                  showToastMessage(context,
                      message: "मूल्य (Price) खाली वा शून्य राख्न मिल्दैन |",
                      alertType: AlertType.Error);
                  return;
                }
                if ((salesReturnItemController.billedItem.discountPercent ??
                        0.00) >
                    100) {
                  showToastMessage(context,
                      message:
                          "छुट १००% भन्दा ठूलो हुन सक्दैन | \nDiscount can't be greater than 100%.",
                      alertType: AlertType.Error);
                  return;
                }

                salesReturnItemController.billedItem.itemId =
                    salesReturnItemController.selectedItem.itemId;
                salesReturnItemController.billedItem.itemName =
                    strTrim(salesReturnItemController.itemNameCtrl.text);

                salesReturnItemController.billedItem.lineItemUnitId =
                    salesReturnItemController.selectedUnit.unitId;
                salesReturnItemController.billedItem.lineItemUnitName =
                    salesReturnItemController.selectedUnit.unitShortName;

                salesReturnItemController.billedItem.pricePerUnit =
                    (parseDouble(salesReturnItemController.rateCtrl.text) ??
                        0.00);
                salesReturnItemController.billedItem.quantity =
                    parseDouble(salesReturnItemController.qtyCtrl.text) ?? 0.00;

                if (salesReturnItemController
                    .billedItem.discountPercent!.isNaN) {
                  salesReturnItemController.billedItem.discountPercent = 0.00;
                  salesReturnItemController.discountPercentageCtrl.text =
                      "0.00";
                }
                // salesReturnItemController.discountPercentageCtrl.text == "0.00";

                salesReturnItemController.billedItem.discountPercent = 0.00;

                if (salesReturnItemController
                    .billedItem.discountAmount!.isNaN) {
                  salesReturnItemController.billedItem.discountAmount = 0.00;
                }

                salesReturnItemController.billedItem.grossAmount = (parseDouble(
                        salesReturnItemController.grossAmountCtrl.text) ??
                    0.00);
                salesReturnItemController.billedItem.totalAmount = (parseDouble(
                        salesReturnItemController.netAmountCtrl.text) ??
                    0.00);

                AddSaleBilledItemResponse _addSaleBilledItemResponse =
                    AddSaleBilledItemResponse(
                        newFlag: false,
                        billedItem: salesReturnItemController.billedItem);
                Navigator.pop(context, _addSaleBilledItemResponse);
              }
            },
          ),
        ]);
      }
    });
  }
}

// // ignore_for_file: dead_code

// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_form_builder/flutter_form_builder.dart';
// import 'package:get/get.dart';
// import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
// import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
// import 'package:mobile_khaata_v2/app/components/item_autocomplete_textfield_with_add.dart';
// import 'package:mobile_khaata_v2/app/model/database/item_modal.dart';
// import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
// import 'package:mobile_khaata_v2/app/modules/sales_return_module/add_sale_return_bill_item/add_edit_sale_return_bill_item_controller.dart';
// import 'package:mobile_khaata_v2/utilities/common_helper.dart';
// import 'package:mobile_khaata_v2/utilities/styles.dart';

// class AddEditSaleReturnBilledItemScreenView extends StatelessWidget {
//   final String tag = "Sales Item Add/Edit View";
//   final LineItemDetailModel? lineItemModel;

//   final salesReturnItemController = AddEditSaleReturnBillItemController();
//   AddEditSaleReturnBilledItemScreenView({super.key, this.lineItemModel}) {
//     if (lineItemModel != null) {
//       salesReturnItemController.initEdit(lineItemModel!);
//     }
//   }
//   @override
//   Widget build(BuildContext context) {
//     return Obx(() {
//       if (false) {
//       } else {
//         return Column(mainAxisSize: MainAxisSize.min, children: [
//           GestureDetector(
//             onTap: () => FocusScope.of(context).unfocus(),
//             child: Form(
//               key: salesReturnItemController.formKey,
//               child: Column(
//                 mainAxisSize: MainAxisSize.min,
//                 children: [
//                   Container(
//                     padding: const EdgeInsets.symmetric(
//                         horizontal: 10, vertical: 10),
//                     child: Column(
//                       children: [
//                         Row(
//                           children: [
//                             //===============================================Item
//                             Expanded(
//                               flex: 2,
//                               child: Column(
//                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                 children: [
//                                   Text(
//                                     'सामान छन्नुहोस्',
//                                     style: labelStyle2,
//                                   ),
//                                   const SizedBox(height: 5.0),
//                                   ItemAutoCompleteTextFieldWithAdd(
//                                       itemID: salesReturnItemController
//                                           .selectedItem.itemId,
//                                       controller: salesReturnItemController
//                                           .itemNameCtrl,
//                                       onChangedFn: (value) {
//                                         // state.selectedItem = null;
//                                       },
//                                       onSuggestionSelectedFn: (ItemModel item) {
//                                         salesReturnItemController
//                                             .itemOnSelectHandler(item.itemId,
//                                                 unitID: item.baseUnitId ?? "",
//                                                 item: item);

//                                         salesReturnItemController
//                                                 .rateCtrl.text =
//                                             item.itemSaleUnitPrice.toString();
//                                       })
//                                 ],
//                               ),
//                             ),

//                             const SizedBox(
//                               width: 10,
//                             ),

//                             //===============================================Unit
//                             Expanded(
//                               flex: 1,
//                               child: Column(
//                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                 children: [
//                                   Text(
//                                     'एकाइ',
//                                     style: labelStyle2,
//                                   ),
//                                   const SizedBox(height: 5.0),
//                                   DropdownButtonFormField(
//                                     isExpanded: true,
//                                     value: salesReturnItemController
//                                         .billedItem.lineItemUnitId,
//                                     decoration: formFieldStyle.copyWith(
//                                       hintText: "Unit",
//                                     ),
//                                     items: salesReturnItemController.unitList
//                                         .map((billedItemUnit) {
//                                       return DropdownMenuItem(
//                                         value: billedItemUnit.unitId,
//                                         child: Text(
//                                             "${billedItemUnit.unitName} (${billedItemUnit.unitShortName})"),
//                                       );
//                                     }).toList(),
//                                     onChanged: (value) {
//                                       if (value != null && value.isNotEmpty) {
//                                         salesReturnItemController
//                                             .unitOnSelectHandler(value);
//                                       }
//                                     },
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           ],
//                         ),
//                         const SizedBox(
//                           height: 15,
//                         ),

//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             //===============================================Quantity
//                             Expanded(
//                               flex: 1,
//                               child: Column(
//                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                 children: [
//                                   Text(
//                                     'परिमाण',
//                                     style: labelStyle2,
//                                   ),
//                                   const SizedBox(height: 5.0),
//                                   FormBuilderTextField(
//                                     name: "qty",
//                                     autocorrect: false,
//                                     keyboardType:
//                                         const TextInputType.numberWithOptions(
//                                             decimal: true),
//                                     inputFormatters: [
//                                       FilteringTextInputFormatter.allow(
//                                           RegExp(r'^(\d+)?\.?\d{0,2}'))
//                                     ],
//                                     textInputAction: TextInputAction.next,
//                                     style: formFieldTextStyle,
//                                     decoration: formFieldStyle.copyWith(
//                                         labelText: "Quantity"),
//                                     textAlign: TextAlign.end,
//                                     controller:
//                                         salesReturnItemController.qtyCtrl,
//                                     onChanged: (value) {
//                                       if (value != null && value.isNotEmpty) {
//                                         salesReturnItemController
//                                             .grossAmountCtrl
//                                             .text = (double.parse(
//                                                     salesReturnItemController
//                                                         .rateCtrl.text) *
//                                                 double.parse(value))
//                                             .toString();
//                                         salesReturnItemController
//                                             .recalculateDataForItem();

//                                         salesReturnItemController
//                                             .netAmountCtrl.text = (double.parse(
//                                                     salesReturnItemController
//                                                         .rateCtrl.text) *
//                                                 double.parse(value))
//                                             .toString();
//                                       }
//                                     },
//                                   ),
//                                 ],
//                               ),
//                             ),

//                             const SizedBox(
//                               width: 10,
//                             ),

//                             //===============================================Price/Rate
//                             Expanded(
//                               flex: 1,
//                               child: Column(
//                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                 children: [
//                                   Text(
//                                     'मूल्य प्रति एकाइ',
//                                     style: labelStyle2,
//                                   ),
//                                   const SizedBox(height: 5.0),
//                                   FormBuilderTextField(
//                                     name: "rate",
//                                     autocorrect: false,
//                                     keyboardType:
//                                         const TextInputType.numberWithOptions(
//                                             decimal: true),
//                                     inputFormatters: [
//                                       FilteringTextInputFormatter.allow(
//                                           RegExp(r'^(\d+)?\.?\d{0,2}'))
//                                     ],
//                                     textInputAction: TextInputAction.done,
//                                     style: formFieldTextStyle,
//                                     decoration: formFieldStyle.copyWith(
//                                         labelText: "Price/Unit"),
//                                     textAlign: TextAlign.end,
//                                     controller:
//                                         salesReturnItemController.rateCtrl,
//                                     onChanged: (value) {
//                                       if (value != null && value.isNotEmpty) {
//                                         salesReturnItemController
//                                             .grossAmountCtrl
//                                             .text = (double.parse(
//                                                     salesReturnItemController
//                                                         .rateCtrl.text) *
//                                                 double.parse(value))
//                                             .toString();
//                                       }

//                                       // salesReturnItemController.
//                                       ///yaha lekhne
//                                       // salesReturnItemController
//                                       //         .grossAmountCtrl =
//                                       //     salesReturnItemController.qtyCtrl;
//                                     },
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           ],
//                         ),
//                         const SizedBox(
//                           height: 15,
//                         ),

//                         //===============================================Amount
//                         Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             Text(
//                               'रकम',
//                               style: labelStyle2,
//                             ),
//                             const SizedBox(height: 5.0),
//                             FormBuilderTextField(
//                               name: "amount",
//                               autocorrect: false,
//                               readOnly: true,
//                               keyboardType:
//                                   const TextInputType.numberWithOptions(
//                                       decimal: true),
//                               inputFormatters: [
//                                 FilteringTextInputFormatter.allow(
//                                     RegExp(r'^(\d+)?\.?\d{0,2}'))
//                               ],
//                               textInputAction: TextInputAction.done,
//                               style: formFieldTextStyle,
//                               decoration:
//                                   formFieldStyle.copyWith(labelText: "Amount"),
//                               textAlign: TextAlign.end,
//                               controller:
//                                   salesReturnItemController.grossAmountCtrl,
//                             ),
//                           ],
//                         ),
//                         const SizedBox(
//                           height: 15,
//                         ),

//                         // =============================================Discount
//                         Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             Text(
//                               "छुट (Discount)",
//                               style: labelStyle2,
//                             ),
//                             const SizedBox(height: 5.0),
//                             Row(
//                               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                               children: [
//                                 SizedBox(
//                                   width: 80,
//                                   child: FormBuilderTextField(
//                                     name: "txn_discount_percent",
//                                     autocorrect: false,
//                                     keyboardType:
//                                         const TextInputType.numberWithOptions(
//                                             decimal: true),
//                                     textInputAction: TextInputAction.done,
//                                     style: formFieldTextStyle,
//                                     decoration: formFieldStyle.copyWith(
//                                         suffix: const Text("%"),
//                                         labelText: "%"),
//                                     textAlign: TextAlign.end,
//                                     controller: salesReturnItemController
//                                         .discountPercentageCtrl,
//                                     onChanged: (value) {
//                                       if (value != null && value.isNotEmpty) {
//                                         final temp =
//                                             (double.parse(value.trim()) *
//                                                 double.parse(
//                                                     salesReturnItemController
//                                                         .grossAmountCtrl.text) /
//                                                 100);
//                                         salesReturnItemController
//                                             .discountAmountCtrl
//                                             .text = temp.toString();

//                                         salesReturnItemController
//                                             .netAmountCtrl.text = (double.parse(
//                                                     salesReturnItemController
//                                                         .grossAmountCtrl.text) -
//                                                 temp)
//                                             .toStringAsFixed(2);
//                                       }
//                                     },
//                                   ),
//                                 ),
//                                 const SizedBox(
//                                   width: 20,
//                                 ),
//                                 Expanded(
//                                   child: FormBuilderTextField(
//                                     name: "txn_discount_amount",
//                                     autocorrect: false,
//                                     readOnly: true,
//                                     keyboardType:
//                                         const TextInputType.numberWithOptions(
//                                             decimal: true),
//                                     textInputAction: TextInputAction.done,
//                                     style: formFieldTextStyle,
//                                     decoration: formFieldStyle.copyWith(
//                                         labelText: "छुट रकम (Dis. Amount)"),
//                                     textAlign: TextAlign.end,
//                                     controller: salesReturnItemController
//                                         .discountAmountCtrl,
//                                   ),
//                                 ),
//                               ],
//                             ),
//                           ],
//                         ),
//                         const SizedBox(
//                           height: 10,
//                         ),

//                         //===============================================Net Amount
//                         Column(
//                           crossAxisAlignment: CrossAxisAlignment.start,
//                           children: [
//                             Text(
//                               'खुद रकम',
//                               style: labelStyle2,
//                             ),
//                             const SizedBox(height: 5.0),
//                             FormBuilderTextField(
//                               name: "amount",
//                               autocorrect: false,
//                               readOnly: true,
//                               keyboardType:
//                                   const TextInputType.numberWithOptions(
//                                       decimal: true),
//                               inputFormatters: [
//                                 FilteringTextInputFormatter.allow(
//                                     RegExp(r'^(\d+)?\.?\d{0,2}'))
//                               ],
//                               textInputAction: TextInputAction.done,
//                               style: formFieldTextStyle,
//                               decoration: formFieldStyle.copyWith(
//                                   labelText: "Net Amount"),
//                               textAlign: TextAlign.end,
//                               controller:
//                                   salesReturnItemController.netAmountCtrl,
//                             ),
//                           ],
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//           BottomSaveCancelButton(
//             shadow: false,
//             onSaveBtnPressedFn: () {
//               if (salesReturnItemController.formKey.currentState!.validate()) {
//                 if (null == salesReturnItemController.selectedItem.itemId) {
//                   showToastMessage(context,
//                       message: "सामान खाली राख्न मिल्दैन |",
//                       alertType: AlertType.Normal);
//                   return;
//                 }

//                 if (salesReturnItemController.itemNameCtrl.text.isEmpty) {
//                   showToastMessage(context,
//                       message: "सामान खाली राख्न मिल्दैन |",
//                       alertType: AlertType.Error);
//                   return;
//                 }

//                 if (salesReturnItemController.qtyCtrl.text.isEmpty ||
//                     0 >= parseDouble(salesReturnItemController.qtyCtrl.text)!) {
//                   showToastMessage(context,
//                       message:
//                           "परिमाण (Quantity) खाली वा शून्य राख्न मिल्दैन |",
//                       alertType: AlertType.Error);
//                   return;
//                 }

//                 if (salesReturnItemController.rateCtrl.text.isEmpty ||
//                     0 >=
//                         parseDouble(salesReturnItemController.rateCtrl.text)!) {
//                   showToastMessage(context,
//                       message: "मूल्य (Price) खाली वा शून्य राख्न मिल्दैन |",
//                       alertType: AlertType.Error);
//                   return;
//                 }

//                 salesReturnItemController.billedItem.itemId =
//                     salesReturnItemController.selectedItem.itemId;
//                 salesReturnItemController.billedItem.itemName =
//                     strTrim(salesReturnItemController.itemNameCtrl.text);

//                 salesReturnItemController.billedItem.lineItemUnitId =
//                     salesReturnItemController.selectedUnit.unitId;
//                 salesReturnItemController.billedItem.lineItemUnitName =
//                     salesReturnItemController.selectedUnit.unitShortName;

//                 salesReturnItemController.billedItem.pricePerUnit =
//                     parseDouble(salesReturnItemController.rateCtrl.text);
//                 salesReturnItemController.billedItem.quantity =
//                     parseDouble(salesReturnItemController.qtyCtrl.text);
//                 salesReturnItemController.billedItem.discountPercent =
//                     parseDouble(
//                         salesReturnItemController.discountPercentageCtrl.text);
//                 salesReturnItemController.billedItem.discountAmount =
//                     parseDouble(
//                         salesReturnItemController.discountAmountCtrl.text);
//                 salesReturnItemController.billedItem.grossAmount =
//                     parseDouble(salesReturnItemController.grossAmountCtrl.text);
//                 salesReturnItemController.billedItem.totalAmount =
//                     parseDouble(salesReturnItemController.netAmountCtrl.text);

//                 AddSaleBilledItemResponse addSaleBilledItemResponse =
//                     AddSaleBilledItemResponse(
//                         newFlag: false,
//                         billedItem: salesReturnItemController.billedItem);
//                 Navigator.pop(context, addSaleBilledItemResponse);
//               }
//             },
//           ),
//         ]);
//       }
//     });
//   }
// }
