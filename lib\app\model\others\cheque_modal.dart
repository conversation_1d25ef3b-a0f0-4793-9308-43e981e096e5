import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';

class ChequeModel {
  ChequeModel({
    this.txnId,
    this.txnRefNumberChar,
    this.txnDate,
    this.txnDateBS,
    this.txnCashAmount,
    this.txnPartyName,
    this.txnType,
    this.txnTypeText,
    this.txnPaymentTypeId,
    this.txnPaymentReference,
    this.chequeCurrentStatus,
    this.chequeCurrentStatusText,
    this.chequeIssueDate,
    this.chequeIssueDateBS,
    this.chequeTransferDate,
    this.chequeTransferDateBS,
    this.chequeTransferredToAccId,
    this.chequeTransferredToAcc,
    this.lastActivityType,
    this.lastActivityAt,
    this.lastActivityBy,
    this.chequeTxnType,
  });

  String? txnId;
  String? txnRefNumberChar;
  String? txnDate;
  String? txnDateBS;
  double? txnCashAmount;
  String? txnPartyName;
  int? txnType;
  String? txnTypeText;
  String? txnPaymentTypeId;
  String? txnPaymentReference;
  int? chequeCurrentStatus;
  String? chequeCurrentStatusText;
  String? chequeIssueDate;
  String? chequeIssueDateBS;
  String? chequeTransferDate;
  String? chequeTransferDateBS;
  String? chequeTransferredToAccId;
  String? chequeTransferredToAcc;

  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;
  String? chequeTxnType;

  factory ChequeModel.fromJson(Map<String, dynamic> json) {
    DateTime? txnDateTimeObj = DateTime.tryParse(json["txn_date"]);
    DateTime? chequeIssueDateObj =
        DateTime.tryParse(json["cheque_issue_date"] ?? "");
    DateTime? chequeTransferDateTimeObj =
        DateTime.tryParse(json["cheque_transfer_date"] ?? "");

    String chequeCurrentStatusText = "";
    if (0 == json["cheque_current_status"]) {
      chequeCurrentStatusText = "Open";
    } else {
      if (TxnType.cashInTransaction.contains(json["txn_type"])) {
        if (PAYMENT_MODE_CASH_ID == json["cheque_transferred_to_acc_id"]) {
          chequeCurrentStatusText = "Added in Cash-in-hand";
        } else {
          chequeCurrentStatusText =
              "Deposit to ${json["cheque_transferred_to_acc"]}";
        }
      } else {
        chequeCurrentStatusText =
            "Withdrawn from ${json["cheque_transferred_to_acc"]}";
      }
    }

    return ChequeModel(
      txnId: json["txn_id"],
      txnRefNumberChar: json["txn_ref_number_char"],
      txnDate: DateFormat('y-MM-dd').format(txnDateTimeObj!),
      txnDateBS: toDateBS(txnDateTimeObj),
      txnCashAmount: parseDouble(json["txn_cash_amount"]),
      txnPartyName: json['txn_display_name'] ?? json['ledger_title'],
      txnType: json["txn_type"],
      txnTypeText: TxnType.txnTypeText[json["txn_type"]],
      txnPaymentTypeId: json["txn_payment_type_id"],
      txnPaymentReference: json["txn_payment_reference"],
      chequeCurrentStatus: json["cheque_current_status"],
      chequeCurrentStatusText: chequeCurrentStatusText,
      chequeIssueDate: (null != chequeIssueDateObj)
          ? DateFormat('y-MM-dd').format(chequeIssueDateObj)
          : '',
      chequeIssueDateBS:
          (null != chequeIssueDateObj) ? toDateBS(chequeIssueDateObj) : '',
      chequeTransferDate: (null != chequeTransferDateTimeObj)
          ? DateFormat('y-MM-dd').format(chequeTransferDateTimeObj)
          : '',
      chequeTransferDateBS: (null != chequeTransferDateTimeObj)
          ? toDateBS(chequeTransferDateTimeObj)
          : '',
      chequeTransferredToAccId: json["cheque_transferred_to_acc_id"],
      chequeTransferredToAcc: json["cheque_transferred_to_acc"],
      lastActivityType: json["last_activity_type"],
      lastActivityAt: json["last_activity_at"],
      lastActivityBy: json["last_activity_by"],
      chequeTxnType: (TxnType.cashInTransaction.contains(json["txn_type"]))
          ? 'Deposit'
          : 'Withdraw',
    );
  }

  Map<String, dynamic> toJson() => {
        "txn_id": txnId,
        "txn_ref_number_char": txnRefNumberChar,
        "txn_date": txnDate,
        "txn_cash_amount": txnCashAmount,
        "txn_party_name": txnPartyName,
        "txn_type": txnType,
        "txn_type_text": txnTypeText,
        "txn_payment_type_id": txnPaymentTypeId,
        "txn_payment_reference": txnPaymentReference,
        "cheque_current_status": chequeCurrentStatus,
        "cheque_current_status_text": chequeCurrentStatusText,
        "cheque_issue_date": chequeIssueDate,
        "cheque_transfer_date": chequeTransferDate,
        "cheque_transferred_to_acc_id": chequeTransferredToAccId,
        "cheque_transferred_to_acc": chequeTransferredToAcc,
        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        "last_activity_by": lastActivityBy,
        "cheque_txn_type": chequeTxnType,
      };
}
