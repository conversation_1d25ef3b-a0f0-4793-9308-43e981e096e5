import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/expanse_model.dart';
import 'package:mobile_khaata_v2/app/repository/expense_repository.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:nepali_utils/nepali_utils.dart';

class ExpensesListController extends GetxController {
  var _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  var _isLoadingForCategory = true.obs;
  bool get isLoadingForCategory => _isLoadingForCategory.value;

  var _expenses = <ExpenseModel>[].obs;
  List<ExpenseModel> get expenses => _expenses;

  var _expensesForSingleCategory = <ExpenseModel>[].obs;
  List<ExpenseModel> get expensesForSingleCategory =>
      _expensesForSingleCategory;

  var _selectedADDate = currentDate;
  var _selectedCategoryId = "";

  String get selectedCategoryId => _selectedCategoryId;

  String get selectedADDate => _selectedADDate;
  String get selectedBSDate => toDateBS(DateTime.parse(selectedADDate));

  ExpensesRepository expensesRepository = ExpensesRepository();

  init({String? forADDate}) async {
    _isLoading.value = true;
    _expenses.clear();
    final fetchedExpenses = await expensesRepository.getAllExpenses(
        forADDate: forADDate ?? selectedADDate);
    _expenses.addAll(fetchedExpenses);

    if (!([null, ""].contains(selectedCategoryId))) {
      await getExpensesForSingleCategory(selectedCategoryId);
    }
    _isLoading.value = false;
  }

  getExpensesForSingleCategory(String categoryID) async {
    _selectedCategoryId = categoryID;
    _isLoadingForCategory(true);
    _expensesForSingleCategory.clear();
    final fetchedCategoryExpenses = await expensesRepository
        .getAllExpensesForCategoryID(categoryID, forADDate: selectedADDate);
    _expensesForSingleCategory.addAll(fetchedCategoryExpenses);
    _isLoadingForCategory(false);
  }

  changeBSDate(String bsdate) {
    _selectedADDate = toDateAD(NepaliDateTime.parse(strTrim(bsdate)));
    update();
    init(forADDate: selectedADDate);
  }

  changeADDate(String addate) {
    _selectedADDate = addate;
    update();
    init(forADDate: addate);
  }

  addButtonOnPressedHandler(BuildContext context) async {
    Navigator.pushNamed(context, "/addExpenses");
  }
}
