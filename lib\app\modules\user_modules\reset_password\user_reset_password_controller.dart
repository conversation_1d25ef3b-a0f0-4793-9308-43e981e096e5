import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:tuple/tuple.dart';

class UserResetPasswordController extends GetxController {
  TextEditingController newPasswordCtrl = TextEditingController();
  TextEditingController verifyNewPasswordCtrl = TextEditingController();
  final formKey = GlobalKey<FormBuilderState>();

  bool _isSubmitting = false;
  bool get isSubmitting => _isSubmitting;

  Future<Tuple2<bool, String>> resetPassword({required int userId}) async {
    bool status = false;
    String message = "";
    _isSubmitting = true;
    update();
    if (newPasswordCtrl.text.isEmpty) {
      message = "New password is required";
    } else if (verifyNewPasswordCtrl.text.isEmpty) {
      message = "Verify password is required";
    } else if (newPasswordCtrl.text != verifyNewPasswordCtrl.text) {
      message = "New Password and Verify Password must be same";
    } else {
      try {
        ApiBaseHelper apiBaseHelper = ApiBaseHelper();
        ApiResponse apiResponse = await apiBaseHelper.post(
            apiBaseHelper.RESET_SUB_USER_PASSWORD + userId.toString(),
            {
              "new_password": newPasswordCtrl.text,
              "verify_password": verifyNewPasswordCtrl.text
            },
            accessToken: true);
        if (apiResponse.status) {
          status = true;
        } else {}
        message = apiResponse.msg ?? "";
      } catch (e) {
        // Log.d(e);
        message = FAILED_OPERATION_ERROR;
      }
    }
    _isSubmitting = false;
    update();
    return Tuple2(status, message);
  }
}
