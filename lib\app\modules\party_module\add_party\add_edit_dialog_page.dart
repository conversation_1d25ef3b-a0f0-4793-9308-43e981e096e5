// import 'package:contacts_service/contacts_service.dart';
// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get_state_manager/get_state_manager.dart';

import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/phone_contact_text_input.dart';
import 'package:mobile_khaata_v2/app/model/database/ledger_model.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/add_party/add_edit_party_controller.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

Future<LedgerModel?> displayPartyAddDialog(BuildContext context,
    {required String partyName}) async {
  LedgerModel? returnedData = await showDialog(
      context: context,
      useRootNavigator: true,
      barrierDismissible: false,
      builder: (_) {
        return AlertDialog(
          insetPadding:
              const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          contentPadding: EdgeInsets.zero,
          clipBehavior: Clip.hardEdge,
          shape:
              RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          content: SizedBox(
            width: MediaQuery.of(context).size.width - 20,
            child: AddPartyDialogPage(
              partyName: partyName,
            ),
          ),
        );
      });
  return returnedData;
}

class AddPartyDialogPage extends StatelessWidget {
  final String tag = "AddItemDialogPage";
  final String? partyName;
  final addEditPartyController = AddEditPartyController();

  AddPartyDialogPage({super.key, this.partyName}) {
    addEditPartyController.party.ledgerTitle = partyName;
    addEditPartyController.titleController.text = partyName!;
    addEditPartyController.onInit();
    addEditPartyController.titleController.text = partyName ?? "";
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (addEditPartyController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      } else {
        return Column(mainAxisSize: MainAxisSize.min, children: [
          GestureDetector(
            onTap: () => FocusScope.of(context).unfocus(),
            child: Container(
              decoration: BoxDecoration(color: backgroundColorShade),
              child: Form(
                key: addEditPartyController.formKey,
                child: SingleChildScrollView(
                  child: Container(
                    decoration: const BoxDecoration(color: Colors.white),
                    child: Column(
                      children: [
                        //==========================================================Top Panel
                        Container(
                          margin: const EdgeInsets.only(top: 10),
                          padding: const EdgeInsets.only(
                            left: 15,
                            right: 15,
                          ),
                          child: Column(
                            children: [
                              const SizedBox(height: 5.0),

                              //===========================FullName
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'व्यक्ति/फर्मको नाम',
                                    style: labelStyle2,
                                  ),
                                  const SizedBox(height: 5.0),
                                  FormBuilderTextField(
                                    name: "व्यक्ति/फर्मको नाम",
                                    readOnly:
                                        addEditPartyController.readOnlyFlag,
                                    autocorrect: false,
                                    keyboardType: TextInputType.text,
                                    textInputAction: TextInputAction.next,
                                    style: formFieldTextStyle,
                                    decoration: formFieldStyle.copyWith(
                                        labelText: "Person/Firm's Name",
                                        counterText: ''),
                                    controller:
                                        addEditPartyController.titleController,
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return "Please provide person name!";
                                      }
                                      return null;
                                    },
                                  ),
                                ],
                              ),

                              const SizedBox(
                                height: 15,
                              ),

                              //===================================Mobile
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'फोन नम्बर',
                                    style: labelStyle2,
                                  ),
                                  const SizedBox(height: 5.0),
                                  FormBuilderTextField(
                                    name: "mobile",
                                    readOnly:
                                        addEditPartyController.readOnlyFlag,
                                    autocorrect: false,
                                    keyboardType: TextInputType.number,
                                    textInputAction: TextInputAction.next,
                                    maxLength: 10,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly
                                    ],
                                    style: formFieldTextStyle,
                                    decoration: formFieldStyle.copyWith(
                                        labelText: "Contact No.",
                                        counterText: ''),
                                    controller:
                                        addEditPartyController.mobileController,
                                    onChanged: (v) {
                                      addEditPartyController.party.mobileNo = v;
                                    },
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return "Please provide mobile number!";
                                      } else if (value.length != 10) {
                                        return "Mobile No. should be 10 digits long!";
                                      }
                                      return null;
                                    },
                                  ),
                                ],
                              ),

                              const SizedBox(height: 15.0),

                              //===================================Address
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'ठेगाना',
                                    style: labelStyle2,
                                  ),
                                  const SizedBox(height: 5.0),
                                  FormBuilderTextField(
                                    name: "address",
                                    readOnly:
                                        addEditPartyController.readOnlyFlag,
                                    autocorrect: false,
                                    keyboardType: TextInputType.text,
                                    textInputAction: TextInputAction.next,
                                    style: formFieldTextStyle,
                                    decoration: formFieldStyle.copyWith(
                                        labelText: "Address"),
                                    initialValue:
                                        addEditPartyController.party.address,
                                    onChanged: (value) {
                                      addEditPartyController.party.address =
                                          strTrim(value!);
                                    },
                                  ),
                                ],
                              ),

                              const SizedBox(height: 15.0),

                              //=====================================PAN No Field
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "पान / मु. अ. कर नम्बर",
                                    style: labelStyle2,
                                  ),
                                  const SizedBox(height: 5.0),
                                  FormBuilderTextField(
                                    name: "pan_no",
                                    readOnly:
                                        addEditPartyController.readOnlyFlag,
                                    autocorrect: false,
                                    keyboardType: TextInputType.number,
                                    inputFormatters: [
                                      FilteringTextInputFormatter.digitsOnly
                                    ],
                                    textInputAction: TextInputAction.done,
                                    style: formFieldTextStyle,
                                    decoration: formFieldStyle.copyWith(
                                        labelText: "PAN/VAT No."),
                                    initialValue:
                                        addEditPartyController.party.tinNo,
                                    onChanged: (value) {
                                      addEditPartyController.party.tinNo =
                                          strTrim(value!);
                                    },
                                    // validator: (value) {
                                    //   if (value == null || value.isEmpty) {
                                    //     return "Please provide PAN/VAT No.!";
                                    //   }
                                    //   return null;
                                    // },
                                    // controller: addEditPartyController
                                    //     .panNoController,
                                  ),
                                ],
                              ),
                              const SizedBox(height: 15.0),

                              //=====================================PAN No Type Field
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const Text(
                                    'PAN हो / VAT हो ? छान्नुहोस',
                                    style: TextStyle(
                                        fontSize: 14, color: Colors.black54),
                                  ),
                                  FormBuilderChoiceChip(
                                    name: "pan_vat_type",
                                    // readOnly:
                                    //     addEditPartyController.readOnlyFlag,
                                    selectedColor: colorPrimaryLight,
                                    disabledColor: Colors.black12,
                                    labelStyle:
                                        const TextStyle(color: Colors.white),
                                    decoration: const InputDecoration(
                                      border: InputBorder.none,
                                    ),
                                    spacing: 20,
                                    initialValue:
                                        addEditPartyController.party.tinFlag,
                                    // validator: (value) {
                                    //   if (value == null || value.isEmpty) {
                                    //     return "Please select one!";
                                    //   }
                                    //   return null;
                                    // },
                                    onChanged: (value) {
                                      // Log.d("on change value$value");
                                      addEditPartyController.party.tinFlag =
                                          value;
                                      addEditPartyController.partyMain
                                          .refresh();
                                      // addEditPartyController._party.
                                    },
                                    alignment: WrapAlignment.start,
                                    options: [
                                      FormBuilderChipOption(
                                        value: "PAN",
                                        child: SizedBox(
                                          height: 30,
                                          width: 80,
                                          child: Row(
                                            children: const [
                                              Icon(
                                                Icons.check_box,
                                                color: Colors.white,
                                              ),
                                              SizedBox(
                                                width: 5,
                                              ),
                                              Text("PAN"),
                                            ],
                                          ),
                                        ),
                                      ),
                                      FormBuilderChipOption(
                                        value: "VAT",
                                        child: SizedBox(
                                          height: 30,
                                          width: 80,
                                          child: Row(
                                            children: const [
                                              Icon(
                                                Icons.check_box,
                                                color: Colors.white,
                                              ),
                                              SizedBox(
                                                width: 5,
                                              ),
                                              Text("VAT"),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                              const SizedBox(height: 15.0),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          BottomSaveCancelButton(
            shadow: false,
            onSaveBtnPressedFn: (addEditPartyController.readOnlyFlag)
                ? null
                : () async {
                    FocusScope.of(context).unfocus();
                    if (addEditPartyController.formKey.currentState!
                        .validate()) {
                      if (addEditPartyController.party.ledgerTitle!
                              .toLowerCase()
                              .contains("cash sale") ||
                          addEditPartyController.party.ledgerTitle!
                              .toLowerCase()
                              .contains("cash purchase")) {
                        showAlertDialog(context,
                            alertType: AlertType.Error,
                            alertTitle: "Error",
                            message: "Cash Sale/Cash Purchase already exists.");
                        return;
                      }

                      ProgressDialog progressDialog = ProgressDialog(context,
                          type: ProgressDialogType.normal,
                          isDismissible: false);
                      progressDialog.update(
                          message: "Saving data. Please wait....");
                      await progressDialog.show();
                      bool status = false;
                      try {
                        status = await addEditPartyController.saveParty();
                      } catch (e, trace) {
                        // Log.e(tag, e.toString() + trace.toString());
                      }
                      await progressDialog.hide();

                      if (status) {
                        Navigator.pop(context, addEditPartyController.party);

                        String message = (addEditPartyController.editFlag)
                            ? "Party Updated Successfully."
                            : "Party Created Successfully.";
                        showToastMessage(context,
                            message: message, duration: 2);
                      } else {
                        showAlertDialog(context,
                            alertType: AlertType.Error,
                            alertTitle: "Error",
                            message: "Failed To Save data");
                      }
                    }
                  },
          ),
        ]);
      }
    });
  }
}
