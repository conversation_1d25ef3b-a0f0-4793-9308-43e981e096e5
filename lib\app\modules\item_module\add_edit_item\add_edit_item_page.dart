import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/components/custom_dropdown.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/add_edit_item/add_edit_item_controller.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';
import 'package:mobile_khaata_v2/database/item_type.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:tuple/tuple.dart';

class AddEditItemPage extends StatefulWidget {
  final String? itemId;

  AddEditItemPage({this.itemId});

  @override
  _AddEditItemPageState createState() => _AddEditItemPageState();
}

class _AddEditItemPageState extends State<AddEditItemPage>
    with SingleTickerProviderStateMixin {
  final String tag = "AddEditItemPage";

  final addEditItemController = AddEditItemController();
  final _scrollController = ScrollController();

  late TabController tabController;

  @override
  void initState() {
    addEditItemController.item.itemType = ItemType.product;
    init();

    tabController = new TabController(length: 2, vsync: this)
      ..addListener(() {
        debugPrint("hello");
        if (ItemType.service == addEditItemController.item.itemType) {
          setState(() {
            tabController.index = 0;
          });
        }
      });

    super.initState();
  }

  init() async {
    await addEditItemController.onInit();
    if (null != widget.itemId && "" != widget.itemId) {
      debugPrint("Loading item for edit: " + widget.itemId.toString());
      await addEditItemController.initEdit(widget.itemId!);
    }
  }

  @override
  void dispose() {
    addEditItemController.onClose();
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (addEditItemController.isLoading) {
        return Container(
            color: Colors.white,
            child: Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
        // resizeToAvoidBottomPadding: true,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          leading: BackButton(
            onPressed: () => Navigator.pop(context),
          ),
          centerTitle: false,
          titleSpacing: -5.0,
          backgroundColor: colorPrimary,
          title: Text(
            (!addEditItemController.editFlag)
                ? "नयाँ सामान (New Product/Item)"
                : "सामान (Edit Product/Item)",
            style: TextStyle(
                fontSize: 17,
                color: Colors.white,
                fontFamily: 'HelveticaRegular',
                fontWeight: FontWeight.bold),
          ),
          actions: [
            if (addEditItemController.editFlag) ...{
              InkWell(
                  onTap: () => addEditItemController.readOnlyFlag =
                      !addEditItemController.readOnlyFlag,
                  splashColor: colorPrimaryLighter,
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                    child: (addEditItemController.readOnlyFlag)
                        ? Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.mode_edit,
                                color: Colors.white,
                              ),
                              Text(
                                "Edit",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          )
                        : Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.close,
                                color: Colors.white,
                              ),
                              Text(
                                "Cancel",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          ),
                  )),
            }
          ],
        ),

        //===========================================================================Body Part
        body: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(new FocusNode()),
          child: Container(
            child: Form(
              key: addEditItemController.formKey,
              child: Container(
                decoration: BoxDecoration(color: Colors.white),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Container(
                        margin: EdgeInsets.only(top: 10),
                        padding: EdgeInsets.only(
                          left: 15,
                          right: 15,
                        ),
                        child: Column(
                          children: [
                            const SizedBox(height: 5.0),

                            //===============================================Item Name
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "सामानको नाम",
                                  style: labelStyle2,
                                ),
                                SizedBox(height: 5.0),
                                FormBuilderTextField(
                                  name: "item_name",
                                  autocorrect: false,
                                  keyboardType: TextInputType.text,
                                  textInputAction: TextInputAction.next,
                                  style: formFieldTextStyle,
                                  decoration: formFieldStyle.copyWith(
                                      labelText: "Item Name"),
                                  readOnly: addEditItemController.readOnlyFlag,
                                  initialValue:
                                      addEditItemController.item.itemName,
                                  onChanged: (value) {
                                    addEditItemController.item.itemName =
                                        strTrim(value!);
                                  },
                                  validator: (value) {
                                    if (null == value || value.isEmpty) {
                                      // value = value!.trim();
                                      return 'सामानको नाम राख्नुहोस् (Fill Item Name)';
                                    }
                                    return null;
                                  },
                                ),
                              ],
                            ),

                            const SizedBox(
                              height: 20,
                            ),

                            //==========================================Item Code Field
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "कोड/ बारकोड",
                                  style: labelStyle2,
                                ),
                                SizedBox(height: 5.0),
                                FormBuilderTextField(
                                  name: "item_code",
                                  readOnly: addEditItemController.readOnlyFlag,
                                  autocorrect: false,
                                  textInputAction: TextInputAction.next,
                                  keyboardType: TextInputType.text,
                                  style: formFieldTextStyle,
                                  decoration: formFieldStyle.copyWith(
                                      labelText: "Item Code / Barcode"),
                                  initialValue:
                                      addEditItemController.item.itemCode,
                                  onChanged: (value) {
                                    addEditItemController.item.itemCode =
                                        strTrim(value!);
                                  },
                                ),
                              ],
                            ),

                            const SizedBox(height: 20.0),

                            //===============================================Item Type Field
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "सामानको किसिम",
                                  style: labelStyle2,
                                ),
                                SizedBox(height: 5.0),
                                FormBuilderDropdown(
                                  name: 'item_type',
                                  style: formFieldTextStyle,
                                  decoration: formFieldStyle.copyWith(
                                      labelText: "Item Type"),
                                  items: ItemType.itemTypeList.map((row) {
                                    return DropdownMenuItem(
                                        value: row["value"],
                                        child: Text(row["text"]));
                                  }).toList(),
                                  // readOnly: addEditItemController.editFlag,
                                  initialValue:
                                      addEditItemController.item.itemType,
                                  onChanged: (value) => addEditItemController
                                      .itemTypeOnChangeHandler(value),
                                  validator: (value) {
                                    if (value == null) {
                                      return "Please select Item type.";
                                    }
                                    return null;
                                  },
                                  // validators: [
                                  //   FormBuilderValidators.required(
                                  //       errorText: "Please select Item type.")
                                  // ],
                                ),
                              ],
                            ),

                            const SizedBox(height: 25.0),
                          ],
                        ),
                      ),

                      //============================================================================== TAB Panel
                      Container(
                        height: 45,
                        decoration: BoxDecoration(
                            color: Colors.black12,
                            border: Border.all(color: colorPrimaryLight)),
                        child: TabBar(
                          controller: tabController,
                          indicatorColor: Colors.white,
                          labelStyle: TextStyle(
                            fontSize: 15,
                          ),
                          unselectedLabelColor: Colors.black,
                          indicator: BoxDecoration(color: colorPrimaryLight),
                          tabs: [
                            Tab(
                              child: Text(
                                "अन्य जानकारी\n(Other Information)",
                                textAlign: TextAlign.center,
                              ),
                            ),
                            Tab(
                                child: Text(
                              "सुरु मौज्दात\n(Opening Balance)",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  color: (ItemType.service ==
                                          addEditItemController.item.itemType)
                                      ? Colors.black12
                                      : null),
                            )),
                          ],
                        ),
                      ),

                      Container(
                        height: MediaQuery.of(context).size.height + 120,
                        padding: EdgeInsets.symmetric(horizontal: 15),
                        child: TabBarView(
                          controller: tabController,
                          children: [
                            //===========================================Extra Information Tab
                            Container(
                              child: Column(
                                children: [
                                  //Image
                                  // Container(
                                  //   width: 130,
                                  //   child: (null==_selectImage)?
                                  //   FormBuilderImagePicker(
                                  //     readOnly: _editModeDisabled,
                                  //     decoration: InputDecoration(border: InputBorder.none,),
                                  //     maxImages: 1,
                                  //     iconColor: colorPrimaryLight,
                                  //     validators: [],
                                  //     onChanged: (value) async {
                                  //       _imageChanged = true;
                                  //       List<dynamic> imageList = value;
                                  //       if(imageList.isNotEmpty){
                                  //         try{
                                  //           _selectImage = imageList[0].readAsBytesSync();
                                  //         }catch(e){
                                  //           _selectImage = null;
                                  //           Log.e(_TAG, e.toString());
                                  //         }
                                  //       }
                                  //     },
                                  //   )
                                  //   :customImageBox(
                                  //       _selectImage,
                                  //       onCancel: (_editModeDisabled)?  null : (){
                                  //         setState(() {
                                  //           _selectImage = null;
                                  //           _imageChanged = true;
                                  //         });
                                  //       }
                                  //   ),
                                  // ),

                                  // const SizedBox(height: 20.0),

                                  //===========================================Primary unit
                                  if (ItemType.product ==
                                      addEditItemController.item.itemType) ...{
                                    const SizedBox(height: 20.0),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "प्राथमिक एकाइ",
                                          style: labelStyle2,
                                        ),
                                        SizedBox(height: 5.0),
                                        CustomDropdown(
                                          readOnly: addEditItemController
                                              .readOnlyFlag,
                                          style: formFieldTextStyle,
                                          decoration: formFieldStyle,
                                          placeholder: "Primary Unit",
                                          options:
                                              addEditItemController.unitMap,
                                          onChange: (value) {
                                            addEditItemController
                                                .primaryUnitOnSelectHandler(
                                                    value);
                                          },
                                          itemRenderer: (optionItem) {
                                            return Container(
                                                padding: EdgeInsets.all(11),
                                                child: Text(
                                                  optionItem['value'] ?? "",
                                                  style: TextStyle(
                                                      color: textColor),
                                                ));
                                          },
                                          allowClear: true,
                                          value: addEditItemController
                                              .item.baseUnitId,
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 20.0),
                                  },

                                  //===========================================Alternate unit
                                  if (null !=
                                          addEditItemController
                                              .item.baseUnitId &&
                                      ItemType.product ==
                                          addEditItemController
                                              .item.itemType) ...{
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "वैकल्पिक एकाइ",
                                          style: labelStyle2,
                                        ),
                                        SizedBox(height: 5.0),
                                        CustomDropdown(
                                          readOnly: addEditItemController
                                              .readOnlyFlag,
                                          style: formFieldTextStyle,
                                          decoration: formFieldStyle,
                                          placeholder: "Alternate Unit",
                                          options:
                                              addEditItemController.unitMap,
                                          onChange: (value) {
                                            addEditItemController
                                                .secondaryUnitOnSelectHandler(
                                                    value);
                                          },
                                          allowClear: true,
                                          value: addEditItemController
                                              .item.alternateUnitId,
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 10.0),
                                  },

                                  //=============================================================Unit Conversion Rate Field
                                  if (null !=
                                          addEditItemController
                                              .item.baseUnitId &&
                                      null !=
                                          addEditItemController
                                              .item.alternateUnitId &&
                                      ItemType.product ==
                                          addEditItemController
                                              .item.itemType) ...{
                                    Column(
                                      children: [
                                        Divider(
                                          thickness: 1,
                                        ),
                                        Text(
                                          "रूपान्तरण दर सेट गर्नुहोस्\n(Set Conversion Rate)",
                                          style: TextStyle(
                                              fontWeight: FontWeight.bold),
                                        ),
                                        const SizedBox(height: 10.0),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.center,
                                          children: [
                                            Container(
                                              child: Text(
                                                  "1 ${addEditItemController.unitMap.firstWhere((element) => (element["key"] == addEditItemController.item.alternateUnitId))["value"]} = "),
                                            ),
                                            Container(
                                              height: 35,
                                              width: 75,
                                              child: FormBuilderTextField(
                                                name: "conversion_rate",
                                                autocorrect: false,
                                                keyboardType: TextInputType
                                                    .numberWithOptions(
                                                        decimal: true),
                                                inputFormatters: [
                                                  FilteringTextInputFormatter
                                                      .allow(RegExp(
                                                          r'^(\d+)?\.?\d{0,8}'))
                                                ],
                                                textInputAction:
                                                    TextInputAction.done,
                                                style: formFieldTextStyle,
                                                decoration: formFieldStyle,
                                                readOnly: addEditItemController
                                                    .readOnlyFlag,
                                                initialValue:
                                                    addEditItemController.item
                                                        .unitConversionFactor
                                                        ?.toString(),
                                                onChanged: (value) {
                                                  addEditItemController.item
                                                          .unitConversionFactor =
                                                      parseDouble(value);
                                                },
                                              ),
                                            ),
                                            Container(
                                              child: Text(
                                                  " ${addEditItemController.unitMap.firstWhere((element) => (element["key"] == addEditItemController.item.baseUnitId))["value"]}"),
                                            ),
                                          ],
                                        ),
                                        Divider(
                                          thickness: 1,
                                        ),
                                      ],
                                    ),
                                    const SizedBox(height: 10.0),
                                  },
                                  const SizedBox(height: 15.0),

                                  //========================================Item Location Field
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "सामान राखेको स्थान",
                                        style: labelStyle2,
                                      ),
                                      SizedBox(height: 5.0),
                                      FormBuilderTextField(
                                        name: "item_location",
                                        autocorrect: false,
                                        keyboardType: TextInputType.text,
                                        textInputAction: TextInputAction.next,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Item's Location"),
                                        readOnly:
                                            addEditItemController.readOnlyFlag,
                                        initialValue: addEditItemController
                                            .item.itemLocation,
                                        onChanged: (value) {
                                          addEditItemController.item
                                              .itemLocation = strTrim(value!);
                                        },
                                        // validators: [],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 15.0),

                                  //========================================Item Description Field
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "विवरण",
                                        style: labelStyle2,
                                      ),
                                      SizedBox(height: 5.0),
                                      FormBuilderTextField(
                                        name: "item_desc",
                                        autocorrect: false,
                                        keyboardType: TextInputType.text,
                                        textInputAction: TextInputAction.next,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Item Description"),
                                        readOnly:
                                            addEditItemController.readOnlyFlag,
                                        initialValue: addEditItemController
                                            .item.itemDescription,
                                        onChanged: (value) {
                                          addEditItemController
                                                  .item.itemDescription =
                                              strTrim(value!);
                                        },
                                        // validators: [],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 15.0),

                                  //==========================================Sale Price Field
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "बिक्री मुल्य",
                                        style: labelStyle2,
                                      ),
                                      SizedBox(height: 5.0),
                                      // Show formatted currency in read-only mode, raw value in edit mode
                                      addEditItemController.readOnlyFlag
                                          ? Container(
                                              width: double.infinity,
                                              padding: EdgeInsets.all(14),
                                              decoration: BoxDecoration(
                                                color: Colors.grey[100],
                                                borderRadius:
                                                    BorderRadius.circular(6.0),
                                                border: Border.all(
                                                    color: Colors.grey[300]!),
                                              ),
                                              child: Text(
                                                formatCurrencyAmount(
                                                    (addEditItemController.item
                                                                .itemSaleUnitPrice ??
                                                            0)
                                                        .toDouble()),
                                                style: formFieldTextStyle,
                                              ),
                                            )
                                          : FormBuilderTextField(
                                              name: "item_sale_price",
                                              autocorrect: false,
                                              textInputAction:
                                                  TextInputAction.next,
                                              keyboardType: TextInputType
                                                  .numberWithOptions(
                                                      decimal: true),
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .allow(RegExp(
                                                        r'^(\d+)?\.?\d{0,2}'))
                                              ],
                                              style: formFieldTextStyle,
                                              decoration:
                                                  formFieldStyle.copyWith(
                                                      labelText: "Sale Price"),
                                              initialValue:
                                                  addEditItemController
                                                      .item.itemSaleUnitPrice
                                                      ?.toString(),
                                              onChanged: (value) {
                                                addEditItemController.item
                                                        .itemSaleUnitPrice =
                                                    parseDouble(value);
                                              },
                                            ),
                                    ],
                                  ),
                                  const SizedBox(height: 20.0),

                                  //==========================================Purchase Price Field
                                  if (ItemType.product ==
                                      addEditItemController.item.itemType) ...{
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "खरिद मुल्य",
                                          style: labelStyle2,
                                        ),
                                        SizedBox(height: 5.0),
                                        // Show formatted currency in read-only mode, raw value in edit mode
                                        addEditItemController.readOnlyFlag
                                            ? Container(
                                                width: double.infinity,
                                                padding: EdgeInsets.all(14),
                                                decoration: BoxDecoration(
                                                  color: Colors.grey[100],
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          6.0),
                                                  border: Border.all(
                                                      color: Colors.grey[300]!),
                                                ),
                                                child: Text(
                                                  formatCurrencyAmount(
                                                      (addEditItemController
                                                                  .item
                                                                  .itemPurchaseUnitPrice ??
                                                              0)
                                                          .toDouble()),
                                                  style: formFieldTextStyle,
                                                ),
                                              )
                                            : FormBuilderTextField(
                                                name: "item_purchase_price",
                                                autocorrect: false,
                                                textInputAction:
                                                    TextInputAction.next,
                                                keyboardType: TextInputType
                                                    .numberWithOptions(
                                                        decimal: true),
                                                inputFormatters: [
                                                  FilteringTextInputFormatter
                                                      .allow(RegExp(
                                                          r'^(\d+)?\.?\d{0,2}'))
                                                ],
                                                style: formFieldTextStyle,
                                                decoration:
                                                    formFieldStyle.copyWith(
                                                        labelText:
                                                            "Purchase Price"),
                                                initialValue:
                                                    addEditItemController.item
                                                        .itemPurchaseUnitPrice
                                                        ?.toString(),
                                                onChanged: (value) {
                                                  addEditItemController.item
                                                          .itemPurchaseUnitPrice =
                                                      parseDouble(value);
                                                },
                                              ),
                                      ],
                                    ),
                                  },
                                ],
                              ),
                            ),

                            //==============================================================Stock Detail Tab
                            if (ItemType.product ==
                                (addEditItemController.item.itemType ??
                                    ItemType.product)) ...{
                              Column(
                                children: [
                                  const SizedBox(height: 20.0),

                                  //=======================================Opening Balance Field
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "सुरु मौज्दात",
                                        style: labelStyle2,
                                      ),
                                      SizedBox(height: 5.0),
                                      FormBuilderTextField(
                                        name: "opening_bal",
                                        autocorrect: false,
                                        keyboardType:
                                            TextInputType.numberWithOptions(
                                                decimal: true),
                                        inputFormatters: [
                                          FilteringTextInputFormatter.allow(
                                              RegExp(r'^(\d+)?\.?\d{0,2}'))
                                        ],
                                        textInputAction: TextInputAction.next,
                                        style: formFieldTextStyle,
                                        maxLength: 10,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Opening Balance",
                                            counterText: ''),
                                        readOnly:
                                            addEditItemController.readOnlyFlag,
                                        initialValue: addEditItemController
                                            .item.openingStock
                                            ?.toString(),
                                        onChanged: (value) {
                                          addEditItemController
                                                  .item.openingStock =
                                              parseDouble(value);
                                        },
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 20.0),

                                  //=================================Opening Balance Date Field
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "सुरु मौज्दात मिति",
                                        style: labelStyle2,
                                      ),
                                      SizedBox(height: 5.0),
                                      CustomDatePickerTextField(
                                        readOnly:
                                            addEditItemController.readOnlyFlag,
                                        maxBSDate: NepaliDateTime.now(),
                                        initialValue: addEditItemController
                                            .item.openingDateBS,
                                        onChange: (selectedDate) {
                                          addEditItemController.item
                                              .openingDateBS = selectedDate;
                                        },
                                      ),
                                    ],
                                  ),

                                  const SizedBox(height: 20.0),

                                  //=============================Minimum Stock Balance Field
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "न्यूनतम मौज्दात",
                                        style: labelStyle2,
                                      ),
                                      SizedBox(height: 5.0),
                                      FormBuilderTextField(
                                        name: "min_stock_qty",
                                        autocorrect: false,
                                        textInputAction: TextInputAction.done,
                                        keyboardType:
                                            TextInputType.numberWithOptions(
                                                decimal: true),
                                        inputFormatters: [
                                          FilteringTextInputFormatter.allow(
                                              RegExp(r'^(\d+)?\.?\d{0,2}'))
                                        ],
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Min Stock Qty"),
                                        readOnly:
                                            addEditItemController.readOnlyFlag,
                                        initialValue: addEditItemController
                                            .item.itemMinStockQuantity
                                            ?.toString(),
                                        onChanged: (value) {
                                          addEditItemController
                                                  .item.itemMinStockQuantity =
                                              parseDouble(value);
                                        },
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 5.0),
                                  Text(
                                      "[ Note: न्यूनतम मौज्दात सतर्कता > 0 (Min Stock Qty Alert > 0) ]",
                                      style: TextStyle(
                                          fontWeight: FontWeight.normal,
                                          color: Colors.red))
                                ],
                              ),
                            } else ...{
                              Container(),
                            },
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),

        //=================================================Save button
        bottomNavigationBar: BottomSaveCancelButton(
          shadow: false,
          hasDelete: (addEditItemController.editFlag &&
                  !addEditItemController.readOnlyFlag)
              ? true
              : false,
          onDeleteBtnPressedFn: () async {
            showAlertDialog(context,
                okText: "YES",
                hasCancel: true,
                cancelText: "NO",
                alertType: AlertType.Error,
                alertTitle: "Confirm Delete", onCloseButtonPressed: () async {
              // Navigator.of(_).pop();
              ProgressDialog progressDialog = ProgressDialog(context,
                  type: ProgressDialogType.normal, isDismissible: false);
              progressDialog.update(
                  message: "Checking Permission. Please wait....");
              await progressDialog.show();
              Tuple2<bool, String> checkResp =
                  await PermissionWrapperController().requestForPermissionCheck(
                      forPermission: PermissionManager.itemDelete);
              if (checkResp.item1) {
                //has  permission
                progressDialog.update(
                    message: "Deleting Data. Please wait....");

                Tuple2<bool, String> deleteResp =
                    await ItemRepository().delete(this.widget.itemId!);
                await progressDialog.hide();
                if (deleteResp.item1) {
                  //  data deleted
                  TransactionHelper.refreshPreviousPages();
                  showAlertDialog(context,
                      barrierDismissible: false,
                      alertType: AlertType.Success,
                      alertTitle: "", onCloseButtonPressed: () {
                    // Navigator.of().pop();
                    // Navigator.of(context).pop();
                    Navigator.of(context).pop();
                    // Navigator.popAndPushNamed(context, '/itemList');
                  }, message: deleteResp.item2);
                } else {
                  //cannot  delete  data
                  showAlertDialog(context,
                      alertType: AlertType.Error,
                      alertTitle: "",
                      message: deleteResp.item2);
                }
              } else {
                await progressDialog.hide();
                showAlertDialog(context,
                    alertType: AlertType.Error,
                    alertTitle: "",
                    message: checkResp.item2);
              }
            }, message: "Are you sure you  want to  delete this item?");
          },
          enableFlag: !addEditItemController.readOnlyFlag,
          onSaveBtnPressedFn: (addEditItemController.readOnlyFlag)
              ? null
              : () async {
                  FocusScope.of(context).unfocus();
                  if (addEditItemController.formKey.currentState!.validate()) {
                    ProgressDialog progressDialog = ProgressDialog(context,
                        type: ProgressDialogType.normal, isDismissible: false);
                    progressDialog.update(
                        message: "Saving data. Please wait....");
                    await progressDialog.show();

                    bool status = false;
                    try {
                      if (!await addEditItemController.checkUniqueItemName()) {
                        throw new CustomException(
                            "सामान पहिल्यै थपिएको छ\n(Item  already exist)");
                      }

                      if (!await addEditItemController.checkUniqueItemCode()) {
                        throw new CustomException(
                            "कोड/बारकोड पह��ल्यै प्रयोगमा छ\n(Item code/Barcode already in use)");
                      }

                      if (null == addEditItemController.item.baseUnitId &&
                          ItemType.product ==
                              addEditItemController.item.itemType) {
                        throw new CustomException(
                            "प्राथमिक एकाइ छनौट गर्नुहोस्\n(Please select Primary Unit)");
                      }

                      if (ItemType.product ==
                              addEditItemController.item.itemType &&
                          addEditItemController.item.baseUnitId ==
                              addEditItemController.item.alternateUnitId) {
                        throw new CustomException(
                            "प्राथमिक एकाइ र वैकल्पिक एकाइ समान ���ुन सक्दैन\nPrimary Unit and Alternate Unit cannot be same.");
                      }

                      if (null != addEditItemController.item.baseUnitId &&
                          null != addEditItemController.item.alternateUnitId) {
                        if (0.00 ==
                            (addEditItemController.item.unitConversionFactor ??
                                0.00)) {
                          throw new CustomException(
                              "रूपान्तरण दर भर्नुहोस्\n(Fill Conversion Rate)");
                        }
                      }

                      if (ItemType.product ==
                              addEditItemController.item.itemType &&
                          parseDouble(
                                  addEditItemController.item.openingStock)! >
                              0 &&
                          null == addEditItemController.item.openingDateBS) {
                        throw new CustomException(
                            "सुरु मौज्दात मिति भर्नुहोस्\n(Fill Opening Balance Date)");
                      }

                      if (!addEditItemController.editFlag) {
                        status = await addEditItemController.createItem();
                      } else {
                        status = await addEditItemController.updateItem();
                      }
                    } on CustomException catch (e) {
                      await progressDialog.hide();
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "Error",
                          message: e.toString());
                      return;
                    } catch (e, trace) {
                      Log.e(tag, e.toString() + trace.toString());
                    }
                    await progressDialog.hide();

                    if (status) {
                      Navigator.pop(context, true);

                      TransactionHelper.refreshPreviousPages();

                      String message = (addEditItemController.editFlag)
                          ? "Item Updated Successfully."
                          : "Item Created Successfully.";
                      showToastMessage(context, message: message, duration: 2);
                    } else {
                      showToastMessage(context,
                          alertType: AlertType.Error,
                          message: "Failed to process operation",
                          duration: 2);
                    }
                  }
                },
        ),
      ));
    });
  }
}
