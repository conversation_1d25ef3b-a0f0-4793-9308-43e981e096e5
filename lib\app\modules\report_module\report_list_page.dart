import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

// ignore: must_be_immutable
class ReportListPage extends StatelessWidget {
  List<Map<String, dynamic>> report = [];

  ReportListPage({super.key});

  @override
  Widget build(BuildContext context) {
    report.add({"title": "लेनदेन (Transaction)", "type": "p", "action": null});
    report.add(
        {"title": "बिक्री (Sales)", "type": "c", "action": "/salesReport"});
    report.add({
      "title": "बिक्री फिर्ता (Sales Return)",
      "type": "c",
      "action": "/salesReturnReport"
    });
    report.add(
        {"title": "खरीद (Purchase)", "type": "c", "action": "/purchaseReport"});
    report.add({
      "title": "खरीद फिर्ता (Purchase Return)",
      "type": "c",
      "action": "/purchaseReturnReport"
    });
    report.add({
      "title": "दैनिक रिपोर्ट (Day Book)",
      "type": "c",
      "action": "/dayBookReport"
    });
    report.add({
      "title": "सबै लेनदेन (All Transactions)",
      "type": "c",
      "action": "/allTransactionReport"
    });

    //======================================================================================================
    report.add({
      "title": "पार्टी रिपोर्ट (Party Report)",
      "type": "p",
      "action": null
    });
    report.add({
      "title": "पार्टी खाता विवरण (Party Statement)",
      "type": "c",
      "action": "/partyStatementReport"
    });
    report.add({
      "title": "सबै पार्टी (All Party)",
      "type": "c",
      "action": "/allPartyReport"
    });

    //======================================================================================================
    report.add({
      "title": "सामान/स्टक रिपोर्ट (Item/Stock Report)",
      "type": "p",
      "action": null
    });
    report.add({
      "title": "स्टक विवरण (Stock Summary)",
      "type": "c",
      "action": "/stockSummaryReport"
    });
    report.add({
      "title": "कम स्टक विवरण (Low Stock Report)",
      "type": "c",
      "action": "/lowStockReport"
    });
    report.add({
      "title": "स्टक डिटेल (Stock Detail)",
      "type": "c",
      "action": "/stockDetailReport"
    });

    //======================================================================================================
    report.add({
      "title": "खर्च रिपोर्ट (Expense Report)",
      "type": "p",
      "action": null
    });
    report.add({
      "title": "खर्च लेनदेन (Expense Transaction)",
      "type": "c",
      "action": "/expenseTransactionReport"
    });
    report.add({
      "title": "खर्च शीर्षक (Expense Category)",
      "type": "c",
      "action": "/expenseCategorySummaryReport"
    });

    //======================================================================================================
    report.add({
      "title": "व्यवसाय स्थिति (Business Status)",
      "type": "p",
      "action": "/cashFlowReport"
    });
    report.add({
      "title": "नगद मौज्दात स्टेट्मेन्ट (Cash In Hand Statement)",
      "type": "c",
      "action": "/cashStatementReport"
    });
    report.add({
      "title": "बैंक स्टेट्मेन्ट (Bank Statement)",
      "type": "c",
      "action": "/bankStatementReport"
    });
    report.add({
      "title": "छुट रिपोर्ट (Discount Report)",
      "type": "c",
      "action": "/discountReport"
    });

    //======================================================================================================
    report.add(
        {"title": "भ्याट रिपोर्ट (VAT Report)", "type": "p", "action": null});
    report.add({
      "title": "भ्याट बिक्री रिपोर्ट (VAT Sales Register)",
      "type": "c",
      "action": "/vatSalesRegisterReport"
    });
    report.add({
      "title": "भ्याट खरीद रिपोर्ट (VAT Purchase Register)",
      "type": "c",
      "action": "/vatPurchaseRegisterReport"
    });
    report.add({
      "title": "भ्याट खर्च रिपोर्ट (VAT Expense Register)",
      "type": "c",
      "action": "/vatExpenseRegisterReport"
    });
    report.add({
      "title": "कारोबार प्रमाणिकरण पत्र (Letter of Confirmation)",
      "type": "c",
      "action": "/accountBalanceConfirmation"
    });
    report.add({
      "title": "अनुसुची-१३ रिपोर्ट (Annex-13 Report)",
      "type": "c",
      "action": "/annex13Report"
    });

    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        titleSpacing: -5.0,
        backgroundColor: colorPrimary,
        title: const Text(
          "रिपोर्ट (Report)",
          style: TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
      ),
      body: ListView(
        children: [
          ...report.map((e) {
            if ('p' == e["type"]) {
              return Container(
                color: colorPrimaryLighter,
                padding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                child: Text(
                  e["title"],
                  style: const TextStyle(
                      color: Colors.white,
                      fontSize: 20,
                      fontWeight: FontWeight.bold),
                ),
              );
            } else {
              return Column(
                children: [
                  InkWell(
                    onTap: () {
                      // Log.d(e["action"]);
                      Navigator.pushNamed(context, e["action"]);
                    },
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 15),
                      child: Padding(
                        padding: const EdgeInsets.only(left: 20.0),
                        child: Row(
                          children: [
                            Icon(
                              Icons.label,
                              color: colorPrimary,
                              size: 16,
                            ),
                            Text(
                              // ignore: prefer_interpolation_to_compose_strings
                              "  " + e["title"],
                              style: TextStyle(
                                  fontSize: 15,
                                  color: colorPrimary,
                                  fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                  const Divider(
                    height: 0,
                    color: Colors.black26,
                  ),
                ],
              );
            }
          })
        ],
      ),
    ));
  }
}
