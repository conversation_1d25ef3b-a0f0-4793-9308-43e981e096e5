import 'dart:typed_data';

import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';

import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:nepali_utils/nepali_utils.dart';

Future<Uint8List> customReport(PdfPageFormat pageFormat) async {
  const products = <Product>[
    Product('03-Poush-\n2077', 'abc', 'adas', 'sale', 152.4, 3, 3.99, 2),
    Product('03-Poush-\n2077', 'abc', 'adas', 'sale', 152.4, 3, 3.99, 2),
    Product('03-Poush-\n2077', 'abc', 'adas', 'sale', 152.4, 3, 3.99, 2),
    Product('03-Poush-\n2077', 'abc', 'adas', 'sale', 152.4, 3, 3.99, 2),
  ];

  final invoice = Invoice(
    txnType: 'All Transaction',
    partyName: 'All Party',
    startDate: _formatDate(DateTime.now()),
    endDate: _formatDate(DateTime.now()),
    invoiceNumber: '982347',
    sellerPan: '151515151',
    products: products,
    sellerAddress: 'Hetauda-4',
    sellerName: 'Vedanta Technologies',
    baseColor: PdfColors.lightBlue,
    accentColor: PdfColors.blueGrey900,
  );

  return await invoice.buildPdf(pageFormat);
}

class Invoice {
  Invoice({
    this.products,
    this.sellerPan,
    this.sellerName,
    this.sellerAddress,
    this.invoiceNumber,
    this.baseColor,
    this.accentColor,
    this.startDate,
    this.endDate,
    this.partyName,
    this.txnType,
  });

  final List<Product>? products;
  final String? sellerPan;
  final String? sellerAddress;
  final String? sellerName;
  final String? invoiceNumber;
  final PdfColor? baseColor;
  final PdfColor? accentColor;
  final String? startDate;
  final String? endDate;
  final String? partyName;
  final String? txnType;

  static const _darkColor = PdfColors.blueGrey800;
  static const _lightColor = PdfColors.black;
  // ignore: constant_identifier_names
  static const _VedColor = PdfColor.fromInt(0xFF3560AF);
  PdfColor get _baseTextColor =>
      baseColor!.luminance < 0.5 ? _lightColor : _darkColor;

  var currencyInWords = NepaliNumberFormat(
    inWords: true,
    language: Language.english,
    isMonetory: true,
    decimalDigits: 2,
  );

  String? _logo;

  Future<Uint8List> buildPdf(PdfPageFormat pageFormat) async {
    // Create a PDF document.
    final doc = pw.Document();

    final font1 = await rootBundle.load('assets/roboto1.ttf');
    final font2 = await rootBundle.load('assets/roboto1.ttf');
    final font3 = await rootBundle.load('assets/roboto3.ttf');

    _logo = await rootBundle.loadString('assets/mobilekhata.svg');

    // Add page to the PDF
    doc.addPage(
      pw.MultiPage(
        // pageTheme: _buildTheme(
        //   pageFormat,
        //   pw.Font.ttf(font1),
        //   pw.Font.ttf(font2),
        //   pw.Font.ttf(font3),
        // ),
        header: _buildHeader,
        footer: _buildFooter,
        build: (context) => [
          _header(context),
          _newHeader(context),
          _contentTable(context),
          pw.SizedBox(height: 20),
          // _contentFooter(context),
          pw.SizedBox(height: 20),
        ],
      ),
    );

    // Return the PDF file content
    return doc.save();
  }

  pw.Widget _buildHeader(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Expanded(
              child: pw.Column(
                mainAxisSize: pw.MainAxisSize.min,
                children: [
                  if (null != _logo) ...{
                    pw.Container(
                      margin: const pw.EdgeInsets.only(top: -20),
                      alignment: pw.Alignment.center,
                      height: 60,
                      child: _logo != null
                          ? pw.SvgImage(svg: _logo ?? "")
                          : pw.PdfLogo(),
                    ),
                  },
                  // pw.Container(
                  //   color: baseColor,
                  //   padding: pw.EdgeInsets.only(top: 3),
                  // ),
                ],
              ),
            ),
          ],
        ),
        pw.Row(crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
          pw.Expanded(
              child: pw.Column(mainAxisSize: pw.MainAxisSize.min, children: [
            pw.Container(
              margin: const pw.EdgeInsets.only(top: 10),
              alignment: pw.Alignment.center,
              child: pw.Text(
                sellerName ?? "",
                style: pw.TextStyle(
                  color: _VedColor,
                  fontWeight: pw.FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            pw.Container(
                margin: const pw.EdgeInsets.only(),
                child: pw.Text(
                  sellerAddress ?? "",
                  style: pw.TextStyle(
                    color: accentColor,
                    fontSize: 8,
                  ),
                )),
          ]))
        ]),
        if (context.pageNumber > 1) pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _header(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Expanded(
              child: pw.Column(
                children: [
                  pw.Container(
                    margin: pw.EdgeInsets.only(bottom: 20, top: 10),
                    alignment: pw.Alignment.center,
                    child: pw.Text('Sales Invoice',
                        style: pw.TextStyle(
                            color: PdfColors.red,
                            fontSize: 15,
                            fontWeight: pw.FontWeight.bold)),
                  )
                ],
              ),
            ),
          ],
        ),
        if (context.pageNumber > 1) pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _buildFooter(pw.Context context) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: [
        // pw.Container(
        //   height: 20,
        //   width: 100,
        //   child: pw.BarcodeWidget(
        //     barcode: pw.Barcode.pdf417(),
        //     data: 'Invoice# $invoiceNumber',
        //   ),
        // ),
        pw.Text(
          'Page ${context.pageNumber}/${context.pagesCount}',
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
        pw.Text(
          FOOTER_PRINT_TEXT,
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
      ],
    );
  }

  // pw.PageTheme _buildTheme(
  //     PdfPageFormat pageFormat, pw.Font base, pw.Font bold, pw.Font italic) {
  //   return pw.PageTheme(
  //     pageFormat: pageFormat,
  //     theme: pw.ThemeData.withFont(
  //       base: base,
  //       bold: bold,
  //       italic: italic,
  //     ),
  //     buildBackground: (context) => pw.FullPage(
  //       ignoreMargins: true,
  //     ),
  //   );
  // }

  pw.Widget _newHeader(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Container(
                height: 15,
                child: pw.Text(
                  'Party Name:\r $partyName',
                  style: pw.TextStyle(
                    color: PdfColors.black,
                    lineSpacing: 10,
                    fontSize: 10,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.Container(
                height: 15,
                child: pw.Text(
                  'Transaction type:\r $txnType',
                  style: pw.TextStyle(
                    color: PdfColors.black,
                    lineSpacing: 5,
                    fontSize: 10,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.Container(
                height: 20,
                child: pw.Text(
                  'Duration:\rFrom\r$startDate\rTo\r$endDate',
                  style: pw.TextStyle(
                    color: PdfColors.black,
                    lineSpacing: 5,
                    fontSize: 10,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  pw.Widget _contentTable(pw.Context context) {
    const tableHeaders = [
      'Date',
      'Bill No.',
      'Name',
      'Type',
      'Total Amount',
      'Received Amount',
      'Paid Amount',
      'Balance Amount',
    ];

    return pw.Table.fromTextArray(
      border: null,
      cellAlignment: pw.Alignment.centerLeft,
      headerDecoration: const pw.BoxDecoration(
        color: PdfColors.blue100,
      ),
      headerHeight: 35,
      cellHeight: 40,
      cellAlignments: {
        0: pw.Alignment.centerLeft,
        1: pw.Alignment.centerLeft,
        2: pw.Alignment.centerLeft,
        3: pw.Alignment.center,
        4: pw.Alignment.center,
        5: pw.Alignment.center,
        6: pw.Alignment.center,
        7: pw.Alignment.center
      },
      headerStyle: pw.TextStyle(
        color: _baseTextColor,
        fontSize: 9.5,
        fontWeight: pw.FontWeight.bold,
      ),
      cellStyle: pw.TextStyle(
        color: accentColor,
        fontSize: 8,
      ),
      rowDecoration: pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: accentColor!,
            width: .5,
          ),
        ),
      ),
      headers: List<String>.generate(
        tableHeaders.length,
        (col) => tableHeaders[col],
      ),
      data: List<List<String>>.generate(
        products!.length,
        (row) => List<String>.generate(
          tableHeaders.length,
          (col) => products![row].getIndex(col),
        ),
      ),
    );
  }
}

String _formatDate(DateTime date) {
  final format = DateFormat.yMMMd('en_US');
  return format.format(date);
}

class Product {
  const Product(
    this.date,
    this.billNo,
    this.productName,
    this.txnType,
    this.totalAmount,
    this.receivedAmount,
    this.paidAmount,
    this.balanceAmount,
  );

  final String date;
  final String billNo;
  final String productName;
  final String txnType;
  final double totalAmount;
  final double receivedAmount;
  final double paidAmount;
  final double balanceAmount;

  String getIndex(int index) {
    switch (index) {
      case 0:
        return date;
      case 1:
        return billNo;
      case 2:
        return productName;
      case 3:
        return txnType;
      case 4:
        return totalAmount.toString();
      case 5:
        return receivedAmount.toString();
      case 6:
        return paidAmount.toString();
      case 7:
        return balanceAmount.toString();
    }
    return '';
  }
}
