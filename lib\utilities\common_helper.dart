import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:gallery_saver/gallery_saver.dart';
import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/login_helper.dart';
import 'package:mobile_khaata_v2/utilities/shared_pref_helper1.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';

import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:tuple/tuple.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:uuid/uuid.dart';
import 'package:nepali_utils/nepali_utils.dart';

// import 'constants.dart';

class CustomException implements Exception {
  String cause;

  CustomException(this.cause);

  @override
  String toString() {
    return cause;
  }
}

String getFiscalYear(String nepaliDate) {
  if (null == nepaliDate || "" == nepaliDate) {
    nepaliDate = currentDateBS;
  }
  List<String> seps = nepaliDate.split("-");
  List<int?> sepsint = seps.map((e) => int.tryParse(e)).toList();
  int year = sepsint[0] ?? 0;
  int month = sepsint[1] ?? 0;
  return month < 4 ? "${year - 1}-$year" : "${year}-${year + 1}";
}

String get uuidV4 {
  var uuid = const Uuid();
  return uuid.v4();
}

Future setExpiredUser({bool flag = true}) async {
  isExpired = flag;
  await SharedPrefHelper1().setExpired(flag);
}

Future setActiveUser({int active = 0}) async {
  isActive = active;
  await SharedPrefHelper1().setActive(active);
}

Future setMultiUser({int status = 0}) async {
  isMultiUser = status;
  await SharedPrefHelper1().setMultiUser(status);
}

Future setWebEnabledValue({bool status = false}) async {
  isWebEnabled = status;
  await SharedPrefHelper1().setWebEnabled(status);
}

Future refreshExpiredUser() async {
  isExpired = await SharedPrefHelper1().isExpired;
}

Future refreshActiveUser() async {
  isActive = await SharedPrefHelper1().isActive;
}

Future refreshMultiUser() async {
  isMultiUser = await SharedPrefHelper1().isMultiUser;
}

Future refreshUnAuthorized() async {
  isUnauthorized = await SharedPrefHelper1().isUnauthorized;
}

bool get isExpiredUser {
  return isExpired;
}

int get isActiveUser {
  return isActive;
}

Future<String> getLastActivityBy() async {
  String lastActivityBy = await LoginHelper().getLastActivityBy;
  return lastActivityBy;
}

Future<String> getPrimaryKeyPrefix() async {
  LoginHelper loginHelper = LoginHelper();
  Map<String, dynamic> decodedToken = await loginHelper.decodedAccessToken;

  String primaryKeyPrefix = '${decodedToken['sub']}-';

  if (!decodedToken['is_admin']) {
    primaryKeyPrefix = '$primaryKeyPrefix${decodedToken['sub_user_id']}-';
  }

  return primaryKeyPrefix;
}

String strTrim(String value) {
  if (value.isNotEmpty) {
    return value.trim();
  } else {
    return "";
  }
}

double? parseDouble(dynamic value) {
  if (Null == value.runtimeType) {
    value = "";
  } else if (String != value.runtimeType) {
    value = value.toString();
  }

  if (null != value && "" != value) {
    return double.tryParse(value);
  }

  return 0.00;
}

Future<Tuple2<List<int>, String>> compressImage(File imageFile) async {
  List<int> compressedImage;
  String? imageExt;
  Uint8List? temp;

  try {
    compressedImage = imageFile.readAsBytesSync();
    String fileName = imageFile.path.split('/').last;
    imageExt = fileName.split('.').last;

    temp = Uint8List.fromList(compressedImage);

    int compressionCounter = 0;
    while (compressedImage.length > MAX_IMAGE_SIZE && compressionCounter < 4) {
      compressedImage = await FlutterImageCompress.compressWithList(
        temp,
        minWidth: 1000,
        minHeight: 1000,
        quality: 40,
      );
      compressionCounter++;
    }
  } catch (e) {
    print("this is error $e");
    // Log.e("Compress Image Fn", e.toString() + trace.toString());
  }

  return Tuple2(temp!, imageExt!);
}

Future<bool> createSms(String number, String message) async {
  message = Uri.encodeFull(message);

  String uri = "sms:$number";

  if (Platform.isAndroid) {
    uri = "$uri?body=$message";
  } else if (Platform.isIOS) {
    uri = "$uri&body=$message";
  }

  if (await canLaunchUrl(Uri.parse(uri))) {
    await launchUrl(Uri.parse(uri));
    return true;
  } else {
    return false;
  }
}

Future<bool> saveImageFile(String tempPath) async {
  bool? status = await GallerySaver.saveImage(tempPath);
  if (status != null) {
    return status;
  } else {
    return false;
  }
}

Future<bool> dialNumber(String number) async {
  String uri = "tel:$number";

  if (Platform.isAndroid) {
    uri = uri;
  } else if (Platform.isIOS) {
    uri = uri;
  }

  // Log.d("phont $uri");

  if (await canLaunchUrl(Uri.parse(uri))) {
    await launchUrl(Uri.parse(uri));
    return true;
  } else {
    // Log.d("can NOT launch");
    return false;
  }
}

Future<bool> sendSMS(String mobileNo, String message) async {
  bool status = await createSms(mobileNo, message);
  return status;
}

//DateTime Functions
String get currentDateTime {
  return DateFormat('y-MM-dd HH:mm:ss').format(DateTime.now());
}

String get tomorrowDateTime {
  return DateFormat('y-MM-dd HH:mm:ss')
      .format(DateTime.now().add(const Duration(days: 1)));
}

String get currentDate {
  return DateFormat("y-MM-dd").format(DateTime.now());
}

String get tomorrowDate {
  return DateFormat("y-MM-dd").format(
    DateTime.now().add(
      const Duration(days: 1),
    ),
  );
}

String get tomorrowDateBS {
  return NepaliDateTime.now()
      .add(const Duration(days: 1))
      .format('y-MM-dd')
      .toString();
}

String get currentTime {
  return DateFormat("HH:mm:ss").format(DateTime.now());
}

String get currentDateTimeBS {
  return NepaliDateTime.now().format('y-MM-dd HH:mm:ss').toString();
}

String get currentDateBS {
  return NepaliDateTime.now().format('y-MM-dd').toString();
}

String toDateTimeBS(DateTime dateTime) {
  return DateTime(dateTime.year, dateTime.month, dateTime.day, dateTime.hour,
          dateTime.minute, dateTime.second)
      .toNepaliDateTime()
      .format("y-MM-dd HH:mm:ss")
      .toString();
}

String toFormattedBS(DateTime dateTime, {String format = 'y-MM-dd HH:mm:ss'}) {
  return DateTime(dateTime.year, dateTime.month, dateTime.day, dateTime.hour,
          dateTime.minute, dateTime.second)
      .toNepaliDateTime()
      .format(format)
      .toString();
}

String toDateBS(DateTime dateTime) {
  return DateTime(dateTime.year, dateTime.month, dateTime.day)
      .toNepaliDateTime()
      .format("y-MM-dd");
}

String toTimeBS(DateTime dateTime) {
  return DateTime(dateTime.year, dateTime.month, dateTime.day, dateTime.hour,
          dateTime.minute, dateTime.second)
      .toNepaliDateTime()
      .format("HH:mm:ss")
      .toString();
}

String toDateTimeAD(NepaliDateTime ntDateTime) {
  return DateFormat('y-MM-dd HH:mm:ss').format(ntDateTime.toDateTime());
}

String toDateAD(NepaliDateTime ntDateTime) {
  return DateFormat('y-MM-dd').format(ntDateTime.toDateTime());
}

Future<String> nepaliDatePicker(BuildContext context,
    [String? previousDate,
    NepaliDateTime? minBSDate,
    NepaliDateTime? maxBSDate]) async {
  NepaliDateTime? dateTime;
  try {
    dateTime = NepaliDateTime.tryParse(previousDate ?? "");

    dateTime ??= NepaliDateTime.now();
  } catch (_) {
    dateTime = NepaliDateTime.now();
  }

  NepaliDateTime? selectedDateTime = await showMaterialDatePicker(
    context: context,
    initialDate: dateTime,
    firstDate: minBSDate ?? NepaliDateTime(2000),
    lastDate: maxBSDate ?? NepaliDateTime(2099),
    initialDatePickerMode: DatePickerMode.day,
  );

  if (selectedDateTime != null) {
    return selectedDateTime.format('y-MM-dd').toString();
  } else {
    return NepaliDateTime.now().format('y-MM-dd').toString();
  }
}

// //6:00 AM
// Future<String> timePicker(BuildContext context, [String previousTime]) async {
//   DateTime dateTime;
//   try {
//     dateTime = DateFormat.jm().parse(previousTime);
//   } catch (_) {
//     dateTime = DateTime.now();
//   }

//   var timeOfDay = await showTimePicker(
//     context: context,
//     initialTime: TimeOfDay.fromDateTime(dateTime),
//   );

//   if (timeOfDay != null) {
//     return timeOfDay.format(context);
//   }
//   return null;
// }

// Widget customImageBox(Uint8List image, {Function onCancel}) {
//   return Container(
//       padding: EdgeInsets.symmetric(vertical: 15),
//       child: Stack(
//         children: [
//           Container(
//             width: 150,
//             height: 150,
//             child: Image(
//               image: MemoryImage(image),
//               fit: BoxFit.fitWidth,
//             ),
//           ),
//           if (null != onCancel) ...{
//             Positioned(
//               right: 5,
//               top: 5,
//               child: GestureDetector(
//                 onTap: onCancel,
//                 child: CircleAvatar(
//                   radius: 15,
//                   backgroundColor: Colors.black45,
//                   child: Icon(
//                     Icons.close,
//                     color: Colors.white,
//                   ),
//                 ),
//               ),
//             )
//           }
//         ],
//       ));
// }

Future<Tuple2<bool, String>> checkPermission(
    {required BuildContext context, required String forPermission}) async {
  String tag = "PermissionCheck";
  ProgressDialog progressDialog = ProgressDialog(context,
      type: ProgressDialogType.normal, isDismissible: true);
  progressDialog.update(message: "Checking Permission. Please wait....");
  await progressDialog.show();
  Tuple2<bool, String> checkResponse = const Tuple2(false, "");

  try {
    checkResponse = await PermissionWrapperController()
        .requestForPermissionCheck(forPermission: forPermission);
  } catch (e) {
    // Log.e(tag, e.toString());
    checkResponse = const Tuple2(false, "Network error");
  }

  await progressDialog.hide();
  return checkResponse;
}
