import 'package:get/get.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
// import 'package:mobile_khaata/http/ApiBaseHelper.dart';

class GetStartedController extends GetxController {
  final String tag = "GetStartedController";

  final _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  final _isError = false.obs;
  bool get isError => _isError.value;
  String errorMessage = "";
  RxString title = "".obs;
  String? subTitle;
  String? url;
  String? urlType; //image or video

  init() async {
    _isError(false);
    _isLoading(true);
    errorMessage = "";

    ApiBaseHelper apiBaseHelper = ApiBaseHelper();
    ApiResponse apiResponse = await apiBaseHelper
        .get(apiBaseHelper.GET_STARTED_INFO, accessToken: false);

    if (apiResponse.status == true) {
      try {
        title.value = apiResponse.data['title'];
        subTitle = apiResponse.data['sub_title'] ?? "";
        url = apiResponse.data['link'] ?? "";
        urlType = apiResponse.data['link_type'] ?? "";

        // Log.d(apiResponse.data);
        _isLoading(false);
        update();
      } catch (e) {
        _isError(true);
        errorMessage = "Cannot load at this moment. Please try again later.";
        _isLoading(false);
      }
    } else {
      //error in gettting links
      _isError(true);
      errorMessage = apiResponse.msg ??
          "Cannot load at this moment. Please try again later.";
      _isLoading(false);
    }
  }
}
