import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/youtube_turorial_model.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';

class YoutubeTutorialListController extends GetxController {
  final String tag = "YoutubeTutorialListController";
  bool _isLoading = true;
  bool get isLoading => _isLoading;

  bool _isError = false;
  bool get isError => _isError;

  List<YoutubeTutorialModal> tutorials = [];

  String errorMessage = "";

  @override
  void onInit() async {
    super.onInit();
    await listAllTutorials();
  }

  Future<List<YoutubeTutorialModal>> listAllTutorials() async {
    _isError = false;
    _isLoading = true;
    errorMessage = "";

    ApiBaseHelper apiBaseHelper = ApiBaseHelper();
    ApiResponse apiResponse = await apiBaseHelper
        .get(apiBaseHelper.YOUTUBE_TUTORIALS, accessToken: false);
    debugPrint(apiResponse.data.toString());

    if (apiResponse.status) {
      try {
        List<dynamic> links = (apiResponse.data['tutorial_links'] ?? []);
        tutorials = links.map((e) => YoutubeTutorialModal.fromJson(e)).toList();
        debugPrint(tutorials.toString());
        _isLoading = false;
      } catch (e) {
        _isError = true;
        errorMessage =
            "Cannot load video at this moment. Please try again later.";
        _isLoading = false;
      }
    } else {
      //error in gettting links
      _isError = true;
      errorMessage = apiResponse.msg ?? "";
      _isLoading = false;
    }

    return tutorials;
  }
}
