// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_image_picker/form_builder_image_picker.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/controllers/registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/update_registration_detail/update_registration_detail_controller.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

class UpdateRegistrationDetailPage extends StatelessWidget {
  final String tag = "UpdateRegistrationDetailPage";

  final updateRegistrationDetailController =
      UpdateRegistrationDetailController();

  UpdateRegistrationDetailPage({super.key}) {
    updateRegistrationDetailController.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (updateRegistrationDetailController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
        appBar: AppBar(
          elevation: 4,
          centerTitle: false,
          backgroundColor: colorPrimary,
          titleSpacing: -5.0,
          title: const Text(
            "Update Registration Detail",
            style: TextStyle(
                fontSize: 18,
                color: Colors.white,
                fontFamily: 'HelveticaRegular',
                fontWeight: FontWeight.bold),
          ),
          actions: [
            InkWell(
                onTap: () => updateRegistrationDetailController.readOnlyFlag =
                    !updateRegistrationDetailController.readOnlyFlag,
                splashColor: colorPrimaryLighter,
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                  child: (updateRegistrationDetailController.readOnlyFlag)
                      ? Column(
                          mainAxisSize: MainAxisSize.min,
                          children: const [
                            Icon(
                              Icons.mode_edit,
                              color: Colors.white,
                            ),
                            Text(
                              "Edit",
                              style:
                                  TextStyle(color: Colors.white, fontSize: 10),
                            ),
                          ],
                        )
                      : Column(
                          mainAxisSize: MainAxisSize.min,
                          children: const [
                            Icon(
                              Icons.close,
                              color: Colors.white,
                            ),
                            Text(
                              "Cancel",
                              style:
                                  TextStyle(color: Colors.white, fontSize: 10),
                            ),
                          ],
                        ),
                )),
          ],
        ),
        body: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: SingleChildScrollView(
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 15, horizontal: 15),
              child: Form(
                key: updateRegistrationDetailController.formKey,
                child: Column(
                  children: [
                    //==========================================Image
                    SizedBox(
                      width: 130,
                      child: Center(
                        child: !updateRegistrationDetailController
                                .fileUploadPermission
                            ? FormBuilderImagePicker(
                                enabled: !updateRegistrationDetailController
                                        .readOnlyFlag &&
                                    updateRegistrationDetailController
                                        .fileUploadPermission,
                                name: "image_picker",
                                decoration: const InputDecoration(
                                  border: InputBorder.none,
                                ),
                                maxImages: 1,
                                iconColor: colorPrimaryLight,
                                initialValue: updateRegistrationDetailController
                                            .kera ==
                                        null
                                    ? []
                                    : [
                                        updateRegistrationDetailController.kera!
                                      ],
                                onChanged: (value) =>
                                    updateRegistrationDetailController
                                        .imagePickerOnChangeHandler(value),
                              )
                            : Container(
                                height: 130,
                                width: 130,
                                decoration: BoxDecoration(
                                  color: Colors.black12,
                                  borderRadius: BorderRadius.circular(10.0),
                                ),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.image_not_supported_outlined,
                                      size: 50.0,
                                      color: Colors.black26,
                                    ),
                                    const SizedBox(height: 10.0),
                                    Text(
                                      "File Upload \nNot Allowed",
                                      style: TextStyle(
                                        color: Colors.black54,
                                        fontSize: 12.0,
                                        fontFamily: 'HelveticaRegular',
                                      ),
                                    ),
                                  ],
                                )),
                      ),
                    ),
                    const SizedBox(height: 20.0),

                    //====================================FullName
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          'व्यवसायीको नाम  (Proprietor’s Name) ',
                          style: RegformLabelStyle,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "fullname",
                          autocorrect: false,
                          readOnly:
                              updateRegistrationDetailController.readOnlyFlag,
                          keyboardType: TextInputType.text,
                          textInputAction: TextInputAction.next,
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              hintText: "Proprietor’s Full Name"),
                          initialValue: updateRegistrationDetailController
                              .registrationDetail.fullName,
                          onChanged: (value) {
                            updateRegistrationDetailController
                                    .registrationDetail.fullName =
                                toBeginningOfSentenceCase(strTrim(value ?? ""));
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Proprietor’s Name cannot be empty';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 20.0),

                    //====================================Mobile Number
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          'मोबाईल नम्बर  (Mobile No.)',
                          style: RegformLabelStyle,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "mobile",
                          readOnly: true,
                          autocorrect: false,
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          textInputAction: TextInputAction.next,
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              hintText: "Mobile No.",
                              fillColor: Colors.black26,
                              filled: true),
                          initialValue: updateRegistrationDetailController
                              .registrationDetail.primaryMobileNo,
                        ),
                      ],
                    ),
                    const SizedBox(height: 20.0),

                    //====================================Shop Name
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          'पसल / व्यवसायको नाम\n(Shop / Company Name)',
                          style: RegformLabelStyle,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: 'company_name',
                          autocorrect: false,
                          readOnly:
                              updateRegistrationDetailController.readOnlyFlag,
                          keyboardType: TextInputType.text,
                          textInputAction: TextInputAction.next,
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                            hintText: 'Shop / Company Name',
                          ),
                          initialValue: updateRegistrationDetailController
                              .registrationDetail.businessName,
                          onChanged: (value) {
                            updateRegistrationDetailController
                                    .registrationDetail.businessName =
                                toBeginningOfSentenceCase(strTrim(value ?? ""));
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Shop / Company Name cannot be empty';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 20.0),

                    //====================================Shop Address
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          'पसल / व्यवसायको ठेगाना\n(Shop / Company Address)',
                          style: RegformLabelStyle,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "company_address",
                          autocorrect: false,
                          readOnly:
                              updateRegistrationDetailController.readOnlyFlag,
                          keyboardType: TextInputType.text,
                          textInputAction: TextInputAction.done,
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              hintText: "Shop / Company Address"),
                          initialValue: updateRegistrationDetailController
                              .registrationDetail.businessAddress,
                          onChanged: (value) {
                            updateRegistrationDetailController
                                    .registrationDetail.businessAddress =
                                toBeginningOfSentenceCase(strTrim(value ?? ""));
                          },
                          validator: (value) {
                            value = value?.trim();
                            if (value == null || value.isEmpty) {
                              return 'Please enter Shop / Company Address!';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 20.0),

                    //====================================PAN/VAT No.
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Text(
                          'पान / मु. अ. कर नम्बर (PAN / VAT No.)',
                          style: RegformLabelStyle,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "pan_no",
                          autocorrect: false,
                          readOnly:
                              updateRegistrationDetailController.readOnlyFlag,
                          keyboardType: TextInputType.number,
                          inputFormatters: [
                            FilteringTextInputFormatter.digitsOnly
                          ],
                          textInputAction: TextInputAction.next,
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              hintText: "PAN / VAT No."),
                          initialValue: updateRegistrationDetailController
                              .registrationDetail.tinNo,
                          onChanged: (value) {
                            updateRegistrationDetailController
                                .registrationDetail
                                .tinNo = strTrim(value ?? "");
                          },
                          validator: (value) {
                            value = value?.trim();
                            if (value != null &&
                                value.isNotEmpty &&
                                value.length < 9) {
                              return 'PAN / VAT No. must be at least 9 characters!';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 20.0),

                    //====================================PAN/VAT Type
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'PAN हो / VAT हो ? छान्नुस् होस्',
                          style: TextStyle(fontSize: 14, color: Colors.black54),
                        ),
                        FormBuilderChoiceChip(
                          name: "pan_vat_type",
                          // readOnly: updateRegistrationDetailController
                          //     .readOnlyFlag,
                          enabled:
                              !updateRegistrationDetailController.readOnlyFlag,
                          selectedColor: colorPrimaryLight,
                          disabledColor: Colors.black12,

                          labelStyle: const TextStyle(color: Colors.white),
                          decoration: const InputDecoration(
                            border: InputBorder.none,
                          ),
                          spacing: 20,
                          initialValue: updateRegistrationDetailController
                              .registrationDetail.tinFlag,
                          onChanged: (value) {
                            updateRegistrationDetailController
                                .registrationDetail.tinFlag = value;
                          },
                          alignment: WrapAlignment.start,
                          options: const [
                            FormBuilderChipOption(
                              value: "PAN",
                              child: Text(
                                "PAN",
                                style: TextStyle(
                                  color: Colors.white,
                                ),
                              ),
                            ),
                            FormBuilderChipOption(
                              value: "VAT",
                              child: Text(
                                "VAT",
                                style: TextStyle(
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        bottomNavigationBar: BottomSaveCancelButton(
          shadow: false,
          enableFlag: !updateRegistrationDetailController.readOnlyFlag,
          onSaveBtnPressedFn: (updateRegistrationDetailController.readOnlyFlag)
              ? null
              : () async {
                  FocusScope.of(context).unfocus();
                  print("=====================");
                  print(updateRegistrationDetailController
                      .registrationDetail.tinNo);
                  print(updateRegistrationDetailController
                      .registrationDetail.tinFlag);
                  if (updateRegistrationDetailController.formKey.currentState!
                      .validate()) {
                    bool isLargeFile =
                        updateRegistrationDetailController.checkIfLargeImage();
                    if (isLargeFile) {
                      showToastMessage(context,
                          message: MAX_IMAGE_SIZE_MESSAGE,
                          alertType: AlertType.Error);
                      return;
                    }

                    if (updateRegistrationDetailController
                                .registrationDetail.tinFlag !=
                            null &&
                        updateRegistrationDetailController
                            .registrationDetail.tinFlag!.isNotEmpty) {
                      if (updateRegistrationDetailController
                                  .registrationDetail.tinNo ==
                              null ||
                          updateRegistrationDetailController
                                  .registrationDetail.tinNo!.length !=
                              9) {
                        showAlertDialog(context,
                            alertType: AlertType.Error,
                            alertTitle: "Error",
                            message: "Enter Pan/Vat No. of 9 digits only");
                        return;
                      }
                    }

                    if (updateRegistrationDetailController
                                .registrationDetail.tinNo !=
                            null &&
                        updateRegistrationDetailController
                            .registrationDetail.tinNo!.isNotEmpty) {
                      print("Tin NO chai xa hai xaa");
                      if (updateRegistrationDetailController
                                  .registrationDetail.tinFlag ==
                              null ||
                          updateRegistrationDetailController
                              .registrationDetail.tinFlag!.isEmpty) {
                        print("Tin flag chai xaina hai xaa");
                        showAlertDialog(context,
                            alertType: AlertType.Error,
                            alertTitle: "Error",
                            message: "Select PAN or VAT Type!");
                        return;
                      }
                    }

                    ProgressDialog progressDialog = ProgressDialog(context,
                        type: ProgressDialogType.normal, isDismissible: false);
                    progressDialog.update(
                        message: "Saving data. Please wait....");
                    await progressDialog.show();

                    bool status = false;

                    try {
                      status = await updateRegistrationDetailController
                          .updateDetail();
                    } catch (e, trace) {
                      Log.e(tag, e.toString() + trace.toString());
                    }
                    await progressDialog.hide();

                    if (status) {
                      Navigator.pop(context, true);

                      TransactionHelper.refreshPreviousPages();

                      String message = "Detail Updated Successfully.";
                      showToastMessage(context, message: message);
                    } else {
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "Error",
                          message: "Failed To Save data");
                    }
                  }
                },
        ),
      ));
    });
  }
}
