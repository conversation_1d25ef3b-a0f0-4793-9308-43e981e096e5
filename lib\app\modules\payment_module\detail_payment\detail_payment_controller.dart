import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/database/txn_image_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/image_repository.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';
import 'package:mobile_khaata_v2/app/repository/txn_image_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';

import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:tuple/tuple.dart';

class DetailPaymentController extends GetxController {
  final String tag = "DetailPaymentController";

  final LedgerRepository _ledgerRepository = LedgerRepository();
  final ImageRepository _imageRepository = ImageRepository();
  final TransactionRepository _transactionRepository = TransactionRepository();
  final TxnImageRepository _txnImageRepository = TxnImageRepository();

  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  final _editFlag = false.obs;
  bool get editFlag => _editFlag.value;

  final _readOnlyFlag = false.obs;
  bool get readOnlyFlag => _readOnlyFlag.value;
  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  LedgerDetailModel _ledger = LedgerDetailModel();
  LedgerDetailModel get ledger => _ledger;
  ImageModel _image = ImageModel();
  ImageModel get image => _image;

  final _transaction = TransactionModel().obs;
  TransactionModel get transaction => _transaction.value;

  TxnImageModel? txnImage;
  File? txnImageFile;

  final formKey = GlobalKey<FormState>();
  TextEditingController remainingBalanceAmount = TextEditingController();
  final TextEditingController paymentRefCtrl = TextEditingController();

  initNew(String ledgerId) async {
    _isLoading(true);

    await loadLedgerDetail(ledgerId);
    transaction.txnDateBS = currentDateBS;

    remainingBalanceAmount.text = ledger.balanceAmount.toString();

    _isLoading(false);
  }

  refreshTransaction() {
    _transaction.refresh();
  }

  @override
  void dispose() {
    paymentRefCtrl.dispose();

    super.dispose();
  }

  initEdit(String txnId) async {
    _isLoading(true);

    _transaction.value =
        (await _transactionRepository.getTransactionByTxnId(txnId))!;
    await loadLedgerDetail(transaction.ledgerId ?? "");
    paymentRefCtrl.text = _transaction.value.txnPaymentReference ?? "";

    remainingBalanceAmount.text = ledger.balanceAmount.toString();
    ledger.balanceAmount = ledger.balanceAmount! - transaction.txnCashAmount!;

    List<TxnImageModel> txnImageList = await _txnImageRepository
        .getImagesForTransaction(transaction.txnId ?? "");
    if (txnImageList.isNotEmpty) {
      //image not null
      final tempDir = await getTemporaryDirectory();
      final file =
          await File('${tempDir.path}/image-000.${txnImageList[0].imageExt}')
              .create();
      file.writeAsBytesSync(txnImageList[0].imageBitmap!);
      txnImageFile = file;
    }

    _editFlag.value = true;
    readOnlyFlag = true;

    _isLoading(false);
  }

  loadLedgerDetail(String ledgerId) async {
    _ledger = await _ledgerRepository.getLedgerWithBalanceById(ledgerId);
    _image = await _imageRepository.getImageById(ledger.ledgerPhoto ?? "");
  }

  imagePickerOnChangeHandler(value) async {
    if (value.length > 0) {
      Tuple2<List<int>, String> selectedImage = await compressImage(value[0]);
      txnImage = TxnImageModel(
          imageBitmap: selectedImage.item1, imageExt: selectedImage.item2);
    } else {
      txnImage = null;
    }
  }

  bool checkIfLargeImage() {
    //preConvertFiles can be used to convert files list to txn image model once, so than it don't need re convert
    bool status = false;
    if (null != txnImage) {
      if (txnImage!.imageBitmap!.length > MAX_IMAGE_SIZE) {
        status = true;
      }
    }
    return status;
  }

  Future<bool> createTransaction() async {
    bool status = false;

    try {
      transaction.ledgerId = ledger.ledgerId;
      transaction.txnType = TxnType.paymentOut;
      transaction.txnBalanceAmount = 0.00;
      transaction.txnDate =
          toDateAD(NepaliDateTime.parse(transaction.txnDateBS ?? ""));

      DatabaseHelper databaseHelper = DatabaseHelper();
      Database? dbClient = await databaseHelper.database;

      await dbClient!.transaction((dbBatchTxn) async {
        // var dbBatchTxn = dnTxnClient.batch();
        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        String txnId = await _transactionRepository.insert(transaction,
            dbClient: dbBatchTxn, batchID: batchID);

        //Inserting new Images
        List<TxnImageModel> txnImageList = [];
        if (null != txnImage) {
          txnImageList.add(txnImage!);
        }
        await _txnImageRepository.setImagesForTransaction(txnId, txnImageList,
            dbClient: dbBatchTxn, batchID: batchID);

        // await dbBatchTxn.commit(continueOnError: false, noResult: true);

        status = true;
      });
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<bool> updateTransaction() async {
    bool status = false;

    try {
      transaction.txnType = TxnType.paymentOut;
      transaction.txnBalanceAmount = 0.00;
      transaction.txnDate =
          toDateAD(NepaliDateTime.parse(transaction.txnDateBS ?? ""));

      DatabaseHelper databaseHelper = DatabaseHelper();
      Database? dbClient = await databaseHelper.database;

      await dbClient!.transaction((dbBatchTxn) async {
        // var dbBatchTxn = dnTxnClient.batch();
        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        await _transactionRepository.update(transaction,
            dbClient: dbBatchTxn, batchID: batchID);

        //Deleting old transaction images
        await _txnImageRepository.deleteImagesForTransaction(
            transaction.txnId ?? "",
            dbClient: dbBatchTxn,
            batchID: batchID);

        //Inserting new Images
        List<TxnImageModel> txnImageList = [];
        if (null != txnImage) {
          txnImageList.add(txnImage!);
        }
        await _txnImageRepository.setImagesForTransaction(
            transaction.txnId ?? "", txnImageList,
            dbClient: dbBatchTxn, batchID: batchID);

        // await dbBatchTxn.commit(continueOnError: false, noResult: true);

        status = true;
      });
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return status;
  }
}
