// ignore_for_file: use_build_context_synchronously

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/model/others/cheque_modal.dart';
import 'package:mobile_khaata_v2/app/modules/cheque_module/cheque_deposit/cheque_deposit_page.dart';
import 'package:mobile_khaata_v2/app/repository/cheque_repository.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:tuple/tuple.dart';

class ChequeListController extends GetxController {
  final String tag = "ChequeListController";

  var _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  List<ChequeModel> _chequeList = [];
  List<ChequeModel> get chequeList => _chequeList;

  ChequeRepository _chequeRepository = ChequeRepository();

  List<Map<String, dynamic>> chequeTypes = [
    {"text": "Open Cheque", "value": 0},
    {"text": "Closed Cheque", "value": 1},
    {"text": "All Cheques", "value": -1},
  ];

  int chequeType = 1;
  String listOrder = "date";

  init() async {
    _isLoading.value = true;

    _chequeList.clear();

    _chequeList.addAll(
        await _chequeRepository.getAllCheque(chequeType, orderBy: listOrder));

    _isLoading.value = false;
  }

  Future<bool> reOpenCheque(ChequeModel cheque) async {
    bool status = false;
    try {
      ChequeModel newChequeModel = ChequeModel();
      newChequeModel = cheque;
      newChequeModel.chequeTransferredToAccId = null;
      newChequeModel.chequeCurrentStatus = 0;
      newChequeModel.chequeTransferDate = null;

      status = await _chequeRepository.updateChequeStatus(newChequeModel);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  depositWithdrawOnClickHandler(BuildContext context, String txnId) async {
    Tuple2<bool, String> checkResponse = await checkPermission(
        context: context,
        forPermission: PermissionManager.chequeDepositWithdraw);
    if (checkResponse.item1) {
      Navigator.pushNamed(context, "/chequeDeposit",
          arguments: ChequeDepositPage(
            chequeTxnId: txnId,
          ));
    } else {
      //no  permission
      showAlertDialog(context,
          alertType: AlertType.Error,
          alertTitle: "",
          message: checkResponse.item2);
    }
  }
}
