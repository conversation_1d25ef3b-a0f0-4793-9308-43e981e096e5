import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';

import 'package:tuple/tuple.dart';
import 'package:get/get.dart';

class UpdateAgentCodeController extends GetxController {
  var _isSubmitting = false.obs;
  bool get isSubmitting => _isSubmitting.value;

  final formKey = GlobalKey<FormBuilderState>();

  TextEditingController agentCodeCtrl = new TextEditingController();
  @override
  void onInit() {
    super.onInit();
  }

  Future<Tuple3<bool, String, dynamic>> verifyAgent() async {
    bool status = false;
    String message = "";
    dynamic dt = {};
    _isSubmitting(true);
    try {
      ApiBaseHelper apiBaseHelper = ApiBaseHelper();
      ApiResponse apiResponse = await apiBaseHelper.post(
          apiBaseHelper.VERIFY_AGENT, {'agent_code': agentCodeCtrl.text},
          accessToken: true);

      if (apiResponse.status) {
        status = true;
        dt = apiResponse.data ?? {};
      } else {}
      message = apiResponse.msg ?? "";
    } catch (e) {
      // Log.d(e);
      message = FAILED_OPERATION_ERROR;
    }
    _isSubmitting(false);
    return Tuple3(status, message, dt);
  }

  Future<Tuple2<bool, String>> updateAgent() async {
    bool status = false;
    String message = "";
    _isSubmitting(true);
    try {
      ApiBaseHelper apiBaseHelper = ApiBaseHelper();
      ApiResponse apiResponse = await apiBaseHelper.post(
          apiBaseHelper.AGENTT_UPDATE, {'agent_code': agentCodeCtrl.text},
          accessToken: true);

      if (apiResponse.status) {
        status = true;
      } else {}
      message = apiResponse.msg ?? "";
    } catch (e) {
      // Log.d(e);
      message = FAILED_OPERATION_ERROR;
    }
    _isSubmitting(false);
    return Tuple2(status, message);
  }
}
