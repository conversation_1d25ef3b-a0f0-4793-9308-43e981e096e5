import 'package:intl/intl.dart';

import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class CashAdjustmentModel {
  CashAdjustmentModel(
      {this.cashAdjId,
      this.cashAdjDate,
      this.cashAdjDateBS,
      this.cashAdjType,
      this.cashAdjAmount,
      this.cashAdjDescription,
      this.lastActivityType,
      this.lastActivityAt,
      this.lastActivityBy});

  String? cashAdjId;
  String? cashAdjDate;
  String? cashAdjDateBS;
  int? cashAdjType;
  double? cashAdjAmount;
  String? cashAdjDescription;

  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;

  factory CashAdjustmentModel.fromJson(Map<String, dynamic> json) {
    DateTime cashAdjDateTime = DateTime.parse(json["cash_adj_date"]);
    String cashAdjDate = DateFormat('y-MM-dd').format(cashAdjDateTime);
    String cashAdjDateBS = toDateBS(cashAdjDateTime);

    return CashAdjustmentModel(
        cashAdjId: json["cash_adj_id"],
        cashAdjDate: cashAdjDate,
        cashAdjDateBS: cashAdjDateBS,
        cashAdjType: json["cash_adj_type"],
        cashAdjAmount: parseDouble(json["cash_adj_amount"]),
        cashAdjDescription: json["cash_adj_description"],
        lastActivityType: json["last_activity_type"],
        lastActivityAt: json["last_activity_at"],
        lastActivityBy: json['last_activity_by']);
  }

  Map<String, dynamic> toJson() => {
        "cash_adj_id": cashAdjId,
        "cash_adj_date": cashAdjDate,
        "cash_adj_type": cashAdjType,
        "cash_adj_amount": cashAdjAmount,
        "cash_adj_description": cashAdjDescription,
        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        "last_activity_by": lastActivityBy
      };
}
