import 'dart:convert';

import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class BankTransactionModel {
  String? bankId;
  String? bankShortName;
  String? bankId2;
  String? bankShortName2;
  String? txnId;
  String? txnDate;
  String? txnDateBS;
  int? txnType;
  String? txnDesc;
  double? txnAmount;
  String? txnMode;
  String? txnTypeText;
  int? bankTransactionType;
  String? bankTransactionDesc;

  BankTransactionModel({
    this.bankId,
    this.bankShortName,
    this.bankId2,
    this.bankShortName2,
    this.txnId,
    this.txnDate,
    this.txnDateBS,
    this.txnType,
    this.txnDesc,
    this.txnAmount,
    this.txnMode,
    this.txnTypeText,
    this.bankTransactionType,
    this.bankTransactionDesc,
  });

  factory BankTransactionModel.fromJson(Map<String, dynamic> json) {
    DateTime txnDateTime = DateTime.parse(json["txn_date"]);
    String txnDate = DateFormat('y-MM-dd').format(txnDateTime);
    String txnDateBS = toDateBS(txnDateTime);

    int bankTransactionType = (0 <= json['txn_amount'])
        ? TxnType.increaseBankBalance
        : TxnType.decreaseBankBalance;
    String? txtTypeText = TxnType.txnTypeText[json["txn_type"]];
    String? bankTransactionDesc = json["txn_desc"];

    if (TxnType.bankToBankTransfer == json["txn_type"]) {
      if (bankTransactionType == TxnType.increaseBankBalance) {
        txtTypeText = "From bank";
      } else {
        txtTypeText = "To bank";
      }
      bankTransactionDesc = json["bank_name_2"];
    } else if ('cheque' == json["txn_mode"]) {
      txtTypeText = TxnType.txnTypeText[TxnType.chequeTransfer];
    }

    return BankTransactionModel(
      bankId: json['bank_id'],
      bankShortName: json['bank_short_name'],
      bankId2: json['bank_id_2'],
      bankShortName2: json['bank_name_2'],
      txnId: json['txn_id'],
      txnDate: txnDate,
      txnDateBS: txnDateBS,
      txnType: json['txn_type'],
      txnDesc: json['txn_desc'],
      txnAmount: json['txn_amount'],
      txnMode: json['txn_mode'],
      txnTypeText: txtTypeText,
      bankTransactionType: bankTransactionType,
      bankTransactionDesc: bankTransactionDesc,
    );
  }

  //TODO  implement this later (might not be necessary)
  Map<String, dynamic> toJson() => {
        "txn_id": txnId,
        "txn_date": txnDate,
        "txn_date_bs": txnDateBS,
        "txn_type": txnType,
        "txn_type_text": txnTypeText,
        "txn_desc": txnDesc,
        "txn_amount": txnAmount,
      };
  String toString() {
    return jsonEncode(this.toJson());
  }
}
