import 'dart:io';
import 'dart:math' as math;
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_archive/flutter_archive.dart';
import 'package:jwt_decode/jwt_decode.dart';

import 'package:localstorage/localstorage.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/modules/home_module/home_screen_controller.dart';

import 'package:mobile_khaata_v2/app/modules/home_module/home_view.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/http/api_exception.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:mobile_khaata_v2/utilities/login_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';

class LandingScreen extends StatefulWidget {
  @override
  _LandingScreenState createState() => _LandingScreenState();
}

class _LandingScreenState extends State<LandingScreen> {
  final String _TAG = "Landing Screen";

  final map = Image.asset(
    'images/nepal_map_outline.png',
    fit: BoxFit.fill,
  );
  final flag = Image.asset(
    'images/nepal_flag.gif',
    fit: BoxFit.cover,
  );

  LoginHelper _loginHelper = new LoginHelper();

  String? _dir;
  String downloadingStr = "";
  double downloadProgress = 0.0;

  bool _downloadingFlag = true;

  @override
  void initState() {
    _downloadDatabase();
    super.initState();
  }

  _downloadDatabase() async {
    try {
      final storage = new LocalStorage(MobileSettings);
      // Log.d("this is storage => $MobileSettings");
      await storage.ready;

      String? accessToken = await _loginHelper.accessToken;

      Map<String, dynamic> decodedToken = Jwt.parseJwt(accessToken);
      String databaseName = decodedToken['sub'].toString() + databaseSuffix;

      // Log.d("this is databaseName: $databaseName");

      bool dbExist = storage.getItem(databaseName) ?? false;

      if (!dbExist) {
        // Log.d("not exist");

        _dir = (await getApplicationDocumentsDirectory()).path;

        String localFileName = 'database.zip';
        String url = Uri.encodeFull(ApiBaseUrl + 'download');

        Dio dio = Dio();
        dio.options.headers = {
          'Content-Type': 'application/json; charset=UTF-8',
          ApiKeyHeader: ApiKeySecret,
          'Authorization': 'Bearer ' + accessToken
        };

        await dio.download(url, '$_dir/$localFileName',
            onReceiveProgress: (rec, total) {
          setState(() {
            _downloadingFlag = true;
            downloadProgress = (rec / total) * 100;
            downloadingStr = (downloadProgress).toStringAsFixed(0) +
                "%" +
                "\nSynchronizing data from server.";
          });
        });

        var file = File('$_dir/$localFileName');

        final destinationDir = Directory(_dir!);

        await ZipFile.extractToDirectory(
            zipFile: file,
            destinationDir: destinationDir,
            onExtracting: (zipEntry, progress) {
              setState(() {
                downloadingStr =
                    progress.toStringAsFixed(0) + "%" + "\nVerifying data.";
              });
              return ZipFileOperation.includeItem;
            });

        setState(() {
          downloadingStr = "Verifying and Preparing data......";
        });

        await storage.setItem(databaseName, true);
        await file.delete();

        DatabaseHelper databaseHelper = new DatabaseHelper();
        Database? dbClient = await databaseHelper.database;

        databaseHelper.dispose();

        setState(() {
          _downloadingFlag = false;
          downloadingStr = "Setup Completed.";
        });
      } else {
        setState(() {
          _downloadingFlag = false;
        });
      }
    } on NoInternetException catch (e) {
      await _loginHelper.logout();

      showAlertDialog(context,
          alertType: AlertType.Error,
          alertTitle: "Error",
          message: e.toString(), onCloseButtonPressed: () {
        SystemChannels.platform.invokeMethod('SystemNavigator.pop');
      });
    } on DioError catch (e) {
      await _loginHelper.logout();

      // if (e.response!.statusCode == 404) {
      //   Log.e(_TAG, e.response!.statusCode.toString());
      //   Log.e(_TAG, e.message);
      // } else {
      //   Log.e(_TAG, e.message);
      //   Log.e(_TAG, e.response.toString());
      // }

      showAlertDialog(context,
          alertType: AlertType.Error,
          alertTitle: "Error",
          message: "Something went wrong while synchronizing data from server",
          onCloseButtonPressed: () {
        SystemChannels.platform.invokeMethod('SystemNavigator.pop');
      });
    } catch (e) {
      setState(() {
        _downloadingFlag = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;

    if (!_downloadingFlag) {
      return HomeScreen();
    } else {
      return SafeArea(
          child: Scaffold(
        body: Container(
          color: colorPrimary,
          child: Stack(
            fit: StackFit.expand,
            children: [
              Positioned(
                  top: screenHeight * 0.06,
                  left: 0,
                  right: 0,
                  child: Image.asset(
                    'images/logo-splash.png',
                    fit: BoxFit.contain,
                    height: 100,
                    width: 100,
                  )),
              Positioned(
                top: screenHeight * 0.22,
                left: 0,
                right: 0,
                child: RichText(
                  text: TextSpan(
                      text: "Preparing your",
                      style: TextStyle(
                        fontSize: 20,
                        color: Colors.white,
                      ),
                      children: <TextSpan>[
                        TextSpan(
                            text: " mobile ",
                            style: TextStyle(
                                fontWeight: FontWeight.w900,
                                fontFamily: "Arial",
                                fontSize: 24)),
                        TextSpan(
                            text: "खाता ",
                            style: TextStyle(
                                fontWeight: FontWeight.w800,
                                fontFamily: "ArialBlack",
                                fontSize: 26)),
                        TextSpan(
                            text: "\nDo not close the Application.",
                            style: TextStyle())
                      ]),
                  textAlign: TextAlign.center,
                ),
              ),
              Container(
                // margin: EdgeInsets.only(bottom: screenHeight * 0.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Center(
                        child: SizedBox(
                            height: 80,
                            width: 80,
                            child: CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                              strokeWidth: 6.0,
                            ))),
                    SizedBox(
                      height: 40,
                    ),
                    Text(downloadingStr,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            color: Colors.white, fontSize: 16, height: 1.8)),
                  ],
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: screenHeight * 0.12,
                child: Container(
                  padding: EdgeInsets.only(top: 15, left: 90),
                  height: 130,
                  width: 320,
                  decoration: BoxDecoration(
                      image: new DecorationImage(image: map.image)),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        height: 60,
                        child: flag,
                      ),
                      Transform.rotate(
                        angle: math.pi / 95,
                        child: Container(
                            margin: EdgeInsets.only(left: 0, top: 60),
                            child: Row(
                              children: [
                                Text(
                                  "अब बन्दैछ नेपाल",
                                  style: TextStyle(
                                    fontSize: 18,
                                    color: colorPrimary,
                                    fontWeight: FontWeight.w800,
                                  ),
                                ),
                                Text(
                                  " Digital",
                                  style: TextStyle(
                                    fontSize: 17,
                                    color: colorPrimary,
                                    fontWeight: FontWeight.w800,
                                  ),
                                )
                              ],
                            )),
                      ),
                    ],
                  ),
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: screenHeight * 0.04,
                child: Text(
                  "Vedanta Technologies",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white,
                  ),
                ),
              ),
            ],
          ),
        ),
      ));
    }
  }
}
