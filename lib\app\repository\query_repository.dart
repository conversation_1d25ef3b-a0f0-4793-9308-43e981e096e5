import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/model/others/batched_group_model.dart';
import 'package:mobile_khaata_v2/app/model/others/synced_batched_model.dart';
import 'package:mobile_khaata_v2/app/repository/synced_batched_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:sqflite/sqflite.dart';

class QueryRepository {
  final String tag = "QueryRepository";
  final String tableName = 'mk_sync_query';

  DatabaseHelper databaseHelper = new DatabaseHelper();
  SyncedBatchRepository syncedBatchRepository = new SyncedBatchRepository();

  Future<bool> pushQuery(QueryModel queryModel,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    if (null == dbClient) {
      dbClient = await databaseHelper.database;
    }

    if (null == batchID) {
      String primaryKeyPrefix = await getPrimaryKeyPrefix();
      batchID = primaryKeyPrefix + uuidV4;
    }

    String dateTime = currentDateTime;
    queryModel.createdAt = dateTime;

    queryModel.batchID = batchID;

    await dbClient.insert(tableName, queryModel.toJson());
    status = true;
    return status;
  }

  Future<bool> executeQuery(QueryModel queryModel, {dynamic dbClient}) async {
    bool status = false;

    try {
      if (null == dbClient) {
        dbClient = await databaseHelper.database;
      }
      switch (queryModel.queryType) {
        case QueryType.insert:
          await dbClient.insert(queryModel.tableName, queryModel.data,
              conflictAlgorithm: ConflictAlgorithm.ignore);
          break;
        case QueryType.delete:
          await dbClient.delete(queryModel.tableName,
              where: queryModel.whereClause, whereArgs: queryModel.whereArgs);
          break;
        case QueryType.update:
          await dbClient.update(queryModel.tableName, queryModel.data,
              where: queryModel.whereClause, whereArgs: queryModel.whereArgs);
          break;
        case QueryType.rawQuery:
          // perform raw query sent from server for complex query
          // Log.d("rawQuery ${queryModel.toJson()}");
          await dbClient.rawQuery(queryModel.whereClause);
          break;

        case QueryType.operation:
          // execute operation sent from server for migration or other operation
          // Log.d("executing ${queryModel.toJson()}");
          await dbClient.execute(queryModel.whereClause);
          break;
        default:
          break;
      }
      status = true;
    } catch (e) {}
    return status;
  }

  Future<bool> executeSingleSyncedQueries(
      Map<String, dynamic> singlebatchedQueries,
      {dynamic dbClient}) async {
    bool status = false;
    try {
      //for single batch;
      List<dynamic> queries = singlebatchedQueries['queries'] ?? [];
      List<QueryModel> queryModels = [];
      queries.map((e) {
        queryModels.add(QueryModel.fromJson(e));
      }).toList();

      if (null == dbClient) {
        dbClient = await databaseHelper.database;
      }
      await dbClient.transaction((batch) async {
        // var batch = txn.batch();
        await Future.wait(queryModels.map((e) async {
          await executeQuery(e, dbClient: batch);
        }).toList());

        //pushing batch id in synced table
        await syncedBatchRepository.pushSyncedBatchId(
            SyncedBatchModel.fromJson(
                {"batch_id": singlebatchedQueries['batch_id']}),
            dbClient: batch);

        // await await batch.commit(continueOnError: false, noResult: true);

        status = true;
      });
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }

  Future<List<BatchedGroupModel>> getPendingQueries(
      {bool all = false, dynamic dbClient, String? singleBatchId}) async {
    if (null == dbClient) {
      dbClient = await databaseHelper.database;
    }
    List<BatchedGroupModel> batchedQueries = [];
    List<Map<String, dynamic>> distincIds = [];

    if (null == singleBatchId || "" == singleBatchId) {
      String query =
          "SELECT DISTINCT batch_id From $tableName ORDER BY created_at ASC Limit 30";
      if (all) {
        query =
            "SELECT DISTINCT batch_id From $tableName ORDER BY created_at ASC";
      }
      distincIds = await dbClient.rawQuery(query);
    } else {
      distincIds = [
        {"batch_id": singleBatchId}
      ];
    }

    await Future.wait(distincIds.map((e) async {
      List<Map<String, dynamic>> queriesForBatch = await dbClient.query(
        tableName,
        orderBy: 'query_id ASC',
        where: 'batch_id = ?',
        whereArgs: [e['batch_id']],
      );
      List<QueryModel> _queries =
          queriesForBatch.map((e) => QueryModel.fromJson(e)).toList();
      batchedQueries.add(
          new BatchedGroupModel(batchId: e['batch_id'], queries: _queries));
    }).toList());
    return batchedQueries;
  }

  //to delete pending queries to be pushed, after being synced
  Future<bool> deleteSyncedQueries(List<dynamic> ids,
      {dynamic dbClient}) async {
    bool status = false;
    if (null == dbClient) {
      dbClient = await databaseHelper.database;
    }
    await dbClient.transaction((txn) async {
      var batch = txn.batch();
      ids.forEach((id) {
        batch.delete(tableName, where: 'batch_id  = ?', whereArgs: [id]);
      });
      await batch.commit(continueOnError: false);
      status = true;
    });
    return status;
  }

  Future<int> getRemainingPendingQueries() async {
    int count = 0;
    Database? dbClient = await databaseHelper.database;
    List<dynamic> ls =
        await dbClient!.query(tableName, distinct: true, groupBy: 'batch_id');
    count = ls.length;
    return count;
  }
}
