// ignore_for_file: must_be_immutable

import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/components/ledger_sms_call_reminder_group.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_wise_transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/report/party_report/party_statement_report_controller.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/add_party/add_edit_party_page.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/party_detail/party_detail_controller.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/party_statement_print_page.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/nepali_date.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:sticky_headers/sticky_headers.dart';

class PartyDetailPage extends StatelessWidget {
  final String tag = "PartyDetailPage";

  late String ledgerId;
  final partyDetailController =
      Get.put(PartyDetailController(), tag: "PartyDetailController");

  PartyDetailPage({super.key, required this.ledgerId}) {
    partyDetailController.init(ledgerId);
  }

  Widget _amountWidget(double balanceAmount) {
    bool isReceivable = (balanceAmount >= 0) ? true : false;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Transform.rotate(
          angle: (isReceivable) ? (3.14 / 1.3) : (-3.14 / 4),
          alignment: Alignment.center,
          child: CircleAvatar(
            radius: 10,
            backgroundColor: (isReceivable) ? colorGreenDark : colorRedLight,
            child: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
              size: 15,
            ),
          ),
        ),
        const SizedBox(
          width: 5,
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                (isReceivable)
                    ? "लिनुपर्ने (Receivable)"
                    : "तिर्नुपर्ने (Payable)",
                style: const TextStyle(color: Colors.black54, fontSize: 12),
              ),
              Text(
                formatCurrencyAmount(balanceAmount.abs()),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: (isReceivable) ? colorGreenDark : colorRedLight,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  searchBoxOnChangeHandler(String searchString) {
    partyDetailController.searchTransaction(searchString);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Log.d(partyDetailController.filteredTransactions);
      if (partyDetailController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
        // resizeToAvoidBottomPadding: false,
        resizeToAvoidBottomInset: false,
        //==============================================AppBar
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          leading: BackButton(
            onPressed: () => Navigator.pop(context),
          ),
          centerTitle: false,
          backgroundColor: colorPrimary,
          titleSpacing: -5.0,
          title: Text(
            partyDetailController.ledger.ledgerTitle ?? "",
            overflow: TextOverflow.clip,
            maxLines: 1,
            style: const TextStyle(
              fontSize: 20,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold,
            ),
          ),
          actions: [
            IconButton(
              icon: const Icon(
                Icons.edit,
                size: 25,
                color: Colors.white,
              ),
              onPressed: () => Navigator.pushNamed(
                context,
                "/addPartyLedger",
                arguments: AddEditPartyPage(
                  partyID: partyDetailController.ledger.ledgerId,
                ),
              ),
            )
          ],
        ),
        body: Container(
          color: backgroundColorShade,
          child: ListView(
            children: [
              Card(
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  width: double.infinity,
                  child: Column(
                    children: [
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 15,
                        ),
                        child: Row(
                          children: [
                            Expanded(
                              child: _amountWidget(
                                partyDetailController.ledger.balanceAmount!,
                              ),
                            ),
                            //=======================================Profile Image Ledger
                            ClipRRect(
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(10)),
                              child: Container(
                                width: 60,
                                height: 60,
                                decoration: BoxDecoration(
                                  color: colorPrimaryLightest,
                                ),
                                child: Obx(() {
                                  final image =
                                      partyDetailController.image.value;
                                  print(
                                      'Current image in UI: ${image.imageBitmap?.length ?? 'null'}');

                                  if (image.imageBitmap == null ||
                                      image.imageBitmap!.isEmpty) {
                                    print(
                                        'Showing default icon because: ${image == null ? 'image is null' : 'bitmap is empty'}');
                                    return const Icon(
                                      Icons.person,
                                      color: Colors.white,
                                      size: 60,
                                    );
                                  }

                                  print(
                                      'Attempting to show image of size: ${image.imageBitmap!.length}');
                                  return Image.memory(
                                    Uint8List.fromList(image.imageBitmap!),
                                    fit: BoxFit.cover,
                                    errorBuilder: (context, error, stackTrace) {
                                      print('Error showing image: $error');
                                      return const Icon(
                                        Icons.person,
                                        color: Colors.white,
                                        size: 60,
                                      );
                                    },
                                  );
                                }),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const Divider(
                        thickness: 0.5,
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 15,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            //=======================================Edit & Share Container
                            SizedBox(
                              width: 100,
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Column(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        height: 35,
                                        width: 45,
                                        child: IconButton(
                                          icon: Icon(
                                            Icons.picture_as_pdf,
                                            color: colorRedLight,
                                            size: 22,
                                          ),
                                          onPressed: () {
                                            PartyStatementReportController
                                                controller = Get.put<
                                                    PartyStatementReportController>(
                                              PartyStatementReportController(),
                                            );

                                            List<String> currentDate =
                                                currentDateBS.split('-');
                                            String startDate;
                                            String endDate;

                                            String days =
                                                NepaliDate.daysInMonth(
                                              year: int.tryParse(
                                                currentDate[0],
                                              )!,
                                              month: int.tryParse(
                                                currentDate[1],
                                              )!,
                                            ).toString();

                                            currentDate[2] = '01';
                                            startDate = toDateAD(
                                              NepaliDateTime.parse(
                                                currentDate.join("-"),
                                              ),
                                            );
                                            currentDate[2] = days;
                                            endDate = toDateAD(
                                              NepaliDateTime.parse(
                                                currentDate.join("-"),
                                              ),
                                            );

                                            controller.generatePartyReport(
                                                startDate: startDate,
                                                endDate: endDate,
                                                ledgerID: ledgerId);

                                            Navigator.pushNamed(context,
                                                '/printSinglePartyStatement',
                                                arguments:
                                                    PartyStatementPrintPage(
                                                  transactions:
                                                      controller.transactions,
                                                  partyText:
                                                      partyDetailController
                                                          .ledger.ledgerTitle,
                                                  startDate: startDate,
                                                  endDate: endDate,
                                                ));
                                          },
                                        ),
                                      ),
                                      const Text(
                                        "Share",
                                        style: TextStyle(
                                            color: Colors.black54,
                                            fontSize: 12),
                                      )
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            // FlatButton(
                            //     onPressed: () async {
                            //       Log.d("delete pressed");
                            //       Tuple2<bool, String> deleteResp =
                            //           await LedgerRepository().delete(ledgerId);
                            //       if (deleteResp.item1) {
                            //         showAlertDialog(context,
                            //             alertType: AlertType.Success,
                            //             alertTitle: "",
                            //             message: deleteResp.item2);
                            //       } else {
                            //         showAlertDialog(context,
                            //             alertType: AlertType.Error,
                            //             alertTitle: "",
                            //             message: deleteResp.item2);
                            //       }
                            //     },
                            //     child: Text("Delete")),
                            LedgerSmsCallReminderGroup(
                              ledger: partyDetailController.ledger,
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),

              //===================================================Second Panel (Transaction List)
              if (null != partyDetailController.ledger.openingBalance &&
                  0.00 !=
                      parseDouble((partyDetailController.ledger.openingBalance!)
                          .toStringAsFixed(2)))
                Container(
                  decoration: const BoxDecoration(
                      border:
                          Border(bottom: BorderSide(color: Colors.black26))),
                  padding: const EdgeInsets.only(bottom: 5, top: 5),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 5, horizontal: 15),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        //============1st Column
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              InkWell(
                                onTap: () {
                                  print(partyDetailController
                                      .ledger.openingBalance);
                                },
                                child: Text(
                                  "${TxnType.txnTypeText[partyDetailController.ledger.openingType]}",
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: colorPrimaryDark,
                                      fontWeight: FontWeight.bold),
                                ),
                              ),
                              Text(
                                "\nTotal: ${formatCurrencyAmount(parseDouble((partyDetailController.ledger.openingBalance!).toStringAsFixed(2))!)}",
                                style: const TextStyle(
                                  fontSize: 12,
                                ),
                              ),
                            ],
                          ),
                        ),

                        const SizedBox(
                          width: 20,
                        ),

                        //============2nd Column
                        Column(
                          children: [
                            Text(
                              "${partyDetailController.ledger.openingDateBS}",
                              style: const TextStyle(fontSize: 12),
                            ),
                          ],
                        ),
                        const SizedBox(
                          width: 10,
                        ),
                      ],
                    ),
                  ),
                ),
              Container(
                  width: double.infinity,
                  decoration: const BoxDecoration(color: Colors.white),
                  padding:
                      const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                  child: _ledgerTxnListView(partyDetailController)),
            ],
          ),
        ),
      ));
    });
  }
}

// ignore: camel_case_types
class _ledgerTxnListView extends StatelessWidget {
  final PartyDetailController partyDetailController;

  const _ledgerTxnListView(this.partyDetailController);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (partyDetailController.txnLoading) {
        return Container(
            padding: const EdgeInsets.all(40),
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }
      if (partyDetailController.transactions.isEmpty) {
        return Container(
            padding: const EdgeInsets.all(40),
            width: double.infinity,
            // height: double.infinity,
            child: const Center(
                child: Text(
              "No Records",
              style: TextStyle(color: Colors.black54),
            )));
      } else {
        return StickyHeader(
            header: Container(
              width: MediaQuery.of(context).size.width,
              padding: const EdgeInsets.only(
                  left: 10, right: 10, top: 10, bottom: 10),
              color: Colors.white,
              height: 55,
              child: FormBuilderTextField(
                name: "searchBox",
                autocorrect: false,
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.done,

                style: const TextStyle(
                  // color: Colors.white,
                  fontSize: 18,
                ),
                // controller: searchController,
                decoration: formFieldStyle.copyWith(
                    // labelText: "Search For",
                    hintText: "Search For",
                    prefixIcon: const Icon(Icons.search),
                    alignLabelWithHint: true),
                onChanged: (searchString) =>
                    partyDetailController.searchTransaction(searchString ?? ""),
              ),
            ),
            content: Column(
              children: [
                if (partyDetailController.filteredTransactions.isEmpty)
                  Container(
                      padding: const EdgeInsets.all(40),
                      width: double.infinity,
                      child: const Center(
                          child: Text(
                        "No Records for given query",
                        style: TextStyle(color: Colors.black54),
                      ))),
                ...partyDetailController.filteredTransactions
                    .map((LedgerWiseTransactionModel txn) {
                  return Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => {
                        TransactionHelper.gotoTransactionEditPage(
                            context, txn.txnId!, txn.txnType!),
                      },
                      child: Container(
                        decoration: const BoxDecoration(
                            border: Border(
                                bottom: BorderSide(color: Colors.black26))),
                        padding: const EdgeInsets.only(bottom: 5, top: 5),
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                              vertical: 5, horizontal: 15),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              //============1st Column
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "${txn.txnTypeText}",
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: colorPrimaryDark,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  Text(
                                    "\nTotal: ${formatCurrencyAmount(parseDouble((txn.txnBalanceAmount! + txn.txnCashAmount!).toStringAsFixed(2))!)}",
                                    style: const TextStyle(
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),

                              const SizedBox(
                                width: 20,
                              ),

                              //============2nd Column
                              Column(
                                children: [
                                  if (txn.txnRefNumberChar != null)
                                    Text(
                                      "#${txn.txnRefNumberChar}\n",
                                      style: const TextStyle(
                                          fontSize: 14,
                                          height: 0.8,
                                          fontWeight: FontWeight.bold),
                                    ),
                                  Text(
                                    "${txn.txnDateBS}",
                                    style: const TextStyle(fontSize: 12),
                                  ),
                                ],
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }).toList(),
              ],
            ));
      }
    });
  }
}
