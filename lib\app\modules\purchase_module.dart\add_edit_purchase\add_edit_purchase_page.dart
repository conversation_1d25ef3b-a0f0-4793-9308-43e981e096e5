import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_image_picker/form_builder_image_picker.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/components/ledger_autocomplete%20_%20textfield_with_add.dart';
import 'package:mobile_khaata_v2/app/components/payment_mode_selector.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_account_list/bank_account_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_module.dart/add_edit_purchase/add_edit_purchase_controller.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_module.dart/add_purchase_bill_item/add_edit_purchase_bill_item_view.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_module.dart/detail_purchase/detail_purchase_page.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

extension ExtendedIterable<E> on Iterable<E> {
  /// Like Iterable<T>.map but callback have index as second argument
  Iterable<T> mapIndex<T>(T f(E e, int i)) {
    var i = 0;
    return this.map((e) => f(e, i++));
  }

  void forEachIndex(void f(E e, int i)) {
    var i = 0;
    this.forEach((e) => f(e, i++));
  }
}

class AddEditPurchasePage extends StatelessWidget {
  final String tag = "Purchase Add/Edit Page";

  final String? purchaseID;
  final bool? reaOnlyFlag;

  final purchaseController = AddEditPurchaseController();

  final bankController =
      Get.put(BankAccountListController(), tag: "BankAccountListController");

  AddEditPurchasePage({this.purchaseID, this.reaOnlyFlag}) {
    purchaseController.onInit();

    if (null != purchaseID) {
      // initiate edit functionality
      purchaseController.initEdit(purchaseID, this.reaOnlyFlag ?? false);
    } else {
      purchaseController.initialize();
    }
  }

  onSave(BuildContext context, {bool forNew = false}) async {
    FocusScope.of(context).unfocus();
    if (purchaseController.formKey.currentState!.validate()) {
      //dont check for purchase
      // if (purchaseController
      //     .billNoCtrl.text.isEmpty) {
      //   showToastMessage(context,
      //       message: "बिल न. खाली राख्न मिल्दैन |",
      //       alertType: AlertType.Error);
      //   return;
      // }

      if (purchaseController.transaction.value.txnDateBS!.isEmpty) {
        showToastMessage(context,
            message: "मिति खाली राख्न मिल्दैन |\nPlease fill the date",
            alertType: AlertType.Error);
        return;
      }
      if (null == purchaseController.transaction.value.ledgerId) {
        showToastMessage(context,
            message:
                "आपूर्तिकर्ता खाली राख्न मिल्दैन |\nSupplier name cannnot be empty.",
            alertType: AlertType.Error);
        return;
      }

      if (100 < parseDouble(purchaseController.discountPercentageCtrl.text)! ||
          parseDouble(purchaseController.subTotalAmountCtrl.text)! <
              parseDouble(purchaseController.discountAmountCtrl.text)!) {
        showToastMessage(context,
            message:
                "छुट १००% भन्दा ठूलो हुन सक्दैन | \nDiscount can't be greater than 100%.",
            alertType: AlertType.Error);
        return;
      }

      if (purchaseController.totalAmountCtrl.text.isEmpty) {
        showToastMessage(context,
            message: "कुल रकम खाली हुन सक्दैन | \nTotal amount can't be empty.",
            alertType: AlertType.Error);
        return;
      }

      if (0.0 > parseDouble(purchaseController.totalAmountCtrl.text)!) {
        showToastMessage(context,
            message:
                "कुल रकम नकारात्मक हुन सक्दैन | \nTotal amount can't be negative.",
            alertType: AlertType.Error);
        return;
      }

      if (purchaseController.transaction.value.txnCashAmount! >
          purchaseController.transaction.value.txnTotalAmount!) {
        showToastMessage(context,
            message:
                "भुक्तानी गरिएको रकम बिल रकम भन्दा ठूलो हुन सक्दैन | \nPaid amount can't be greater than bill amount.",
            alertType: AlertType.Error);
        return;
      }
      if (purchaseController.transaction.value.txnPaymentTypeId !=
              PAYMENT_MODE_CASH_ID &&
          !(purchaseController.transaction.value.txnCashAmount! > 0)) {
        showToastMessage(context,
            message:
                "चेक वा बैंक मा भुक्तानी रकम 0 राख्न मिल्दैन | \nPaid amount cannot be 0 for Cheque and Bank",
            alertType: AlertType.Error);
        return;
      }

      if (purchaseController.transaction.value.txnPaymentTypeId ==
              PAYMENT_MODE_CHEQUE_ID &&
          (null == purchaseController.transaction.value.txnPaymentReference ||
              "" == purchaseController.transaction.value.txnPaymentReference)) {
        showToastMessage(context,
            message:
                "चेक/भौचर न. खाली राख्न मिल्दैन | \ncheque/voucher no. cannot be empty for Cheque and Bank",
            alertType: AlertType.Error);
        return;
      }
      if (purchaseController.transaction.value.txnPaymentTypeId ==
              PAYMENT_MODE_CHEQUE_ID &&
          (null == purchaseController.transaction.value.chequeIssueDateBS ||
              "" == purchaseController.transaction.value.chequeIssueDateBS)) {
        showToastMessage(context,
            message: "चेक खाली राख्न मिल्दैन | \nPlease fill the cheque date",
            alertType: AlertType.Error);
        return;
      }

      if (purchaseController.transaction.value.txnPaymentTypeId !=
              PAYMENT_MODE_CASH_ID &&
          purchaseController.transaction.value.txnPaymentTypeId !=
              PAYMENT_MODE_CHEQUE_ID) {
        if (bankController.bankList
                .where((element) =>
                    element.pmtTypeId ==
                    purchaseController.transaction.value.txnPaymentTypeId)
                .first
                .pmtTypeCurrentBalance! <
            purchaseController.transaction.value.txnCashAmount!) {
          print(
              "चयन गरिएको बैंक खातामा अपर्याप्त मौज्दात रकम। कृपया अर्को बैंक खाता चयन गर्नुहोस् वा भुक्तानी मोड परिवर्तन गर्नुहोस्। | \nInsufficient balance in selected bank account. Please select another bank account or change payment mode.");
          showToastMessage(
            context,
            alertType: AlertType.Error,
            message:
                "चयन गरिएको बैंक खातामा अपर्याप्त मौज्दात रकम। कृपया अर्को बैंक खाता चयन गर्नुहोस् वा भुक्तानी मोड परिवर्तन गर्नुहोस्। | \nInsufficient balance in selected bank account. Please select another bank account or change payment mode.",
          );
          return;
        }
      }

      ProgressDialog progressDialog = ProgressDialog(context,
          type: ProgressDialogType.normal, isDismissible: false);
      progressDialog.update(message: "Saving data. Please wait....");
      await progressDialog.show();

      bool isLargeFile =
          await purchaseController.checkLargeImage(purchaseController.files);
      if (isLargeFile) {
        await progressDialog.hide();
        showToastMessage(context,
            message: MAX_IMAGE_SIZE_MESSAGE, alertType: AlertType.Error);
        return;
      }

      bool status = false;
      String? txnID;
      try {
        if (!purchaseController.editFlag) {
          txnID = await purchaseController.createPurchase();
          status = (null != txnID);
        } else {
          status = await purchaseController.updatePurchase();
        }
      } catch (e, trace) {
        // Log.e(tag, e.toString() + trace.toString());
      }
      await progressDialog.hide();

      if (status) {
        // Navigator.pop(context, true);
        String message = (purchaseController.editFlag)
            ? "Purchase Updated Successfully."
            : "Purchase Created Successfully.";
        if (forNew) {
          Navigator.of(context).pushReplacementNamed('/addPurchase');
        } else {
          if (purchaseController.editFlag) {
            Navigator.of(context).pop();
            Navigator.of(context).pushReplacementNamed('/detailPurchase',
                arguments: DetailPurchasePage(
                  purchaseID: this.purchaseID,
                ));
          } else {
            Navigator.of(context).pop();
            if (null != txnID) {
              TransactionHelper.goToPrintPage(context, txnID, TxnType.purchase);
            }
          }
        }
        TransactionHelper.refreshPreviousPages();
        showToastMessage(context, message: message, duration: 2);
      } else {
        showToastMessage(context,
            alertType: AlertType.Error,
            message: "Failed to process operation",
            duration: 2);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      bool iscashPurchase = purchaseController.transaction.value.ledgerId ==
          CASH_PURCHASE_LEDGER_ID;
      bool hasSubTotal =
          (purchaseController.transaction.value.txnSubTotalAmount != null &&
              purchaseController.transaction.value.txnSubTotalAmount! > 0.0);

      if (purchaseController.isLoading) {
        return Container(
            color: Colors.white,
            child: Center(child: CircularProgressIndicator()));
      } else {
        return SafeArea(
            child: Scaffold(
                // resizeToAvoidBottomPadding: purchaseController.editFlag,
                // resizeToAvoidBottomInset: purchaseController.editFlag,
                // resizeToAvoidBottomPadding: true,
                resizeToAvoidBottomInset: true,
                appBar: AppBar(
                  toolbarHeight: 60,
                  backgroundColor: colorPrimary,
                  elevation: 4,
                  leading: BackButton(
                    onPressed: () => Navigator.pop(context, false),
                  ),
                  centerTitle: false,
                  titleSpacing: -5.0,
                  title: Text(
                    "खरिद (Purchase)",
                    style: TextStyle(
                        fontSize: 20,
                        color: Colors.white,
                        fontFamily: 'HelveticaRegular',
                        fontWeight: FontWeight.bold),
                  ),
                ),

                //===========================================================================Body Part
                body: Center(
                  child: Container(
                    color: backgroundColorShade,
                    child: GestureDetector(
                      onTap: () => FocusScope.of(context).unfocus(),
                      child: Container(
                        child: Form(
                          key: purchaseController.formKey,
                          child: SingleChildScrollView(
                            child: Container(
                              child: Column(
                                children: [
                                  Card(
                                    elevation: 2,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 15),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          //===========================Bill No.
                                          Expanded(
                                            flex: 1,
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "बिल न. ",
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                FormBuilderTextField(
                                                  name: "bill_no",
                                                  readOnly: purchaseController
                                                      .readOnlyFlag,
                                                  autocorrect: false,
                                                  keyboardType:
                                                      TextInputType.text,
                                                  textInputAction:
                                                      TextInputAction.done,
                                                  textAlign: TextAlign.right,
                                                  style: formFieldTextStyle,
                                                  decoration:
                                                      formFieldStyle.copyWith(
                                                          labelText:
                                                              "Bill No."),
                                                  controller: purchaseController
                                                      .billNoCtrl,
                                                  onChanged: (value) {
                                                    purchaseController
                                                            .transaction
                                                            .value
                                                            .txnRefNumberChar =
                                                        value;
                                                    purchaseController
                                                        .transaction
                                                        .refresh();
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),

                                          SizedBox(
                                            width: 20,
                                          ),

                                          //===========================Transaction Date
                                          Expanded(
                                            flex: 1,
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "मिति",
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                CustomDatePickerTextField(
                                                  readOnly: purchaseController
                                                      .readOnlyFlag,
                                                  maxBSDate:
                                                      NepaliDateTime.now(),
                                                  initialValue:
                                                      purchaseController
                                                          .transaction
                                                          .value
                                                          .txnDateBS,
                                                  onChange: (selectedDate) {
                                                    purchaseController
                                                            .transaction
                                                            .value
                                                            .txnDateBS =
                                                        selectedDate;
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),

                                  Card(
                                    elevation: 2,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 10),
                                      child: Column(
                                        children: [
                                          //===============================================Party Balance
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Row(
                                                children: [
                                                  // Inside the checkbox onChanged handler:
                                                  Container(
                                                    width: 20,
                                                    height: 20,
                                                    child: Checkbox(
                                                      activeColor: colorPrimary,
                                                      checkColor: Colors.white,
                                                      value: purchaseController
                                                          .isCashPurchaseSelected,
                                                      onChanged:
                                                          purchaseController
                                                                  .readOnlyFlag
                                                              ? null
                                                              : (value) {
                                                                  purchaseController
                                                                          .setisCashPurchaseSelected =
                                                                      value!;
                                                                  if (value) {
                                                                    // Auto fill for Cash Purchase
                                                                    purchaseController.onChangeParty(LedgerDetailModel(
                                                                        ledgerId:
                                                                            CASH_PURCHASE_LEDGER_ID,
                                                                        ledgerTitle:
                                                                            CASH_PURCHASE_LEDGER_NAME));

                                                                    // Set display text to "Cash Purchase"
                                                                    purchaseController
                                                                            .displayTextCtrl
                                                                            .text =
                                                                        "Cash Purchase";
                                                                    purchaseController
                                                                        .transaction
                                                                        .value
                                                                        .txnDisplayName = "Cash Purchase";

                                                                    // Set payment received to true and full amount
                                                                    purchaseController
                                                                            .setIsReceived =
                                                                        true;
                                                                    purchaseController
                                                                            .transaction
                                                                            .value
                                                                            .txnCashAmount =
                                                                        purchaseController
                                                                            .transaction
                                                                            .value
                                                                            .txnTotalAmount;
                                                                    purchaseController
                                                                        .transaction
                                                                        .value
                                                                        .txnBalanceAmount = 0.00;

                                                                    // Set payment mode to cash
                                                                    purchaseController
                                                                        .transaction
                                                                        .value
                                                                        .txnPaymentTypeId = PAYMENT_MODE_CASH_ID;

                                                                    // Clear reference fields
                                                                    purchaseController
                                                                        .transaction
                                                                        .value
                                                                        .txnPaymentReference = null;
                                                                    purchaseController
                                                                        .transaction
                                                                        .value
                                                                        .chequeIssueDateBS = null;

                                                                    // Refresh UI
                                                                    purchaseController
                                                                        .transaction
                                                                        .refresh();
                                                                    purchaseController
                                                                        .assignTransactionToTextFields();
                                                                  } else {
                                                                    // Clear party and display text when unchecked
                                                                    purchaseController.onChangeParty(LedgerDetailModel(
                                                                        ledgerId:
                                                                            null));
                                                                    purchaseController
                                                                        .displayTextCtrl
                                                                        .text = "";
                                                                    purchaseController
                                                                        .transaction
                                                                        .value
                                                                        .txnDisplayName = "";
                                                                  }
                                                                },
                                                    ),
                                                  ),
                                                  Text(
                                                      "  खुद्रा खरीद\n  (Cash Purchase)",
                                                      style: labelStyle2)
                                                ],
                                              ),
                                              RichText(
                                                textAlign: TextAlign.right,
                                                text: TextSpan(
                                                    text: "पुरानो बाँकी: ",
                                                    style: TextStyle(
                                                        color: textColor),
                                                    children: [
                                                      if (null !=
                                                          purchaseController
                                                              .transaction
                                                              .value
                                                              .ledgerId) ...{
                                                        TextSpan(
                                                          text:
                                                              "${purchaseController.selectedLedger.value.balanceAmount ?? 0}",
                                                          style: TextStyle(
                                                              color: ((purchaseController
                                                                              .selectedLedger
                                                                              .value
                                                                              .balanceAmount ??
                                                                          0) >=
                                                                      0.0)
                                                                  ? colorGreenDark
                                                                  : colorRedLight),
                                                        )
                                                      }
                                                    ]),
                                              )
                                            ],
                                          ),

                                          Container(
                                            height: 10,
                                          ),
                                          //===============================================Party Field
                                          if (!purchaseController
                                              .isCashPurchaseSelected)
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'आपूर्तिकर्ताको नाम',
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                LedgerAutoCompleteTextFieldWithAdd(
                                                    excludedIDS: [
                                                      CASH_SALES_LEDGER_ID
                                                    ],
                                                    enableFlag:
                                                        !purchaseController
                                                            .readOnlyFlag,
                                                    labelText: "Supplier Name",
                                                    controller:
                                                        purchaseController
                                                            .partyNameCtrl,
                                                    onChangedFn: (value) {
                                                      // Log.d("called i text change");
                                                    },
                                                    ledgetID: purchaseController
                                                        .transaction
                                                        .value
                                                        .ledgerId,
                                                    onSuggestionSelectedFn:
                                                        (LedgerDetailModel
                                                            ledger) {
                                                      purchaseController
                                                          .onChangeParty(
                                                              ledger);
                                                    })
                                              ],
                                            ),
                                          ...iscashPurchase
                                              ? [
                                                  const SizedBox(
                                                    height: 10,
                                                  ),
                                                  Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        "खुद्रा आपूर्तिकर्ताको नाम",
                                                        style: labelStyle2,
                                                      ),
                                                      SizedBox(height: 5.0),
                                                      FormBuilderTextField(
                                                        name: "display_text",
                                                        // readOnly:
                                                        //     (null == state.selectedLedger.ledgerId)
                                                        //         ? false
                                                        //         : true,
                                                        readOnly:
                                                            purchaseController
                                                                .readOnlyFlag,
                                                        autocorrect: false,
                                                        textInputAction:
                                                            TextInputAction
                                                                .done,
                                                        style:
                                                            formFieldTextStyle,
                                                        decoration: formFieldStyle
                                                            .copyWith(
                                                                labelText:
                                                                    "Billing Name"),
                                                        controller:
                                                            purchaseController
                                                                .displayTextCtrl,
                                                        onChanged: (value) {
                                                          purchaseController
                                                                  .transaction
                                                                  .value
                                                                  .txnDisplayName =
                                                              value;
                                                          purchaseController
                                                              .transaction
                                                              .refresh();
                                                        },
                                                      ),
                                                    ],
                                                  )
                                                ]
                                              : [],

                                          const SizedBox(
                                            height: 10,
                                          ),
                                          if (!iscashPurchase)
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                //===================================Mobile
                                                Container(
                                                  width: MediaQuery.of(context)
                                                          .size
                                                          .width *
                                                      0.3,
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        'फोन नम्बर',
                                                        style: labelStyle2,
                                                      ),
                                                      SizedBox(height: 5.0),
                                                      FormBuilderTextField(
                                                          name: "mobile",
                                                          readOnly: true,
                                                          autocorrect: false,
                                                          keyboardType:
                                                              TextInputType
                                                                  .number,
                                                          textInputAction:
                                                              TextInputAction
                                                                  .done,
                                                          inputFormatters: [
                                                            FilteringTextInputFormatter
                                                                .digitsOnly
                                                          ],
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration: formFieldStyle.copyWith(
                                                              labelText:
                                                                  "Contact no.",
                                                              hintText:
                                                                  "Contact no"),
                                                          controller:
                                                              purchaseController
                                                                  .mobileCtrl),
                                                    ],
                                                  ),
                                                ),

                                                //===================================Address
                                                Container(
                                                  width: MediaQuery.of(context)
                                                          .size
                                                          .width *
                                                      0.5,
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        'ठेगाना',
                                                        style: labelStyle2,
                                                      ),
                                                      SizedBox(height: 5.0),
                                                      FormBuilderTextField(
                                                        name: "address",
                                                        readOnly: true,
                                                        autocorrect: false,
                                                        keyboardType:
                                                            TextInputType.text,
                                                        textInputAction:
                                                            TextInputAction
                                                                .done,
                                                        style:
                                                            formFieldTextStyle,
                                                        decoration: formFieldStyle
                                                            .copyWith(
                                                                labelText:
                                                                    "Address"),
                                                        controller:
                                                            purchaseController
                                                                .addressCtrl,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          if (!iscashPurchase)
                                            const SizedBox(height: 10.0),

                                          //=====================================PAN No Field
                                          if (!iscashPurchase)
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "पान / मु. अ. कर नम्बर",
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                FormBuilderTextField(
                                                    name: "pan_no",
                                                    readOnly: true,
                                                    autocorrect: false,
                                                    keyboardType:
                                                        TextInputType.number,
                                                    inputFormatters: [
                                                      FilteringTextInputFormatter
                                                          .digitsOnly
                                                    ],
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            labelText:
                                                                "PAN/VAT No."),
                                                    controller:
                                                        purchaseController
                                                            .panNoCtrl),
                                              ],
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),

                                  //================================================Item Container
                                  Card(
                                    child: Container(
                                        width: double.infinity,
                                        padding: EdgeInsets.all(0.0),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              width: double.infinity,
                                              padding: EdgeInsets.symmetric(
                                                  horizontal: 15, vertical: 8),
                                              decoration: BoxDecoration(
                                                  color: colorPrimaryLight,
                                                  borderRadius:
                                                      BorderRadius.only(
                                                          topLeft:
                                                              Radius.circular(
                                                                  4),
                                                          topRight:
                                                              Radius.circular(
                                                                  4))),
                                              child: DefaultTextStyle(
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 15,
                                                ),
                                                child: Row(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment
                                                          .spaceBetween,
                                                  children: [
                                                    Text(
                                                      "खरिद सामानहरु (Purchase Items)",
                                                    ),
                                                    Text(
                                                      "Total Item: ${purchaseController.items.length}",
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            Container(
                                              constraints: BoxConstraints(
                                                  maxHeight: 300),
                                              child: getItemListView(
                                                  context,
                                                  purchaseController.items,
                                                  purchaseController),
                                            ),
                                            Container(
                                              margin: EdgeInsets.symmetric(
                                                  vertical: 10),
                                              child: Center(
                                                child: ElevatedButton(
                                                  style:
                                                      ElevatedButton.styleFrom(
                                                    shape:
                                                        RoundedRectangleBorder(
                                                      borderRadius:
                                                          BorderRadius.circular(
                                                              20.0),
                                                    ),
                                                    backgroundColor:
                                                        colorPrimary,
                                                    foregroundColor:
                                                        colorPrimaryLightest,
                                                  ),
                                                  child: Padding(
                                                    padding: const EdgeInsets
                                                        .symmetric(
                                                        vertical: 10,
                                                        horizontal: 10),
                                                    child: Text(
                                                      "खरिद सामान थप्नुहोस्  (Add Purchase Item)",
                                                      style: TextStyle(
                                                        color: Colors.white,
                                                        fontSize: 15,
                                                      ),
                                                    ),
                                                  ),
                                                  onPressed:
                                                      purchaseController
                                                              .readOnlyFlag
                                                          ? null
                                                          : () async {
                                                              var returnedData =
                                                                  await showDialog(
                                                                      context:
                                                                          context,
                                                                      useRootNavigator:
                                                                          true,
                                                                      barrierDismissible:
                                                                          false,
                                                                      builder:
                                                                          (d_c) {
                                                                        return AlertDialog(
                                                                          insetPadding: EdgeInsets.symmetric(
                                                                              horizontal: 10,
                                                                              vertical: 10),
                                                                          contentPadding:
                                                                              EdgeInsets.zero,
                                                                          clipBehavior:
                                                                              Clip.hardEdge,
                                                                          content: Container(
                                                                              width: MediaQuery.of(context).size.width - 20,
                                                                              child: AddEditPurchaseBilledItem()),
                                                                        );
                                                                      });
                                                              if (null !=
                                                                  returnedData) {
                                                                if (null !=
                                                                    returnedData
                                                                        .billedItem) {
                                                                  purchaseController
                                                                      .items
                                                                      .add(returnedData
                                                                          .billedItem);

                                                                  purchaseController
                                                                      .items
                                                                      .refresh();

                                                                  purchaseController
                                                                      .recalculateForItems();
                                                                  // Log.d(
                                                                  //     "got  billed item ${returnedData.billedItem.toJson()}");
                                                                }
                                                              }
                                                            },
                                                ),
                                              ),
                                            ),
                                          ],
                                        )),
                                  ),

                                  //===============================================Total Amount
                                  Card(
                                    elevation: 2,
                                    child: Column(
                                      children: [
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 6, horizontal: 8),
                                          decoration: BoxDecoration(
                                              color: colorPrimaryLight,
                                              borderRadius: BorderRadius.only(
                                                  topLeft: Radius.circular(4),
                                                  topRight:
                                                      Radius.circular(4))),
                                          child: Center(
                                              child: Text(
                                            "Bill Totals (जम्मा बिल)",
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 16),
                                          )),
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 10,
                                          ),
                                          child: Column(
                                            children: [
                                              // =============================================Sub Total
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    "उप कुल",
                                                    style: labelStyle2,
                                                  ),
                                                  SizedBox(height: 5.0),
                                                  FormBuilderTextField(
                                                    name: "txn_subtotal",
                                                    // readOnly: true,
                                                    readOnly: purchaseController
                                                            .readOnlyFlag
                                                        ? true
                                                        : (0 <
                                                                purchaseController
                                                                    .items
                                                                    .length)
                                                            ? true
                                                            : false,
                                                    autocorrect: false,
                                                    keyboardType: TextInputType
                                                        .numberWithOptions(
                                                            decimal: true),
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    inputFormatters: [
                                                      FilteringTextInputFormatter
                                                          .allow(RegExp(
                                                              r'^(\d+)?\.?\d{0,2}'))
                                                    ],
                                                    maxLength: 10,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            hintText:
                                                                "Sub Total",
                                                            counterText: ''),
                                                    textAlign: TextAlign.end,
                                                    controller:
                                                        purchaseController
                                                            .subTotalAmountCtrl,
                                                    onChanged: (value) {
                                                      purchaseController
                                                              .subTotalAmountCtrl
                                                              .selection =
                                                          TextSelection.fromPosition(
                                                              TextPosition(
                                                                  offset: purchaseController
                                                                      .subTotalAmountCtrl
                                                                      .text
                                                                      .length));
                                                      purchaseController
                                                          .onSubTotalIndividualChange(
                                                              value!,
                                                              editorTag:
                                                                  'txn_subtotal');
                                                      purchaseController
                                                          .transaction
                                                          .refresh();
                                                    },
                                                  ),
                                                ],
                                              ),
                                              SizedBox(
                                                height: 20,
                                              ),

                                              // =============================================Discount
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    "छुट (Discount)",
                                                    style: labelStyle2,
                                                  ),
                                                  SizedBox(height: 5.0),
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Container(
                                                        width: 80,
                                                        child:
                                                            FormBuilderTextField(
                                                          name:
                                                              "txn_discount_percent",
                                                          readOnly:
                                                              purchaseController
                                                                      .readOnlyFlag ||
                                                                  (!hasSubTotal),
                                                          autocorrect: false,
                                                          keyboardType:
                                                              TextInputType
                                                                  .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                          textInputAction:
                                                              TextInputAction
                                                                  .done,
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration:
                                                              formFieldStyle
                                                                  .copyWith(
                                                                      suffix: Text(
                                                                          "%"),
                                                                      labelText:
                                                                          "%"),
                                                          textAlign:
                                                              TextAlign.end,
                                                          controller:
                                                              purchaseController
                                                                  .discountPercentageCtrl,
                                                          onChanged: (value) {
                                                            if (value!
                                                                .isEmpty) {
                                                              //remove keyboard
                                                              FocusScope.of(
                                                                      context)
                                                                  .unfocus();
                                                            }
                                                            purchaseController
                                                                    .discountPercentageCtrl
                                                                    .selection =
                                                                TextSelection.fromPosition(TextPosition(
                                                                    offset: purchaseController
                                                                        .discountPercentageCtrl
                                                                        .text
                                                                        .length));
                                                            purchaseController
                                                                .updateDiscountPercentage(
                                                                    value!,
                                                                    editorTag:
                                                                        'txn_discount_percent');
                                                          },
                                                        ),
                                                      ),
                                                      SizedBox(
                                                        width: 20,
                                                      ),
                                                      Expanded(
                                                        child: Container(
                                                          child:
                                                              FormBuilderTextField(
                                                                  name:
                                                                      "txn_discount_amount",
                                                                  readOnly: purchaseController
                                                                          .readOnlyFlag ||
                                                                      (!hasSubTotal),
                                                                  autocorrect:
                                                                      false,
                                                                  keyboardType: TextInputType.numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                                  textInputAction:
                                                                      TextInputAction
                                                                          .done,
                                                                  style:
                                                                      formFieldTextStyle,
                                                                  decoration:
                                                                      formFieldStyle.copyWith(
                                                                          labelText:
                                                                              "छुट रकम (Dis. Amount)"),
                                                                  textAlign:
                                                                      TextAlign
                                                                          .end,
                                                                  controller:
                                                                      purchaseController
                                                                          .discountAmountCtrl,
                                                                  onChanged:
                                                                      (value) {
                                                                    purchaseController
                                                                            .discountAmountCtrl
                                                                            .selection =
                                                                        TextSelection.fromPosition(TextPosition(
                                                                            offset:
                                                                                purchaseController.discountAmountCtrl.text.length));
                                                                    purchaseController.updateDiscountAmount(
                                                                        value!,
                                                                        editorTag:
                                                                            'txn_discount_amount');
                                                                  }),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                              SizedBox(
                                                height: 25,
                                              ),

                                              //====================================================VAT
                                              ...[
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Row(
                                                      children: [
                                                        Container(
                                                          width: 20,
                                                          height: 20,
                                                          child: Checkbox(
                                                            activeColor:
                                                                colorPrimary,
                                                            checkColor:
                                                                Colors.white,
                                                            value:
                                                                purchaseController
                                                                    .isVatEnabled,
                                                            onChanged: (purchaseController
                                                                        .readOnlyFlag ||
                                                                    (!hasSubTotal))
                                                                ? null
                                                                : (value) {
                                                                    purchaseController
                                                                        .onToggleVat(
                                                                            value!);
                                                                  },
                                                          ),
                                                        ),
                                                        Text("  मु.अ. कर (VAT)",
                                                            style: labelStyle2)
                                                      ],
                                                    ),
                                                    SizedBox(height: 10.0),
                                                    Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        Container(
                                                          width: 80,
                                                          child:
                                                              FormBuilderTextField(
                                                            name:
                                                                "txn_tax_percent",
                                                            readOnly: true,
                                                            autocorrect: false,
                                                            keyboardType:
                                                                TextInputType
                                                                    .numberWithOptions(
                                                                        decimal:
                                                                            true),
                                                            textInputAction:
                                                                TextInputAction
                                                                    .done,
                                                            style:
                                                                formFieldTextStyle,
                                                            decoration: formFieldStyle
                                                                .copyWith(
                                                                    suffix: Text(
                                                                        "%"),
                                                                    labelText:
                                                                        "%"),
                                                            textAlign:
                                                                TextAlign.end,
                                                            controller:
                                                                purchaseController
                                                                    .vatPercentCtrl,
                                                            onChanged: (value) {
                                                              purchaseController
                                                                  .onvatPercentChange(
                                                                      value!,
                                                                      editorTag:
                                                                          'txn_tax_percent');
                                                            },
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          width: 20,
                                                        ),
                                                        Expanded(
                                                          child: Container(
                                                            child:
                                                                FormBuilderTextField(
                                                              name:
                                                                  "txn_tax_amount",
                                                              readOnly: true,
                                                              autocorrect:
                                                                  false,
                                                              keyboardType: TextInputType
                                                                  .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                              textInputAction:
                                                                  TextInputAction
                                                                      .done,
                                                              style:
                                                                  formFieldTextStyle,
                                                              decoration: formFieldStyle
                                                                  .copyWith(
                                                                      labelText:
                                                                          "मु.अ. कर रकम (VAT Amount) "),
                                                              textAlign:
                                                                  TextAlign.end,
                                                              controller:
                                                                  purchaseController
                                                                      .vatAmountCtrl,
                                                              onChanged:
                                                                  (value) {
                                                                purchaseController
                                                                    .onvatAmountChange(
                                                                        value!,
                                                                        editorTag:
                                                                            'txn_tax_amount');
                                                              },
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(
                                                  height: 5,
                                                ),
                                              ],

                                              Divider(
                                                height: 5,
                                              ),
                                              Divider(
                                                height: 0,
                                              ),
                                              SizedBox(
                                                height: 15,
                                              ),

                                              // =============================================Total Amount
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    "कुल रकम",
                                                    style: labelStyle2,
                                                  ),
                                                  SizedBox(
                                                    height: 5,
                                                  ),
                                                  Container(
                                                      child: FormBuilderTextField(
                                                          name: "txn_total",
                                                          readOnly: true,
                                                          autocorrect: false,
                                                          keyboardType:
                                                              TextInputType.numberWithOptions(
                                                                  decimal:
                                                                      true),
                                                          textInputAction:
                                                              TextInputAction
                                                                  .done,
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration: formFieldStyle
                                                              .copyWith(
                                                                  labelText:
                                                                      "Total Amount"),
                                                          textAlign:
                                                              TextAlign.end,
                                                          controller:
                                                              purchaseController
                                                                  .totalAmountCtrl)),
                                                ],
                                              ),
                                              SizedBox(
                                                height: 25,
                                              ),

                                              // =============================================Received Amount

                                              Row(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.end,
                                                children: [
                                                  Expanded(
                                                      child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Row(
                                                        children: [
                                                          Container(
                                                            width: 20,
                                                            height: 20,
                                                            child: Checkbox(
                                                              activeColor:
                                                                  colorPrimary,
                                                              checkColor:
                                                                  Colors.white,
                                                              value: iscashPurchase
                                                                  ? true
                                                                  : (purchaseController
                                                                      .isReceived),
                                                              onChanged: (purchaseController
                                                                          .readOnlyFlag ||
                                                                      iscashPurchase ||
                                                                      (!hasSubTotal))
                                                                  ? null
                                                                  : (value) {
                                                                      purchaseController
                                                                              .setIsReceived =
                                                                          value!;
                                                                      if (value) {
                                                                        purchaseController
                                                                            .transaction
                                                                            .value
                                                                            .txnCashAmount = purchaseController.transaction.value.txnTotalAmount;

                                                                        purchaseController
                                                                            .transaction
                                                                            .value
                                                                            .txnBalanceAmount = 0.00;
                                                                        purchaseController
                                                                            .transaction
                                                                            .refresh();
                                                                      } else {
                                                                        purchaseController
                                                                            .transaction
                                                                            .value
                                                                            .txnCashAmount = 0.00;
                                                                        purchaseController
                                                                            .transaction
                                                                            .value
                                                                            .txnBalanceAmount = purchaseController.transaction.value.txnTotalAmount;
                                                                      }
                                                                      purchaseController
                                                                          .assignTransactionToTextFields();
                                                                    },
                                                            ),
                                                          ),
                                                          Text(" भुक्तानी रकम",
                                                              style:
                                                                  labelStyle2)
                                                        ],
                                                      ),
                                                      SizedBox(
                                                        height: 10,
                                                      ),
                                                      PaymentModeSelector(
                                                        onChangedFn: (v) {
                                                          purchaseController
                                                              .transaction
                                                              .value
                                                              .txnPaymentTypeId = v;

                                                          purchaseController
                                                                  .transaction
                                                                  .value
                                                                  .txnPaymentReference =
                                                              null;
                                                          purchaseController
                                                                  .transaction
                                                                  .value
                                                                  .chequeIssueDateBS =
                                                              null;
                                                          purchaseController
                                                              .transaction
                                                              .refresh();
                                                        },
                                                        paymentModeID:
                                                            purchaseController
                                                                .transaction
                                                                .value
                                                                .txnPaymentTypeId,
                                                        enableFlag:
                                                            purchaseController
                                                                .editFlag,
                                                      ),
                                                    ],
                                                  )),
                                                  SizedBox(
                                                    width: 10,
                                                  ),
                                                  Expanded(
                                                    child: FormBuilderTextField(
                                                      name: "txn_cash_amount",
                                                      readOnly:
                                                          (purchaseController
                                                                  .readOnlyFlag ||
                                                              !hasSubTotal ||
                                                              iscashPurchase),
                                                      autocorrect: false,
                                                      keyboardType: TextInputType
                                                          .numberWithOptions(
                                                              decimal: true),
                                                      textInputAction:
                                                          TextInputAction.done,
                                                      inputFormatters: [
                                                        FilteringTextInputFormatter
                                                            .digitsOnly
                                                      ],
                                                      style: formFieldTextStyle,
                                                      decoration: formFieldStyle
                                                          .copyWith(
                                                              labelText:
                                                                  "Paid Amount"),
                                                      textAlign: TextAlign.end,
                                                      controller:
                                                          purchaseController
                                                              .receivedAmountCtrl,
                                                      onChanged: (value) {
                                                        purchaseController
                                                            .changeReceivedAmount(
                                                                value!,
                                                                editorTag:
                                                                    'txn_cash_amount');
                                                      },
                                                    ),
                                                  )
                                                ],
                                              ),

                                              ...(purchaseController
                                                          .transaction
                                                          .value
                                                          .txnPaymentTypeId ==
                                                      PAYMENT_MODE_CHEQUE_ID)
                                                  ? [
                                                      SizedBox(
                                                        height: 25,
                                                      ),
                                                      Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text("चेक/भौचर न.",
                                                              style:
                                                                  labelStyle2),
                                                          SizedBox(
                                                            height: 10,
                                                          ),
                                                          TextField(
                                                              autocorrect:
                                                                  false,
                                                              readOnly:
                                                                  purchaseController
                                                                      .readOnlyFlag,
                                                              style:
                                                                  formFieldTextStyle,
                                                              decoration: formFieldStyle
                                                                  .copyWith(
                                                                      labelText:
                                                                          "Cheque/Voucher No."),
                                                              controller:
                                                                  purchaseController
                                                                      .paymentRefCtrl,
                                                              onChanged: (v) {
                                                                purchaseController
                                                                    .transaction
                                                                    .value
                                                                    .txnPaymentReference = v;
                                                                purchaseController
                                                                    .transaction
                                                                    .refresh();
                                                              }),
                                                        ],
                                                      ),
                                                    ]
                                                  : [],
                                              if (purchaseController.transaction
                                                      .value.txnPaymentTypeId ==
                                                  PAYMENT_MODE_CHEQUE_ID) ...[
                                                SizedBox(
                                                  height: 25,
                                                ),
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text("चेक मिति",
                                                        style: labelStyle2),
                                                    SizedBox(
                                                      height: 10,
                                                    ),
                                                    CustomDatePickerTextField(
                                                      labelText: "Cheque Date",
                                                      readOnly:
                                                          purchaseController
                                                              .readOnlyFlag,
                                                      // maxBSDate: NepaliDateTime.now(),
                                                      initialValue:
                                                          purchaseController
                                                              .transaction
                                                              .value
                                                              .chequeIssueDateBS,
                                                      onChange: (selectedDate) {
                                                        purchaseController
                                                                .transaction
                                                                .value
                                                                .chequeIssueDateBS =
                                                            selectedDate;
                                                      },
                                                    ),
                                                  ],
                                                )
                                              ],

                                              SizedBox(
                                                height: 25,
                                              ),

                                              // =============================================Balance Amount
                                              ...iscashPurchase
                                                  ? []
                                                  : [
                                                      Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                              "बाँकी रहेको रकम",
                                                              style:
                                                                  labelStyle2),
                                                          SizedBox(
                                                            height: 10,
                                                          ),
                                                          FormBuilderTextField(
                                                              name:
                                                                  "txn_balance_amount",
                                                              readOnly: true,
                                                              autocorrect:
                                                                  false,
                                                              keyboardType: TextInputType
                                                                  .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                              textInputAction:
                                                                  TextInputAction
                                                                      .done,
                                                              style:
                                                                  formFieldTextStyle,
                                                              decoration: formFieldStyle
                                                                  .copyWith(
                                                                      labelText:
                                                                          "Balance Due"),
                                                              textAlign:
                                                                  TextAlign.end,
                                                              controller:
                                                                  purchaseController
                                                                      .dueAmountCtrl),
                                                        ],
                                                      ),
                                                      SizedBox(
                                                        height: 20,
                                                      ),
                                                    ],
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  //===============================================Image
                                  Container(
                                    width: double.infinity,
                                    child: Card(
                                      elevation: 2,
                                      child: Container(
                                        child: Container(
                                            // color: Colors.red,
                                            height: 140,
                                            width: 100,
                                            // width: ,
                                            // child: (null==state.selectImage)?
                                            child: FormBuilderImagePicker(
                                                name: "image_picker",
                                                bottomSheetPadding:
                                                    EdgeInsets.all(0),
                                                decoration: InputDecoration(
                                                  border: InputBorder.none,
                                                ),
                                                maxImages: 2,
                                                iconColor: colorPrimaryLight,

                                                // initialValue:
                                                //     purchaseController.files.value,
                                                onChanged: (_fls) async {
                                                  if (_fls != null &&
                                                      _fls.isNotEmpty) {
                                                    _fls.forEach((element) {
                                                      purchaseController.files
                                                          .add(File(
                                                              element.path));
                                                    });
                                                    purchaseController.files
                                                        .refresh();
                                                    bool isLargeFile =
                                                        await purchaseController
                                                            .checkLargeImage(
                                                                _fls);
                                                    if (isLargeFile) {
                                                      showToastMessage(context,
                                                          message:
                                                              MAX_IMAGE_SIZE_MESSAGE,
                                                          alertType:
                                                              AlertType.Error);
                                                      return;
                                                    }
                                                  }
                                                  // purchaseController.files.value =
                                                  //     _fls.cast<File>();
                                                  // purchaseController.files
                                                  //     .refresh();
                                                })),
                                      ),
                                    ),
                                  ),

                                  //===============================================Description
                                  Card(
                                    elevation: 2,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 10),
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "कैफियत",
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          FormBuilderTextField(
                                            name: "description",
                                            readOnly:
                                                purchaseController.readOnlyFlag,
                                            autocorrect: false,
                                            textAlign: TextAlign.start,
                                            textInputAction:
                                                TextInputAction.newline,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                labelText: "Remarks"),
                                            minLines: 4,
                                            maxLines: 4,
                                            controller:
                                                purchaseController.descCtrl,
                                            onChanged: (value) {
                                              purchaseController.transaction
                                                  .value.txnDescription = value;
                                              purchaseController.transaction
                                                  .refresh();
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                //=================================================Save button
                bottomNavigationBar: BottomSaveCancelButton(
                  shadow: false,
                  hasNew: !purchaseController.editFlag,
                  onSaveAndNewBtnPressedFn: () {
                    this.onSave(context, forNew: true);
                  },
                  enableFlag: !purchaseController.readOnlyFlag,
                  onSaveBtnPressedFn: (purchaseController.readOnlyFlag)
                      ? null
                      : () {
                          this.onSave(context);
                        },
                )));
      }
    });
  }
}

Widget getItemListView(
  BuildContext context,
  List<LineItemDetailModel> _items,
  AddEditPurchaseController purchaseController,
) {
  // return Container(height: 40, color: Colors.red);
  ScrollController scrollController = ScrollController();
  var listView = Scrollbar(
    controller: scrollController,
    isAlwaysShown: true,
    child: ListView.builder(
        controller: scrollController,
        itemCount: _items.length,
        shrinkWrap: true,
        itemBuilder: (context, int index) {
          LineItemDetailModel _item = _items[index];

          return Row(children: [
            Expanded(
                child: GestureDetector(
              onTap: purchaseController.readOnlyFlag
                  ? null
                  : () async {
                      var returnedData = await showDialog(
                          context: context,
                          useRootNavigator: true,
                          barrierDismissible: false,
                          builder: (d_c) {
                            return AlertDialog(
                                insetPadding: EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 10),
                                contentPadding: EdgeInsets.zero,
                                clipBehavior: Clip.hardEdge,
                                content: Container(
                                  width: MediaQuery.of(context).size.width - 20,
                                  child: AddEditPurchaseBilledItem(
                                    lineItemModel: _item,
                                    // purchaseController: purchaseController,
                                  ),
                                ));
                          });
                      if (null != returnedData) {
                        if (returnedData.deleteFlag) {
                          purchaseController.items.removeAt(index);
                        } else if (null != returnedData.billedItem) {
                          purchaseController.items.replaceRange(
                              index, 1, [returnedData.billedItem]);
                          purchaseController.recalculateForItems();
                        }
                        purchaseController.items.refresh();
                        purchaseController.recalculateForItems();
                      }
                    },
              child: Card(
                elevation: 2,
                margin: EdgeInsets.only(
                    left: 10,
                    right: 10,
                    top: (0 == index) ? 15 : 8,
                    bottom: ((_items.length - 1) == index) ? 20 : 8),
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                  decoration: BoxDecoration(color: Colors.black12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // =============================================item name
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              _item.itemName ?? "",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: colorPrimaryDark),
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          Container(
                            child: GestureDetector(
                              onTap: purchaseController.readOnlyFlag
                                  ? null
                                  : () {
                                      purchaseController.items.removeAt(index);
                                      purchaseController.items.refresh();
                                      purchaseController.recalculateForItems();
                                    },
                              child: Icon(Icons.delete,
                                  color: (purchaseController.readOnlyFlag)
                                      ? Colors.black54
                                      : Colors.red),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 5,
                      ),

                      // =============================================gross amount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            width: 75,
                            child: Text(
                              "Amount",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  color: Colors.black54),
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              "${_item.quantity} ${_item.lineItemUnitName ?? ""} X ${formatCurrencyAmount(_item.pricePerUnit ?? 0.00)} = ${formatCurrencyAmount(_item.grossAmount ?? 0.00, false)}",
                              textAlign: TextAlign.right,
                              style: TextStyle(fontSize: 12, color: textColor),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 5,
                      ),

                      // =============================================Discount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            width: 100,
                            child: Text(
                              "Discount(%): ${_item.discountPercent}",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  color: Colors.black54),
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              " = ${formatCurrencyAmount(_item.discountAmount ?? 0.00, false)}",
                              textAlign: TextAlign.right,
                              style: TextStyle(fontSize: 12, color: textColor),
                            ),
                          ),
                        ],
                      ),
                      Divider(
                        height: 5,
                      ),
                      Divider(
                        height: 0,
                      ),

                      // =============================================netAmount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              formatCurrencyAmount(
                                  _item.totalAmount ?? 0.00, false),
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: colorPrimary),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            )),
          ]);
        }),
  );

  return listView;
}
