class PartyStatementItemModel {
  String? date;
  String? ledgerName;
  int? txnType;
  String? txnTypeText;
  double? amount;
  double? cumulativeBalance;
  PartyStatementItemModel({
    this.date,
    this.ledgerName,
    this.txnType,
    this.txnTypeText,
    this.amount,
    this.cumulativeBalance,
  });

  PartyStatementItemModel copyWith({
    String? date,
    String? ledgerName,
    int? txnType,
    String? txnTypeText,
    double? amount,
    double? cumulativeBalance,
  }) {
    return PartyStatementItemModel(
      date: date ?? this.date,
      ledgerName: ledgerName ?? this.ledgerName,
      txnType: txnType ?? this.txnType,
      txnTypeText: txnTypeText ?? this.txnTypeText,
      amount: amount ?? this.amount,
      cumulativeBalance: cumulativeBalance ?? this.cumulativeBalance,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'date': date,
      'ledgerName': ledgerName,
      'txnType': txnType,
      'txnTypeText': txnTypeText,
      'amount': amount,
      'cumulativeBalance': cumulativeBalance,
    };
  }

  factory PartyStatementItemModel.fromJson(Map<String, dynamic> map) {
    // if (map == null) return null;

    return PartyStatementItemModel(
      date: map['date'],
      ledgerName: map['ledgerName'],
      txnType: map['txnType'],
      txnTypeText: map['txnTypeText'],
      amount: map['amount'],
      cumulativeBalance: map['cumulativeBalance'],
    );
  }

  @override
  String toString() {
    return 'PartyStatementItemModel(date: $date, ledgerName: $ledgerName, txnType: $txnType, txnTypeText: $txnTypeText, amount: $amount, cumulativeBalance: $cumulativeBalance)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is PartyStatementItemModel &&
        other.date == date &&
        other.ledgerName == ledgerName &&
        other.txnType == txnType &&
        other.txnTypeText == txnTypeText &&
        other.amount == amount &&
        other.cumulativeBalance == cumulativeBalance;
  }

  @override
  int get hashCode {
    return date.hashCode ^
        ledgerName.hashCode ^
        txnType.hashCode ^
        txnTypeText.hashCode ^
        amount.hashCode ^
        cumulativeBalance.hashCode;
  }
}
