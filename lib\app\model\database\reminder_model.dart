import 'package:intl/intl.dart';

import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class ReminderModel {
  ReminderModel(
      {this.reminderId,
      this.reminderType,
      this.description,
      this.startDatetime,
      this.startDate,
      this.startDateBS,
      this.startTime,
      this.reminderPeriod,
      // this.scheduledId,
      this.ledgerID,
      this.lastActivityType,
      this.lastActivityAt,
      this.lastActivityBy,
      this.reminderCategory});

  String? reminderId;
  int? reminderType;
  String? description;
  String? startDatetime;
  String? startDate;
  String? startDateBS;
  String? startTime;
  int? reminderPeriod;
  // int scheduledId;
  String? ledgerID;

  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;
  String? reminderCategory;

  factory ReminderModel.fromJson(Map<String, dynamic> json) {
    DateTime startDateTime = DateTime.parse(json["start_datetime"]);
    String startTime = DateFormat('hh:mm a').format(startDateTime);
    String startDate = DateFormat('y-MM-dd').format(startDateTime);
    String startDateBS = toDateBS(startDateTime);

    return ReminderModel(
      reminderId: json["reminder_id"],
      reminderType: json["reminder_type"],
      description: json["description"],
      startDatetime: json["start_datetime"],
      reminderPeriod: json["reminder_period"],
      // scheduledId: json["scheduled_id"],
      lastActivityType: json["last_activity_type"],
      lastActivityAt: json["last_activity_at"],
      lastActivityBy: json['last_activity_by'],
      reminderCategory: json["reminder_category"],
      ledgerID: json['ledger_id'],
      startDate: startDate,
      startDateBS: startDateBS,
      startTime: startTime,
    );
  }

  Map<String, dynamic> toJson() => {
        "reminder_id": reminderId,
        "reminder_type": reminderType,
        "description": description,
        "start_datetime": startDatetime,
        "reminder_period": reminderPeriod,
        // "scheduled_id": scheduledId,
        'ledger_id': ledgerID,
        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        "last_activity_by": lastActivityBy
      };
}
