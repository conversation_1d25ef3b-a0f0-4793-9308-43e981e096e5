import 'package:mobile_khaata_v2/app/model/database/transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/database/txn_image_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/sale_return_model.dart';
import 'package:mobile_khaata_v2/app/repository/line_item_repository.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';
import 'package:mobile_khaata_v2/app/repository/txn_image_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:sqflite/sqflite.dart';

import 'package:tuple/tuple.dart';

class SaleReturnRepository {
  final String tag = "SaleReturnRepository";
  DatabaseHelper databaseHelper = DatabaseHelper();

  //get from transaction repository and return from here
  Future<List<SaleReturnModal>> getAllSales() async {
    List<SaleReturnModal> sales = [];
    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> txnDataListJson = (await dbClient!.rawQuery(
          'SELECT * FROM mk_transactions WHERE txn_type=? AND last_activity_type!=?',
          [TxnType.salesReturn, LastActivityType.Delete]));
      sales = txnDataListJson.map((txnData) {
        return SaleReturnModal.fromJson(txnData);
      }).toList();
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return sales;
  }

  Future<
      Tuple3<SaleReturnModal, List<LineItemDetailModel>,
          List<TxnImageModel>>> getSaleReturnById(String? txnID) async {
    TransactionModel? txnData = TransactionModel();
    List<LineItemDetailModel> items = [];
    List<TxnImageModel> images = [];
    TransactionRepository transactionRepository = TransactionRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();
    LineItemRepository lineItemRepository = LineItemRepository();

    try {
      if (null != txnID) {
        txnData = await transactionRepository.getTransactionByTxnId(txnID);
        items =
            await lineItemRepository.getLineDetailItemsForTransaction(txnID);
        images = await txnImageRepository.getImagesForTransaction(txnID);
      } else {
        // Throw error for null id
      }
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return Tuple3(SaleReturnModal.fromJson(txnData!.toJson()), items, images);
  }

  Future<String?> addSaleReturn(SaleReturnModal saleReturnModal,
      List<LineItemDetailModel> listItem, List<TxnImageModel> images) async {
    String? status;
    TransactionRepository transactionRepository = TransactionRepository();
    LineItemRepository lineItemRepository = LineItemRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();

    try {
      Database? dbClient = await databaseHelper.database;
      await dbClient!.transaction((batch) async {
        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        String txnID = await transactionRepository.insert(
            TransactionModel.fromJson(saleReturnModal.toJson()),
            dbClient: batch,
            batchID: batchID);

        await lineItemRepository.setLineDetailItemsForTransaction(
            txnID, listItem,
            dbClient: batch, batchID: batchID);

        await txnImageRepository.setImagesForTransaction(txnID, images,
            dbClient: batch, batchID: batchID);

        // bool isSuccess = await pushPendingQueries(
        //     singleBatchId: batchID, source: "TRIGGER", dbClient: batch);

        // if (!isSuccess) {
        // throw CustomException("No Net");
        pushPendingQueries(
            singleBatchId: batchID, source: "TRIGGER", dbClient: batch);
        // }

        status = txnID;
      });
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }

  Future<bool> updateSaleReturn(SaleReturnModal saleReturnModal,
      List<LineItemDetailModel> listItem, List<TxnImageModel> images) async {
    bool status = false;
    TransactionRepository transactionRepository = TransactionRepository();
    LineItemRepository lineItemRepository = LineItemRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();

    try {
      Database? dbClient = await databaseHelper.database;

      await dbClient!.transaction((batch) async {
        // var batch = txn.batch();
        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        await transactionRepository.update(
            TransactionModel.fromJson(saleReturnModal.toJson()),
            dbClient: batch,
            batchID: batchID);

        await lineItemRepository.deleteLineItemsForTransaction(
            saleReturnModal.txnId ?? "",
            dbClient: batch,
            batchID: batchID);

        await lineItemRepository.setLineDetailItemsForTransaction(
            saleReturnModal.txnId ?? "", listItem,
            dbClient: batch, batchID: batchID);

        await txnImageRepository.deleteImagesForTransaction(
            saleReturnModal.txnId ?? "",
            dbClient: batch,
            batchID: batchID);

        await txnImageRepository.setImagesForTransaction(
            saleReturnModal.txnId ?? "", images,
            dbClient: batch, batchID: batchID);

        // bool isSuccess = await pushPendingQueries(
        //     singleBatchId: batchID, source: "TRIGGER", dbClient: batch);
        // if (!isSuccess) {
        // throw CustomException("No Net");
        pushPendingQueries(
            singleBatchId: batchID, source: "TRIGGER", dbClient: batch);
        // }
        // await batch.commit(continueOnError: false, noResult: true);

        status = true;
      });
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }

  Future<bool> isBillDuplicate(String? billID) async {
    bool status = false;
    try {
      Database? dbClient = await databaseHelper.database;

      int count = 0;

      if (null != billID && "" != billID) {
        count = Sqflite.firstIntValue(await dbClient!.rawQuery(
            'SELECT COUNT(txn_id) AS total_txn FROM mk_transactions WHERE txn_type=? AND last_activity_type!=3 AND txn_ref_number_char=? limit 1',
            [TxnType.salesReturn, billID]))!;
      }

      if (0 < count) {
        // Bill id exist
        status = true;
      } else {
        status = false;
      }
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }
}
