// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/modules/home_module/logout_controller.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:mobile_khaata_v2/utilities/login_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import '../login_screen.dart';

class ChangePasswordController extends GetxController {
  final String tag = "ChangePasswordController";
  final _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  final _isSubmitting = false.obs;
  bool get isSubmitting => _isSubmitting.value;

  final formKey = GlobalKey<FormState>();

  TextEditingController oldPasswordCtrl = TextEditingController();
  TextEditingController newPasswordCtrl = TextEditingController();
  TextEditingController verifyNewPasswordCtrl = TextEditingController();

  @override
  void onInit() async {}

  changePassword(BuildContext context) async {
    _isSubmitting(true);
    ProgressDialog progressDialog = ProgressDialog(context,
        type: ProgressDialogType.normal, isDismissible: false);
    progressDialog.update(
        message: "Syncing remaining queries. Please wait....");
    await progressDialog.show();

    bool isSyncedAll = await pushPendingQueries(all: true);

    if (isSyncedAll) {
      progressDialog.update(message: "Updating password. Please wait....");

      //all push synced, so reset and logout
      ApiBaseHelper apiBaseHelper = ApiBaseHelper();
      ApiResponse apiResponse = await apiBaseHelper.post(
          apiBaseHelper.CHANGE_PASSWORD,
          {
            "old_password": oldPasswordCtrl.text,
            "new_password": newPasswordCtrl.text,
            "verify_password": verifyNewPasswordCtrl.text
          },
          accessToken: true);
      await progressDialog.hide();
      if (apiResponse.status) {
        // change successfully
        await LoginHelper().logout();
        showAlertDialog(context,
            alertType: AlertType.Success,
            alertTitle: "Success", onCloseButtonPressed: () {
          Navigator.of(context).pop();
          // logoutFromDevice(context);
          Navigator.pushNamedAndRemoveUntil(
            context,
            "/login",
            (route) => false,
          );
        }, message: apiResponse.msg ?? "");

        _isSubmitting(false);
      } else {
        showAlertDialog(context,
            alertType: AlertType.Error,
            alertTitle: "Error",
            message: apiResponse.msg ?? "");

        _isSubmitting(false);
      }
    } else {
      await progressDialog.hide();
      showAlertDialog(context,
          alertType: AlertType.Error,
          alertTitle: "Error",
          message:
              "Failed to sync remaining data to server. Please try again later");

      _isSubmitting(false);
    }
  }
}
