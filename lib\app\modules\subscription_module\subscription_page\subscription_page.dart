// ignore_for_file: library_private_types_in_public_api, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/modules/subscription_module/payment_subscription/payment_subscription_controller.dart';
import 'package:mobile_khaata_v2/app/modules/subscription_module/subscription_page/subscription_page_controller.dart';
import 'package:mobile_khaata_v2/app/modules/subscription_module/subscription_payment_detail/subscriptiobn_payment_detail_page.dart';
import 'package:mobile_khaata_v2/app/modules/subscription_module/update_agent/update_agent_code.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/ui_helper.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

import '../payment_subscription/payment_subscription_view.dart';

class SubscriptionPage extends StatefulWidget {
  const SubscriptionPage({super.key});

  @override
  _SubscriptionPageState createState() => _SubscriptionPageState();
}

class _SubscriptionPageState extends State<SubscriptionPage> {
  final subscriptionPageController = SubscriptionPageController();

  final paymentSubscriptionController = PaymentSubscriptionController();

  String? planId;
  double? amount;

  @override
  void initState() {
    super.initState();
    subscriptionPageController.onInit();
    paymentSubscriptionController.init();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      // Log.d("sub detail");
      // Log.d(subscriptionPageController.subDetail);
      bool isExpired = subscriptionPageController.isExpired;
      var expiryInfo =
          subscriptionPageController.subDetail['expiry_info'] ?? {};
      var expiryDate = expiryInfo['account_expiry_date'];
      var lastRenewed = expiryInfo['last_renewed_at'];
      String? lastRenewedText;
      int remainingDays = 0;
      if (null != expiryDate) {
        DateTime expDateTime = DateTime.parse(expiryDate);
        Duration difference = expDateTime.difference(DateTime.now());
        remainingDays = difference.inDays;
      }
      if (null != lastRenewed) {
        DateTime renewDateTime = DateTime.parse(lastRenewed);
        lastRenewedText = toFormattedBS(renewDateTime, format: 'MMMM d, y');
      }

      var agentDetail =
          subscriptionPageController.subDetail['agent_detail'] ?? {};
      return SafeArea(
          child: Scaffold(
              // resizeToAvoidBottomPadding: true,
              resizeToAvoidBottomInset: true,
              appBar: AppBar(
                centerTitle: true,
                backgroundColor: colorPrimary,
                elevation: 0,
                title: const Text(
                  "सदस्यता जानकारी\n(Subscription Information)",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: 18,
                      color: Colors.white,
                      fontFamily: 'HelveticaRegular',
                      fontWeight: FontWeight.bold),
                ),
              ),
              body: ListView(
                // padding: EdgeInsets.all(20),
                children: [
                  Stack(
                    children: [
                      Container(
                        height: 100,
                        color: Theme.of(context).primaryColor,
                      ),
                      Container(
                        padding:
                            const EdgeInsets.only(left: 20, right: 20, top: 10),
                        child: Stack(children: [
                          Container(
                            clipBehavior: Clip.hardEdge,
                            decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border.all(color: colorPrimary),
                                borderRadius: BorderRadius.circular(10)),
                            child: Column(
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(20),
                                  child: Column(
                                    children: [
                                      Text(
                                        remainingDays.toString(),
                                        style: labelStyle2.copyWith(
                                            fontSize: 28,
                                            fontWeight: FontWeight.bold,
                                            color: remainingDays < 20
                                                ? colorRedDark
                                                : colorPrimary),
                                      ),
                                      const SizedBox(
                                        height: 10,
                                      ),
                                      Text(
                                        remainingDays > 0
                                            ? "DAYS LEFT"
                                            : "DAYS PAST",
                                        style: labelStyle2.copyWith(
                                            fontWeight: FontWeight.bold,
                                            color: textColor),
                                      ),
                                      const SizedBox(
                                        height: 10,
                                      ),
                                      Card(
                                        color: Theme.of(context).primaryColor,
                                        child: Container(
                                            padding: const EdgeInsets.symmetric(
                                                vertical: 4, horizontal: 12),
                                            child: Text(
                                              null == lastRenewed
                                                  ? "Demo Account"
                                                  : "Paid Account",
                                              style: const TextStyle(
                                                  color: Colors.white),
                                            )),
                                      ),
                                      const SizedBox(
                                        height: 10,
                                      ),
                                      if (null != lastRenewed)
                                        Text("Last Renewed on $lastRenewedText",
                                            style: formFieldTextStyle),
                                    ],
                                  ),
                                ),
                                Divider(
                                  height: 4,
                                  color: colorPrimary,
                                  thickness: 1,
                                ),
                                Stack(
                                  children: [
                                    Container(
                                        padding: const EdgeInsets.all(10),
                                        child: Column(children: [
                                          Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Expanded(
                                                  child: Text(
                                                "Representative Name :",
                                                style: kLabelStyle,
                                              )),
                                              Expanded(
                                                  child: Text(
                                                agentDetail['name'] ?? "N/A",
                                                style: kLabelStyle,
                                              ))
                                            ],
                                          ),
                                          const SizedBox(
                                            height: 10,
                                          ),
                                          Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Expanded(
                                                  child: Text(
                                                "Mobile No :",
                                                style: kLabelStyle,
                                              )),
                                              Expanded(
                                                  child: null ==
                                                          agentDetail[
                                                              'mobile_no']
                                                      ? Text(
                                                          "N/A",
                                                          style: kLabelStyle,
                                                        )
                                                      : Row(
                                                          mainAxisAlignment:
                                                              MainAxisAlignment
                                                                  .spaceBetween,
                                                          children: [
                                                            Material(
                                                              color: Colors
                                                                  .transparent,
                                                              child: InkWell(
                                                                onTap: () {
                                                                  dialNumber(
                                                                      agentDetail[
                                                                          'mobile_no']);
                                                                },
                                                                child: Row(
                                                                  children: [
                                                                    const Icon(
                                                                      Icons
                                                                          .call,
                                                                      size: 14,
                                                                    ),
                                                                    const SizedBox(
                                                                      width: 6,
                                                                    ),
                                                                    Text(
                                                                      agentDetail[
                                                                          'mobile_no'],
                                                                      style:
                                                                          kLabelStyle,
                                                                    )
                                                                  ],
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ))
                                            ],
                                          ),
                                        ])),
                                    if (isAdmin)
                                      Positioned(
                                          right: 0,
                                          child: Material(
                                            color: Colors.transparent,
                                            child: InkWell(
                                              onTap: () async {
                                                var resp =
                                                    await showModalBottomSheet(
                                                        isScrollControlled:
                                                            true,
                                                        context: context,
                                                        builder: (_) =>
                                                            UpdateAgentDialogContent(
                                                              builderContext: _,
                                                            ));
                                                if (null != resp) {
                                                  subscriptionPageController
                                                      .recheckExpiryFromNetwork();
                                                }
                                              },
                                              child: Container(
                                                padding:
                                                    const EdgeInsets.all(8),
                                                // color: Colors.red,
                                                child: const Icon(
                                                  Icons.edit,
                                                  size: 18,
                                                ),
                                              ),
                                            ),
                                          ))
                                  ],
                                ),
                              ],
                            ),
                          ),
                          Positioned(
                              right: 0,
                              child: Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  onTap: () async {
                                    ProgressDialog progressDialog =
                                        ProgressDialog(context,
                                            type: ProgressDialogType.normal,
                                            isDismissible: false);
                                    progressDialog.update(
                                        message:
                                            "Checking payment. Please wait....");
                                    await progressDialog.show();
                                    Tuple2<bool, String> checkResponse =
                                        await subscriptionPageController
                                            .recheckExpiryFromNetwork();
                                    await progressDialog.hide();
                                    if (!checkResponse.item1) {
                                      // Navigator.pop(context, true);
                                      showToastMessage(context,
                                          message: checkResponse.item2,
                                          duration: 2);
                                      // Navigator.popAndPushNamed(
                                      //     context, '/home');
                                      // Navigator.of(context).pop();
                                    } else {
                                      showToastMessage(context,
                                          alertType: AlertType.Error,
                                          message: checkResponse.item2,
                                          duration: 2);
                                    }
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.all(8),
                                    // color: Colors.red,
                                    child: const Icon(Icons.refresh),
                                  ),
                                ),
                              ))
                        ]),
                      )
                    ],
                  ),

                  const SizedBox(
                    height: 20,
                  ),

                  Text(
                    "Select Your Plan",
                    style: labelStyle2.copyWith(
                        fontSize: 24, decoration: TextDecoration.underline),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(
                    height: 30.0,
                  ),

                  ListView.separated(
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return InkWell(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) =>
                                  SubscriptionPaymentDetailPage(
                                package: paymentSubscriptionController
                                    .subPackages[index],
                              ),
                              // builder: (context) => PaymentSubscriptionView(
                              //   id: paymentSubscriptionController
                              //       .subPackages[0].packageId,
                              //   price: paymentSubscriptionController
                              //       .subPackages[0].packagePrice,
                              //   taxAmount: paymentSubscriptionController
                              //       .subPackages[0].vatAmount,
                              // ),
                            ),
                          );
                        },
                        child: Container(
                          // height: MediaQuery.of(context).size.height * .11,
                          padding: const EdgeInsets.all(12),
                          width: double.infinity,
                          decoration: BoxDecoration(
                            color: colorPrimary,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          margin: const EdgeInsets.symmetric(horizontal: 6),
                          child: Column(
                            children: [
                              xxsHeightSpan,
                              xxsHeightSpan,
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 20),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Expanded(
                                      child: Text(
                                        paymentSubscriptionController
                                            .subPackages[index].title!,
                                        textAlign: TextAlign.center,
                                        style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w300,
                                          fontFamily: "ArialBlack",
                                          fontSize: 18,
                                          decoration: TextDecoration.underline,
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              xsHeightSpan,
                              Text(
                                paymentSubscriptionController
                                    .subPackages[index].subTitle!,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w500,
                                  fontSize: 16,
                                ),
                              ),
                              // xsHeightSpan,
                              //  Text(
                              //   paymentSubscriptionController.subPackages[0].vatAmount,
                              //   style: TextStyle(
                              //     color: Colors.white,
                              //     fontWeight: FontWeight.w500,
                              //     fontSize: 16,
                              //   ),
                              // ),
                            ],
                          ),
                        ),
                      );
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return sHeightSpan;
                    },
                    itemCount: paymentSubscriptionController.subPackages.length,
                  ),

                  if (paymentSubscriptionController.isLoading)
                    Container(
                        color: Colors.white,
                        child:
                            const Center(child: CircularProgressIndicator())),
                  if (paymentSubscriptionController.isError)
                    Container(
                      alignment: Alignment.center,
                      child: InkWell(
                        onTap: () {
                          paymentSubscriptionController.init();
                        },
                        child: Text(paymentSubscriptionController.errorMessage),
                      ),
                    ),
                  // ...paymentSubscriptionController.subPackages.map((e) {
                  //   return null != e.displayImage
                  //       ? Container(
                  //           height: 100,
                  //           margin: const EdgeInsets.only(
                  //               bottom: 20, left: 10, right: 10),
                  //           child: Ink(
                  //             decoration: BoxDecoration(
                  //               image: DecorationImage(
                  //                   image: NetworkImage(e.displayImage!),
                  //                   fit: BoxFit.fitWidth),
                  //             ),
                  //             child: InkWell(
                  //               onTap: () async {
                  //                 // var esewaRes = await Navigator.pushNamed(
                  //                 //     context, '/subscriptionPaymentDetail',
                  //                 //     arguments: SubscriptionPaymentDetailPage(
                  //                 //       package: e,
                  //                 //     ));
                  //                 // Log.d("got esewa resp $esewaRes ");
                  //               },
                  //               splashColor: Colors.brown.withOpacity(0.5),
                  //             ),
                  //           ),
                  //         )
                  //       : Container(
                  //           padding: const EdgeInsets.only(
                  //             bottom: 10,
                  //           ),
                  //           child: Material(
                  //             color: Colors.transparent,
                  //             child: InkWell(
                  //               onTap: () async {
                  //                 var esewaRes = await Navigator.pushNamed(
                  //                     context, '/subscriptionPaymentDetail',
                  //                     arguments: SubscriptionPaymentDetailPage(
                  //                       package: e,
                  //                     ));
                  //                 // Log.d("got esewa resp $esewaRes ");
                  //               },
                  //               child: Container(
                  //                 width: double.infinity,
                  //                 padding: const EdgeInsets.all(20),
                  //                 alignment: Alignment.center,
                  //                 clipBehavior: Clip.hardEdge,
                  //                 decoration: BoxDecoration(
                  //                   border: Border.all(
                  //                       color: (planId == e.packageId)
                  //                           ? Theme.of(context).primaryColor
                  //                           : Colors.black26,
                  //                       width: 2),
                  //                   borderRadius: BorderRadius.circular(10),
                  //                 ),
                  //                 child: Padding(
                  //                   padding: const EdgeInsets.all(20),
                  //                   child: Text(
                  //                     "${e.title}",
                  //                     style: labelStyle2,
                  //                   ),
                  //                 ),
                  //               ),
                  //             ),
                  //           ),
                  //         );
                  // }).toList(),
                  // if()

                  if (isExpired) ...[
                    const SizedBox(
                      height: 20,
                    ),
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 20),
                      clipBehavior: Clip.hardEdge,
                      decoration: BoxDecoration(
                          border: Border.all(color: colorRedDark),
                          borderRadius: BorderRadius.circular(10)),
                      child: Column(
                        children: [
                          Container(
                            color: colorRedDark,
                            alignment: Alignment.center,
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(vertical: 6),
                            child: Text(
                              "Service Expired",
                              style: labelStyle2.copyWith(
                                  color: Colors.white, fontSize: 20),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.only(
                                left: 20, right: 20, bottom: 20, top: 10),
                            child: Column(
                              children: [
                                Text(
                                  "$SUBSCRIPTION_EXPIRY_MESSAGE \n$SUBSCRIPTION_EXPIRY_MESSAGE_EN",
                                  style: labelStyle2.copyWith(
                                      color: textColor,
                                      fontWeight: FontWeight.w300),
                                  textAlign: TextAlign.center,
                                )
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    // Container(
                    //   child: Center(
                    //     child: RaisedButton(
                    //         color: colorPrimary,
                    //         elevation: 10,
                    //         shape: RoundedRectangleBorder(
                    //           borderRadius: BorderRadius.circular(10.0),
                    //         ),
                    //         splashColor: colorPrimaryLightest,
                    //         child: Padding(
                    //           padding: const EdgeInsets.symmetric(
                    //               vertical: 10, horizontal: 40),
                    //           child: Text(
                    //             "Update Representative",
                    //             style: TextStyle(
                    //                 color: Colors.white,
                    //                 fontSize: 15,
                    //                 fontWeight: FontWeight.bold),
                    //           ),
                    //         ),
                    //         onPressed: () async {
                    //           showModalBottomSheet(
                    //               isScrollControlled: true,
                    //               context: context,
                    //               builder: (_) => UpdateAgentDialogContent(
                    //                     builderContext: _,
                    //                   ));
                    //         }),
                    //   ),
                    // ),
                    // SizedBox(
                    //   height: 20,
                    // ),
                  ],

                  const SizedBox(
                    height: 30,
                  ),
                ],
              )));
    });
  }
}
