import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/app/model/database/item_modal.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

// ignore: must_be_immutable
class ItemAutoCompleteTextFieldWithAdd extends StatefulWidget {
  bool? enableFlag;
  String? labelText;
  TextEditingController? controller;
  Function? onChangedFn;
  String? itemID;
  Function? onSuggestionSelectedFn;

  ItemAutoCompleteTextFieldWithAdd(
      {this.enableFlag = true,
      this.labelText = "Select Item",
      this.controller,
      this.onChangedFn,
      this.onSuggestionSelectedFn,
      this.itemID});

  @override
  _ItemAutoCompleteTextFieldWithAddState createState() =>
      _ItemAutoCompleteTextFieldWithAddState();
}

class _ItemAutoCompleteTextFieldWithAddState
    extends State<ItemAutoCompleteTextFieldWithAdd> {
  // ItemListController itemListController = Get.put(ItemListController());
  ItemRepository itemRepository = new ItemRepository();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: TypeAheadField<ItemModel?>(
        // suggestionsBoxController: _suggestionsBoxController,
        textFieldConfiguration: TextFieldConfiguration(
          enabled: widget.enableFlag!,
          controller: widget.controller,
          decoration: formFieldStyle.copyWith(labelText: widget.labelText),
          onChanged: (value) => widget.onChangedFn!(value),
        ),
        suggestionsCallback: (pattern) async {
          // _suggestionsBoxController.open();

          List<ItemModel> results = await itemRepository.getItemsByTitle(
              pattern,
              includes: widget.itemID != null ? [widget.itemID!] : []);

          // await ledgerListController.searchByTitleWithBalance(pattern,
          //     included: widget.ledgetID != null ? [widget.ledgetID] : []);

          return results;

          // await itemListController.searchByTitle(pattern.toLowerCase(),
          //     includes: widget.itemID != null ? [widget.itemID] : []);
          // // return [];
          // return itemListController.items;
        },
        transitionBuilder: (context, suggestionsBox, controller) {
          return suggestionsBox;
        },
        noItemsFoundBuilder: (no_item_cotext) {
          if (widget.controller!.text.isEmpty) {
            return Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text("No Items Found"),
            );
          }
          return InkWell(
            onTap: () async {
              await ItemRepository()
                  .createIfNotExistByItemName(widget.controller!.text);
              // Optional: Clear and reassign text to force refresh
              String currentText = widget.controller!.text;
              widget.controller!.text = '';
              widget.controller!.text = currentText;

              // Optional: You can also manually trigger suggestions box if needed
              // _suggestionsBoxController.open();
            },
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Text(
                "Add \"${widget.controller!.text}\"",
                style: labelStyle2,
              ),
            ),
          );
        },
        itemBuilder: (context, item) {
          return Column(
            children: [
              ListTile(
                contentPadding:
                    EdgeInsets.symmetric(horizontal: 10, vertical: 0),
                title: Text(
                  "${item!.itemName}",
                  style: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
                ),
                trailing: (null != item.itemCode)
                    ? Text(
                        "(${item.itemCode})",
                        style: TextStyle(fontSize: 12, color: Colors.black54),
                      )
                    : null,
              ),
              Divider(
                height: 0,
                thickness: 1,
              ),
            ],
          );
        },
        onSuggestionSelected: (item) => widget.onSuggestionSelectedFn!(item),
      ),
    );
  }
}
