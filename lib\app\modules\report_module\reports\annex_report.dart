// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
import 'package:mobile_khaata_v2/app/model/others/annex_item_model.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/annex_report_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/report_controllers/report_transaction_controller.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';

/**
 * ANNEX 13 REPORT (अनुसुची-१३ रिपोर्ट)
 *
 * This is a VAT compliance report required by Nepal's tax authorities.
 * It shows party-wise transaction summaries for tax filing purposes.
 *
 * FEATURES:
 * - Date range filtering for fiscal periods
 * - Party-wise opening and closing balances
 * - Sales and sales return amounts (taxable)
 * - Purchase and purchase return amounts (taxable)
 * - TIN number display for tax compliance
 * - Print functionality for official submissions
 *
 * TAX COMPLIANCE:
 * - Shows only taxable amounts for VAT calculation
 * - Includes TIN numbers for registered parties
 * - Follows Nepal's tax reporting format
 * - Required for VAT return filing
 */
// ignore: must_be_immutable
class AnnexReport extends StatefulWidget {
  const AnnexReport({super.key});

  @override
  _AnnexReportState createState() => _AnnexReportState();
}

class _AnnexReportState extends State<AnnexReport> {
  final ReportTransactionController _controller = ReportTransactionController();

  String startDate = currentDate;
  String endDate = currentDate;

  @override
  void initState() {
    super.initState();
    generate();
  }

  @override
  void dispose() {
    super.dispose();
  }

  /**
   * GENERATE ANNEX 13 REPORT
   *
   * Triggers the generation of tax compliance report for the selected period
   */
  generate() {
    _controller.generateAnnex13Report(startDate: startDate, endDate: endDate);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        elevation: 0,
        titleSpacing: -5.0,
        backgroundColor: colorPrimary,
        title: const Text(
          "अनुसुची-१३ रिपोर्ट\n(Annex 13 Report)",
          style: TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
      ),
      body: GestureDetector(
        onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
        child: Container(
          color: Colors.black12,
          child: Column(
            children: [
              // DATE RANGE FILTER SECTION
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: ReportCustomDatePickerTextField(
                        initialValue: toDateBS(DateTime.parse(startDate)),
                        hintText: "From Date",
                        onChange: (selectedDate) {
                          startDate =
                              toDateAD(NepaliDateTime.parse(selectedDate));
                          setState(() {});
                          generate();
                        },
                      ),
                    ),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Text(
                          "TO",
                          style: labelStyle2,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: ReportCustomDatePickerTextField(
                        initialValue: toDateBS(DateTime.parse(endDate)),
                        hintText: "To Date",
                        onChange: (selectedDate) {
                          endDate =
                              toDateAD(NepaliDateTime.parse(selectedDate));
                          setState(() {});
                          generate();
                        },
                      ),
                    ),
                  ],
                ),
              ),

              const Divider(
                height: 4,
                color: Colors.black54,
              ),

              // COLUMN HEADERS
              DefaultTextStyle(
                style: TextStyle(
                    fontSize: 14,
                    color: textColor,
                    fontWeight: FontWeight.bold),
                child: Container(
                  color: Colors.white,
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: const [
                      // Party Name & TIN Column
                      Expanded(flex: 2, child: Text("Party")),

                      // Opening Balance Column
                      Expanded(
                        flex: 1,
                        child: Text(
                          "Opening",
                          textAlign: TextAlign.right,
                        ),
                      ),

                      // Closing Balance Column
                      Expanded(
                        flex: 1,
                        child: Text(
                          "Closing",
                          textAlign: TextAlign.right,
                        ),
                      ),

                      // Sales/Sales Return Column
                      Expanded(
                        flex: 1,
                        child: Text(
                          "Sales\n/\nSales Return",
                          textAlign: TextAlign.right,
                        ),
                      ),
                      SizedBox(width: 6),

                      // Purchase/Purchase Return Column
                      Expanded(
                        flex: 1,
                        child: Text(
                          "Purchase\n/\nPurchase Return",
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const Divider(
                height: 0,
                color: Colors.black54,
              ),

              // MAIN CONTENT SECTION
              Obx(() {
                if (_controller.txnLoading) {
                  return Container(
                      color: Colors.white,
                      child: const Center(child: CircularProgressIndicator()));
                }

                if (_controller.annexItems.isEmpty) {
                  return Container(
                      color: Colors.white,
                      width: double.infinity,
                      child: const Center(
                          child: Text(
                        "No Records Found for Selected Period",
                        style: TextStyle(color: Colors.black54),
                      )));
                } else {
                  return Expanded(
                      child: _AnnexListView(_controller.annexItems));
                }
              }),
            ],
          ),
        ),
      ),

      // GENERATE REPORT BUTTON
      bottomNavigationBar: SizedBox(
          height: 45,
          child: ElevatedButton(
            style: ElevatedButton.styleFrom(
              backgroundColor: colorPrimary,
            ),
            child: const Text(
              "Generate Report",
              style: TextStyle(color: Colors.white, fontSize: 18),
            ),
            onPressed: () {
              // Navigate to print page for official report generation
              Navigator.of(context).pushNamed('/printAnnex13',
                  arguments: AnnexReportPrintPage(
                    transactions: _controller.annexItems,
                    startDate: startDate,
                    endDate: endDate,
                  ));
            },
          )),
    ));
  }
}

/**
 * ANNEX LIST VIEW WIDGET
 *
 * Displays party-wise tax compliance data in tabular format
 * Shows all financial transactions relevant for VAT filing
 */
class _AnnexListView extends StatelessWidget {
  final List<AnnexItemModel> _annexItems;

  const _AnnexListView(this._annexItems);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: _annexItems.length,
      padding: const EdgeInsets.only(bottom: 20),
      itemBuilder: (context, int index) {
        AnnexItemModel txn = _annexItems[index];

        return InkWell(
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                DefaultTextStyle(
                  style: TextStyle(fontSize: 12, color: colorPrimary),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // PARTY NAME & TIN SECTION
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${txn.ledgerName ?? 'Unknown Party'}",
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                              // TIN NUMBER DISPLAY (Tax Identification Number)
                              if (![null, ""].contains(txn.tinNo)) ...[
                                const SizedBox(height: 4),
                                Text(
                                  "${txn.tinNo ?? ""} (${txn.tinFlag ?? ''})",
                                  style: TextStyle(
                                    fontSize: 10,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ]
                            ],
                          ),
                        ),

                        // OPENING BALANCE SECTION - FIXED null safety
                        Expanded(
                          flex: 1,
                          child: Text(
                            _formatAmount(txn.openingBalance ?? 0.0),
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              color: (txn.openingBalance ?? 0.0) >= 0
                                  ? Colors.green
                                  : Colors.red,
                            ),
                          ),
                        ),

                        // CLOSING BALANCE SECTION - FIXED null safety
                        Expanded(
                          flex: 1,
                          child: Text(
                            _formatAmount(txn.closingBalance ?? 0.0),
                            textAlign: TextAlign.right,
                            style: TextStyle(
                              color: (txn.closingBalance ?? 0.0) >= 0
                                  ? Colors.green
                                  : Colors.red,
                            ),
                          ),
                        ),
                        const SizedBox(width: 6),

                        // SALES & SALES RETURN SECTION - FIXED null safety
                        Expanded(
                            flex: 1,
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  // Sales Amount (Taxable)
                                  Text(
                                    _formatAmount(txn.salesTaxableTotal ?? 0.0),
                                    textAlign: TextAlign.right,
                                    style: const TextStyle(color: Colors.blue),
                                  ),
                                  // Sales Return Amount (Taxable)
                                  Text(
                                    _formatAmount(
                                        txn.salesReturnTaxableTotal ?? 0.0),
                                    textAlign: TextAlign.right,
                                    style:
                                        const TextStyle(color: Colors.orange),
                                  ),
                                ])),

                        const SizedBox(width: 6),

                        // PURCHASE & PURCHASE RETURN SECTION - FIXED null safety
                        Expanded(
                            flex: 1,
                            child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  // Purchase Amount (Taxable)
                                  Text(
                                    _formatAmount(
                                        txn.purchaseTaxableTotal ?? 0.0),
                                    textAlign: TextAlign.right,
                                    style:
                                        const TextStyle(color: Colors.purple),
                                  ),
                                  // Purchase Return Amount (Taxable)
                                  Text(
                                    _formatAmount(
                                        txn.purchaseReturnTaxableTotal ?? 0.0),
                                    textAlign: TextAlign.right,
                                    style: const TextStyle(color: Colors.teal),
                                  ),
                                ])),
                      ],
                    ),
                  ),
                ),
                const Divider(
                  height: 4,
                  color: Colors.black54,
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /**
   * FORMAT AMOUNT FOR DISPLAY
   *
   * Formats financial amounts with proper decimal places
   * Handles null values safely and shows 0.00 for empty amounts
   */
  String _formatAmount(double? amount) {
    if (amount == null) return "0.00";
    return amount.toStringAsFixed(2);
  }
}
