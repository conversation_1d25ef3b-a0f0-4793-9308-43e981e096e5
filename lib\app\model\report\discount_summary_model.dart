class DiscountSummaryReportModel {
  String? ledgerTitle;
  double? saleDiscountAmount;
  double? purchaseDiscountAmount;

  DiscountSummaryReportModel({
    this.ledgerTitle,
    this.saleDiscountAmount,
    this.purchaseDiscountAmount,
  });

  factory DiscountSummaryReportModel.fromJson(Map<String, dynamic> json) {
    return DiscountSummaryReportModel(
      ledgerTitle: json['ledger_title'],
      saleDiscountAmount: json['sale_discount_amount'],
      purchaseDiscountAmount: json['purchase_discount_amount'],
    );
  }
}
