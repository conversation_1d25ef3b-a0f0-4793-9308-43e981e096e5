import 'dart:convert';

import 'package:flutter/material.dart';

class PaymentResponseView extends StatelessWidget {
  const PaymentResponseView(
      {super.key, this.isSuccess = false, required this.url});

  final bool isSuccess;
  final Uri url;

  Map<String, dynamic>? extractAndDecodeEsewaData(String url) {
    try {
      // Parse the URL
      Uri uri = Uri.parse(url);

      // Extract the 'data' query parameter
      String? encodedData = uri.queryParameters['data'];

      if (encodedData == null || encodedData.isEmpty) {
        debugPrint("No 'data' parameter found in the URL.");
        return null;
      }

      // Decode the Base64-encoded JSON string
      String decodedJson = utf8.decode(base64.decode(encodedData));

      // Parse the JSON string into a Map
      Map<String, dynamic> result = jsonDecode(decodedJson);

      return result;
    } catch (e) {
      debugPrint("Error decoding Esewa data: $e");
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final Map<String, dynamic>? decodedData =
        extractAndDecodeEsewaData(url.toString());
    return WillPopScope(
      onWillPop: () async {
        Navigator.of(context).popUntil((route) => route.isFirst);
        return false; // Prevent default pop
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(isSuccess ? "Payment Successful" : "Payment Failed"),
          // automaticallyImplyLeading: false,
        ),
        body: Center(
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  isSuccess ? Icons.check_circle : Icons.error,
                  color: isSuccess ? Colors.green : Colors.red,
                  size: 100,
                ),
                const SizedBox(height: 20),
                Text(
                  isSuccess ? "Payment Successful" : "Payment Failed",
                  style: TextStyle(
                    fontSize: 24,
                    color: isSuccess ? Colors.green : Colors.red,
                  ),
                ),
                const SizedBox(height: 20),
                Text(
                  isSuccess
                      ? "Your payment was successful. Thank you!"
                      : "Your payment failed. Please try again.",
                  textAlign: TextAlign.center,
                  style: const TextStyle(fontSize: 16),
                ),
                const SizedBox(height: 20),
                decodedData == null
                    ? const Center(child: Text("Invalid or missing data"))
                    : ListView(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        padding: const EdgeInsets.all(16),
                        children: decodedData.entries.map((entry) {
                          if (entry.key != "product_code" &&
                              entry.key != "signed_field_names") {
                            return ListTile(
                              title: Text(entry.key.toUpperCase()),
                              subtitle: Text(entry.value.toString()),
                            );
                          } else {
                            return const SizedBox.shrink();
                          }
                        }).toList(),
                      ),
                const SizedBox(height: 20),
                ElevatedButton(
                  onPressed: () {
                    Navigator.of(context).popUntil((route) => route.isFirst);
                  },
                  child: const Text("Go Back"),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
