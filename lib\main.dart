import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/login_helper.dart';
import 'package:mobile_khaata_v2/utilities/route_generator.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:get/get.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp, // Allows portrait mode (device held upright)
    DeviceOrientation
        .portraitDown, // Allows inverse portrait mode (device held upside down)
  ]).then((_) {
    // Once the orientation is set, run your app
    runApp(const MyApp());
  });
}

final GlobalKey<NavigatorState> navigatorKey =
    GlobalKey(debugLabel: "Main Navigator");

bool isExpired = false;
int isActive = 1;
bool isAdmin = false;
int isMultiUser = 1;
bool isWebEnabled = false;
bool isUnauthorized = false;
bool isUserLoggedIn = false;

void resetGlobalVariables() {
  isExpired = false;
  isActive = 1;
  isAdmin = false;
  isMultiUser = 1;
  isWebEnabled = false;
  isUnauthorized = false;
  isUserLoggedIn = false;
}

refreshGlobalVariables() async {
  isAdmin = await LoginHelper().isAdminUser;
  await refreshExpiredUser();
  await refreshActiveUser();
  await refreshMultiUser();
  await refreshUnAuthorized();
}

final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
    FlutterLocalNotificationsPlugin();
NotificationAppLaunchDetails? notificationAppLaunchDetails;

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      theme: ThemeData(
        primaryColor: colorPrimary,
        scaffoldBackgroundColor: Colors.white,
        textTheme: const TextTheme(
          bodyText2: TextStyle(),
        ).apply(
          bodyColor: textColor,
        ),
        fontFamily: 'HelveticaRegular',
      ),

      // Routes
      initialRoute: '/',
      navigatorKey: navigatorKey,
      onGenerateRoute: RouteGenerator.generateRoute,
    );
  }
}
