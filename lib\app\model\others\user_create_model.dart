// ignore_for_file: unnecessary_brace_in_string_interps

class UserCreateModel {
  String? userFullName;
  String? userName;
  String? password;
  String? verifyPassword;

  UserCreateModel(
      {this.userFullName, this.userName, this.password, this.verifyPassword});

  Map<String, dynamic> toJson() {
    return {
      'full_name': userFullName,
      'mobile_no': userName,
      'pass_word': password,
      'verify_password': verifyPassword
    };
  }

  factory UserCreateModel.fromJson(Map<String, dynamic> json) {
    return UserCreateModel(
        userFullName: json['full_name'],
        userName: json['mobile_no'],
        password: json['pass_word'],
        verifyPassword: json['verify_password']);
  }
  @override
  String toString() {
    return "${userFullName} ${userName}";
  }
}
