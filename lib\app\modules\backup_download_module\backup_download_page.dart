import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import 'package:permission_handler/permission_handler.dart';

class BackupDownloadPage extends StatefulWidget {
  const BackupDownloadPage({super.key});

  @override
  State<StatefulWidget> createState() => _BackupDownloadPageState();
}

class _BackupDownloadPageState extends State<BackupDownloadPage> {
  bool isAllowed = false;

  @override
  void initState() {
    isAllowed = true;
    super.initState();
  }

  void downloadBackup() async {
    var granted = await Permission.storage.status;
    if (!granted.isGranted) {
      await Permission.storage.request();
    }

    // // the downloads folder path
    // Directory tempDir = await DownloadsPathProvider.downloadsDirectory;
    // String tempPath = tempDir.path;
  }

  bool checkCode(context) {
    GlobalKey<FormState> formKey = GlobalKey<FormState>();
    TextEditingController securityCodeController = TextEditingController();

    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          titlePadding: EdgeInsets.zero,
          title: Container(
            padding: const EdgeInsets.only(left: 10, right: 10, top: 15),
            child: Container(
              padding: const EdgeInsets.only(bottom: 10),
              decoration: const BoxDecoration(
                  border: Border(bottom: BorderSide(color: Colors.black12))),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Icon(Icons.security, color: textColor),
                  const Text(" "),
                  Text(
                    "Enter Security code",
                    style: TextStyle(
                        color: textColor,
                        fontSize: 16,
                        fontWeight: FontWeight.bold),
                  )
                ],
              ),
            ),
          ),
          contentPadding: const EdgeInsets.all(0.0),
          content: Container(
            padding:
                const EdgeInsets.only(left: 15, right: 15, top: 15, bottom: 10),
            child: Form(
              key: formKey,
              child: FormBuilderTextField(
                name: "security_code",
                autocorrect: false,
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.next,
                style: formFieldTextStyle,
                decoration: formFieldStyle.copyWith(labelText: "Security Code"),
                controller: securityCodeController,
                validator: (value) {
                  if (null == value || value.isEmpty) {
                    return "सुरक्षा कोड हाल्नुहोस् (Enter Security Code)";
                  }
                  return null;
                },
              ),
            ),
          ),
          elevation: 24.0,
          actions: <Widget>[
            ElevatedButton(
              child: const Text("Cancel"),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              child: const Text("OK"),
              onPressed: () {
                if (formKey.currentState!.validate()) {
                  String code = securityCodeController.text;

                  // Log.d("code==>$code");

                  if ("1234" == code) {
                    Navigator.of(context).pop();
                    return;
                  }
                }
              },
            )
          ],
        );
      },
    );

    return false;
  }

  @override
  Widget build(BuildContext context) {
    if (isAllowed) {
      return SafeArea(
          child: Scaffold(
        // resizeToAvoidBottomPadding: true,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          leading: BackButton(
            onPressed: () => Navigator.pop(context, false),
          ),
          centerTitle: false,
          titleSpacing: -10.0,
          title: const ListTile(
            contentPadding: EdgeInsets.only(right: 15),
            title: Text(
              "डाउनलोड ब्याकअप\n(Download Backup)",
              style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontFamily: 'HelveticaRegular',
                  fontWeight: FontWeight.bold),
            ),
          ),
        ),
        body: Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                "ब्याकअप डाउनलोड गर्ने तरिका",
                style: labelStyle2.copyWith(fontSize: 22),
                textAlign: TextAlign.center,
              ),
              Text(
                "Steps to Download Backup",
                style: labelStyle2.copyWith(fontSize: 20),
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 40,
              ),
              Text(
                "ब्याकअप डाउनलोड गर्न डाउनलोड बटनमा क्लिक गर्नुहोस् र सुरक्षा कोड हाल्नुहोस् र ब्याकअप फाइललाई इच्छित स्थानमा सेभ गर्नुहोस् ।",
                style: TextStyle(color: textColor, fontSize: 14),
                textAlign: TextAlign.center,
              ),
              Text(
                "\nTo Download backup click on the download button and enter the security code and save the backup file to desired location.",
                style: TextStyle(color: textColor, fontSize: 14),
                textAlign: TextAlign.center,
              ),
              const SizedBox(
                height: 20,
              ),
            ],
          ),
        ),
        bottomNavigationBar: ElevatedButton(
          // color: colorPrimary,
          // elevation: 10,
          // splashColor: colorPrimaryLightest,
          child: const Padding(
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 40),
            child: Text(
              "डाउनलोड ब्याकअप\n(Download Backup)",
              style: TextStyle(
                color: Colors.white,
                fontSize: 18,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          onPressed: () {
            // importController.importData(context);
            bool result = checkCode(context);
            // Log.d("result==>$result");
          },
        ),
      ));
    } else {
      return Container(
          color: Colors.white,
          child: const Center(child: CircularProgressIndicator()));
    }
  }
}
