import 'dart:convert';

class AppException implements Exception {
  late var _message;
  late var _prefix;
  late int _statusCode;
  late bool _isJsonMessage;
  late var _jsonMessage;

  AppException([String? message, String? prefix, int? statusCode]) {
    this._message = message;
    this._prefix = prefix;
    this._statusCode = statusCode!;
    _checkJsonMessage();
  }
  _checkJsonMessage() {
    try {
      this._jsonMessage = json.decode(this._message) as Map<String, dynamic>;
      this._isJsonMessage = true;
    } catch (e) {
      this._isJsonMessage = false;
    }
  }

  String toString() {
    String msg = "";
    msg += (null == _statusCode) ? "" : _statusCode.toString() + ": ";
    msg += (null == _prefix) ? "" : _prefix;
    msg += (null == _message) ? "" : _message;
    return msg;
  }

  int statusCode() {
    return this._statusCode;
  }

  String getMessage() {
    return this._message;
  }

  dynamic getJson() {
    return this._jsonMessage;
  }

  bool get isJson {
    return this._isJsonMessage;
  }
}

class NoInternetException extends AppException {
  NoInternetException([String? message, int? statusCode])
      : super(message, "No Internet Connection: ", statusCode);
}

class FetchDataException extends AppException {
  FetchDataException([String? message, int? statusCode])
      : super(message, "Error During Communication: ", statusCode);
}

class BadRequestException extends AppException {
  BadRequestException([String? message, int? statusCode])
      : super(message, "Invalid Request: ", statusCode);
}

class FormatException extends AppException {
  FormatException([String? message, int? statusCode])
      : super(message, "Invalid Data Format: ", statusCode);
}

class ForbiddenException extends AppException {
  ForbiddenException([String? message, int? statusCode])
      : super(message, "Forbidden: ", statusCode);
}

class UnauthorizedException extends AppException {
  UnauthorizedException([String? message, int? statusCode])
      : super(message, "Unauthorised: ", statusCode);
}

class NotFoundException extends AppException {
  NotFoundException([String? message, int? statusCode])
      : super(message, "Requested Service unavailable: ", statusCode);
}

class InvalidInputException extends AppException {
  InvalidInputException([String? message, int? statusCode])
      : super(message, "Invalid Input: ", statusCode);
}

class MethodNotAllowedException extends AppException {
  MethodNotAllowedException([String? message, int? statusCode])
      : super(message, "Requested Service Not-Allowed: ", statusCode);
}

class InternalServeErrorException extends AppException {
  InternalServeErrorException([String? message, int? statusCode])
      : super(message, "Error During Communication with Server: ", statusCode);
}
