import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

import 'package:sqflite/sqflite.dart';

class ItemModel {
  ItemModel(
      {this.itemId,
      this.itemCode,
      this.itemName,
      this.itemDescription,
      this.categoryId,
      this.itemLocation,
      this.itemType = 1,
      this.itemSaleUnitPrice,
      this.itemPurchaseUnitPrice,
      this.itemMinStockQuantity,
      this.baseUnitId,
      this.alternateUnitId,
      this.unitConversionFactor,
      this.itemIsActive = 1,
      this.openingStock,
      this.openingDate,
      this.openingDateBS,
      this.lastActivityType,
      this.lastActivityAt,
      this.lastActivityBy});

  String? itemId;
  String? itemCode;
  String? itemName;
  String? itemDescription;
  String? categoryId;
  String? itemLocation;
  int? itemType;
  double? itemSaleUnitPrice;
  double? itemPurchaseUnitPrice;
  double? itemMinStockQuantity;
  String? baseUnitId;
  String? alternateUnitId;
  double? unitConversionFactor;
  int? itemIsActive;

  double? openingStock;
  String? openingDate;
  String? openingDateBS;

  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;

  factory ItemModel.fromJson(Map<String, dynamic> json) {
    DateTime? openingDateTimeObj =
        DateTime.tryParse(json["opening_date"] ?? "");
    // print("base uni id");
    // print(json["unit_conversion_factor"]);
    // print(json["unit_conversion_factor"].runtimeType.runtimeType);
    // print(type(json["unit_conversion_factor"]))

    return ItemModel(
        itemId: json["item_id"],
        itemCode: json["item_code"],
        itemName: json["item_name"],
        itemDescription: json["item_description"],
        categoryId: json["category_id"],
        itemLocation: json["item_location"],
        itemType: json["item_type"],
        itemSaleUnitPrice: json["item_sale_unit_price"],
        itemPurchaseUnitPrice: json["item_purchase_unit_price"],
        itemMinStockQuantity: json["item_min_stock_quantity"],
        baseUnitId: json["base_unit_id"],
        alternateUnitId: json["alternate_unit_id"],
        unitConversionFactor:
            json["unit_conversion_factor"].runtimeType == String
                ? 1.0
                : double.parse(json["unit_conversion_factor"].toString()),
        itemIsActive: json["item_is_active"],
        openingStock: json["opening_stock"],
        openingDate: (null != openingDateTimeObj)
            ? DateFormat('y-MM-dd').format(openingDateTimeObj)
            : '',
        openingDateBS:
            (null != openingDateTimeObj) ? toDateBS(openingDateTimeObj) : '',
        lastActivityType: json["last_activity_type"],
        lastActivityAt: json["last_activity_at"],
        lastActivityBy: json['last_activity_by']);
  }

  Map<String, dynamic> toJson() => {
        "item_id": itemId,
        "item_code": itemCode,
        "item_name": itemName,
        "item_description": itemDescription,
        "category_id": categoryId,
        "item_location": itemLocation,
        "item_type": itemType,
        "item_sale_unit_price": itemSaleUnitPrice,
        "item_purchase_unit_price": itemPurchaseUnitPrice,
        "item_min_stock_quantity": itemMinStockQuantity,
        "base_unit_id": baseUnitId,
        "alternate_unit_id": alternateUnitId,
        "unit_conversion_factor": unitConversionFactor,
        "item_is_active": itemIsActive,
        "opening_stock": openingStock,
        "opening_date": openingDate,
        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        "last_activity_by": lastActivityBy
      };
}
