import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/item_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/item_transaction_model.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';

class ItemDetailController extends GetxController {
  var _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  var _txnLoading = true.obs;
  bool get txnLoading => _txnLoading.value;

  var _isSearching = true.obs;
  bool get isSearching => _isSearching.value;

  ItemDetailModel? _item;
  ItemDetailModel? get item => _item;

  List<ItemTransactionModel> transactions = [];

  List<ItemTransactionModel> _filteredTransactions = [];
  List<ItemTransactionModel> get filteredTransactions => _filteredTransactions;

  final ItemRepository _itemRepository = ItemRepository();

  init(String itemId) async {
    loadItemDetail(itemId);
    loadItemTransactions(itemId);
  }

  loadItemDetail(String itemId) async {
    _isLoading(true);
    _item = await _itemRepository.getItemsDetailById(itemId);
    _isLoading(false);
  }

  loadItemTransactions(String itemId) async {
    _txnLoading(true);

    // Log.d("loading item  transactios");
    List<ItemTransactionModel> txns =
        await _itemRepository.getTransactionForItem(itemId);

    transactions = txns;

    _filteredTransactions.clear();
    _filteredTransactions.addAll(transactions);

    _txnLoading(false);
  }

  searchTransaction(String searchString) {
    _isSearching(true);
    _filteredTransactions.clear();
    for (var item in transactions) {
      _filteredTransactions.addIf(
          item
              .toString()
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          item);
    }
    _isSearching(false);
  }

  reload() async {
    await init(item?.itemId ?? "");
  }
}
