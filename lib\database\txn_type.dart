class TxnType {
  static final int sales = 1;
  static final int purchase = 2;

  static final int paymentIn = 3;
  static final int paymentOut = 4;

  static final int openingReceive = 5;
  static final int openingPay = 6;

  static final int expense = 7;

  static final int salesReturn = 8;
  static final int purchaseReturn = 9;

  static final int openingStock = 10;
  static final int addStock = 11;
  static final int reduceStock = 12;

  static final int bankDeposit = 14;
  static final int bankWithdrawn = 15;
  static final int increaseBankBalance = 16;
  static final int decreaseBankBalance = 17;
  static final int bankToBankTransfer = 18;

  static final int addCash = 19;
  static final int reduceCash = 20;

  static final int chequeTransfer = 30;

  static final Map<int, String> txnTypeText = ({
    TxnType.sales: "Sales",
    TxnType.purchase: "Purchase",
    TxnType.paymentIn: "Receipt",
    TxnType.paymentOut: "Payment-Out",
    TxnType.openingReceive: "Opening (Receivable)",
    TxnType.openingPay: "Opening (Payable)",
    TxnType.expense: "Expense",
    TxnType.salesReturn: "Sales Return",
    TxnType.purchaseReturn: "Purchase Return",
    TxnType.openingStock: "Opening Stock",
    TxnType.addStock: "Add Stock",
    TxnType.reduceStock: "Reduce Stock",
    TxnType.bankDeposit: "Deposited",
    TxnType.bankWithdrawn: "Withdrawn",
    TxnType.increaseBankBalance: "Increase Balance",
    TxnType.decreaseBankBalance: "Decrease Balance",
    TxnType.bankToBankTransfer: "Bank To Bank Transfer",
    TxnType.addCash: "Add Cash",
    TxnType.reduceCash: "Reduce Cash",
    TxnType.chequeTransfer: "Cheque Transfer",
  });

  static final List<int> allTypes = [
    1,
    2,
    3,
    4,
    5,
    6,
    7,
    8,
    9,
    10,
    11,
    12,
    19,
    20
  ];

  static final List<int> financialTxnTypeList = [
    TxnType.sales,
    TxnType.purchase,
    TxnType.salesReturn,
    TxnType.purchaseReturn,
    TxnType.paymentIn,
    TxnType.paymentOut,
    TxnType.expense,
  ];

  static final List<int> ledgerTxnTypeList = [
    TxnType.sales,
    TxnType.purchase,
    TxnType.salesReturn,
    TxnType.purchaseReturn,
    TxnType.paymentIn,
    TxnType.paymentOut,
    TxnType.expense,
  ];

  static final List<int> cashInTransaction = [
    sales,
    purchaseReturn,
    paymentIn,
    addCash,
  ];

  static final List<int> cashOutTransaction = [
    purchase,
    salesReturn,
    paymentOut,
    expense,
    reduceCash,
  ];

  static final List<int> payableTxn = [
    purchase,
    salesReturn,
    paymentOut,
    expense,
    openingPay
  ];
  static final List<int> receivableTxn = [
    sales,
    purchaseReturn,
    paymentIn,
    openingReceive
  ];

  static final List<int> bankInTransaction = [
    bankDeposit,
    increaseBankBalance,
    // sales,
    // purchaseReturn,
    // paymentIn,
  ];

  static final List<int> bankOutTransaction = [
    bankWithdrawn,
    decreaseBankBalance,
    // purchase,
    // salesReturn,
    // paymentOut,
    // expense,
  ];
}
