import 'package:mobile_khaata_v2/app/model/database/item_adjustment_model.dart';
import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:tuple/tuple.dart';

class ItemAdjustmentRepository {
  final String tag = "ItemAdjustmentRepository";
  final String tableName = 'mk_item_adjustments';
  DatabaseHelper databaseHelper = DatabaseHelper();
  QueryRepository queryRepository = QueryRepository();

  //========================================================================================= SYNCING ACTIONS
  Future<String> insert(ItemAdjustmentModel adjustment,
      {dynamic dbClient, String? batchID}) async {
    String itemAdjId;

    dbClient ??= await databaseHelper.database;

    String primaryKeyPrefix = await getPrimaryKeyPrefix();
    itemAdjId = primaryKeyPrefix + uuidV4;
    adjustment.itemAdjId = itemAdjId;

    adjustment.lastActivityAt = currentDateTime;
    adjustment.lastActivityBy = await getLastActivityBy();
    adjustment.lastActivityType = LastActivityType.New;

    await dbClient.insert(tableName, adjustment.toJson());

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.insert,
      data: adjustment.toJson(),
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);
    pushPendingQueries(source: "TRIGGER", dbClient: dbClient);

    return itemAdjId;
  }

  Future<bool> update(ItemAdjustmentModel adjustment,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    adjustment.lastActivityAt = currentDateTime;
    adjustment.lastActivityBy = await getLastActivityBy();
    adjustment.lastActivityType = LastActivityType.Edit;

    String whereClause = "item_adj_id = ?";
    List<dynamic> whereArgs = [adjustment.itemAdjId];

    await dbClient.update(tableName, adjustment.toJson(),
        where: whereClause, whereArgs: whereArgs);

    QueryModel newQueryModel = QueryModel(
        tableName: tableName,
        queryType: QueryType.update,
        data: adjustment.toJson(),
        whereArgs: whereArgs,
        whereClause: whereClause);
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);
    pushPendingQueries(source: "TRIGGER", dbClient: dbClient);

    status = true;

    return status;
  }

  Future<Tuple2<bool, String>> delete(String txnID,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;
    String message = "";

    dbClient ??= await databaseHelper.database;
    String whereClause = "item_adj_id = ?";
    List<dynamic> whereArgs = [txnID];
    try {
      // can soft delete for given id
      await dbClient.update(
          tableName, {"last_activity_type": LastActivityType.Delete},
          where: whereClause, whereArgs: whereArgs);

      QueryModel newQueryModel = QueryModel(
          tableName: tableName,
          queryType: QueryType.update,
          whereArgs: whereArgs,
          whereClause: whereClause,
          data: {"last_activity_type": LastActivityType.Delete});

      await queryRepository.pushQuery(newQueryModel,
          batchID: batchID, dbClient: dbClient);
      pushPendingQueries(source: "TRIGGER", dbClient: dbClient);

      status = true;
      message = "Item Adjustment deleted successfully";
    } catch (e) {
      // Log.d("error updating" + e.toString());
      message =
          "Cannot delete Item Adjustment at this moment. Please try again later";
    }

    return Tuple2(status, message);
  }

  //=========================================================================================NON SYNCING ACTIONS
  Future<ItemAdjustmentModel> getAdjustmentById(String adjustmentId,
      {dynamic dbClient}) async {
    ItemAdjustmentModel adjustment = ItemAdjustmentModel();

    try {
      if (null == dbClient) {
        dbClient = await databaseHelper.database;
      }

      if (null != adjustmentId) {
        Map<String, dynamic> json = (await dbClient.rawQuery(
                "SELECT * FROM mk_item_adjustments WHERE last_activity_type!=? AND item_adj_id = ?",
                [LastActivityType.Delete, adjustmentId]))
            .first;
        adjustment = ItemAdjustmentModel.fromJson(json);
      }
    } catch (e, trace) {
      print("error $e");
      // Log.e(tag, e.toString() + trace.toString());
    }

    return adjustment;
  }
}
