import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/user_modal.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';

import 'package:tuple/tuple.dart';

class UserPermissionController extends GetxController {
  var _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  var _permissions = [].obs;
  get permissions => _permissions.value;

  var _readOnlyFlag = false.obs;
  bool get readOnlyFlag => _readOnlyFlag.value;

  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  UserModel? selectedUser;

  init(UserModel userModel) {
    selectedUser = userModel;
    _permissions.clear();
    _permissions.addAll(userModel.userPermissions!);
    _readOnlyFlag(true);
  }

  onSelectSinglePermission(String singlePermission) {
    if (_permissions.contains(singlePermission)) {
      _permissions.remove(singlePermission);
      update();
    } else {
      _permissions.add(singlePermission);
      update();
    }
  }

  clearAllPermission() {
    _permissions.clear();
  }

  bool isAllExistForGroup(int groupID) {
    List<String> groupValues =
        PermissionManager.permissionValuesForGroup(groupID);
    List<String> remainingItemsOfGroupsUnselected =
        groupValues.toSet().difference(permissions.toSet()).toList();
    // Log.d(remainingItemsOfGroupsUnselected);
    bool status = remainingItemsOfGroupsUnselected.isEmpty;
    return status;
  }

  onSelectGroupPermission(int groupID) {
    List<String> groupValues =
        PermissionManager.permissionValuesForGroup(groupID);
    List<String> remainingItemsOfGroupsUnselected =
        groupValues.toSet().difference(permissions.toSet()).toList();
    List newPermissions = [];
    if (remainingItemsOfGroupsUnselected.isEmpty) {
      newPermissions =
          permissions.toSet().difference(groupValues.toSet()).toList();
      _permissions.clear();
      _permissions(newPermissions);
      update();
    } else {
      _permissions.addAll(remainingItemsOfGroupsUnselected);
    }
  }

  onSelectAllPermission(List<String> selectedPermissions) {
    _permissions.clear();
    _permissions.addAll(selectedPermissions);
    update();
  }

  Future<Tuple2<bool, String>> updateUserPermission() async {
    bool status = false;
    String message = "";

    try {
      ApiBaseHelper apiBaseHelper = new ApiBaseHelper();
      ApiResponse apiResponse = await apiBaseHelper.post(
          apiBaseHelper.UPDATE_USER_PERMISSION.toString(),
          {
            'sub_user_id': selectedUser?.userId ?? "",
            'permissions': permissions
          },
          accessToken: true);

      if (apiResponse.status) {
        status = true;
        // update within the list
      } else {}
      message = apiResponse.msg ?? "";
    } catch (e) {
      message = FAILED_OPERATION_ERROR;
    }

    return Tuple2(status, message);
  }
}
