// ignore_for_file: non_constant_identifier_names

import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/model/database/notification_model.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/notification_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

import 'package:sqflite/sqflite.dart';

int CLEAR_NOTIFICATION_BEFORE_DAYS = 2;

class NotificationRepository {
  final String tag = "NotificationRepository";
  final String tableName = "mk_notifications";
  DatabaseHelper databaseHelper = DatabaseHelper();

  Future<bool> insert(NotificationModel notificationModel,
      {dynamic dbClient}) async {
    bool status = false;

    try {
      dbClient ??= await databaseHelper.database;
      // Database dbClient = await databaseHelper.database;
      await dbClient.insert(tableName, notificationModel.toJson());
      status = true;
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }

  Future<bool> insertLocal(NotificationModel notificationModel,
      {dynamic dbClient}) async {
    bool status = false;

    try {
      dbClient ??= await databaseHelper.database;
      // Database dbClient = await databaseHelper.database;
      await dbClient.insert(tableName, notificationModel.toJson());
      status = true;
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }

  Future<List<NotificationModel>> getNotificationFor(
      {String? date, dynamic dbClient}) async {
    date ??= currentDate;
    dbClient ??= await databaseHelper.database;

    List<NotificationModel> notifications = [];

    List<Map<String, dynamic>> json = await dbClient.query(tableName,
        where: 'notification_date = ?',
        whereArgs: [date],
        orderBy: 'notification_id ASC');

    notifications = json.map((e) => NotificationModel.fromJson(e)).toList();

    return notifications;
  }

  Future<bool> createNotificationFromReminder() async {
    bool status = true;

    try {
      Database? dbClient = await databaseHelper.database;
      String query = """
            INSERT INTO mk_notifications (notification_date,title,subtitle,reminder_id,notification_type,link_type,link_url,params) 
            SELECT 
            start_datetime, 
            CASE WHEN reminder_type=1 THEN 'भुक्तानी रिमाइन्डर (Payment Reminder)' ELSE 'कार्य रिमाइन्डर (Task Reminder)' END, 
            description,
            reminder_id, 
            CASE WHEN reminder_type=1 THEN '${NotificationType.payment}' ELSE '${NotificationType.local}' END, 
            '${NotificationLinkType.INTERNAL}' as link_type,
            CASE WHEN reminder_type=1 THEN '/partyLedgerDetail' ELSE '/reminderList' END, 
            '{"ledger_id":"'|| IFNULL(ledger_id,'') || '"}' as params
            FROM mk_reminders 
            WHERE last_activity_type<>3 AND start_datetime<=?
            ;""";

      await dbClient!.rawQuery(query, [tomorrowDate]);
      // Log.d("success");
      status = true;
    } catch (e) {
      // Log.e(tag, e.toString());
    }

    return status;
  }

  // Future<int> deleteNotificationFor({String date, dynamic dbClient}) async {
  //   int deleteCount = 0;
  //   try {
  //     if (dbClient == null) {
  //       dbClient = await databaseHelper.database;
  //     }
  //     // Database dbClient = await databaseHelper.database;
  //     deleteCount =
  //         await dbClient.delete(tableName, where: 'notification_date = ', whereArgs: [date]);
  //   } catch (e) {
  //     Log.e(tag, e.toString());
  //   }
  //   return deleteCount;
  // }

  Future<bool> clearOldNotifications({dynamic dbClient}) async {
    bool status = false;
    // CLEAR notification before CLEAR_NOTIFICATION_BEFORE_DAYS
    String dateBefore = DateFormat('y-MM-dd').format(DateTime.now()
        .subtract(Duration(days: CLEAR_NOTIFICATION_BEFORE_DAYS)));
    try {
      dbClient ??= await databaseHelper.database;
      // Database dbClient = await databaseHelper.database;
      await dbClient.delete(tableName,
          where: 'notification_date < ? ', whereArgs: [dateBefore]);
      status = true;
    } catch (e) {
      // Log.e(tag, e.toString());
    }

    return status;
  }
}
