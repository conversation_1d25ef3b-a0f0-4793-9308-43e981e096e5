import 'dart:async';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:tuple/tuple.dart';

class PasswordResetController extends GetxController {
  final TextEditingController otpController = TextEditingController();
  final formKey = GlobalKey<FormState>();
  StreamController<ErrorAnimationType> errorController =
      StreamController<ErrorAnimationType>();

  TextEditingController newPasswordCtrl = TextEditingController();
  TextEditingController verifyNewPasswordCtrl = TextEditingController();

  TextEditingController mobileNoCtrl = TextEditingController();

  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  var _isChecking = false.obs;
  bool get isChecking => _isChecking.value;

  set setChecking(bool val) {
    _isChecking(val);
    _isChecking.refresh();
  }

  final _isSubmitting = false.obs;
  bool get isSubmitting => _isSubmitting.value;

  String? token;
  String? mobileNo;

  @override
  // ignore: unnecessary_overrides
  void onInit() {
    super.onInit();
    print("Mah suru vako ");
  }

  // Method to clear all previous data
  void clearAllData() {
    mobileNoCtrl.clear();
    otpController.clear();
    newPasswordCtrl.clear();
    verifyNewPasswordCtrl.clear();
    token = null;
    mobileNo = null;
    _isChecking(false);
    _isSubmitting(false);
    _isLoading(false);
  }

  Future<Tuple2<bool, String>> checkUserExistForReset() async {
    _isChecking(true);
    _isChecking.refresh();
    bool status = false;
    String message = "";

    if (mobileNoCtrl.text.isEmpty) {
      message = "Registered Mobile No cannot be empty";
    } else if (mobileNoCtrl.text.length < 10) {
      message = "Not a valid mobile number";
    } else {
      try {
        ApiBaseHelper apiBaseHelper = ApiBaseHelper();
        ApiResponse apiResponse = await apiBaseHelper.get(
            "${apiBaseHelper.CHECK_USER_NUMBER}/${mobileNoCtrl.text}",
            accessToken: false);
        if (apiResponse.status) {
          token = apiResponse.data['token'];
          mobileNo = apiResponse.data['mobile_no'];
          status = true;
        } else {}
        message = apiResponse.msg ?? "";
      } catch (e) {
        // Log.d(e);
        message = FAILED_OPERATION_ERROR;
      }
    }

    _isChecking(false);
    _isChecking.refresh();
    return Tuple2(status, message);
  }

  Future<Tuple2<bool, String>> resetPassword({String? tk}) async {
    bool status = false;
    String message = "";
    // Log.d("this is final token $tk ");
    _isSubmitting(true);
    _isSubmitting.refresh();
    if (6 != otpController.text.length) {
      message = "Not a valid OTP";
    } else if (newPasswordCtrl.text.isEmpty) {
      message = "New password is required";
    } else if (verifyNewPasswordCtrl.text.isEmpty) {
      message = "Verify password is required";
    } else if (newPasswordCtrl.text != verifyNewPasswordCtrl.text) {
      message = "New Password and Verify Password must be same";
    } else {
      try {
        var data = {
          "otp": otpController.text,
          "new_password": newPasswordCtrl.text,
          "verify_password": verifyNewPasswordCtrl.text
        };
        ApiBaseHelper apiBaseHelper = ApiBaseHelper();
        Log.d(
            "${apiBaseHelper.ACTION_AUTH_RESET_PASS}/${strTrim(tk.toString())}");
        Log.d("this is data ${data}");
        ApiResponse apiResponse = await apiBaseHelper.post(
            "${apiBaseHelper.ACTION_AUTH_RESET_PASS}/${strTrim(tk.toString())}",
            data,
            accessToken: false);
        if (apiResponse.status) {
          status = true;
        } else {}
        message = apiResponse.msg ?? "";
      } catch (e) {
        // Log.d(e);
        message = FAILED_OPERATION_ERROR;
      }
    }
    _isSubmitting(false);
    _isSubmitting.refresh();
    return Tuple2(status, message);
  }

  Future<Tuple2<bool, String>> resendOTP({String tk = ""}) async {
    bool status = false;
    String message = "";

    try {
      ApiBaseHelper apiBaseHelper = ApiBaseHelper();
      ApiResponse apiResponse = await apiBaseHelper
          .get("${apiBaseHelper.ACTION_RESENDOTP}/$tk", accessToken: false);

      if (apiResponse.status) {
        status = true;
      } else {}
      message = apiResponse.msg ?? "";
    } catch (e) {
      // Log.d(e);
      message = FAILED_OPERATION_ERROR;
    }

    return Tuple2(status, message);
  }
}
