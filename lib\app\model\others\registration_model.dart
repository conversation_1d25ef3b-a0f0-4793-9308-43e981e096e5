class NewRegistrationModel {
  NewRegistrationModel(
      {this.fullName,
      this.mobileNo,
      this.companyName,
      this.companyAddress,
      this.gpsLat,
      this.gpsLng,
      this.panVatNo,
      this.panVatFlag,
      this.countryCode});

  String? fullName;
  String? mobileNo;
  String? companyName;
  String? companyAddress;
  String? gpsLat;
  String? gpsLng;
  String? panVatNo;
  String? panVatFlag;
  String? countryCode;

  factory NewRegistrationModel.fromJson(Map<String, dynamic> json) =>
      NewRegistrationModel(
        fullName: json["full_name"],
        mobileNo: json["mobile_no"],
        companyName: json["company_name"],
        companyAddress: json["company_address"],
        gpsLat: json["gps_lat"],
        gpsLng: json["gps_lng"],
        panVatNo: json["pan_vat_no"],
        panVatFlag: json["pan_vat_flag"],
        countryCode: json["country_code"],
      );

  Map<String, dynamic> toJson() => {
        "full_name": fullName,
        "mobile_no": mobileNo,
        "company_name": companyName,
        "company_address": companyAddress,
        "gps_lat": gpsLat,
        "gps_lng": gpsLng,
        "pan_vat_no": panVatNo,
        "pan_vat_flag": panVatFlag,
        "country_code": countryCode,
      };
}

class NewRegistrationApiResponseModel {
  NewRegistrationApiResponseModel(
      {required this.token, required this.registeredMobileNo});

  String token;
  String registeredMobileNo;

  factory NewRegistrationApiResponseModel.fromJson(Map<String, dynamic> json) =>
      NewRegistrationApiResponseModel(
        token: json["token"],
        registeredMobileNo: json["registered_mobile_no"],
      );
}
