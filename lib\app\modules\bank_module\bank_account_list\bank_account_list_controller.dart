import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/payment_type_model.dart';
import 'package:mobile_khaata_v2/app/repository/payment_type_repository.dart';

class BankAccountListController extends GetxController {
  final String tag = "BankAccountListController";

  var _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  List<PaymentTypeModel> _bankList = [];
  List<PaymentTypeModel> get bankList => _bankList;

  final PaymentTypeRepository _paymentTypeRepository = PaymentTypeRepository();

  init() async {
    _isLoading.value = true;

    _bankList.clear();
    _bankList.addAll(await _paymentTypeRepository.getAllBank());

    _isLoading.value = false;
  }
}
