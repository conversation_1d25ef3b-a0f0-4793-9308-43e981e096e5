import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/model/database/transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_wise_transaction_model.dart';
import 'package:mobile_khaata_v2/app/repository/line_item_repository.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/app/repository/txn_image_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:mobile_khaata_v2/utilities/nepali_date.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:sqflite/sqflite.dart';
import 'package:tuple/tuple.dart';

class TransactionRepository {
  final String tag = "TransactionRepository";
  final String tableName = "mk_transactions";
  DatabaseHelper databaseHelper = DatabaseHelper();
  QueryRepository queryRepository = QueryRepository();

  //========================================================================================= SYNCING ACTIONS
  Future<String> insert(TransactionModel transactionModel,
      {dynamic dbClient, String? batchID}) async {
    String txnId;

    dbClient ??= await databaseHelper.database;

    String primaryKeyPrefix = await getPrimaryKeyPrefix();
    transactionModel.txnId = primaryKeyPrefix + uuidV4;

    transactionModel.lastActivityAt = currentDateTime;
    transactionModel.lastActivityBy = await getLastActivityBy();
    transactionModel.lastActivityType = LastActivityType.New;

    await dbClient.insert(tableName, transactionModel.toJson());
    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.insert,
      data: transactionModel.toJson(),
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    txnId = transactionModel.txnId ?? "";

    return txnId;
  }

  Future<bool> update(TransactionModel transactionModel,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    transactionModel.lastActivityAt = currentDateTime;
    transactionModel.lastActivityBy = await getLastActivityBy();
    transactionModel.lastActivityType = LastActivityType.Edit;

    String whereClause = "txn_id = ?";
    List<dynamic> whereArgs = [transactionModel.txnId];

    await dbClient.update(tableName, transactionModel.toJson(),
        where: whereClause, whereArgs: whereArgs);

    QueryModel newQueryModel = QueryModel(
        tableName: tableName,
        queryType: QueryType.update,
        data: transactionModel.toJson(),
        whereArgs: whereArgs,
        whereClause: whereClause);
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    status = true;

    return status;
  }

  Future<Tuple2<bool, String>> delete(String txnID,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;
    String message = "";
    LineItemRepository lineItemRepository = LineItemRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();

    dbClient ??= await databaseHelper.database;
    String whereClause = "txn_id = ?";
    List<dynamic> whereArgs = [txnID];
    try {
      // can soft delete for given id
      dbClient.update(
          tableName, {"last_activity_type": LastActivityType.Delete},
          where: whereClause, whereArgs: whereArgs);

      //delete all line items and txn image linked to this  transaction

      await lineItemRepository.deleteLineItemsForTransaction(txnID,
          dbClient: dbClient, batchID: batchID);

      await txnImageRepository.deleteImagesForTransaction(txnID,
          dbClient: dbClient, batchID: batchID);

      QueryModel newQueryModel = QueryModel(
          tableName: tableName,
          queryType: QueryType.update,
          whereArgs: whereArgs,
          whereClause: whereClause,
          data: {"last_activity_type": LastActivityType.Delete});

      await queryRepository.pushQuery(newQueryModel,
          batchID: batchID, dbClient: dbClient);

      pushPendingQueries(singleBatchId: batchID, source: "TRIGGER");

      status = true;
      message = "Transaction deleted successfully";
    } catch (e) {
      message =
          "Cannot delete transaction at this moment. Please try again later";
    }

    return Tuple2(status, message);
  }

  //=========================================================================================NON SYNCING ACTIONS

  Future<TransactionModel?> getTransactionByTxnId(String txnID) async {
    TransactionModel? transactionModel;

    try {
      Database? dbClient = await databaseHelper.database;
      Map<String, dynamic> jsonData = (await dbClient!
              .rawQuery("SELECT * FROM $tableName WHERE txn_id=?", [txnID]))
          .first;
      transactionModel = TransactionModel.fromJson(jsonData);
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return transactionModel;
  }

  Future<AllTransactionModel?> getTransactionDetailById(String txnID) async {
    AllTransactionModel? transactionModel;
    try {
      Database? dbClient = await databaseHelper.database;
      Map<String, dynamic> jsonData = (await dbClient!.rawQuery(
              "SELECT mk_transactions.*,mk_expense_category.expense_title FROM mk_transactions LEFT JOIN mk_expense_category ON mk_expense_category.expense_category_id = mk_transactions.expense_category_id WHERE mk_transactions.txn_id=?",
              [txnID]))
          .first;
      transactionModel = AllTransactionModel.fromJson(jsonData);
    } catch (e, trace) {
      Log.e(tag, e.toString() + trace.toString());
    }
    return transactionModel;
  }

  Future<List<LedgerWiseTransactionModel>> getLedgerWiseTransaction(
      String ledgerID) async {
    List<LedgerWiseTransactionModel> transactions = [];
    try {
      Database? dbClient = await databaseHelper.database;

      String query =
          "SELECT * FROM mk_transactions WHERE ledger_id=? AND last_activity_type!=?  ORDER BY txn_date DESC, last_activity_at DESC";
      List<Map<String, dynamic>> jsonData = (await dbClient!
          .rawQuery(query, [ledgerID, LastActivityType.Delete]));

      transactions =
          jsonData.map((e) => LedgerWiseTransactionModel.fromJson(e)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return transactions;
  }

  Future<Tuple2<String, double>> getTotalSalesCurrentMonth() async {
    double totalReceivable = 0.00;
    try {
      Database? dbClient = await databaseHelper.database;

      List<String> currentDate = currentDateBS.split('-');

      String days = NepaliDate.daysInMonth(
              year: int.tryParse(currentDate[0])!,
              month: int.tryParse(currentDate[1])!)
          .toString();

      currentDate[2] = '01';
      String startDate = toDateAD(NepaliDateTime.parse(currentDate.join("-")));
      currentDate[2] = days;
      String endDate = toDateAD(NepaliDateTime.parse(currentDate.join("-")));

      String query = "SELECT "
          "IFNULL(SUM("
          "(txn_cash_amount+txn_balance_amount) "
          "), 0.00) AS total "
          "FROM mk_transactions "
          "WHERE txn_type = '${TxnType.sales}' AND last_activity_type<>3 AND txn_date BETWEEN ? AND ?;";
      List<Map<String, dynamic>> data =
          await dbClient!.rawQuery(query, [startDate, endDate]);
      if (data.isNotEmpty) {
        totalReceivable = data[0]['total'];
      }
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return Tuple2(
        NepaliDateTime.now().format('MMMM').toString(), totalReceivable);
  }

  Future<Tuple2<String, double>> getTotalPurchaseCurrentMonth() async {
    double totalReceivable = 0.00;
    try {
      Database? dbClient = await databaseHelper.database;

      List<String> currentDate = currentDateBS.split('-');

      String days = NepaliDate.daysInMonth(
              year: int.tryParse(currentDate[0])!,
              month: int.tryParse(currentDate[1])!)
          .toString();

      currentDate[2] = '01';
      String startDate = toDateAD(NepaliDateTime.parse(currentDate.join("-")));
      currentDate[2] = days;
      String endDate = toDateAD(NepaliDateTime.parse(currentDate.join("-")));

      String query = "SELECT "
          "IFNULL(SUM("
          "(txn_cash_amount+txn_balance_amount) "
          "), 0.00) AS total "
          "FROM mk_transactions "
          "WHERE txn_type = '${TxnType.purchase}' AND last_activity_type<>3 AND txn_date BETWEEN ? AND ?;";

      List<Map<String, dynamic>> data =
          await dbClient!.rawQuery(query, [startDate, endDate]);
      if (data.isNotEmpty) {
        totalReceivable = data[0]['total'];
      }
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return Tuple2(
        NepaliDateTime.now().format('MMMM').toString(), totalReceivable);
  }

  Future<Tuple2<String, double>> getTotalExpensesCurrentMonth() async {
    double totalExpenses = 0.00;
    try {
      Database? dbClient = await databaseHelper.database;

      List<String> currentDate = currentDateBS.split('-');

      String days = NepaliDate.daysInMonth(
              year: int.tryParse(currentDate[0])!,
              month: int.tryParse(currentDate[1])!)
          .toString();

      currentDate[2] = '01';
      String startDate = toDateAD(NepaliDateTime.parse(currentDate.join("-")));
      currentDate[2] = days;
      String endDate = toDateAD(NepaliDateTime.parse(currentDate.join("-")));

      String query = "SELECT "
          "IFNULL(SUM("
          "txn_cash_amount+txn_balance_amount), 0.00) AS total "
          "FROM mk_transactions "
          "WHERE txn_type = 7 AND last_activity_type<>3 AND txn_date BETWEEN ? AND ?;";

      List<Map<String, dynamic>> data =
          await dbClient!.rawQuery(query, [startDate, endDate]);

      if (data.isNotEmpty) {
        totalExpenses = data[0]['total'];
      }
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return Tuple2(
        NepaliDateTime.now().format('MMMM').toString(), totalExpenses);
  }
}
