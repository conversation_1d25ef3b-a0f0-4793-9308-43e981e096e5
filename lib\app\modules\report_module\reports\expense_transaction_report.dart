// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_dropdown.dart';
import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
import 'package:mobile_khaata_v2/app/controllers/expense_category_list_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/expanse_category_model.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/expense_transaction_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/report_controllers/report_transaction_controller.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';

// ignore: must_be_immutable
class ExpenseTransactionReport extends StatefulWidget {
  final String? receivedStart;
  final String? receivedEnd;
  const ExpenseTransactionReport(
      {super.key, this.receivedStart, this.receivedEnd});

  @override
  _ExpenseTransactionReportState createState() =>
      _ExpenseTransactionReportState();
}

class _ExpenseTransactionReportState extends State<ExpenseTransactionReport> {
  final ReportTransactionController _controller = ReportTransactionController();
  ExpensesCategoryListController expensesCategoryListController =
      ExpensesCategoryListController();

  List<int> types = [TxnType.expense];

  String? startDate;
  String? endDate;
  String? categoryID;

  @override
  void initState() {
    super.initState();
    startDate = widget.receivedStart ?? currentDate;
    endDate = widget.receivedEnd ?? currentDate;
    setState(() {});
    expensesCategoryListController.fetchAll();
    generate();
  }

  generate() {
    _controller.generateAllTransactionReport(
        startDate: startDate!,
        endDate: endDate!,
        types: (types.isEmpty) ? TxnType.financialTxnTypeList : types,
        categoryID: categoryID);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        elevation: 0,
        titleSpacing: -5.0,
        backgroundColor: colorPrimary,
        title: const Text(
          "खर्च लेनदेन रिपोर्ट\n(Expenses Transaction Report)",
          style: TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
        actions: [
          PrintButton(
            onPressed: () {
              var categoryText = "All Category";
              if (null != categoryID) {
                ExpenseCategoryModel l =
                    expensesCategoryListController.categories.firstWhere(
                        (element) => element.expenseCategoryId == categoryID);
                categoryText = l.expenseTitle!;
              }
              Navigator.pushNamed(context, '/printExpenseTransactionReport',
                  arguments: ExpenseTransactionReportPrintPage(
                      transactions: _controller.transactions,
                      startDate: startDate,
                      endDate: endDate,
                      categoryText: categoryText,
                      totalAmount: _controller.totalExpense.value));
            },
          )
        ],
      ),
      body: GestureDetector(
        onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
        child: Container(
          color: Colors.black12,
          child: Column(
            children: [
              //=============================transaction date filter
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: ReportCustomDatePickerTextField(
                        initialValue: toDateBS(DateTime.parse(startDate!)),
                        hintText: "From Date",
                        onChange: (selectedDate) {
                          startDate =
                              toDateAD(NepaliDateTime.parse(selectedDate));
                          setState(() {});
                          generate();
                        },
                      ),
                    ),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Text(
                          "TO",
                          style: labelStyle2,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: ReportCustomDatePickerTextField(
                        initialValue: toDateBS(DateTime.parse(endDate!)),
                        hintText: "To Date",
                        onChange: (selectedDate) {
                          endDate =
                              toDateAD(NepaliDateTime.parse(selectedDate));
                          setState(() {});
                          generate();
                        },
                      ),
                    ),
                  ],
                ),
              ),
              const Divider(
                height: 4,
                color: Colors.black54,
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Obx(() => CustomDropdown(
                    borderless: true,
                    style: formFieldTextStyle,
                    decoration: formFieldStyle,
                    value: categoryID,
                    allowClear: false,
                    placeholder: "Select Category",
                    options: [
                      const {'key': null, 'value': "All Category"},
                      ...expensesCategoryListController.categories.map((e) {
                        return {
                          'key': e.expenseCategoryId,
                          'value': e.expenseTitle
                        };
                      }).toList(),
                    ],
                    onChange: (value) {
                      // Log.d("on change $value");
                      categoryID = value;
                      setState(() {});
                      generate();
                    })),
              ),
              const Divider(
                height: 4,
                color: Colors.black54,
              ),

              Obx(() {
                if (_controller.txnLoading) {
                  return Container(
                      color: Colors.white,
                      child: const Center(child: CircularProgressIndicator()));
                }

                if (_controller.transactions.isEmpty) {
                  return Container(
                      color: Colors.white,
                      width: double.infinity,
                      child: const Center(
                          child: Text(
                        "No Records",
                        style: TextStyle(color: Colors.black54),
                      )));
                } else {
                  return Expanded(
                      child: _TxnListView(_controller.transactions));
                }
              }),
            ],
          ),
        ),
      ),
      // extendBody: true,
      bottomNavigationBar: Container(
        height: 45,
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        color: colorPrimary,
        child: SingleChildScrollView(
          child: Obx(() {
            return DefaultTextStyle(
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              child: Row(
                children: [
                  Expanded(
                    flex: 1,
                    child:
                        Text("No. of Txn: ${_controller.transactions.length}"),
                  ),
                  Expanded(
                      flex: 2,
                      child: Text(
                        "Total: ${formatCurrencyAmount(_controller.totalExpense.value, false)}",
                        textAlign: TextAlign.right,
                      )),
                ],
              ),
            );
          }),
        ),
      ),
    ));
  }
}

class _TxnListView extends StatelessWidget {
  final List<AllTransactionModel> _transactionList;

  const _TxnListView(this._transactionList);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: _transactionList.length,
      // shrinkWrap: true,
      itemBuilder: (context, int index) {
        AllTransactionModel txn = _transactionList[index];

        return InkWell(
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                DefaultTextStyle(
                  style: TextStyle(fontSize: 14, color: colorPrimary),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        //====================================1st Column
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${txn.ledgerTitle ?? txn.txnDisplayName}",
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                                style: const TextStyle(
                                    fontSize: 14, fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(
                                height: 5,
                              ),
                              Text(
                                "${txn.txnDateBS}",
                                style: const TextStyle(
                                    fontSize: 12, color: Colors.black54),
                              ),
                            ],
                          ),
                        ),

                        //====================================2nd Column
                        Expanded(
                          flex: 1,
                          child: Text(
                            (null != txn.txnRefNumberChar)
                                ? "${txn.txnTypeText}: #${txn.txnRefNumberChar}"
                                : "${txn.txnTypeText}",
                            textAlign: TextAlign.left,
                          ),
                        ),

                        //====================================3rd Column
                        Expanded(
                          flex: 2,
                          child: Text(
                            formatCurrencyAmount(txn.txnTotalAmount!, false),
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                //Add space if last element
                if (_transactionList.length - 1 == index) ...{
                  const SizedBox(
                      // height: 100,
                      )
                },
              ],
            ),
          ),
        );
      },
    );
  }
}
