import 'package:flutter/material.dart';
import 'package:nepali_utils/nepali_utils.dart';

Color backgroundColorShade = const Color(0xFFf0f5ff);

Color colorPrimaryLightest = const Color(0xFF8BA1C9);
Color colorPrimaryLighter = const Color(0xFF6C89BF);
Color colorPrimaryLight = const Color(0xFF4F73B7);
Color colorPrimary = const Color(0xFF3560AF);
Color colorPrimaryDark = const Color(0xFF0f2754);

Color veryLightOrange = const Color(0xfffcfae3);

Color colorOrangeLight = const Color(0xFFF6AA74);
Color colorOrangeDark = const Color(0xFFDD5F03);

Color colorGreenLight = const Color(0xFF5BBC6A);
Color colorGreen = const Color(0xFF00A219);
Color colorGreenDark = const Color(0xFF008014);

Color colorRedDark = const Color(0xFFC90101);
Color colorRedLight = const Color(0xFFD14545);

Color textColor = const Color(0xFF454545);

Color khaltiColor = const Color(0xff5C2D91);
Color esewaColor = const Color(0xff48A230);

List<BoxShadow> downShadow = [
//  BoxShadow(
//    color: Colors.green.withOpacity(0.8),
//    spreadRadius: -5,
//    offset: Offset(-5, -5),
//    blurRadius: 30
//  ),
  BoxShadow(
      color: Colors.black54.withOpacity(0.2),
      spreadRadius: 0,
      offset: const Offset(0, 2),
      blurRadius: 8),
];

List<BoxShadow> upShadow = [
  BoxShadow(
      color: Colors.black54.withOpacity(0.2),
      spreadRadius: -5,
      offset: const Offset(-2, -2),
      blurRadius: 10),
];

String formatCurrencyAmount(double amount, [bool showCurrencySymbol = true]) {
  String formattedAmount = "0.00";

  // Ensure amount is rounded to exactly 2 decimal places
  amount = double.parse(amount.toStringAsFixed(2));
  amount = (0.00 == amount) ? amount.abs() : amount;

  if (amount >= 0) {
    formattedAmount = NepaliNumberFormat(
      symbol: null,
      isMonetory: true,
      decimalDigits: 2,
    ).format(amount);
  } else {
    formattedAmount = "-${NepaliNumberFormat(
      symbol: null,
      isMonetory: true,
      decimalDigits: 2,
    ).format(amount.abs())}";
  }

  String currencySymbol = (showCurrencySymbol) ? "Rs. " : "";

  return currencySymbol + formattedAmount;
}

//Form styling
final formFieldStyle = InputDecoration(
  contentPadding: const EdgeInsets.symmetric(horizontal: 8, vertical: 10),
  isDense: true,
  prefixStyle: formFieldTextStyle,
  hintStyle: kHintTextStyle,
  border: OutlineInputBorder(
    borderRadius: BorderRadius.circular(6.0),
  ),
  focusedBorder: OutlineInputBorder(
    borderSide: BorderSide(color: colorPrimary, width: 0.5),
    borderRadius: BorderRadius.circular(6.0),
  ),
);

final formFieldStyle2 = InputDecoration(
  contentPadding: const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
  prefixStyle: formFieldTextStyle,
  hintStyle: kHintTextStyle,
  alignLabelWithHint: true,
);

const kHintTextStyle = TextStyle(
  fontSize: 14,
  color: Colors.black38,
  fontFamily: 'HelveticaRegular',
);

final formFieldTextStyle = TextStyle(
  color: textColor,
  fontSize: 16,
);

// ignore: non_constant_identifier_names
final RegformLabelStyle = TextStyle(
  fontSize: 16,
  color: colorPrimary,
  fontFamily: 'HelveticaRegular',
);

const kLabelTextStyle = TextStyle(
  fontSize: 14,
  color: Colors.black38,
  fontFamily: 'HelveticaRegular',
);

final kLabelStyle = TextStyle(
  fontSize: 14,
  color: textColor,
  fontFamily: 'HelveticaRegular',
);

final labelStyle2 = TextStyle(
  fontSize: 16,
  color: colorPrimary,
  fontWeight: FontWeight.bold,
  fontFamily: 'HelveticaRegular',
);
