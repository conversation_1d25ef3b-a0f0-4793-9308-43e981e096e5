import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/registration_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/image_repository.dart';
import 'package:mobile_khaata_v2/app/repository/registration_detail_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';

import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:tuple/tuple.dart';

import '../../../../utilities/shared_pref_helper1.dart';

class UpdateRegistrationDetailController extends GetxController {
  final String tag = "UpdateRegistrationDetailController";

  var _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  var _editFlag = true.obs;
  bool get editFlag => _editFlag.value;

  var _readOnlyFlag = true.obs;
  bool get readOnlyFlag => _readOnlyFlag.value;
  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  var _fileUploadPermission = false.obs;
  bool get fileUploadPermission => _fileUploadPermission.value;

  var _registrationDetail = RegistrationDetailModel().obs;
  RegistrationDetailModel get registrationDetail => _registrationDetail.value;

  ImageModel? logo;
  File? logoImage;

  List<int> initialPhoto = [];

  // ignore: prefer_final_fields
  RegistrationDetailRepository _registrationDetailRepository =
      RegistrationDetailRepository();

  final SharedPrefHelper1 prefsHelper = SharedPrefHelper1();

  final _registrationDetailController = Get.find<RegistrationDetailController>(
      tag: "RegistrationDetailController");

  final formKey = GlobalKey<FormState>();

  @override
  Future<void> onInit() async {
    _isLoading(true);
    _registrationDetail.value =
        _registrationDetailController.registrationDetail;
    if (_registrationDetailController.logo.imageBitmap != null) {
      //image not null
      final tempDir = await getTemporaryDirectory();
      final file = await File(
              '${tempDir.path}/image-000.${_registrationDetailController.logo.imageExt}')
          .create();
      file.writeAsBytesSync(_registrationDetailController.logo.imageBitmap!);
      logoImage = file;
      final lamo = await file.readAsBytes();
      kera = lamo;
      update();
      logo = _registrationDetailController.logo;
    }

    _fileUploadPermission.value =
        await prefsHelper.checkPermission("File Upload");

    _isLoading(false);
    super.onInit();
  }

  Uint8List? kera;

  imagePickerOnChangeHandler(value) async {
    if (value.length > 0) {
      Tuple2<List<int>, String> selectedImage =
          await compressImage(File(value[0].path));
      logo = ImageModel(
          imageBitmap: selectedImage.item1, imageExt: selectedImage.item2);
    } else {
      logo = null;
    }
  }

  bool checkIfLargeImage() {
    //preConvertFiles can be used to convert files list to txn image model once, so than it don't need re convert
    bool status = false;
    if (null != logo) {
      if (logo!.imageBitmap!.length > MAX_IMAGE_SIZE) {
        status = true;
      }
    }
    return status;
  }

  Future<bool> updateDetail() async {
    bool status = false;

    ImageModel? newLogoImage;
    RegistrationDetailModel newRegistrationDetailModel;

    try {
      newLogoImage = logo;
      newRegistrationDetailModel = registrationDetail;

      DatabaseHelper databaseHelper = DatabaseHelper();
      Database? dbClient = await databaseHelper.database;

      await dbClient!.transaction((dbBatchTxn) async {
        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        ImageRepository imageRepository = ImageRepository();
        if (newRegistrationDetailModel.logoId != null) {
          //ledger had previous image, so delete it
          await imageRepository.delete(newRegistrationDetailModel.logoId!,
              dbClient: dbBatchTxn, batchID: batchID);
          newRegistrationDetailModel.logoId = null;
        }

        if (newLogoImage != null) {
          String imageId = await imageRepository.insert(newLogoImage,
              dbClient: dbBatchTxn, batchID: batchID);
          newRegistrationDetailModel.logoId = imageId;
        }

        await _registrationDetailRepository.update(newRegistrationDetailModel,
            dbClient: dbBatchTxn, batchID: batchID);

        status = true;
      });
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return status;
  }
}
