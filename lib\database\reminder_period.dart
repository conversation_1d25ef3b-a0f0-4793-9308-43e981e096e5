class ReminderPeriod {
  static final int once = 1;
  static final int daily = 2;
  static final int weekly = 3;
  static final int monthly = 4;
  static final int yearly = 5;

  static final Map<int, String> reminderPeriodText = ({
    ReminderPeriod.once: "एक पटक (One Time)",
    ReminderPeriod.daily: "दैनिक (Daily)",
    ReminderPeriod.weekly: "साप्ताहिक (Weekly)",
    ReminderPeriod.monthly: "मासिक (Monthly)",
    ReminderPeriod.yearly: "बार्षिक (Yearly)",
  });

  static final List reminderPeriodList = [
    {
      "value": ReminderPeriod.once,
      "text": ReminderPeriod.reminderPeriodText[ReminderPeriod.once]
    },
    {
      "value": ReminderPeriod.daily,
      "text": ReminderPeriod.reminderPeriodText[ReminderPeriod.daily]
    },
    {
      "value": ReminderPeriod.weekly,
      "text": ReminderPeriod.reminderPeriodText[ReminderPeriod.weekly]
    },
    {
      "value": ReminderPeriod.monthly,
      "text": ReminderPeriod.reminderPeriodText[ReminderPeriod.monthly]
    },
    {
      "value": ReminderPeriod.yearly,
      "text": ReminderPeriod.reminderPeriodText[ReminderPeriod.yearly]
    },
  ];
}
