import 'dart:convert';

import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class LedgerWiseTransactionModel {
  LedgerWiseTransactionModel(
      {this.txnId,
      this.txnDate,
      this.txnDateBS,
      this.txnRefNumberChar,
      this.txnType,
      this.txnTypeText,
      this.txnCashAmount,
      this.txnBalanceAmount,
      this.lastActivityBy});

  String? txnId;
  String? txnDate;
  String? txnDateBS;
  String? txnRefNumberChar;
  int? txnType;
  String? txnTypeText;
  double? txnCashAmount;
  double? txnBalanceAmount;
  String? lastActivityBy;

  factory LedgerWiseTransactionModel.fromJson(Map<String, dynamic> json) {
    DateTime txnDateTime = DateTime.parse(json["txn_date"]);
    String txnDate = DateFormat('y-MM-dd').format(txnDateTime);
    String txnDateBS = toDateBS(txnDateTime);

    return LedgerWiseTransactionModel(
        txnId: json["txn_id"],
        txnDate: txnDate,
        txnDateBS: txnDateBS,
        txnRefNumberChar: json["txn_ref_number_char"],
        txnType: json["txn_type"],
        txnTypeText: TxnType.txnTypeText[json["txn_type"]],
        txnCashAmount: parseDouble(json["txn_cash_amount"]),
        txnBalanceAmount: parseDouble(json["txn_balance_amount"]),
        lastActivityBy: json['last_activity_by']);
  }

  Map<String, dynamic> toJson() => {
        "txn_id": txnId,
        "txn_date": txnDate,
        "txn_date_bs": txnDateBS,
        "txn_ref_number_char": txnRefNumberChar,
        "txn_type": txnType,
        "txn_type_text": txnTypeText,
        "txn_cash_amount": txnCashAmount,
        "txn_balance_amount": txnBalanceAmount,
        "last_activity_by": lastActivityBy
      };

  String toString() {
    return jsonEncode(this.toJson());
  }
}
