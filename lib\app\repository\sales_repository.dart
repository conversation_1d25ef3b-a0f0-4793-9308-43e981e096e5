import 'package:mobile_khaata_v2/app/model/database/transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/database/txn_image_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/sale_model.dart';
import 'package:mobile_khaata_v2/app/repository/line_item_repository.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';
import 'package:mobile_khaata_v2/app/repository/txn_image_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:sqflite/sqflite.dart';

import 'package:tuple/tuple.dart';

class SaleRepository {
  final String tag = "SaleRepository";
  DatabaseHelper databaseHelper = DatabaseHelper();
  QueryRepository queryRepository = QueryRepository();

  //get from transaction repository and return from here
  Future<List<SaleModel>> getAllSales() async {
    List<SaleModel> sales = [];
    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> txnDataListJson = (await dbClient!.rawQuery(
          'SELECT * FROM mk_transactions WHERE txn_type=? AND last_activity_type!=?',
          [TxnType.sales, LastActivityType.Delete]));
      sales = txnDataListJson.map((txnData) {
        return SaleModel.fromJson(txnData);
      }).toList();
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return sales;
  }

  Future<Tuple3<SaleModel, List<LineItemDetailModel>, List<TxnImageModel>>>
      getSaleById(String txnID) async {
    TransactionModel? txnData = TransactionModel();
    List<LineItemDetailModel> items = [];
    List<TxnImageModel> images = [];
    TransactionRepository transactionRepository = TransactionRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();
    LineItemRepository lineItemRepository = LineItemRepository();

    try {
      if (null != txnID) {
        txnData = await transactionRepository.getTransactionByTxnId(txnID);
        // Log.d("txn data ${txnData.toJson()}");
        items =
            await lineItemRepository.getLineDetailItemsForTransaction(txnID);
        images = await txnImageRepository.getImagesForTransaction(txnID);
      } else {
        // Throw error for null id
      }
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return Tuple3(SaleModel.fromJson(txnData!.toJson()), items, images);
  }

  Future<String?> addSale(SaleModel saleModel,
      List<LineItemDetailModel> listItem, List<TxnImageModel> images) async {
    String? status;
    TransactionRepository transactionRepository = TransactionRepository();
    LineItemRepository lineItemRepository = LineItemRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();

    try {
      Database? dbClient = await databaseHelper.database;
      String primaryKeyPrefix = await getPrimaryKeyPrefix();
      String batchID = primaryKeyPrefix + uuidV4;
      await dbClient!.transaction((batch) async {
        String txnID = await transactionRepository.insert(
            TransactionModel.fromJson(saleModel.toJson()),
            dbClient: batch,
            batchID: batchID);

        await lineItemRepository.setLineDetailItemsForTransaction(
            txnID, listItem,
            dbClient: batch, batchID: batchID);

        await txnImageRepository.setImagesForTransaction(txnID, images,
            dbClient: batch, batchID: batchID);

        // bool isSuccess = await pushPendingQueries(
        //     singleBatchId: batchID, source: "TRIGGER", dbClient: batch);

        // if (!isSuccess) {
        // throw CustomException("No Net");

        // }
        status = txnID;
      });
      pushPendingQueries(singleBatchId: batchID, source: "TRIGGER");
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }

  Future<bool> updateSale(SaleModel saleModel,
      List<LineItemDetailModel> listItem, List<TxnImageModel>? images) async {
    bool status = false;
    TransactionRepository transactionRepository = TransactionRepository();
    LineItemRepository lineItemRepository = LineItemRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();
    // Log.d(" ${saleModel.toJson()}");
    try {
      Database? dbClient = await databaseHelper.database;
      String primaryKeyPrefix = await getPrimaryKeyPrefix();
      String batchID = primaryKeyPrefix + uuidV4;
      await dbClient!.transaction((batch) async {
        // var batch = txn.batch();

        await transactionRepository.update(
            TransactionModel.fromJson(saleModel.toJson()),
            dbClient: batch,
            batchID: batchID);

        await lineItemRepository.deleteLineItemsForTransaction(
            saleModel.txnId ?? "",
            dbClient: batch,
            batchID: batchID);

        await lineItemRepository.setLineDetailItemsForTransaction(
            saleModel.txnId ?? "", listItem,
            dbClient: batch, batchID: batchID);

        await txnImageRepository.deleteImagesForTransaction(
            saleModel.txnId ?? "",
            dbClient: batch,
            batchID: batchID);
        if (images != null) {
          await txnImageRepository.setImagesForTransaction(
              saleModel.txnId ?? "", images,
              dbClient: batch, batchID: batchID);
        }

        // await batch.commit(continueOnError: false, noResult: true);

        status = true;
      });
      pushPendingQueries(singleBatchId: batchID, source: "TRIGGER");
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }

  Future<bool> isBillDuplicate(String? billID) async {
    bool status = false;
    try {
      Database? dbClient = await databaseHelper.database;

      int count = 0;

      if (null != billID && "" != billID) {
        count = Sqflite.firstIntValue(await dbClient!.rawQuery(
            'SELECT COUNT(txn_id) AS total_txn FROM mk_transactions WHERE txn_type=? AND last_activity_type!=3 AND txn_ref_number_char=? limit 1',
            [TxnType.sales, billID]))!;
      }

      if (0 < count) {
        // Bill id exist
        status = true;
      } else {
        status = false;
      }
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }
}

// class SaleRepository {
//   final String tag = "SaleRepository";
//   DatabaseHelper databaseHelper = DatabaseHelper();
//   QueryRepository queryRepository = QueryRepository();

//   //get from transaction repository and return from here
//   Future<List<SaleModel>> getAllSales() async {
//     List<SaleModel> sales = [];
//     try {
//       Database dbClient = await databaseHelper.database;
//       List<Map<String, dynamic>> txnDataListJson = (await dbClient.rawQuery(
//           'SELECT * FROM mk_transactions WHERE txn_type=? AND last_activity_type!=?',
//           [TxnType.sales, LastActivityType.Delete]));
//       sales = txnDataListJson.map((txnData) {
//         return SaleModel.fromJson(txnData);
//       }).toList();
//     } catch (e) {
//       Log.e(tag, e.toString());
//     }
//     return sales;
//   }

//   Future<Tuple3<SaleModel, List<LineItemDetailModel>, List<TxnImageModel>>>
//       getSaleById(String txnID) async {
//     TransactionModel txnData = TransactionModel();
//     List<LineItemDetailModel> items = [];
//     List<TxnImageModel> images = [];
//     TransactionRepository transactionRepository = TransactionRepository();
//     TxnImageRepository txnImageRepository = TxnImageRepository();
//     LineItemRepository lineItemRepository = LineItemRepository();

//     try {
//       if (null != txnID) {
//         txnData = await transactionRepository.getTransactionByTxnId(txnID);
//         Log.d("txn data ${txnData.toJson()}");
//         items =
//             await lineItemRepository.getLineDetailItemsForTransaction(txnID);
//         images = await txnImageRepository.getImagesForTransaction(txnID);
//       } else {
//         // Throw error for null id
//       }
//     } catch (e) {
//       Log.e(tag, e.toString());
//     }
//     return Tuple3(SaleModel.fromJson(txnData.toJson()), items, images);
//   }

//   Future<String> addSale(SaleModel saleModel,
//       List<LineItemDetailModel> listItem, List<TxnImageModel> images) async {
//     String status;
//     TransactionRepository transactionRepository = TransactionRepository();
//     LineItemRepository lineItemRepository = LineItemRepository();
//     TxnImageRepository txnImageRepository = TxnImageRepository();

//     try {
//       Database dbClient = await databaseHelper.database;
//       String primaryKeyPrefix = await getPrimaryKeyPrefix();
//       String batchID = primaryKeyPrefix + uuidV4;
//       await dbClient.transaction((batch) async {
//         String txnID = await transactionRepository.insert(
//             TransactionModel.fromJson(saleModel.toJson()),
//             dbClient: batch,
//             batchID: batchID);

//         await lineItemRepository.setLineDetailItemsForTransaction(
//             txnID, listItem,
//             dbClient: batch, batchID: batchID);

//         await txnImageRepository.setImagesForTransaction(txnID, images,
//             dbClient: batch, batchID: batchID);

//         // bool isSuccess = await pushPendingQueries(
//         //     singleBatchId: batchID, source: "TRIGGER", dbClient: batch);

//         // if (!isSuccess) {
//         // throw CustomException("No Net");

//         // }
//         status = txnID;
//       });
//       pushPendingQueries(singleBatchId: batchID, source: "TRIGGER");
//     } catch (e) {
//       Log.e(tag, e.toString());
//     }
//     return status;
//   }

//   Future<bool> updateSale(SaleModel saleModel,
//       List<LineItemDetailModel> listItem, List<TxnImageModel> images) async {
//     bool status = false;
//     TransactionRepository transactionRepository = TransactionRepository();
//     LineItemRepository lineItemRepository = LineItemRepository();
//     TxnImageRepository txnImageRepository = TxnImageRepository();
//     Log.d("c ${saleModel.toJson()}");
//     try {
//       Database dbClient = await databaseHelper.database;
//       String primaryKeyPrefix = await getPrimaryKeyPrefix();
//       String batchID = primaryKeyPrefix + uuidV4;
//       await dbClient.transaction((batch) async {
//         // var batch = txn.batch();

//         await transactionRepository.update(
//             TransactionModel.fromJson(saleModel.toJson()),
//             dbClient: batch,
//             batchID: batchID);

//         await lineItemRepository.deleteLineItemsForTransaction(saleModel.txnId,
//             dbClient: batch, batchID: batchID);

//         await lineItemRepository.setLineDetailItemsForTransaction(
//             saleModel.txnId, listItem,
//             dbClient: batch, batchID: batchID);

//         await txnImageRepository.deleteImagesForTransaction(saleModel.txnId,
//             dbClient: batch, batchID: batchID);

//         await txnImageRepository.setImagesForTransaction(
//             saleModel.txnId, images,
//             dbClient: batch, batchID: batchID);

//         // bool isSuccess = await pushPendingQueries(
//         //     singleBatchId: batchID, source: "TRIGGER", dbClient: batch);
//         // if (!isSuccess) {
//         // throw CustomException("No Net");

//         // }
//         // await batch.commit(continueOnError: false, noResult: true);

//         status = true;
//       });
//       pushPendingQueries(singleBatchId: batchID, source: "TRIGGER");
//     } catch (e) {
//       Log.e(tag, e.toString());
//     }
//     return status;
//   }

//   Future<bool> isBillDuplicate(String billID) async {
//     bool status = false;
//     try {
//       Database dbClient = await databaseHelper.database;

//       int count = 0;

//       if (null != billID && "" != billID) {
//         count = Sqflite.firstIntValue(await dbClient.rawQuery(
//             'SELECT COUNT(txn_id) AS total_txn FROM mk_transactions WHERE txn_type=? AND last_activity_type!=3 AND txn_ref_number_char=? limit 1',
//             [TxnType.sales, billID]));
//       }

//       if (0 < count) {
//         // Bill id exist
//         status = true;
//       } else {
//         status = false;
//       }
//     } catch (e) {
//       Log.e(tag, e.toString());
//     }
//     return status;
//   }
// }
