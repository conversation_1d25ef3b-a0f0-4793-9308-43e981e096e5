import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import 'package:nepali_utils/nepali_utils.dart';

// ignore: must_be_immutable
class CustomDatePickerTextField extends StatelessWidget {
  final Function? onChange;
  final String? initialValue;
  final bool? readOnly;
  TextEditingController? dateCtrl;
  NepaliDateTime? minBSDate;
  NepaliDateTime? maxBSDate;
  final String? labelText;

  CustomDatePickerTextField(
      {super.key,
      this.onChange,
      this.initialValue,
      this.readOnly = false,
      this.labelText = "Date",
      this.minBSDate,
      this.maxBSDate}) {
    dateCtrl = TextEditingController(text: initialValue);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: (readOnly == true)
            ? null
            : () async {
                String selectedDateTime = await nepaliDatePicker(
                    context, dateCtrl!.text, minBSDate, maxBSDate);
                dateCtrl!.text = selectedDateTime;
                onChange!(selectedDateTime);
              },
        child: AbsorbPointer(
          child: TextFormField(
            readOnly: readOnly!,
            style: formFieldTextStyle,
            decoration: formFieldStyle.copyWith(
              suffixIcon: const Icon(
                Icons.calendar_today,
                size: 16,
              ),
              suffixIconConstraints:
                  const BoxConstraints(maxWidth: 30, minWidth: 30),
              labelText: labelText,
              enabled: !readOnly!,
            ),
            controller: dateCtrl,
          ),
        ));
  }
}
