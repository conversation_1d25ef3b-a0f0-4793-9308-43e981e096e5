import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'dart:io';
import 'package:path/path.dart';
import 'package:excel/excel.dart';

import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:path_provider/path_provider.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/model/database/ledger_model.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';

import 'package:permission_handler/permission_handler.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';
import 'dart:io';

import 'package:excel/excel.dart';

extension ExtendedIterable<E> on Iterable<E> {
  /// Like Iterable<T>.map but callback have index as second argument
  Iterable<T> mapIndex<T>(T f(E e, int i)) {
    var i = 0;
    return this.map((e) => f(e, i++));
  }

  void forEachIndex(void f(E e, int i)) {
    var i = 0;
    this.forEach((e) => f(e, i++));
  }
}

class ImportPartyController extends GetxController {
  var _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  String sheetName = "Sheet1";

  Future<String?> getDownloadPath() async {
    Directory? directory;
    try {
      if (Platform.isIOS) {
        directory = await getApplicationDocumentsDirectory();
      } else {
        directory = Directory('/storage/emulated/0/Download');
        // Put file in global download folder, if for an unknown reason it didn't exist, we fallback
        // ignore: avoid_slow_async_io
        if (!await directory.exists())
          directory = await getExternalStorageDirectory();
      }
    } catch (err, stack) {
      print("Cannot get download folder path");
    }
    return directory?.path;
  }

  Future<Tuple2<bool, String>> saveTemplate(BuildContext context) async {
    bool status = false;
    String msg = "";

    ProgressDialog progressDialog = ProgressDialog(context,
        type: ProgressDialogType.normal, isDismissible: false);
    progressDialog.update(message: "Generating Template. Please wait....");
    await progressDialog.show();

    var excel = Excel.createExcel();
    excel.rename('Sheet1', sheetName);

    Sheet sheetObject = excel[sheetName];
    [
      "Party Name",
      "Mobile No",
      "Address",
      "VAT/PAN",
      "VAT/PAN No",
      "Opening Balance",
      "Receivable/Payable"
    ].mapIndex((e, i) {
      // Log.d("alphabet id  ${String.fromCharCode(i + 65)}");
      sheetObject
          .cell(CellIndex.indexByString("${String.fromCharCode(i + 65)}1"))
          .value = e;
    }).toList();
    var granted = await Permission.storage.status;
    if (!granted.isGranted) {
      await Permission.storage.request();
    }
    try {
      Log.d("File  saaved successfully ");

      final path = await getDownloadPath();
      if (path == null) {
        return Tuple2(status, msg);
      }
      print("this is path $path");
      excel.encode();
      var fileBytes = excel.save();

      File(join("$path/MKhaata_Import_Party.xlsx"))
        ..createSync(recursive: true)
        ..writeAsBytesSync(fileBytes!);

      await progressDialog.hide();

      showAlertDialog(context,
          alertType: AlertType.Success,
          alertTitle: "Sample File",
          message:
              "A sample excel file has been saved to\nDownload/MKhaata_Import_Party.xlsx");
    } catch (e) {
      await progressDialog.hide();
      Log.d(e.toString());
      // showAlertDialog(context,
      //     alertType: AlertType.Error,
      //     alertTitle: "Sample File",
      //     message: "save fail");
    }

    return Tuple2(status, msg);
  }

  Future<Tuple2<bool, String>> importData(BuildContext context) async {
    bool status = false;
    String msg = "";

    await Permission.storage.request();
    await Permission.manageExternalStorage.request();

    // PermissionStatus permissionStatus = await Permission.storage.request();
    // debugPrint(permissionStatus.toString());

    // if(permissionStatus.isGranted){

    FilePickerResult? pickedFile = await FilePicker.platform.pickFiles(
      type: FileType.custom,
      allowedExtensions: ['xlsx', 'xls'],
      allowMultiple: false,
    );

    ProgressDialog progressDialog = ProgressDialog(context,
        type: ProgressDialogType.normal, isDismissible: false);

    /// file might be picked

    if (pickedFile != null) {
      progressDialog.update(message: "Importing Data. Please wait....");
      await progressDialog.show();

      var file = pickedFile.files.first.path;
      var bytes = File(file!).readAsBytesSync();
      var excel = Excel.decodeBytes(bytes);

      List<LedgerModel> ledgers = [];
      bool isAllValid = true;
      // var table = excel.tables[sheetName];

      for (var table in excel.tables.keys) {
        int i = 0;
        for (var row in excel.tables[table]!.rows) {
          if (i != 0) {
            String name = toBeginningOfSentenceCase(row[0]!.value.toString())!;

            String mobileNo =
                (parseDouble(row[1]!.value.toString())!.toStringAsFixed(0));

            String adddress = row[2]!.value.toString();

            final tempFlag = (row[3]!.value.toString());

            String tinFlag = tempFlag.contains('vat') ? "VAT" : "PAN";

            String tinNo = (row[4]!.value.toString());

            String openingBalance = (row[5]!.value.toString());

            String openingType = (row[6]!.value.toString().toLowerCase());

            int? openingTypeVal;
            double openingAmount = 0.0;
            // //validate and add in ledger list
            if ([null, "", " "].contains(name)) {
              isAllValid = false;
              msg = "Party Name is missing in row $i";
            }

            if (mobileNo.length > 10) {
              isAllValid = false;
              msg =
                  "Incorrect Mobile Number in row $i . It must not be of more than 10 character";
            }

            Log.d("opening account ${openingAmount} => type ${openingType}");

            if (openingBalance.length > 0) {
              openingAmount = parseDouble(openingBalance) ?? 0.00;
            }
            if (openingAmount > 0) {
              if (["receivable", "payable"].contains(openingType)) {
                openingTypeVal = ("receivable" == openingType)
                    ? TxnType.openingReceive
                    : TxnType.openingPay;
              } else {
                isAllValid = false;
                msg =
                    "Payable or Receivable is required for opening  balance of party in row $i .";
              }
            }

            Log.d("this is name $name");
            Log.d("this is mobile $mobileNo");
            Log.d("this is addr $adddress");
            Log.d("this is tin $tinNo");
            Log.d("this is tin $tinFlag");
            Log.d("this is amt $openingAmount");
            Log.d("this is type $openingTypeVal");
            Log.d("this is date $currentDate");

            // // Log.d("is alll valid ${isAllValid} for row $row");

            if (isAllValid) {
              var newParty = LedgerModel(
                ledgerTitle: name,
                mobileNo: mobileNo,
                address: adddress,
                tinNo: tinNo,
                tinFlag: tinFlag,
                openingBalance: openingAmount > 0 ? openingAmount : null,
                openingType: openingAmount > 0 ? openingTypeVal : null,
                openingDate: openingAmount > 0 ? currentDate : null,
              );
              ledgers.add(newParty);
              Log.d("party  is ${newParty.toJson()}");
            } else {
              break;
            }
          }
          i++;
        }
      }

      if (isAllValid) {
        status = true;

        status = await LedgerRepository().insertMultipleLedger(ledgers);
        //insert all ledger to database;
      }
      if (isAllValid) {
        //save in db and pushupdate
        msg = "Imported ${ledgers.length} records";
      }
    } else {
      msg = "Supported File Not Picked";
    }

    await progressDialog.hide();

    if (status) {
      showAlertDialog(context,
          alertType: AlertType.Success,
          alertTitle: "Parties Imported Successfully",
          message: msg);
    } else {
      showAlertDialog(context,
          alertType: AlertType.Error, alertTitle: "Error", message: msg);
    }

    return Tuple2(status, msg);

    // }else{
    //   return Tuple2(status, msg);
    // }
  }
}
