import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/notification_model.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/item_detail/item_detail_page.dart';
import 'package:mobile_khaata_v2/app/modules/notification_module/notification_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/party_detail/party_detail_page.dart';
import 'package:mobile_khaata_v2/database/notification_type.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:url_launcher/url_launcher.dart';

class NotificationPage extends StatelessWidget {
  final String? notificationID;
  final notificationListController = NotificationListController();
  NotificationPage({super.key, this.notificationID}) {
    notificationListController.getNotification();
  }

  Widget? getNotificationWidgetForType(String? type) {
    switch (type) {
      case 'LOCAL':
        return Icon(
          Icons.notifications,
          color: colorPrimary,
          size: 30,
        );
      case 'PAYMENT':
        return Image.asset(
          'images/purchase-small.png',
          height: 30.0,
          fit: BoxFit.cover,
        );
      case 'SERVER':
        return Image.asset(
          'images/logo-bottom.png',
          height: 30.0,
          fit: BoxFit.cover,
        );
      case 'LOW_STOCK':
        return Image.asset(
          'images/product-blue.png',
          height: 30.0,
          fit: BoxFit.cover,
        );
    }
    return null;
  }

  handleNotificationClick(NotificationModel notificationModel) async {
    if (notificationModel.notificationLinkType ==
        NotificationLinkType.EXTERNAL) {
      //handle external notification
      if (await canLaunchUrl(Uri.parse(notificationModel.linkURL ?? ""))) {
        launchUrl(Uri.parse(notificationModel.linkURL ?? ""));
      }
    } else if (notificationModel.notificationLinkType ==
        NotificationLinkType.INTERNAL) {
      Map<String, dynamic> params = notificationModel.params ?? {};

      // Log.d(
      //     "opening internal ${notificationModel.linkURL} ${notificationModel.params}");
      switch (notificationModel.linkURL) {
        //handler as per url and params here for notification click
        case '/partyLedgerDetail':
          navigatorKey.currentState!.pushNamed('/partyLedgerDetail',
              arguments: PartyDetailPage(
                ledgerId: params['ledger_id'],
              ));
          break;
        case '/itemDetail':
          navigatorKey.currentState!.pushNamed('/itemDetail',
              arguments: ItemDetailPage(
                itemId: params['id'],
              ));
          break;
        default:
          break;
      }
      //hanndle internal notification
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (notificationListController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }
      return SafeArea(
          child: Scaffold(
        appBar: AppBar(
          titleSpacing: -5.0,
          backgroundColor: colorPrimary,
          title: const Text(
            "Notifications",
            style: TextStyle(
                fontSize: 18,
                color: Colors.white,
                fontFamily: 'HelveticaRegular',
                fontWeight: FontWeight.bold),
          ),
        ),
        body: notificationListController.notifications.isEmpty
            ? const Center(
                child: Text("No any notification"),
              )
            : Container(
                padding: const EdgeInsets.only(
                  top: 5,
                ),
                color: backgroundColorShade,
                child: ListView.builder(
                  padding: const EdgeInsets.only(bottom: 40),
                  itemCount: notificationListController.notifications.length,
                  itemBuilder: (_, index) {
                    NotificationModel notificationItem =
                        notificationListController.notifications[index];
                    return Container(
                      margin:
                          const EdgeInsets.only(bottom: 0, left: 0, right: 0),
                      child: Card(
                          child: InkWell(
                        onTap: () {
                          handleNotificationClick(notificationItem);
                        },
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          child: Row(
                            children: [
                              Container(
                                child: getNotificationWidgetForType(
                                    notificationItem.notificationType),
                              ),
                              const SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      notificationItem.title ?? "",
                                      style: labelStyle2,
                                    ),
                                    if (![
                                      null,
                                      ""
                                    ].contains(notificationItem.subtitle)) ...[
                                      const SizedBox(height: 4),
                                      Text(
                                        notificationItem.subtitle ?? "",
                                        style: const TextStyle(
                                            color: Colors.black54,
                                            fontSize: 16),
                                      ),
                                    ],
                                    const SizedBox(height: 4),
                                    Text(
                                      notificationItem.dateBS ?? "",
                                      style: const TextStyle(
                                          color: Colors.black38, fontSize: 12),
                                    )
                                  ],
                                ),
                              )
                            ],
                          ),
                          // color: Colors.white,
                          // ch
                        ),
                      )),
                    );
                  },
                ),
              ),
      ));
    });
  }
}
