import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/report/party_satement_report_model.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/app/repository/report_repository.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

/**
 * PARTY STATEMENT REPORT CONTROLLER - BALANCE CALCULATION LOGIC:
 *
 * This controller manages the generation of party statement reports with proper
 * running balance calculations. The balance represents the cumulative amount
 * owed between the business and the party.
 *
 * BALANCE CALCULATION FLOW:
 * 1. Get opening balance for the start date
 * 2. Add each transaction's balance impact sequentially
 * 3. Maintain running total for accurate closing balance
 *
 * BALANCE INTERPRETATION:
 * - Positive Balance: Party owes money to the business (Receivable)
 * - Negative Balance: Business owes money to the party (Payable)
 */

class PartyStatementReportController extends GetxController {
  final String tag = "PartyStatementReportController";

  ReportRepository _reportRepository = ReportRepository();

  var _txnLoading = false.obs;
  bool get txnLoading => _txnLoading.value;

  var transactions = <PartyStatementReportModel>[].obs;

  var totalClosingBalance = 0.00.obs;

  /**
   * GENERATE PARTY REPORT METHOD:
   *
   * This method creates a complete party statement with:
   * 1. Opening balance entry
   * 2. All transactions within date range
   * 3. Running balance calculations
   *
   * @param startDate - Report start date
   * @param endDate - Report end date
   * @param types - Transaction types to include (optional)
   * @param ledgerID - Specific party/ledger ID
   */
  generatePartyReport(
      {required String startDate,
      required String endDate,
      List<int>? types,
      String? ledgerID}) async {
    _txnLoading(true);

    // Clear previous data
    transactions.clear();

    // STEP 1: GET OPENING BALANCE
    // Calculate the party's balance at the start of the report period
    double openingForDate = await LedgerRepository()
        .getOpeningBalanceForPartyForDate(startDate, ledgerID ?? "");

    // STEP 2: CREATE OPENING BALANCE ENTRY
    // This shows the starting position before any transactions in the period
    PartyStatementReportModel opnTxn = PartyStatementReportModel(
      txnDateBS: toDateBS(DateTime.parse(startDate)),
      txnDate: startDate,
    );

    // FIXED: Set Dr/Cr amounts based on opening balance type
    // ACCOUNTING PRINCIPLE: Opening balance should appear in appropriate Dr/Cr column
    if (openingForDate >= 0) {
      // Positive opening balance = Party owes us money (Asset/Receivable)
      // Show in DEBIT column as it represents money owed TO us
      opnTxn.drAmount = openingForDate.abs();
      opnTxn.crAmount = 0.00;
      opnTxn.txnType = TxnType.openingReceive;
      opnTxn.description = "Balance B/F (Receivable)";
    } else {
      // Negative opening balance = We owe party money (Liability/Payable)
      // Show in CREDIT column as it represents money owed BY us
      opnTxn.drAmount = 0.00;
      opnTxn.crAmount = openingForDate.abs();
      opnTxn.txnType = TxnType.openingPay;
      opnTxn.description = "Balance B/F (Payable)";
    }

    // BALANCE CALCULATION: Use Dr - Cr logic for opening balance
    opnTxn.txnBalanceAmount =
        (opnTxn.drAmount ?? 0.0) - (opnTxn.crAmount ?? 0.0);

    opnTxn.txnTypeText = TxnType.txnTypeText[opnTxn.txnType];
    transactions.add(opnTxn);

    // STEP 3: INITIALIZE RUNNING BALANCE
    // Start with opening balance for cumulative calculations
    totalClosingBalance.value = openingForDate;

    // STEP 4: GET TRANSACTION DATA
    // Fetch all transactions for the party within the date range
    List<Map<String, dynamic>> txnDataListJson =
        await _reportRepository.getPartyTransactions(
            startDate: startDate,
            endDate: endDate,
            types: types,
            ledgerID: ledgerID);

    // STEP 5: PROCESS EACH TRANSACTION
    // Apply each transaction's balance impact to get running total
    for (int i = 0; i < txnDataListJson.length; i++) {
      // Limit to 100 transactions for performance (can be increased if needed)
      if (i < 100) {
        // Parse transaction from JSON with proper amount calculations
        PartyStatementReportModel txn =
            PartyStatementReportModel.fromJson(txnDataListJson[i]);

        // RUNNING BALANCE CALCULATION USING DR - CR:
        // Calculate transaction impact using Dr - Cr logic
        double transactionImpact =
            (txn.drAmount ?? 0.0) - (txn.crAmount ?? 0.0);

        // Add this transaction's impact to running total
        totalClosingBalance.value += transactionImpact;

        // Store the transaction (balance will be calculated in UI)
        transactions.add(txn);
      }
    }

    // ENSURE CLOSING BALANCE IS PROPERLY CALCULATED
    // Final closing balance should reflect all transactions
    totalClosingBalance.value =
        parseDouble(totalClosingBalance.value.toStringAsFixed(2)) ?? 0.0;

    _txnLoading(false);
  }
}
