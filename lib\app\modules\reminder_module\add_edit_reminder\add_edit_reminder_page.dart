// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/modules/reminder_module/add_edit_reminder/add_edit_reminder_controller.dart';
import 'package:mobile_khaata_v2/database/reminder_period.dart';
import 'package:mobile_khaata_v2/database/reminder_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

class AddEditReminderPage extends StatelessWidget {
  final String tag = "Reminder Add/Edit Page";

  final String? reminderId;
  final String? ledgerId;
  final bool? reaOnlyFlag;
  final int? reminderType;
  final String? reminderDesc;

  final reminderController = AddEditReminderController();

  AddEditReminderPage(
      {super.key,
      this.reminderId,
      this.reaOnlyFlag,
      this.ledgerId,
      this.reminderType,
      this.reminderDesc}) {
    reminderController.reminder.reminderType =
        reminderType ?? ReminderType.task;
    reminderController.reminder.description = reminderDesc;
    reminderController.reminder.ledgerID = ledgerId;

    if (null != ledgerId) {
      reminderController.reminder.reminderPeriod = ReminderPeriod.once;
    }

    if (null != reminderId) {
      reminderController.initEdit(reminderId, reaOnlyFlag ?? true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (reminderController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
        // resizeToAvoidBottomPadding: true,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          backgroundColor: colorPrimary,
          leading: BackButton(
            onPressed: () => Navigator.pop(context, false),
          ),
          centerTitle: false,
          titleSpacing: -5.0,
          title: Text(
            (!reminderController.editFlag)
                ? "नयाँ रिमाइन्डर (New Reminder)"
                : "रिमाइन्डर (Edit Reminder)",
            style: const TextStyle(
                fontSize: 18,
                color: Colors.white,
                fontFamily: 'HelveticaRegular',
                fontWeight: FontWeight.bold),
          ),
          actions: [
            if (reminderController.editFlag) ...{
              InkWell(
                  onTap: () => reminderController.readOnlyFlag =
                      !reminderController.readOnlyFlag,
                  splashColor: colorPrimaryLighter,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 15),
                    child: (reminderController.readOnlyFlag)
                        ? Column(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(
                                Icons.mode_edit,
                                color: Colors.white,
                              ),
                              Text(
                                "Edit",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          )
                        : Column(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(
                                Icons.close,
                                color: Colors.white,
                              ),
                              Text(
                                "Cancel",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          ),
                  )),
            }
          ],
        ),
        body: GestureDetector(
          onTap: () {
            FocusScope.of(context).unfocus();
          },
          child: SizedBox(
            // decoration: BoxDecoration(color: backgroundColorShade),
            height: MediaQuery.of(context).size.height,
            child: SingleChildScrollView(
              child: Container(
                padding: const EdgeInsets.all(15),
                child: Form(
                    key: reminderController.formKey,
                    child: Column(
                      children: [
                        Column(
                          children: [
                            const SizedBox(
                              height: 10,
                            ),

                            Row(
                              children: [
                                //===========================Reminder Date
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "मिति",
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      CustomDatePickerTextField(
                                        readOnly:
                                            reminderController.readOnlyFlag,
                                        minBSDate: NepaliDateTime.parse(
                                            tomorrowDateBS),
                                        initialValue: reminderController
                                            .reminder.startDateBS,
                                        onChange: (selectedDate) {
                                          reminderController
                                                  .reminder.startDateBS =
                                              strTrim(selectedDate);
                                        },
                                      ),
                                    ],
                                  ),
                                ),

                                const SizedBox(width: 10),
                                //==========================================Reminder Period
                                if (null ==
                                    reminderController.reminder.ledgerID)
                                  Expanded(
                                    flex: 1,
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "रिमाइन्डर अवधि",
                                          style: labelStyle2,
                                        ),
                                        const SizedBox(height: 5.0),
                                        FormBuilderDropdown(
                                          name: 'reminder_period',
                                          style: formFieldTextStyle,
                                          decoration: formFieldStyle.copyWith(
                                              labelText: "Reminder Period"),
                                          items: ReminderPeriod
                                              .reminderPeriodList
                                              .map((row) {
                                            return DropdownMenuItem(
                                                value: row["value"],
                                                child: Text(row["text"]));
                                          }).toList(),
                                          validator: (value) {
                                            if (value.isBlank!) {
                                              return "Required";
                                            }
                                            return null;
                                          },
                                          initialValue: reminderController
                                              .reminder.reminderPeriod,
                                          onChanged: (value) {
                                            reminderController.reminder
                                                .reminderPeriod = value as int?;
                                          },
                                        ),
                                      ],
                                    ),
                                  ),
                              ],
                            ),

                            const SizedBox(
                              height: 20,
                            ),

                            //===============================================Reminder Type
                            // Column(
                            //   crossAxisAlignment: CrossAxisAlignment.start,
                            //   children: [
                            //     Text(
                            //       "रिमाइन्डर किसिम",
                            //       style: labelStyle2,
                            //     ),
                            //     SizedBox(height: 5.0),
                            //     FormBuilderDropdown(
                            //       attribute: 'reminder_type',
                            //       style: formFieldTextStyle,
                            //       decoration: formFieldStyle.copyWith(
                            //           labelText: "Reminder Type"),
                            //       items: ReminderType.reminderTypeList
                            //           .map((row) {
                            //         return DropdownMenuItem(
                            //             value: row["value"],
                            //             child: Text(row["text"]));
                            //       }).toList(),
                            //       validators: [
                            //         FormBuilderValidators.required(
                            //             errorText: "Required")
                            //       ],
                            //       readOnly: reminderController.readOnlyFlag,
                            //       initialValue: reminderController
                            //           .reminder.reminderType,
                            //       onChanged: (value) {
                            //         reminderController
                            //             .reminder.reminderType = value;
                            //       },
                            //     ),
                            //   ],
                            // ),

                            // const SizedBox(
                            //   height: 20,
                            // ),

                            //==========================================Reminder Desc
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "रिमाइन्डर विवरण",
                                  style: labelStyle2,
                                ),
                                const SizedBox(height: 5.0),
                                FormBuilderTextField(
                                  name: "reminder_text",
                                  autocorrect: false,
                                  textInputAction: TextInputAction.newline,
                                  keyboardType: TextInputType.multiline,
                                  style: formFieldTextStyle,
                                  decoration: formFieldStyle.copyWith(
                                      labelText: "Reminder Text"),
                                  minLines: 4,
                                  maxLines: 4,
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return "Required";
                                    }
                                    return null;
                                  },
                                  readOnly: reminderController.readOnlyFlag,
                                  initialValue:
                                      reminderController.reminder.description,
                                  onChanged: (value) {
                                    reminderController.reminder.description =
                                        strTrim(value!);
                                  },
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    )),
              ),
            ),
          ),
        ),
        bottomNavigationBar: BottomSaveCancelButton(
          shadow: false,
          enableFlag: !reminderController.readOnlyFlag,
          onSaveBtnPressedFn: (reminderController.readOnlyFlag)
              ? null
              : () async {
                  FocusScope.of(context).unfocus();
                  if (reminderController.formKey.currentState!.validate()) {
                    ProgressDialog progressDialog = ProgressDialog(context,
                        type: ProgressDialogType.normal, isDismissible: false);
                    progressDialog.update(
                        message: "Saving data. Please wait....");
                    await progressDialog.show();

                    bool status = false;
                    try {
                      if (null == reminderController.reminder.startDateBS ||
                          "" == reminderController.reminder.startDateBS) {
                        throw CustomException(
                            "मिति खाली राख्न मिल्दैन\n(Fill Date.)");
                      }

                      if (currentDateBS ==
                          reminderController.reminder.startDateBS) {
                        throw CustomException(
                            "आजको लागि रिमाइन्डर सेट गर्न सकिदैन\n(Reminder can't be scheduled for same day.)");
                      }

                      if (reminderController.reminder.reminderPeriod == null) {
                        throw CustomException(
                            "रिमाइन्डर अवधि खाली राख्न मिल्दैन\n"
                            "(Reminder Period is required.)");
                      }

                      if (!reminderController.editFlag) {
                        status = await reminderController.createReminder();
                      } else {
                        status = await reminderController.updateReminder();
                      }
                    } on CustomException catch (e) {
                      await progressDialog.hide();
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "Error",
                          message: e.toString());
                      return;
                    } catch (e) {
                      // Log.e(tag, e.toString() + trace.toString());
                    }
                    await progressDialog.hide();

                    if (status) {
                      Navigator.pop(context, true);

                      TransactionHelper.refreshPreviousPages();

                      String message = (reminderController.editFlag)
                          ? "Reminder Updated Successfully."
                          : "Reminder Created Successfully.";
                      showToastMessage(context, message: message, duration: 2);
                    } else {
                      showToastMessage(context,
                          alertType: AlertType.Error,
                          message: "Failed to process operation",
                          duration: 2);
                    }
                  }
                },
        ),
      ));
    });
  }
}
