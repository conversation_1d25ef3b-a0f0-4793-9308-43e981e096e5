import 'package:get/get.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:tuple/tuple.dart';

/**
 * PERMISSION MODULE WORKING EXPLANATION:
 *
 * The Permission Module is designed to control access to different features in the mobile app.
 * It works as a wrapper around UI components to check if a user has permission to access specific features.
 *
 * HOW IT WORKS:
 * 1. When a feature is accessed, PermissionWrapper wraps the child widget
 * 2. PermissionWrapperController is initialized with required permission string
 * 3. The controller checks if user is admin or regular user
 * 4. For admin users: Permission checking is bypassed entirely (immediate access)
 * 5. For regular users: API call is made to server to verify permissions
 * 6. Based on response, either show the feature or show error screen
 *
 * USER TYPES:
 * - Admin Users: Have unrestricted access to all features (no API calls needed)
 * - Regular Users: Must have explicit permissions granted by admin
 *
 * PERMISSION FLOW:
 * Admin User -> Skip all checks -> Show feature immediately
 * Regular User -> API call -> Check response -> Show feature or error
 */
class PermissionWrapperController extends GetxController {
  // Observable variable to track loading state
  var _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  // Error handling variables
  bool hasError = false;
  String errorMessage = "";
  String requiredPermission = "";

  /**
   * MAIN INITIALIZATION METHOD
   * This method is called when PermissionWrapper is created
   *
   * LOGIC:
   * 1. Set loading to true and reset error states
   * 2. Check if current user is admin
   * 3. If admin: Skip all permission checks (immediate access)
   * 4. If regular user: Call API to check permissions
   */
  init({String? forPermission}) {
    _isLoading(true);
    hasError = false;
    requiredPermission = forPermission ?? "";
    errorMessage = "";

    // Log.d("isAdmin $isAdmin");

    if (isAdmin) {
      // ADMIN USER LOGIC:
      // Admin users get immediate access without any permission validation
      // This provides seamless experience for administrators
      _isLoading(false);
    } else {
      // REGULAR USER LOGIC:
      // Regular users must go through permission checking process
      // This involves API call to server to verify permissions
      checkForPermission(forPermission: requiredPermission);
    }

    // OLD CODE (COMMENTED OUT):
    // Previous implementation had inconsistent logic for admin users
    // It would skip checks for single-user mode but still check for multi-user mode
    /*
    if (isAdmin) {
      //user is admin
      if (isMultiUser == 1) {
        //check from internet
        checkForPermission(forAdmin: true, forPermission: requiredPermission);
        // _isLoading(false);
      } else {
        //  give access
        _isLoading(false);
      }
    } else {
      //check from interet
      checkForPermission(forPermission: requiredPermission);
    }
    */
  }

  /**
   * PERMISSION CHECK FOR SPECIFIC ACTIONS
   * This method is used for one-time permission checks without UI loading
   * Returns a tuple with status and message
   */
  Future<Tuple2<bool, String>> requestforPermissionOnly(
      {String? forPermission}) async {
    bool status = true;
    String message = "";

    // Refresh global variables to get latest user state
    await refreshGlobalVariables();
    await refreshMultiUser();

    // print("this is isadmin $isAdmin  ismulti $isMultiUser"); yaha multiuser ==0 true xa

    if (isAdmin) {
      // ADMIN USERS: Always granted permission
      status = true;
    } else {
      // REGULAR USERS: Check permission via API
      try {
        ApiBaseHelper apiBaseHelper = ApiBaseHelper();
        ApiResponse apiResponse = await apiBaseHelper.get(
            apiBaseHelper.CHECK_FOR_PERMISSION + forPermission!,
            accessToken: true);
        // Log.d("API RESPONSE FOR CHECKING $forPermission");
        // Log.d(apiResponse.data);

        if (apiResponse.status) {
          bool hasPermission = apiResponse.data['has_permission'];
          bool ismulti = apiResponse.data['multi_user_flag'].toString() == "1";
          bool isexpired = apiResponse.data['is_account_expired'];
          bool isactive = apiResponse.data['is_user_active'].toString() == "1";

          if (!hasPermission) {
            status = false;
            message = "You  dont have permission for  this action";
          }
        } else {
          //network error
          status = false;
          message = apiResponse.msg ?? "";
        }
      } catch (e) {
        // Log.d("error${e.toString()}")
        status = false;
        message = FAILED_OPERATION_ERROR.toString();
      }
    }
    return Tuple2(status, message);
  }

  /**
   * COMPREHENSIVE PERMISSION CHECK
   * This method performs detailed permission validation including:
   * - Permission status
   * - Multi-user mode status
   * - Account expiry status
   * - User active status
   */
  Future<Tuple2<bool, String>> requestForPermissionCheck(
      {String? forPermission}) async {
    bool status = true;
    String message = "";
    // return Tuple2(status, message);

    // OLD LOGIC (COMMENTED OUT):
    // Previous implementation only checked for single-user admin
    /*
    if (isAdmin && isMultiUser == 0) {
      status = true;
    } else {
    */

    // NEW LOGIC: All admin users skip permission checks
    if (isAdmin) {
      status = true;
    } else {
      try {
        // API CALL TO CHECK PERMISSIONS
        ApiBaseHelper apiBaseHelper = ApiBaseHelper();
        ApiResponse apiResponse = await apiBaseHelper.get(
            apiBaseHelper.CHECK_FOR_PERMISSION + forPermission!,
            accessToken: true);
        // Log.d("API RESPONSE FOR CHECKING $forPermission");
        // Log.d(apiResponse.data);

        if (apiResponse.status) {
          // EXTRACT PERMISSION DATA FROM API RESPONSE
          bool hasPermission = apiResponse.data['has_permission'];
          bool ismulti = apiResponse.data['multi_user_flag'].toString() == "1";
          bool isexpired = apiResponse.data['is_account_expired'];
          bool isactive = apiResponse.data['is_user_active'].toString() == "1";

          // CHECK MULTI-USER MODE (FOR REGULAR USERS ONLY)
          if (!isAdmin) {
            if (!ismulti) {
              status = false;
              message =
                  "Multi User mode is turned off by Admin. Please contact your admin to turn on multi user mode for further operations.";
            }
          }

          // CHECK SPECIFIC PERMISSION
          if (!hasPermission) {
            status = false;
            message = "You  dont have permission for  this action";
          }

          // CHECK ACCOUNT EXPIRY
          if (isexpired) {
            await setExpiredUser();
            status = false;
            message = "Your account is expired";
          }

          // CHECK USER ACTIVE STATUS
          if (!isactive) {
            await setActiveUser();
            status = false;
            message = "Your account is inactive";
          }

          // is expired, is active
        } else {
          //network error
          status = false;
          message = apiResponse.msg ?? "";
        }
      } catch (e) {
        // Log.d("error${e.toString()}");
        status = false;
        message = FAILED_OPERATION_ERROR.toString();
      }
    }
    return Tuple2(status, message);
  }

  /**
   * PERMISSION CHECK HANDLER
   * This method calls the comprehensive permission check and updates UI state
   *
   * @param forAdmin - Legacy parameter (no longer used)
   * @param forPermission - The permission string to check
   */
  checkForPermission({bool forAdmin = false, String? forPermission}) async {
    // Call comprehensive permission check
    Tuple2<bool, String> checkingResp =
        await requestForPermissionCheck(forPermission: forPermission);

    // Update UI state based on response
    hasError = !checkingResp.item1;
    errorMessage = checkingResp.item2;
    _isLoading(false);
  }
}
