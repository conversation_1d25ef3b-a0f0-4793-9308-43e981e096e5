import 'dart:convert';

import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class ItemTransactionModel {
  ItemTransactionModel({
    this.txnId,
    this.itemId,
    this.quantity,
    this.pricePerUnit,
    this.totalAmount,
    this.txnType,
    this.txnTypeText,
    this.txnDate,
    this.txnDateBS,
    this.unitId,
    this.unitShortName,
  });

  String? txnId;
  String? itemId;
  double? quantity;
  double? pricePerUnit;
  double? totalAmount;
  int? txnType;
  String? txnTypeText;
  String? txnDate;
  String? txnDateBS;
  String? unitId;
  String? unitShortName;

  factory ItemTransactionModel.fromJson(Map<String, dynamic> json) {
    DateTime txnDateTime = DateTime.parse(json["txn_date"]);
    String txnDate = DateFormat('y-MM-dd').format(txnDateTime);
    String txnDateBS = toDateBS(txnDateTime);

    return ItemTransactionModel(
      txnId: json["txn_id"],
      itemId: json["item_id"],
      txnDate: txnDate,
      txnDateBS: txnDateBS,
      txnType: json["txn_type"],
      txnTypeText: TxnType.txnTypeText[json["txn_type"]],
      quantity: parseDouble(json["quantity"]),
      pricePerUnit: parseDouble(json["unit_price"]),
      totalAmount: parseDouble(json["total_amount"]),
      unitId: json["unit_id"],
      unitShortName: json["unit_short_name"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
        "txn_id": txnId,
        "item_id": itemId,
        "txn_date": txnDate,
        "txn_type": txnType,
        "txn_type_text": txnTypeText,
        "quantity": quantity,
        "unit_price": pricePerUnit,
        "total_amount": totalAmount,
        "unit_id": unitId,
        "unit_short_name": unitShortName,
      };
  @override
  String toString() {
    return jsonEncode(toJson());
  }
}
