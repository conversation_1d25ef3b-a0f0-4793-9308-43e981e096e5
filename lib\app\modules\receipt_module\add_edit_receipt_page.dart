// ignore_for_file: use_build_context_synchronously

import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_image_picker/form_builder_image_picker.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/components/payment_mode_selector.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/modules/receipt_module/add_edit_receiipt_controller.dart';
import 'package:mobile_khaata_v2/app/modules/receipt_module/detailed_receipt/detailed_receipt_screen.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

// ignore: must_be_immutable
class AddEditReceiptPage extends StatelessWidget {
  final String tag = "AddEditReceiptPage";

  final String? ledgerId;
  final String? txnId;

  final addEditReceiptController = AddEditReceiptController();

  AddEditReceiptPage({super.key, this.ledgerId, this.txnId}) {
    if (null != txnId) {
      addEditReceiptController.initEdit(txnId ?? "");
    } else {
      addEditReceiptController.initNew(ledgerId ?? "");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (addEditReceiptController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }
      return SafeArea(
          child: Scaffold(
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          backgroundColor: colorGreenLight,
          leading: BackButton(
            onPressed: () => Navigator.pop(context),
          ),
          centerTitle: false,
          titleSpacing: -5.0,
          title: InkWell(
            onTap: () async {
              if (addEditReceiptController.editFlag) {
                Tuple2<bool, String> deleteResp =
                    await TransactionRepository().delete(txnId ?? "");
                if (deleteResp.item1) {
                  showAlertDialog(context,
                      alertType: AlertType.Success,
                      alertTitle: "",
                      message: deleteResp.item2);
                } else {
                  showAlertDialog(context,
                      alertType: AlertType.Error,
                      alertTitle: "",
                      message: deleteResp.item2);
                }
              }
            },
            child: Text(
              (!addEditReceiptController.editFlag)
                  ? "प्राप्त (Received) - Credit Party"
                  : "प्राप्त (Received) - Credit Party",
              style: const TextStyle(
                  fontSize: 20,
                  color: Colors.white,
                  fontFamily: 'HelveticaRegular',
                  fontWeight: FontWeight.bold),
            ),
          ),
        ),

        body: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: SingleChildScrollView(
            child: Container(
              decoration: BoxDecoration(
                color: backgroundColorShade,
              ),
              child: Column(
                children: [
                  Card(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      width: double.infinity,
                      child: Column(
                        children: [
                          SizedBox(
                            width: double.infinity,
                            child: Text(
                              "${addEditReceiptController.ledger.ledgerTitle}",
                              textAlign: TextAlign.left,
                              style: const TextStyle(
                                  fontSize: 18, fontWeight: FontWeight.bold),
                            ),
                          ),
                          const Divider(
                            thickness: 0.5,
                          ),
                          if (null !=
                              addEditReceiptController.ledger.mobileNo) ...{
                            Row(
                              children: [
                                const Icon(
                                  Icons.call,
                                  size: 14,
                                  color: Colors.black54,
                                ),
                                Text(
                                  " ${addEditReceiptController.ledger.mobileNo}",
                                  textAlign: TextAlign.left,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                          },
                          if (null !=
                              addEditReceiptController.ledger.address) ...{
                            Row(
                              children: [
                                Text(
                                  "Address: ${addEditReceiptController.ledger.address}",
                                  textAlign: TextAlign.left,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 5,
                            ),
                          },
                          if (null !=
                              addEditReceiptController.ledger.tinNo) ...{
                            Row(
                              children: [
                                Text(
                                  "PAN/VAT: ${addEditReceiptController.ledger.tinNo}",
                                  textAlign: TextAlign.left,
                                  style: const TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black54,
                                  ),
                                ),
                              ],
                            ),
                          },
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              //=================================Balance Amount
                              Expanded(
                                child: _balanceAmountWidget(
                                    addEditReceiptController.ledger),
                              ),

                              //=======================================Profile Image Ledger
                              ClipRRect(
                                borderRadius:
                                    const BorderRadius.all(Radius.circular(10)),
                                child: Container(
                                  width: 80,
                                  height: 80,
                                  decoration: BoxDecoration(
                                      color: colorPrimaryLightest),
                                  child: (null ==
                                          addEditReceiptController
                                              .image.imageBitmap)
                                      ? const Icon(
                                          Icons.person,
                                          color: Colors.white,
                                          size: 60,
                                        )
                                      :
                                      // SizedBox(),
                                      Image(
                                          image: MemoryImage(Uint8List.fromList(
                                              addEditReceiptController
                                                  .image.imageBitmap!)),
                                          fit: BoxFit.cover,
                                        ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                  Card(
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      child: Form(
                        key: addEditReceiptController.formKey,
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                //===========================Txn No.
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "भौचर न. ",
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      TextFormField(
                                        // attribute: "txn_no",
                                        readOnly: addEditReceiptController
                                            .readOnlyFlag,
                                        autocorrect: false,
                                        keyboardType: TextInputType.number,
                                        textInputAction: TextInputAction.done,
                                        textAlign: TextAlign.right,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Voucher No"),
                                        initialValue: addEditReceiptController
                                            .transaction.txnRefNumberChar,
                                        onChanged: (value) {
                                          addEditReceiptController.transaction
                                              .txnRefNumberChar = value;
                                        },
                                      ),
                                    ],
                                  ),
                                ),

                                const SizedBox(
                                  width: 20,
                                ),

                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "मिति",
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      CustomDatePickerTextField(
                                        readOnly: addEditReceiptController
                                            .readOnlyFlag,
                                        initialValue: addEditReceiptController
                                            .transaction.txnDateBS,
                                        maxBSDate: NepaliDateTime.now(),
                                        onChange: (selectedDate) {
                                          addEditReceiptController.transaction
                                              .txnDateBS = selectedDate;
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 20.0),
                            Row(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "प्राप्त रकम",
                                          style: labelStyle2,
                                        ),
                                        const SizedBox(height: 5.0),
                                        PaymentModeSelector(
                                            onChangedFn: (v) {
                                              debugPrint("=============");
                                              debugPrint(v.toString());
                                              addEditReceiptController
                                                  .transaction
                                                  .txnPaymentTypeId = v;
                                              addEditReceiptController
                                                  .transaction
                                                  .txnPaymentReference = null;
                                              addEditReceiptController
                                                  .transaction
                                                  .chequeIssueDateBS = null;
                                              addEditReceiptController
                                                  .paymentRefCtrl
                                                  .clear();
                                              addEditReceiptController
                                                  .refreshTransaction();
                                            },
                                            paymentModeID:
                                                addEditReceiptController
                                                    .transaction
                                                    .txnPaymentTypeId,
                                            enableFlag:
                                                (addEditReceiptController
                                                    .readOnlyFlag))
                                      ],
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 10,
                                  ),
                                  Expanded(
                                    child: FormBuilderTextField(
                                        name: "receivable_amount",
                                        autocorrect: false,
                                        readOnly: addEditReceiptController
                                            .readOnlyFlag,
                                        keyboardType: const TextInputType
                                            .numberWithOptions(decimal: true),
                                        textInputAction: TextInputAction.done,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Received Amount"),
                                        textAlign: TextAlign.end,
                                        initialValue: addEditReceiptController
                                            .transaction.txnCashAmount
                                            ?.toString(),
                                        onChanged: (value) {
                                          addEditReceiptController
                                                  .transaction.txnCashAmount =
                                              parseDouble(value);

                                          addEditReceiptController
                                              .remainingBalanceAmount
                                              .text = (addEditReceiptController
                                                      .ledger.balanceAmount! -
                                                  addEditReceiptController
                                                      .transaction
                                                      .txnCashAmount!)
                                              .toStringAsFixed(2);
                                        }),
                                  )
                                ]),
                            ...(addEditReceiptController
                                        .transaction.txnPaymentTypeId ==
                                    PAYMENT_MODE_CHEQUE_ID)
                                ? [
                                    const SizedBox(
                                      height: 25,
                                    ),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text("चेक/भौचर न.", style: labelStyle2),
                                        const SizedBox(
                                          height: 10,
                                        ),
                                        TextField(
                                            autocorrect: false,
                                            readOnly: addEditReceiptController
                                                .readOnlyFlag,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                labelText:
                                                    "Cheque/Voucher No."),
                                            controller: addEditReceiptController
                                                .paymentRefCtrl,
                                            onChanged: (v) {
                                              addEditReceiptController
                                                  .transaction
                                                  .txnPaymentReference = v;
                                            }),
                                      ],
                                    ),
                                  ]
                                : [],
                            if (addEditReceiptController
                                    .transaction.txnPaymentTypeId ==
                                PAYMENT_MODE_CHEQUE_ID) ...[
                              const SizedBox(
                                height: 25,
                              ),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text("चेक मिति", style: labelStyle2),
                                  const SizedBox(
                                    height: 10,
                                  ),
                                  CustomDatePickerTextField(
                                    labelText: "Cheque Date",
                                    readOnly:
                                        addEditReceiptController.readOnlyFlag,
                                    // maxBSDate: NepaliDateTime.now(),
                                    initialValue: addEditReceiptController
                                        .transaction.chequeIssueDateBS,
                                    onChange: (selectedDate) {
                                      addEditReceiptController.transaction
                                          .chequeIssueDateBS = selectedDate;
                                    },
                                  ),
                                ],
                              )
                            ],
                            const SizedBox(height: 20.0),
                          ],
                        ),
                      ),
                    ),
                  ),

                  //===============================================Description
                  Card(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Expanded(
                            flex: 5,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "कैफियत",
                                  style: labelStyle2,
                                ),
                                const SizedBox(height: 5.0),
                                FormBuilderTextField(
                                  name: "description",
                                  readOnly:
                                      addEditReceiptController.readOnlyFlag,
                                  autocorrect: false,
                                  textAlign: TextAlign.start,
                                  textInputAction: TextInputAction.newline,
                                  style: formFieldTextStyle,
                                  decoration: formFieldStyle.copyWith(
                                      labelText: "Remarks"),
                                  minLines: 4,
                                  maxLines: 4,
                                  initialValue: addEditReceiptController
                                      .transaction.txnDescription,
                                  onChanged: (value) {
                                    addEditReceiptController
                                        .transaction.txnDescription = value;
                                  },
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(
                            width: 20,
                          ),
                          Expanded(
                            flex: 1,
                            child: FormBuilderImagePicker(
                                maxHeight: 50,
                                maxWidth: 50,
                                name: "image_picker",
                                // readOnly:
                                //     addEditReceiptController.readOnlyFlag,
                                decoration: const InputDecoration(
                                  border: InputBorder.none,
                                ),
                                maxImages: 1,
                                iconColor: colorPrimaryLight,
                                // validators: [],
                                initialValue: addEditReceiptController
                                            .txnImageFile ==
                                        null
                                    ? []
                                    : [addEditReceiptController.txnImageFile],
                                onChanged: (value) => addEditReceiptController
                                    .imagePickerOnChangeHandler(value)),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),

        //=================================================Save button
        bottomNavigationBar: BottomSaveCancelButton(
          shadow: false,
          hasDelete: (addEditReceiptController.editFlag &&
                  !addEditReceiptController.readOnlyFlag)
              ? true
              : false,
          onDeleteBtnPressedFn: () async {
            showAlertDialog(context,
                okText: "YES",
                hasCancel: true,
                cancelText: "NO",
                alertType: AlertType.Error,
                alertTitle: "Confirm Delete", onCloseButtonPressed: () async {
              // Navigator.of(_).pop();
              ProgressDialog progressDialog = ProgressDialog(context,
                  type: ProgressDialogType.normal, isDismissible: false);
              progressDialog.update(
                  message: "Checking Permission. Please wait....");
              await progressDialog.show();
              Tuple2<bool, String> checkResp =
                  await PermissionWrapperController().requestForPermissionCheck(
                      forPermission: PermissionManager.receiptDelete);
              if (checkResp.item1) {
                //has  permission
                progressDialog.update(
                    message: "Deleting Data. Please wait....");
                Tuple2<bool, String> deleteResp =
                    await TransactionRepository().delete(txnId ?? "");
                await progressDialog.hide();
                if (deleteResp.item1) {
                  //  data deleted
                  TransactionHelper.refreshPreviousPages();
                  showAlertDialog(context,
                      barrierDismissible: false,
                      alertType: AlertType.Success,
                      alertTitle: "", onCloseButtonPressed: () {
                    // Navigator.of(_).pop();
                    Navigator.of(context).pop();
                  }, message: deleteResp.item2);
                } else {
                  //cannot  delete  data
                  showAlertDialog(context,
                      alertType: AlertType.Error,
                      alertTitle: "",
                      message: deleteResp.item2);
                }
              } else {
                await progressDialog.hide();
                showAlertDialog(context,
                    alertType: AlertType.Error,
                    alertTitle: "",
                    message: checkResp.item2);
              }
            },
                message:
                    "Are you sure you  want to  delete this receipt record?");
          },
          enableFlag: !addEditReceiptController.readOnlyFlag,
          onSaveBtnPressedFn: (addEditReceiptController.readOnlyFlag)
              ? null
              : () async {
                  FocusScope.of(context).unfocus();
                  if (addEditReceiptController.formKey.currentState!
                      .validate()) {
                    ProgressDialog progressDialog = ProgressDialog(context,
                        type: ProgressDialogType.normal, isDismissible: false);
                    progressDialog.update(
                        message: "Saving data. Please wait....");
                    await progressDialog.show();

                    bool status = false;
                    try {
                      if (null ==
                              addEditReceiptController.transaction.txnDateBS ||
                          addEditReceiptController
                              .transaction.txnDateBS!.isEmpty) {
                        throw CustomException(
                            "मिति खाली राख्न मिल्दैन\n(Fill Date.)");
                      }

                      if (null ==
                              addEditReceiptController
                                  .transaction.txnCashAmount ||
                          0 >=
                              addEditReceiptController
                                  .transaction.txnCashAmount!) {
                        throw CustomException(
                            "प्राप्त रकम खाली वा जिरो राख्न मिल्दैन\n(Fill Received Amount.)");
                      }

                      if (addEditReceiptController
                                  .transaction.txnPaymentTypeId ==
                              PAYMENT_MODE_CHEQUE_ID &&
                          (null ==
                                  addEditReceiptController
                                      .transaction.chequeIssueDateBS ||
                              "" ==
                                  addEditReceiptController
                                      .transaction.chequeIssueDateBS)) {
                        throw CustomException(
                            "चेक मिति खाली राख्न मिल्दैन | \nPlease fill the cheque date");
                      }

                      if (addEditReceiptController
                                  .transaction.txnPaymentTypeId ==
                              PAYMENT_MODE_CHEQUE_ID &&
                          (null ==
                                  addEditReceiptController
                                      .transaction.txnPaymentReference ||
                              "" ==
                                  addEditReceiptController
                                      .transaction.txnPaymentReference)) {
                        throw CustomException(
                            "चेक/भौचर न. खाली राख्न मिल्दैन | \nPlease fill the cheque/voucher no.");
                      }

                      bool isLargeFile =
                          addEditReceiptController.checkIfLargeImage();
                      if (isLargeFile) {
                        throw CustomException(MAX_IMAGE_SIZE_MESSAGE);
                      }

                      if (!addEditReceiptController.editFlag) {
                        status =
                            await addEditReceiptController.createTransaction();
                      } else {
                        status =
                            await addEditReceiptController.updateTransaction();
                      }
                    } on CustomException catch (e) {
                      await progressDialog.hide();
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "Error",
                          message: e.toString());
                      return;
                    } catch (e) {
                      // Log.e(tag, e.toString() + trace.toString());
                    }
                    await progressDialog.hide();

                    if (status) {
                      Navigator.pop(context, true);
                      if (addEditReceiptController.editFlag) {
                        Navigator.of(context)
                            .pushReplacementNamed('/detailcreditReceive',
                                arguments: DetailReceiptPage(
                                  txnId: txnId,
                                  ledgerId: ledgerId,
                                ));
                      }

                      TransactionHelper.refreshPreviousPages();

                      String message = (addEditReceiptController.editFlag)
                          ? "Payment updated successfully."
                          : "Payment created successfully.";
                      showToastMessage(context, message: message, duration: 2);
                    } else {
                      showToastMessage(context,
                          alertType: AlertType.Error,
                          message: "Failed to process operation",
                          duration: 2);
                    }
                  }
                },
        ),
      ));
    });
  }

  Widget _balanceAmountWidget(LedgerDetailModel selectedLedger) {
    bool isReceivable = (selectedLedger.balanceAmount! >= 0) ? true : false;

    return Row(
      children: [
        Transform.rotate(
          angle: (isReceivable) ? (3.14 / 1.3) : (-3.14 / 4),
          alignment: Alignment.center,
          child: CircleAvatar(
            radius: 10,
            backgroundColor: (isReceivable) ? colorGreenDark : colorRedLight,
            child: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
              size: 15,
            ),
          ),
        ),
        const SizedBox(
          width: 10,
        ),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                (isReceivable)
                    ? "लिनुपर्ने (Receivable)"
                    : "तिर्नुपर्ने (Payable)",
                style: const TextStyle(color: Colors.black54, fontSize: 12),
              ),
              Text(
                formatCurrencyAmount(selectedLedger.balanceAmount!.abs()),
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 14,
                  color: (isReceivable) ? colorGreenDark : colorRedLight,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
