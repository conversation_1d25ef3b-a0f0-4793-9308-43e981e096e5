import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/unit_list_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/txn_image_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/purchase_return_model.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/app/repository/purchase_return_repository.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';

import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tuple/tuple.dart';

class AddEditPurchaseReturnController extends GetxController {
  final String tag = "AddEditPurchaseReturnController";

  var _isLoading = true.obs;
  var _editFlag = false.obs;
  var _readOnlyFlag = false.obs;
  var _isVatEnabled = false.obs;
  var _isReceived = false.obs;
  var _iscashPurchaseReturnSelected = false.obs;

  bool get iscashPurchaseReturnSelected => _iscashPurchaseReturnSelected.value;
  bool get isLoading => _isLoading.value;
  bool get editFlag => _editFlag.value;
  bool get readOnlyFlag => _readOnlyFlag.value;

  bool get isVatEnabled => _isVatEnabled.value;
  bool get isReceived => _isReceived.value;

  set setiscashPurchaseReturnSelected(bool flag) {
    _iscashPurchaseReturnSelected.value = flag;
    _iscashPurchaseReturnSelected.refresh();
  }

  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  set setIsReceived(bool flag) {
    _isReceived.value = flag;
  }

  // for edit case, to check if same bill no is used in update case, not to give
  // duplicate error;
  String? previousBillNo;

  PurchaseReturnRepository purchaseReturnRepository =
      new PurchaseReturnRepository();

  LedgerRepository _ledgerRepository = new LedgerRepository();

  var transaction = PurchaseReturnModal(
          txnDateBS: currentDateBS,
          txnSubTotalAmount: 0,
          txnPaymentTypeId: PAYMENT_MODE_CASH_ID)
      .obs;

  var items = <LineItemDetailModel>[].obs;

  var images = <TxnImageModel>[].obs;

  var files = <File>[].obs;

  final formKey = GlobalKey<FormState>();

  final TextEditingController billNoCtrl = TextEditingController();

  final TextEditingController partyNameCtrl = TextEditingController();
  final TextEditingController mobileCtrl = TextEditingController();
  final TextEditingController addressCtrl = TextEditingController();
  final TextEditingController panNoCtrl = TextEditingController();

  final TextEditingController displayTextCtrl = TextEditingController();
  final TextEditingController subTotalAmountCtrl = TextEditingController();
  final TextEditingController discountPercentageCtrl = TextEditingController();
  final TextEditingController discountAmountCtrl = TextEditingController();
  final TextEditingController vatAmountCtrl = TextEditingController();
  final TextEditingController vatPercentCtrl = TextEditingController();

  final TextEditingController totalAmountCtrl = TextEditingController();
  final TextEditingController receivedAmountCtrl = TextEditingController();
  final TextEditingController dueAmountCtrl = TextEditingController();
  final TextEditingController descCtrl = TextEditingController();
  final TextEditingController paymentRefCtrl = TextEditingController();

  UnitListController unitListController = Get.put(UnitListController());

  var selectedLedger = LedgerDetailModel().obs;

  @override
  void onInit() async {
    // clear all cache images of bill
    imageCache.clear();
    super.onInit();
  }

  initialize() {
    _isLoading(false);
  }

  @override
  void dispose() {
    discountPercentageCtrl.dispose();
    billNoCtrl.dispose();
    partyNameCtrl.dispose();
    vatAmountCtrl.dispose();
    totalAmountCtrl.dispose();
    receivedAmountCtrl.dispose();
    dueAmountCtrl.dispose();
    descCtrl.dispose();
    mobileCtrl.dispose();
    addressCtrl.dispose();
    panNoCtrl.dispose();
    displayTextCtrl.dispose();
    subTotalAmountCtrl.dispose();
    discountAmountCtrl.dispose();
    vatPercentCtrl.dispose();
    paymentRefCtrl.dispose();
    super.dispose();
  }

  addTotalAmt() {
    subTotalAmountCtrl.text =
        (transaction.value.txnTotalAmount ?? 0.00).toStringAsFixed(2);
  }

  recalculateForItems() {
    double itemSubTotal = 0.00;
    items.forEach((LineItemDetailModel li) {
      itemSubTotal = parseDouble(
              (itemSubTotal + (li.totalAmount ?? 0.0)).toStringAsFixed(2)) ??
          0.00;
    });
    onSubTotalIndividualChange(itemSubTotal.toString());
  }

  onvatAmountChange(String value, {String? editorTag}) {
    double vatAmt = (transaction.value.txnTaxAmount ?? 0.00);
    double taxableAmt = (transaction.value.txnTaxableTotalAmount ?? 0.00);

    double totalAmt = vatAmt + taxableAmt;
    transaction.value.txnTotalAmount = totalAmt;
    totalAmountCtrl.text = totalAmt.toStringAsFixed(2);
    transaction.value.txnBalanceAmount = totalAmt;
    dueAmountCtrl.text = totalAmt.toStringAsFixed(2);
  }

  changeReceivedAmount(String value, {String? editorTag}) {
    transaction.value.txnCashAmount = parseDouble(value);
    transaction.value.txnBalanceAmount = parseDouble(
        (transaction.value.txnTotalAmount ??
                0.00 - (transaction.value.txnCashAmount ?? 0.00))
            .toStringAsFixed(2));
    assignTransactionToTextFields(editorTAG: editorTag);
  }

  onSubTotalIndividualChange(String val, {String? editorTag}) {
    transaction.value.txnSubTotalAmount = (parseDouble(val) ?? 0.00);
    transaction.refresh();
    totalAmountCtrl.text = (parseDouble(val) ?? 0.00).toStringAsFixed(2);
    updateDiscountPercentage(transaction.value.txnDiscountPercent.toString(),
        editorTag: editorTag);
  }

  onToggleVat(bool flag) {
    _isVatEnabled.value = flag;
    _isVatEnabled.refresh();
    double VAT = flag ? VAT_PERCENTAGE : 0.00;

    transaction.value.txnTaxPercent = VAT;
    onvatPercentChange(VAT);
  }

  onvatPercentChange(double value) {
    double vatPercent = value;
    double taxableAmt = (transaction.value.txnSubTotalAmount ?? 0.00) -
        (transaction.value.txnDiscountAmount ?? 0.00);
    double vatAmt = vatPercent * 0.01 * taxableAmt;
    transaction.value.txnTaxAmount = vatAmt;
    transaction.value.txnTaxPercent = vatPercent;
    vatPercentCtrl.text = value.toStringAsFixed(2);
    vatAmountCtrl.text = vatAmt.toStringAsFixed(2);
  }

  onChangeParty(LedgerDetailModel party) {
    if (party.ledgerId != null) {
      transaction.value.ledgerId = party.ledgerId;
      partyNameCtrl.text = party.ledgerTitle ?? "";
      mobileCtrl.text = party.mobileNo ?? "";
      addressCtrl.text = party.address ?? "";
      panNoCtrl.text = party.tinNo ?? "";
      if (party.ledgerId == CASH_PURCHASE_LEDGER_ID) {
        displayTextCtrl.text = (transaction.value.txnDisplayName == "" ||
                transaction.value.txnDisplayName == null)
            ? party.ledgerTitle!
            : transaction.value.txnDisplayName!;
        transaction.value.txnDisplayName = displayTextCtrl.text;
        _iscashPurchaseReturnSelected.value = true;
        _iscashPurchaseReturnSelected.refresh();
        transaction.value.txnCashAmount =
            transaction.value.txnTotalAmount ?? 0.0;
        transaction.value.txnBalanceAmount = 0.00;
      }
    } else {
      print("arko  wala ");

      transaction.value.ledgerId = null;
      transaction.value.txnCashAmount = 0.00;
      transaction.value.txnBalanceAmount = transaction.value.txnTotalAmount;
      transaction.value.txnDisplayName = displayTextCtrl.text = "";
      partyNameCtrl.text = "";
      mobileCtrl.text = "";
      addressCtrl.text = "";
      panNoCtrl.text = "";
    }
    selectedLedger.value = party;
    selectedLedger.refresh();
    transaction.refresh();

    if ((transaction.value.txnSubTotalAmount ?? 0.0) > 0.00) {
      assignTransactionToTextFields();
    }
  }

  assignTransactionToTextFields({String? editorTAG}) {
    // Log.d("assignning to text ${transaction.value.toJson()}");

    // // formKey.currentState.
    billNoCtrl.text = transaction.value.txnRefNumberChar ?? "";
    totalAmountCtrl.text = (transaction.value.txnTotalAmount ?? 0.0).toString();
    dueAmountCtrl.text = (transaction.value.txnBalanceAmount ?? 0.0).toString();
    displayTextCtrl.text = transaction.value.txnDisplayName ?? "";
    paymentRefCtrl.text = transaction.value.txnPaymentReference ?? "";

    descCtrl.text = transaction.value.txnDescription ?? "";

    if (editorTAG != 'txn_subtotal')
      subTotalAmountCtrl.text = transaction.value.txnSubTotalAmount != 0.0
          ? transaction.value.txnSubTotalAmount.toString()
          : "";

    if (editorTAG != 'txn_discount_percent')
      discountPercentageCtrl.text = transaction.value.txnDiscountPercent != 0.0
          ? transaction.value.txnDiscountPercent.toString()
          : "";

    if (editorTAG != 'txn_discount_amount')
      discountAmountCtrl.text = transaction.value.txnDiscountAmount != 0.0
          ? transaction.value.txnDiscountAmount!.toStringAsFixed(2)
          : "";

    if (editorTAG != 'txn_tax_percent')
      vatPercentCtrl.text = transaction.value.txnTaxPercent != 0.0
          ? transaction.value.txnTaxPercent.toString()
          : "";

    if (editorTAG != 'txn_tax_amount')
      vatAmountCtrl.text =
          (transaction.value.txnTaxAmount?.abs() ?? 0.0).toString();

    if (editorTAG != 'txn_cash_amount') {
      receivedAmountCtrl.text = transaction.value.txnCashAmount != 0.0
          ? transaction.value.txnCashAmount.toString()
          : "";
    }
  }

  updateDiscountPercentage(String dis, {String? editorTag}) {
    transaction.value.txnDiscountPercent = (parseDouble(dis) ?? 0.00);
    print("this is vat ${transaction.value.txnTaxAmount} ");
    print("this is dis percent ${transaction.value.txnDiscountPercent} ");
    double amt = parseDouble(((parseDouble(dis) ?? 0.00) *
                0.01 *
                (transaction.value.txnSubTotalAmount ?? 0.0))
            .toStringAsFixed(2)) ??
        0.00;
    print("this is dis amt ${amt} ");

    double taxableAmt = (transaction.value.txnSubTotalAmount ?? 0.0) - amt;
    print("this is taxable amt ${taxableAmt}");
    transaction.value.txnDiscountAmount = amt;
    transaction.refresh();
    recalculateDataForItem(editorTag: editorTag);

    // transaction.value.txnTaxableTotalAmount = taxableAmt;
    // totalAmountCtrl.text = taxableAmt.toStringAsFixed(2);
    // discountAmountCtrl.text = amt.toStringAsFixed(2);
    // dueAmountCtrl.text = taxableAmt.toStringAsFixed(2);
    // transaction.value.txnDiscountAmount = amt;
    // transaction.refresh();
    // discountAmountCtrl.text = amt.toStringAsFixed(2);
  }

  recalculateDataForItem({String? editorTag}) {
    // handle value change in purchase return model in valid step:

    double subtoal = transaction.value.txnSubTotalAmount ?? 0.00;
    print("hami ya xum subtotal $subtoal");

    transaction.value.txnTaxableTotalAmount = parseDouble(
        (subtoal - (transaction.value.txnDiscountAmount ?? 0.00))
            .toStringAsFixed(2));
    print("hami ya xum taxable amt ${transaction.value.txnTaxableTotalAmount}");
    print("tax percent ${transaction.value.txnTaxPercent}");
    print("tax percent ${transaction.value.txnTaxPercent}");
    transaction.value.txnTaxAmount = (transaction.value.txnTaxPercent! *
        0.01 *
        transaction.value.txnTaxableTotalAmount!);
    print("tax amt ${transaction.value.txnTaxAmount}");

    transaction.value.txnTotalAmount =
        (transaction.value.txnTaxableTotalAmount! +
            transaction.value.txnTaxAmount!);
    print("total amount=> ${transaction.value.txnTotalAmount}");
    if (transaction.value.ledgerId != CASH_PURCHASE_LEDGER_ID) {
      // print("yo k ho k ${transaction.value.ledgerId != CASH_PURCHASE_LEDGER_ID}");
      // print("this is isReceived ==> ${isReceived}");
      transaction.value.txnCashAmount = isReceived
          ? transaction.value.txnTotalAmount
          : transaction.value.txnCashAmount!;

      // print("this is txnCashAmt ${transaction.value.txnCashAmount}");
      transaction.value.txnBalanceAmount = isReceived
          ? 0.00
          : transaction.value.txnTotalAmount! -
              transaction.value.txnCashAmount!;
    } else {
      //for cash
      //total will always be cash amount for cash ledger
      transaction.value.txnCashAmount = transaction.value.txnTotalAmount;
      transaction.value.txnBalanceAmount = 0.00;
    }

    assignTransactionToTextFields(editorTAG: editorTag);
  }

  updateDiscountAmount(String dis, {String? editorTag}) {
    double disAmt = (parseDouble(dis) ?? 0.00);
    // print("this is dis amt $disAmt");

    double subTotal = (transaction.value.txnSubTotalAmount ?? 0.00);

    double disPercent = (disAmt / subTotal) * 100;
    // print("this is dis percent $disPercent");

    transaction.value.txnDiscountAmount = disAmt;
    transaction.value.txnDiscountPercent = disPercent;
    transaction.refresh();
    discountPercentageCtrl.text = disPercent.toStringAsFixed(2);
    // // recalculateDataForItem(editorTag: editorTag);
    // // assignTransactionToTextFields(editorTAG: "txn_discount_amount");

    // print("This is is subtotal $subTotal ");
    // double taxableAmt = subTotal - disAmt;
    // print("this is taxable amt $taxableAmt");
    // transaction.value.txnSubTotalAmount = taxableAmt;
    // transaction.refresh();

    // subTotalAmountCtrl.text = taxableAmt.toStringAsFixed(2);
  }

  initEdit(purchaseReturnID, readOnlyFlag) async {
    _isLoading(true);
    _isLoading.refresh();
    final tempDir = await getTemporaryDirectory();

    Tuple3<PurchaseReturnModal, List<LineItemDetailModel>, List<TxnImageModel>>
        dt =
        await purchaseReturnRepository.getPurchaseReturnById(purchaseReturnID);

    LedgerDetailModel party = await _ledgerRepository
        .getLedgerWithBalanceById(dt.item1.ledgerId ?? "");

    _editFlag.value = true;
    transaction.value = dt.item1;
    items.value = dt.item2;
    images.value = dt.item3;
    files.clear();
    List<File> prevFiles = [];

    await Future.wait(dt.item3.map((e) async {
      final file =
          await new File('${tempDir.path}/image-${e.sno}.${e.imageExt}')
              .create();
      file.writeAsBytesSync(e.imageBitmap!);
      prevFiles.add(file);
    }));
    files.addAll(prevFiles);
    items.refresh();
    files.refresh();
    previousBillNo = dt.item1.txnRefNumberChar;
    onChangeParty(party);

    if ((dt.item1.txnTaxAmount ?? 0.00) > 0.0) {
      _isVatEnabled.value = true;
      _isVatEnabled.refresh();
    }

    // assignTransactionToTextFields();
    print("this is read only flag $readOnlyFlag");
    _readOnlyFlag.value = readOnlyFlag;
    _isLoading(false);
    _isLoading.refresh();
  }

  Future<List<TxnImageModel>> getTxnImageModelFromFiles(
      List<File> _files) async {
    List<TxnImageModel> txnImageModels = [];

    await Future.wait(_files.map((element) async {
      Tuple2<List<int>, String> compressedImage = await compressImage(element);
      txnImageModels.add(TxnImageModel(
          imageBitmap: compressedImage.item1, imageExt: compressedImage.item2));
    }));
    return txnImageModels;
  }

  Future<bool> checkLargeImage(List<dynamic> fls,
      {bool? preConvertFiles}) async {
    //preConvertFiles can be used to convert files list to txn image model once, so than it don't need re convert
    bool status = false;
    await Future.wait(fls.map((element) async {
      Tuple2<List<int>, String> compressedImage =
          await compressImage(File(element.path));
      // Log.d("file size is" + compressedImage.item1.length.toString());
      if (compressedImage.item1.length > MAX_IMAGE_SIZE) {
        status = true;
        // return;
      }
    }).toList());

    return status;
  }

  Future<bool> checkDuplicateBillNo() async {
    bool status = false;
    try {
      if (editFlag && transaction.value.txnRefNumberChar == previousBillNo) {
        status = false;
      } else {
        status = await purchaseReturnRepository
            .isBillDuplicate(transaction.value.txnRefNumberChar);
      }
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<String?> createPurchaseReturn() async {
    String? status;

    try {
      // String primaryKeyPrefix = await getPrimaryKeyPrefix();
      // transaction.value.txnId = primaryKeyPrefix + uuidV4;
      transaction.value.txnType = TxnType.purchaseReturn;
      transaction.value.txnDate =
          toDateAD(NepaliDateTime.parse(transaction.value.txnDateBS ?? ""));
      if (!([null, ""].contains(transaction.value.chequeIssueDateBS))) {
        transaction.value.chequeIssueDate = toDateAD(NepaliDateTime.parse(
            strTrim(transaction.value.chequeIssueDateBS ?? "")));
      }
      List<TxnImageModel> tempImages = await getTxnImageModelFromFiles(files);

      status = await purchaseReturnRepository.addPurchaseReturn(
          transaction.value, items, tempImages);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<bool> updatePurchaseReturn() async {
    bool status = false;

    try {
      transaction.value.txnDate = toDateAD(
          NepaliDateTime.parse(strTrim(transaction.value.txnDateBS ?? "")));
      if (!([null, ""].contains(transaction.value.chequeIssueDateBS))) {
        transaction.value.chequeIssueDate = toDateAD(NepaliDateTime.parse(
            strTrim(transaction.value.chequeIssueDateBS ?? "")));
      }
      List<TxnImageModel> tempImages =
          await getTxnImageModelFromFiles(files.value);
      status = await purchaseReturnRepository.updatePurchaseReturn(
          transaction.value, items, tempImages);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }
}
