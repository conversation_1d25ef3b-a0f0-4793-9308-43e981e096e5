// ignore_for_file: use_build_context_synchronously

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/timer_button.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

class OtpVerifyScreenArguments {
  final String id;
  final String mobileNo;

  const OtpVerifyScreenArguments(this.id, this.mobileNo);
}

class OtpVerificationScreen extends StatelessWidget {
  final String id;
  final String mobileNo;

  // ignore: use_key_in_widget_constructors
  const OtpVerificationScreen(this.id, this.mobileNo);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: false,
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(60.0),
        child: AppBar(
          elevation: 4.0,
          backgroundColor: colorPrimary,
          automaticallyImplyLeading: false,
          centerTitle: true,
          title: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: const <Widget>[
              Text(
                "Verification",
                style: TextStyle(
                    letterSpacing: 1.5,
                    fontSize: 25,
                    color: Colors.white,
                    fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Container(
          color: Colors.white,
          child: Column(
            children: <Widget>[
              Expanded(
                child: SingleChildScrollView(
                  child: Container(
                      width: double.infinity,
                      margin: const EdgeInsets.symmetric(
                          vertical: 10, horizontal: 15),
                      decoration: const BoxDecoration(),
                      child: OtpVerifyForm(id, mobileNo)),
                ),
              ),
            ],
          ),
        ),
      ),
    ));
  }
}

class OtpVerifyForm extends StatefulWidget {
  final String id;
  final String mobileNo;

  // ignore: use_key_in_widget_constructors
  const OtpVerifyForm(this.id, this.mobileNo);

  @override
  // ignore: library_private_types_in_public_api
  _OtpVerifyFormState createState() => _OtpVerifyFormState();
}

class _OtpVerifyFormState extends State<OtpVerifyForm> {
  final _formKey = GlobalKey<FormState>();
  final _otpController = TextEditingController();

  bool sendingOTP = false;

  StreamController<ErrorAnimationType> errorController =
      StreamController<ErrorAnimationType>();

  bool hasError = false;

  Future<void> _verifyOTP() async {
    FocusScope.of(context).unfocus();

    var body = <String, String>{};
    body["otp"] = _otpController.text;

    ProgressDialog progressDialog = ProgressDialog(context,
        type: ProgressDialogType.normal, isDismissible: false);
    progressDialog.update(message: "Please wait....");
    await progressDialog.show();

    ApiBaseHelper apiBaseHelper = ApiBaseHelper();
    ApiResponse apiResponse = await apiBaseHelper.post(
        "${apiBaseHelper.ACTION_REG_ACTIVATE}/${widget.id}", body,
        accessToken: false);

    await progressDialog.hide();

    if (apiResponse.status) {
      Navigator.pushReplacementNamed(context, "/login");
      showToastMessage(context, message: apiResponse.msg ?? "");
    } else {
      showAlertDialog(context,
          alertType: AlertType.Error,
          alertTitle: "Error",
          message: apiResponse.msg ?? "");
    }
  }

  Future<void> _resendOTP() async {
    FocusScope.of(context).unfocus();
    setState(() {
      sendingOTP = true;
    });
    ProgressDialog progressDialog = ProgressDialog(context,
        type: ProgressDialogType.normal, isDismissible: false);
    progressDialog.update(message: "Please wait....");
    await progressDialog.show();

    ApiBaseHelper apiBaseHelper = ApiBaseHelper();
    ApiResponse apiResponse = await apiBaseHelper.get(
        "${apiBaseHelper.ACTION_RESENDOTP}/${widget.id}",
        accessToken: false);

    await progressDialog.hide();
    setState(() {
      sendingOTP = false;
    });

    if (apiResponse.status) {
      showToastMessage(context, message: apiResponse.msg ?? "");
    } else {
      showAlertDialog(context,
          alertType: AlertType.Error,
          alertTitle: "Error",
          message: apiResponse.msg ?? "");
    }
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    errorController.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: <Widget>[
          RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
                text:
                    "Please verify your registered mobile number to activate your",
                style: kLabelStyle.copyWith(fontSize: 17, height: 1.4),
                children: <TextSpan>[
                  TextSpan(
                      text: " mobile खाता ",
                      style: TextStyle(
                          color: colorPrimary,
                          fontWeight: FontWeight.bold,
                          fontFamily: "ArialBlack")),
                  const TextSpan(text: "account."),
                ]),
          ),
          const SizedBox(
            height: 20,
          ),
          Image.asset(
            "images/verification-blue.png",
            height: 80,
          ),
          const SizedBox(
            height: 10,
          ),
          RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                  text: 'Enter the OTP just sent to your number ',
                  style: kLabelStyle.copyWith(
                      fontSize: 16, height: 1.4, color: textColor),
                  children: <TextSpan>[
                    TextSpan(
                        text: widget.mobileNo,
                        style: TextStyle(color: colorPrimary))
                  ])),
          const SizedBox(height: 15.0),
          PinCodeTextField(
            onChanged: (value) {},
            appContext: context,
            pastedTextStyle: TextStyle(
              color: Colors.green.shade600,
              fontWeight: FontWeight.bold,
            ),
            length: 6,
            obscureText: false,
            controller: _otpController,
            errorAnimationController: errorController,
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            animationType: AnimationType.fade,
            pinTheme: PinTheme(
              shape: PinCodeFieldShape.box,
              borderRadius: BorderRadius.circular(5),
              fieldHeight: 45,
              fieldWidth: 45,
              inactiveColor: hasError ? Colors.orange : colorPrimary,
            ),
          ),
          const SizedBox(height: 20.0),
          Center(
              child: GradientButtonBlue(
                  buttonText: "Verify",
                  onClick: () {
                    if (_otpController.text.length != 6) {
                      errorController.add(ErrorAnimationType
                          .shake); // Triggering error shake animation
                      setState(() {
                        hasError = true;
                      });
                    } else {
                      setState(() {
                        hasError = false;
                      });
                      _verifyOTP();
                    }
                  })),
          const SizedBox(height: 35.0),
          Text(
            "OTP successfully sent to your registered mobile number. ",
            style: TextStyle(
                fontSize: 18, fontWeight: FontWeight.w600, color: colorGreen),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 20.0),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text("Didn't receive a code ? "),
              sendingOTP
                  ? const CircularProgressIndicator(
                      strokeWidth: 1,
                    )
                  : DTTimerButton(
                      label: "Resend",
                      onPressed: () {
                        _resendOTP();
                      },
                      // color: Colors.red,
                      timeOutInSeconds: 120,
                    ),
              // GestureDetector(
              //     child: Text(
              //       "Resend",
              //       style: TextStyle(color: colorPrimary),
              //     ),
              //     onTap: () {
              //       _resendOTP();
              //     },
              //   ),
            ],
          ),
          const SizedBox(height: 50.0),
          Center(
            child: Image.asset(
              'images/logo-bottom.png',
              height: 100.0,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(height: 10.0),
        ],
      ),
    );
  }
}
