import 'dart:convert';
import 'dart:io' as io;

import 'package:localstorage/localstorage.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';

import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:tuple/tuple.dart';
// import 'package:mobile_khaata/utilities/extension.dart';
import 'package:mobile_khaata_v2/utilities/extension.dart';

class DatabaseHelper {
  static final DatabaseHelper _databaseInstance =
      new DatabaseHelper._privateConstructor();
  DatabaseHelper._privateConstructor();

  factory DatabaseHelper() => _databaseInstance;

  // static Database _database;
  static Database? _database;

  Future<Database?> get database async {
    if (null == _database) {
      _database = await _initDatabase();
    }
    return _database;
  }

  _initDatabase() async {
    SharedPreferences _prefs = await SharedPreferences.getInstance();
    String? databaseName = _prefs.getString(CurrentDatabase);

    // Log.d("database ==> init database");
    // Log.d("database ==>" + databaseName);

    io.Directory documentsDirectory = await getApplicationDocumentsDirectory();
    final path = join(documentsDirectory.path, databaseName);

    final exist = await databaseExists(path);

    if (exist) {
      Database database = await openDatabase(path, readOnly: false);
      return database;
    } else {
      final storage = new LocalStorage(MobileSettings);
      await storage.ready;
      await storage.deleteItem(databaseName!);
    }
  }

  void dispose() {
    if (null != _database && _database!.isOpen) _database!.close();
    _database = null;
  }

  static destroy() {
    if (null != _database && _database!.isOpen) _database!.close();
    _database = null;
  }

  static Future<bool> doesExistForGivenAttribute(
      Map<String, dynamic> attributes, String tableName) async {
    bool doesExist = false;
    try {
      String whereClause = "";
      List<dynamic> whereArgs = [];
      attributes.keys.mapIndex((e, i) {
        dynamic val = attributes[e] ?? "";
        if (i == 0) {
          whereClause = "$e = ?";
        } else {
          whereClause = whereClause + " AND $e = ?";
        }
        whereArgs.add(val);
      }).toList();
      print("Got items where clause $whereClause");
      // Log.d("Got items where clause $whereClause");
      // Log.d("Got items where args $whereArgs");
      print("Got items where args $whereArgs");
      Database? dbClient = await _databaseInstance.database;
      List<Map<String, dynamic>> refDatas = await dbClient!
          .query(tableName, where: whereClause, whereArgs: whereArgs);

      if (refDatas.length > 0) {
        //record exist for given conditions
        doesExist = true;
      }

      // Log.d("Got items are $refDatas");
      print("Got items are $refDatas");
    } catch (e) {
      // Log.d("Got items ERROR: ${e.toString()}");
      print("Got items ERROR: ${e.toString()}");
    }
    return doesExist;
  }

  static Future<Tuple2<bool, List<String>>> hasReferences(
      {required List<Map<String, String>> tablesWithColName,
      required String value}) async {
    List<String> includedIn = [];
    bool isIncluded = false;

    try {
      Database? dbClient = await _databaseInstance.database;
      await Future.wait(tablesWithColName.map((e) async {
        String tableName = e['table_name'] ?? "";
        String colName = e['column_name'] ?? "";
        String query =
            "SELECT count(*) AS total_rec FROM $tableName WHERE $colName==? AND last_activity_type!=3";
        if (['mk_line_items'].contains(tableName)) {
          query =
              "SELECT count(*) AS total_rec FROM mk_line_items LEFT JOIN mk_transactions ON mk_line_items.txn_id=mk_transactions.txn_id  WHERE mk_line_items.$colName==? AND mk_transactions.last_activity_type!=3";
        }
        int count =
            Sqflite.firstIntValue(await dbClient!.rawQuery(query, [value]))!;
        // Log.d("ref count for $tableName $count");
        print("ref count for $tableName $count");
        if (count > 0) {
          //include in given table name
          includedIn.add(tableName);
        }
      }).toList());
      isIncluded = includedIn.length > 0;
    } catch (e) {
      // Log.d("ERROR IN GETTING REFERENCE  " + e.toString());
      print("ERROR IN GETTING REFERENCE  " + e.toString());
      // this should not trigger
      isIncluded = true;
    }
    // Log.d("included tables $includedIn");
    print("included tables $includedIn");

    return Tuple2(isIncluded, includedIn);
  }

  static Future<bool> isUnique(
      {required String tableName,
      required String columnName,
      required String checkValue,
      String? keyColumn,
      String? keyValue}) async {
    if (null == checkValue) {
      return true;
    }

    int count;
    Database? dbClient = await _databaseInstance.database;

    if (null != keyValue) {
      count = Sqflite.firstIntValue(await dbClient!.rawQuery(
          "SELECT count(*) AS total_rec FROM $tableName WHERE $keyColumn!=? AND lower($columnName)=? AND last_activity_type!=?",
          [keyValue, checkValue.toLowerCase(), LastActivityType.Delete]))!;
    } else {
      count = Sqflite.firstIntValue(await dbClient!.rawQuery(
          "SELECT count(*) AS total_rec FROM $tableName WHERE lower($columnName)=? AND last_activity_type!=?",
          [checkValue.toLowerCase(), LastActivityType.Delete]))!;
    }

    if (count > 0) {
      return false;
    } else {
      return true;
    }
  }

  Future<bool> handleDynamicQuery({
    required String queryType,
    required String tableName,
    required String data,
    String? whereClause,
    dynamic whereArgs,
  }) async {
    try {
      Database? dbClient = await _databaseInstance.database;

      // Parse the data JSON string into a Map
      Map<String, dynamic> parsedData =
          Map<String, dynamic>.from(jsonDecode(data));

      // Convert any "null" string values to actual nulls
      parsedData.updateAll((key, value) => value == "null" ? null : value);

      // Helper to safely parse whereArgs into List<String>
      List<String>? _parseWhereArgs(dynamic args) {
        if (args == null) return null;
        if (args is List) {
          return List<String>.from(args);
        } else if (args is String) {
          try {
            var decoded = jsonDecode(args);
            if (decoded is List) {
              return List<String>.from(decoded);
            } else {
              return [args]; // Treat as single string wrapped in list
            }
          } catch (_) {
            return [args]; // Not JSON — treat as single string
          }
        }
        throw Exception("Invalid whereArgs type");
      }

      switch (queryType.toLowerCase()) {
        case 'insert':
          await dbClient!.insert(tableName, parsedData);
          break;

        case 'update':
          if (whereClause == null || whereArgs == null) {
            throw Exception(
                "Missing WHERE clause or arguments for UPDATE query.");
          }
          await dbClient!.update(
            tableName,
            parsedData,
            where: whereClause,
            whereArgs: _parseWhereArgs(whereArgs),
          );
          break;

        case 'delete':
          if (whereClause == null || whereArgs == null) {
            throw Exception(
                "Missing WHERE clause or arguments for DELETE query.");
          }
          await dbClient!.delete(
            tableName,
            where: whereClause,
            whereArgs: _parseWhereArgs(whereArgs),
          );
          break;

        default:
          throw Exception("Unsupported query type: $queryType");
      }

      return true;
    } catch (e) {
      print("Error in handleDynamicQuery: $e");
      return false;
    }
  }
}
