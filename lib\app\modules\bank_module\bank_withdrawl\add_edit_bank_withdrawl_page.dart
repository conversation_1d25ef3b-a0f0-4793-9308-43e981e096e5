// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_withdrawl/add_edit_bank_withdrawl_controller.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';

import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

import '../../../repository/bank_adjustment_repository.dart';

class AddEditBankWithdrawalPage extends StatelessWidget {
  final String tag = "AddEditBankWithdrawalPage";

  final String? adjustmentId;
  final String? bankID;

  final addEditBankWithdrawalController = AddEditBankWithdrawalController();

  AddEditBankWithdrawalPage({super.key, this.adjustmentId, this.bankID}) {
    addEditBankWithdrawalController.bankAdjustmentTxn.bankAdjId = adjustmentId;
    addEditBankWithdrawalController.bankAdjustmentTxn.bankAdjBankId = bankID;

    addEditBankWithdrawalController.init();
  }

  final _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (addEditBankWithdrawalController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
        // resizeToAvoidBottomPadding: true,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          leading: BackButton(
            onPressed: () => Navigator.pop(context, false),
          ),
          centerTitle: false,
          titleSpacing: -5.0,
          title: Text(
            (!addEditBankWithdrawalController.editFlag)
                ? "बैंक निकासी (Bank Withdrawal)"
                : "बैंक निकासी (Edit Bank Withdrawal)",
            style: const TextStyle(
                fontSize: 18,
                color: Colors.white,
                fontFamily: 'HelveticaRegular',
                fontWeight: FontWeight.bold),
          ),
          actions: [
            if (addEditBankWithdrawalController.editFlag) ...{
              InkWell(
                  onTap: () => addEditBankWithdrawalController.readOnlyFlag =
                      !addEditBankWithdrawalController.readOnlyFlag,
                  splashColor: colorPrimaryLighter,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 15),
                    child: (addEditBankWithdrawalController.readOnlyFlag)
                        ? Column(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(
                                Icons.mode_edit,
                                color: Colors.white,
                              ),
                              Text(
                                "Edit",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          )
                        : Column(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(
                                Icons.close,
                                color: Colors.white,
                              ),
                              Text(
                                "Cancel",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          ),
                  )),
            }
          ],
        ),
        body: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: Container(
            decoration: const BoxDecoration(color: Colors.white),
            padding: const EdgeInsets.only(
              left: 15,
              right: 15,
            ),
            height: MediaQuery.of(context).size.height,
            child: Form(
              key: addEditBankWithdrawalController.formKey,
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  children: [
                    const SizedBox(
                      height: 15,
                    ),

                    //===================================Adjustment Date Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "मिति",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        CustomDatePickerTextField(
                          readOnly:
                              addEditBankWithdrawalController.readOnlyFlag,
                          initialValue: addEditBankWithdrawalController
                              .bankAdjustmentTxn.bankAdjDateBS,
                          maxBSDate: NepaliDateTime.now(),
                          onChange: (selectedDate) {
                            addEditBankWithdrawalController
                                .bankAdjustmentTxn.bankAdjDateBS = selectedDate;
                          },
                        ),
                      ],
                    ),

                    const SizedBox(
                      height: 20,
                    ),

                    //===============================================Adjustment Type Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "From Bank",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderDropdown(
                          name: 'adjustment_type',
                          style: formFieldTextStyle,
                          decoration:
                              formFieldStyle.copyWith(labelText: "Bank"),
                          items:
                              addEditBankWithdrawalController.banks.map((row) {
                            return DropdownMenuItem(
                                value: row.pmtTypeId,
                                child: Text(row.pmtTypeShortName ?? ""));
                          }).toList(),
                          // readOnly:
                          //     addEditBankWithdrawalController.readOnlyFlag,
                          initialValue: addEditBankWithdrawalController
                              .bankAdjustmentTxn.bankAdjBankId,
                          onChanged: (value) {
                            // addEditBankWithdrawalController.banks.

                            addEditBankWithdrawalController
                                .bankAdjustmentTxn.bankAdjBankId = value;
                          },
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return "(Please select Bank)";
                            }
                            return null;
                          },
                        ),
                      ],
                    ),

                    const SizedBox(
                      height: 20,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "To",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderDropdown(
                          name: 'adjustment_to',
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(labelText: ""),
                          items: const [],
                          // items:
                          //     addEditBankWithdrawlController.banks.map((row) {
                          //   return DropdownMenuItem(
                          //       value: row.pmtTypeId,
                          //       child: Text(row.pmtTypeShortName));
                          // }).toList(),
                          // readOnly: true,
                          initialValue: "Cash",
                        ),
                      ],
                    ),

                    const SizedBox(
                      height: 20,
                    ),

                    //===============================================Amount Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "रकम",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "amount",
                          autocorrect: false,
                          textInputAction: TextInputAction.done,
                          keyboardType: const TextInputType.numberWithOptions(
                              decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^(\d+)?\.?\d{0,2}'))
                          ],
                          maxLength: 10,
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              labelText: "Amount", counterText: ''),
                          readOnly:
                              addEditBankWithdrawalController.readOnlyFlag,
                          initialValue: addEditBankWithdrawalController
                              .bankAdjustmentTxn.bankAdjAmount
                              ?.toString(),
                          onChanged: (value) {
                            addEditBankWithdrawalController.bankAdjustmentTxn
                                .bankAdjAmount = parseDouble(value);
                          },
                          validator: (value) {
                            if (value == null || parseDouble(value)! <= 0) {
                              return 'रकम खाली वा शून्य राख्नमिल्दैन (Amount cannot be empty or zero)';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),

                    const SizedBox(
                      height: 20,
                    ),

                    //========================================Description Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "विवरण",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "adjustment_desc",
                          autocorrect: false,
                          textInputAction: TextInputAction.done,
                          keyboardType: TextInputType.text,
                          style: formFieldTextStyle,
                          decoration:
                              formFieldStyle.copyWith(labelText: "Description"),
                          readOnly:
                              addEditBankWithdrawalController.readOnlyFlag,
                          initialValue: addEditBankWithdrawalController
                              .bankAdjustmentTxn.bankAdjDescription,
                          onChanged: (value) {
                            addEditBankWithdrawalController.bankAdjustmentTxn
                                .bankAdjDescription = strTrim(value ?? "");
                          },
                          // validators: [],
                        ),
                      ],
                    ),

                    const SizedBox(
                      height: 150,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),

        //=================================================Save button
        bottomNavigationBar: BottomSaveCancelButton(
          shadow: false,
          hasDelete: (addEditBankWithdrawalController.editFlag &&
                  !addEditBankWithdrawalController.readOnlyFlag)
              ? true
              : false,
          onDeleteBtnPressedFn: () async {
            showAlertDialog(context,
                okText: "YES",
                hasCancel: true,
                cancelText: "NO",
                alertType: AlertType.Error,
                alertTitle: "Confirm Delete", onCloseButtonPressed: () async {
              // Navigator.of(_).pop();
              ProgressDialog progressDialog = ProgressDialog(context,
                  type: ProgressDialogType.normal, isDismissible: false);
              progressDialog.update(
                  message: "Checking Permission. Please wait....");
              await progressDialog.show();
              Tuple2<bool, String> checkResp =
                  await PermissionWrapperController().requestForPermissionCheck(
                      forPermission: PermissionManager.bankWithdrawDelete);
              if (checkResp.item1) {
                //has  permission
                progressDialog.update(
                    message: "Deleting Data. Please wait....");
                Tuple2<bool, String> deleteResp =
                    await BankAdjustmentRepository().delete(adjustmentId ?? "");
                await progressDialog.hide();
                if (deleteResp.item1) {
                  //  data deleted
                  TransactionHelper.refreshPreviousPages();
                  showAlertDialog(context,
                      barrierDismissible: false,
                      alertType: AlertType.Success,
                      alertTitle: "", onCloseButtonPressed: () {
                    // Navigator.of(_).pop();
                    Navigator.of(context).pop();
                  }, message: deleteResp.item2);
                } else {
                  //cannot  delete  data
                  showAlertDialog(context,
                      alertType: AlertType.Error,
                      alertTitle: "",
                      message: deleteResp.item2);
                }
              } else {
                await progressDialog.hide();
                showAlertDialog(context,
                    alertType: AlertType.Error,
                    alertTitle: "",
                    message: checkResp.item2);
              }
            },
                message:
                    "Are you sure you  want to  delete this adjustment record?");
          },
          enableFlag: !addEditBankWithdrawalController.readOnlyFlag,
          onSaveBtnPressedFn: (addEditBankWithdrawalController.readOnlyFlag)
              ? null
              : () async {
                  FocusScope.of(context).unfocus();
                  if (addEditBankWithdrawalController.formKey.currentState!
                      .validate()) {
                    if (null ==
                        addEditBankWithdrawalController
                            .bankAdjustmentTxn.bankAdjDateBS) {
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "Error",
                          message: "मिति भर्नुहोस्\n(Fill Date)");
                      return;
                    }

                    if (addEditBankWithdrawalController.banks
                            .where((element) =>
                                element.pmtTypeId ==
                                addEditBankWithdrawalController
                                    .bankAdjustmentTxn.bankAdjBankId)
                            .first
                            .pmtTypeCurrentBalance! <
                        addEditBankWithdrawalController
                            .bankAdjustmentTxn.bankAdjAmount!) {
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "Error",
                          message:
                              "चयन गरिएको बैंक खातामा अपर्याप्त मौज्दात रकम। कृपया अर्को बैंक खाता चयन गर्नुहोस् वा भुक्तानी मोड परिवर्तन गर्नुहोस्। | \nInsufficient balance in selected bank account. Please select another bank account or change payment mode.");
                      return;
                    }

                    ProgressDialog progressDialog = ProgressDialog(context,
                        type: ProgressDialogType.normal, isDismissible: false);
                    progressDialog.update(
                        message: "Saving data. Please wait....");
                    await progressDialog.show();

                    bool status = false;
                    try {
                      if (!addEditBankWithdrawalController.editFlag) {
                        status = await addEditBankWithdrawalController
                            .createAdjustment();
                      } else {
                        status = await addEditBankWithdrawalController
                            .updateAdjustment();
                      }
                    } catch (e, trace) {
                      // Log.e(tag, e.toString() + trace.toString());
                    }
                    await progressDialog.hide();

                    if (status) {
                      Navigator.pop(context, true);

                      TransactionHelper.refreshPreviousPages();

                      String message =
                          (addEditBankWithdrawalController.editFlag)
                              ? "Adjustment Updated Successfully."
                              : "Adjustment Created Successfully.";
                      showToastMessage(context, message: message, duration: 2);
                    } else {
                      showToastMessage(context,
                          alertType: AlertType.Error,
                          message: "Failed to process operation",
                          duration: 2);
                    }
                  }
                },
        ),
      ));
    });
  }
}
