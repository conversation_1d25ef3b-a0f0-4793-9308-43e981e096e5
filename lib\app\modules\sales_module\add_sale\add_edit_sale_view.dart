import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_image_picker/form_builder_image_picker.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/components/ledger_autocomplete%20_%20textfield_with_add.dart';
import 'package:mobile_khaata_v2/app/components/payment_mode_selector.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/sales_module/add_sale/add_edit_sale_controller.dart';
import 'package:mobile_khaata_v2/app/modules/sales_module/add_sell_bill_item/add_edit_sell_bill_view_page.dart';
import 'package:mobile_khaata_v2/app/modules/sales_module/detail_sale/detail_sale_page.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import '../../../../utilities/shared_pref_helper1.dart';
import '../../../common_widgets/alerts.dart';

extension ExtendedIterable<E> on Iterable<E> {
  /// Like Iterable<T>.map but callback have index as second argument
  Iterable<T> mapIndex<T>(T f(E e, int i)) {
    var i = 0;
    return this.map((e) => f(e, i++));
  }

  void forEachIndex(void f(E e, int i)) {
    var i = 0;
    this.forEach((e) => f(e, i++));
  }
}

class AddEditSalePage extends StatelessWidget {
  final String tag = "Sales Add/Edit Page";

  final String? saleID;
  final bool? reaOnlyFlag;

  final saleController = SaleController();

  AddEditSalePage({this.saleID, this.reaOnlyFlag}) {
    saleController.onInit();

    if (null != saleID) {
      saleController.initEdit(saleID, this.reaOnlyFlag ?? false);
    } else {
      saleController.initialize();
    }
  }

  onSave(BuildContext context, {bool forNew = false}) async {
    print("ya xu");
    FocusScope.of(context).unfocus();
    if (saleController.formKey.currentState!.validate()) {
      if (saleController.transaction.value.txnDateBS!.isEmpty) {
        showToastMessage(context,
            message: "मिति खाली राख्न मिल्दैन | \nPlease fill the date.",
            alertType: AlertType.Error);
        return;
      }
      if (null == saleController.transaction.value.ledgerId) {
        showToastMessage(context,
            message:
                "ग्राहक खाली राख्न मिल्दैन | \nCustomer name cannnot be empty.",
            alertType: AlertType.Error);
        return;
      }

      if (100 < parseDouble(saleController.discountPercentageCtrl.text)! ||
          parseDouble(saleController.subTotalAmountCtrl.text)! <
              parseDouble(saleController.discountAmountCtrl.text)!) {
        showToastMessage(context,
            message:
                "छुट १००% भन्दा ठूलो हुन सक्दैन | \nDiscount can't be greater than 100%.",
            alertType: AlertType.Error);
        return;
      }

      if (saleController.totalAmountCtrl.text.isEmpty) {
        showToastMessage(context,
            message: "कुल रकम खाली हुन सक्दैन | \nTotal amount can't be empty.",
            alertType: AlertType.Error);
        return;
      }

      if (0.0 > parseDouble(saleController.totalAmountCtrl.text)!) {
        showToastMessage(context,
            message:
                "कुल रकम नकारात्मक हुन सक्दैन | \nTotal amount can't be negative.",
            alertType: AlertType.Error);
        return;
      }

      if (saleController.transaction.value.txnCashAmount! >
          saleController.transaction.value.txnTotalAmount!) {
        showToastMessage(context,
            message:
                "प्राप्त रकम बिल रकम भन्दा ठूलो हुन सक्दैन  | \nReceived amount can't be greater than bill amount.",
            alertType: AlertType.Error);
        return;
      }

      if (saleController.transaction.value.txnPaymentTypeId !=
              PAYMENT_MODE_CASH_ID &&
          !(saleController.transaction.value.txnCashAmount! > 0)) {
        showToastMessage(context,
            message:
                "चेक वा बैंक मा प्राप्त रकम 0 राख्न मिल्दैन | \nReceived amount cannot be 0 for Cheque and Bank",
            alertType: AlertType.Error);
        return;
      }

      if (saleController.transaction.value.txnPaymentTypeId ==
              PAYMENT_MODE_CHEQUE_ID &&
          (null == saleController.transaction.value.txnPaymentReference ||
              "" == saleController.transaction.value.txnPaymentReference)) {
        showToastMessage(context,
            message:
                "चेक/भौचर न. खाली राख्न मिल्दैन | \nPlease fill the cheque/voucher no.",
            alertType: AlertType.Error);
        return;
      }

      if (saleController.transaction.value.txnPaymentTypeId ==
              PAYMENT_MODE_CHEQUE_ID &&
          (null == saleController.transaction.value.chequeIssueDateBS ||
              "" == saleController.transaction.value.chequeIssueDateBS)) {
        showToastMessage(context,
            message:
                "चेक मिति खाली राख्न मिल्दैन | \nPlease fill the cheque date",
            alertType: AlertType.Error);
        return;
      }

      ProgressDialog progressDialog = ProgressDialog(context,
          type: ProgressDialogType.normal, isDismissible: false);
      progressDialog.update(message: "Saving data. Please wait....");
      await progressDialog.show();
      progressDialog.hide();

      bool isLargeFile =
          await saleController.checkLargeImage(saleController.files);
      if (isLargeFile) {
        await progressDialog.hide();
        saleController.files.clear();
        showToastMessage(context,
            message: MAX_IMAGE_SIZE_MESSAGE, alertType: AlertType.Error);
        return;
      }

      bool status = false;
      String? txnID;
      try {
        if (!saleController.editFlag) {
          txnID = await saleController.createSale();
          status = (null != txnID);
        } else {
          status = await saleController.updateSale();
        }
      } catch (e, trace) {
        // Log.e(tag, e.toString() + trace.toString());
      }

      await progressDialog.hide();

      if (status) {
        // Navigator.pop(context, true);
        String message = (saleController.editFlag)
            ? "Sale Updated Successfully."
            : "Sale Created Successfully.";
        if (forNew) {
          Navigator.of(context).pushReplacementNamed('/addSale');
        } else {
          if (saleController.editFlag) {
            Navigator.of(context).pop();
            Navigator.of(context).pushReplacementNamed('/detailSale',
                arguments: DetailSalePage(
                  saleID: this.saleID,
                ));
          } else {
            Navigator.of(context).pop();
            if (null != txnID) {
              TransactionHelper.goToPrintPage(context, txnID, TxnType.sales);
            }
          }
        }

        TransactionHelper.refreshPreviousPages();
        showToastMessage(context, message: message, duration: 2);
        // Navigator.of(context).pop();
      } else {
        showToastMessage(context,
            alertType: AlertType.Error,
            message: "Failed to process operation",
            duration: 2);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      bool isCashSale =
          saleController.transaction.value.ledgerId == CASH_SALES_LEDGER_ID;
      bool hasSubTotal =
          (saleController.transaction.value.txnSubTotalAmount != null &&
              saleController.transaction.value.txnSubTotalAmount! > 0.0);

      if (saleController.isLoading) {
        return Container(
            color: Colors.white,
            child: Center(child: CircularProgressIndicator()));
      } else {
        return SafeArea(
            child: Scaffold(
          // resizeToAvoidBottomPadding: true,
          resizeToAvoidBottomInset: true,
          appBar: AppBar(
            toolbarHeight: 60,
            backgroundColor: colorPrimary,
            elevation: 4,
            leading: BackButton(
              onPressed: () => Navigator.pop(context, false),
            ),
            centerTitle: false,
            titleSpacing: -5.0,
            title: Text(
              "बिक्री (Sale)",
              style: TextStyle(
                  fontSize: 18,
                  color: Colors.white,
                  fontFamily: 'HelveticaRegular',
                  fontWeight: FontWeight.bold),
            ),
          ),

          //===========================================================================Body Part
          body: Center(
            child: Container(
              color: backgroundColorShade,
              child: GestureDetector(
                onTap: () => FocusScope.of(context).unfocus(),
                child: Container(
                  // padding: EdgeInsets.only(bottom: 40),
                  child: Form(
                    key: saleController.formKey,
                    child: SingleChildScrollView(
                      child: Container(
                        child: Column(
                          children: [
                            Card(
                              elevation: 2,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 15),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    //===========================Bill No.
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "बिल न. ",
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          FormBuilderTextField(
                                            name: "bill_no",
                                            readOnly:
                                                saleController.readOnlyFlag,
                                            autocorrect: false,
                                            keyboardType: TextInputType.text,
                                            textInputAction:
                                                TextInputAction.done,
                                            textAlign: TextAlign.right,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                labelText: "Bill No."),
                                            controller:
                                                saleController.billNoCtrl,
                                            onChanged: (value) {
                                              saleController.transaction.value
                                                  .txnRefNumberChar = value;
                                              saleController.transaction
                                                  .refresh();
                                            },
                                          ),
                                        ],
                                      ),
                                    ),

                                    SizedBox(
                                      width: 20,
                                    ),

                                    //===========================Transaction Date
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "मिति",
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          CustomDatePickerTextField(
                                            readOnly:
                                                saleController.readOnlyFlag,
                                            maxBSDate: NepaliDateTime.now(),
                                            initialValue: saleController
                                                .transaction.value.txnDateBS,
                                            onChange: (selectedDate) {
                                              saleController.transaction.value
                                                  .txnDateBS = selectedDate;
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),

                            Card(
                              elevation: 2,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 10),
                                child: Column(
                                  children: [
                                    //===============================================Party Balance
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Row(
                                          children: [
                                            Container(
                                              width: 20,
                                              height: 20,
                                              child: Checkbox(
                                                activeColor: colorPrimary,
                                                checkColor: Colors.white,
                                                value: saleController
                                                    .isCashSaleSelected,
                                                onChanged:
                                                    saleController.readOnlyFlag
                                                        ? null
                                                        : (value) {
                                                            saleController
                                                                    .isCashSaleSelected =
                                                                value!;
                                                            if (value) {
                                                              saleController.onChangeParty(
                                                                  LedgerDetailModel(
                                                                      ledgerId:
                                                                          CASH_SALES_LEDGER_ID,
                                                                      ledgerTitle:
                                                                          CASH_SALES_LEDGER_NAME));
                                                            } else {
                                                              saleController.onChangeParty(
                                                                  LedgerDetailModel(
                                                                      ledgerId:
                                                                          null));
                                                            }
                                                          },
                                              ),
                                            ),
                                            Text(
                                                "  खुद्रा बिक्री\n  (Cash Sale)",
                                                style: labelStyle2)
                                          ],
                                        ),
                                        RichText(
                                          textAlign: TextAlign.right,
                                          text: TextSpan(
                                              text: "पुरानो बाँकी: ",
                                              style:
                                                  TextStyle(color: textColor),
                                              children: [
                                                if (null !=
                                                    saleController.transaction
                                                        .value.ledgerId) ...{
                                                  TextSpan(
                                                    text:
                                                        "${saleController.selectedLedger.value.balanceAmount ?? 0}",
                                                    style: TextStyle(
                                                        color: ((saleController
                                                                        .selectedLedger
                                                                        .value
                                                                        .balanceAmount ??
                                                                    0) >=
                                                                0.0)
                                                            ? colorGreenDark
                                                            : colorRedLight),
                                                  )
                                                }
                                              ]),
                                        )
                                      ],
                                    ),
                                    Container(
                                      height: 10,
                                    ),
                                    //===============================================Party Field
                                    if (!saleController.isCashSaleSelected)
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'ग्राहकको नाम',
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          LedgerAutoCompleteTextFieldWithAdd(
                                              excludedIDS: [
                                                CASH_PURCHASE_LEDGER_ID
                                              ],
                                              enableFlag:
                                                  !saleController.readOnlyFlag,
                                              labelText: "Customer Name",
                                              controller:
                                                  saleController.partyNameCtrl,
                                              onChangedFn: (value) {
                                                // Log.d("called i text change");
                                              },
                                              ledgetID: saleController
                                                  .transaction.value.ledgerId,
                                              onSuggestionSelectedFn:
                                                  (LedgerDetailModel ledger) {
                                                saleController
                                                    .onChangeParty(ledger);
                                              })
                                        ],
                                      ),

                                    ...isCashSale
                                        ? [
                                            const SizedBox(
                                              height: 10,
                                            ),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "खुद्रा ग्राहकको नाम",
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                FormBuilderTextField(
                                                  name: "display_text",
                                                  // readOnly:
                                                  //     (null == state.selectedLedger.ledgerId)
                                                  //         ? false
                                                  //         : true,
                                                  readOnly: saleController
                                                      .readOnlyFlag,
                                                  autocorrect: false,
                                                  textInputAction:
                                                      TextInputAction.done,
                                                  style: formFieldTextStyle,
                                                  decoration:
                                                      formFieldStyle.copyWith(
                                                          labelText:
                                                              "Billing Name"),
                                                  controller: saleController
                                                      .displayTextCtrl,
                                                  onChanged: (value) {
                                                    saleController
                                                        .transaction
                                                        .value
                                                        .txnDisplayName = value;
                                                    saleController.transaction
                                                        .refresh();
                                                  },
                                                ),
                                              ],
                                            )
                                          ]
                                        : [],

                                    const SizedBox(
                                      height: 10,
                                    ),
                                    if (!isCashSale)
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          //===================================Mobile
                                          Container(
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.3,
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'फोन नम्बर',
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                FormBuilderTextField(
                                                    name: "mobile",
                                                    readOnly: true,
                                                    autocorrect: false,
                                                    keyboardType: TextInputType
                                                        .number,
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    inputFormatters: [
                                                      FilteringTextInputFormatter
                                                          .digitsOnly
                                                    ],
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            labelText:
                                                                "Contact no.",
                                                            hintText:
                                                                "Contact no"),
                                                    controller: saleController
                                                        .mobileCtrl
                                                    // controller: state.mobileCtrl,
                                                    ),
                                              ],
                                            ),
                                          ),

                                          //===================================Address
                                          Container(
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.5,
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'ठेगाना',
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                FormBuilderTextField(
                                                  name: "address",
                                                  readOnly: true,
                                                  autocorrect: false,
                                                  keyboardType:
                                                      TextInputType.text,
                                                  textInputAction:
                                                      TextInputAction.done,
                                                  style: formFieldTextStyle,
                                                  decoration:
                                                      formFieldStyle.copyWith(
                                                          labelText: "Address"),
                                                  controller: saleController
                                                      .addressCtrl,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    if (!isCashSale)
                                      const SizedBox(height: 10.0),

                                    //=====================================PAN No Field
                                    if (!isCashSale)
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "पान / मु. अ. कर नम्बर",
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          FormBuilderTextField(
                                              name: "pan_no",
                                              readOnly: true,
                                              autocorrect: false,
                                              keyboardType:
                                                  TextInputType.number,
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .digitsOnly
                                              ],
                                              textInputAction:
                                                  TextInputAction.done,
                                              style: formFieldTextStyle,
                                              decoration:
                                                  formFieldStyle.copyWith(
                                                      labelText: "PAN/VAT No."),
                                              controller:
                                                  saleController.panNoCtrl),
                                        ],
                                      ),
                                  ],
                                ),
                              ),
                            ),

                            //================================================Item Container
                            Card(
                              child: Container(
                                  width: double.infinity,
                                  padding: EdgeInsets.all(0.0),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: double.infinity,
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 15, vertical: 8),
                                        decoration: BoxDecoration(
                                          color: colorPrimaryLight,
                                          borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(4),
                                            topRight: Radius.circular(4),
                                          ),
                                        ),
                                        child: DefaultTextStyle(
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontSize: 15,
                                          ),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                "बिक्री सामानहरु (Sale Items)",
                                              ),
                                              Text(
                                                "Total Item: ${saleController.items.length}",
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                      Container(
                                        constraints:
                                            BoxConstraints(maxHeight: 300),
                                        child: getItemListView(
                                            context,
                                            saleController.items,
                                            saleController),
                                      ),
                                      Container(
                                        margin:
                                            EdgeInsets.symmetric(vertical: 10),
                                        child: Center(
                                          child: ElevatedButton(
                                            style: ElevatedButton.styleFrom(
                                                backgroundColor: colorPrimary,
                                                foregroundColor:
                                                    colorPrimaryLightest,
                                                shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                  20,
                                                ))),
                                            child: Padding(
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 10,
                                                      horizontal: 10),
                                              child: Text(
                                                "बिक्री सामान थप्नुहोस्  (Add Sales Item)",
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 15,
                                                ),
                                              ),
                                            ),
                                            onPressed: saleController
                                                    .readOnlyFlag
                                                ? null
                                                : () async {
                                                    var returnedData =
                                                        await showDialog(
                                                            context: context,
                                                            useRootNavigator:
                                                                true,
                                                            barrierDismissible:
                                                                false,
                                                            builder: (d_c) {
                                                              return AlertDialog(
                                                                  insetPadding: EdgeInsets.symmetric(
                                                                      horizontal:
                                                                          10,
                                                                      vertical:
                                                                          10),
                                                                  contentPadding:
                                                                      EdgeInsets
                                                                          .zero,
                                                                  clipBehavior: Clip
                                                                      .hardEdge,
                                                                  content:
                                                                      Container(
                                                                    width: MediaQuery.of(context)
                                                                            .size
                                                                            .width -
                                                                        20,
                                                                    child:
                                                                        AddSaleBilledItemScreenView(),
                                                                  ));
                                                            });

                                                    if (null != returnedData) {
                                                      print(
                                                          "this is returend data ${returnedData.billedItem}");
                                                      // saleController.formKey.currentState.
                                                      if (null !=
                                                          returnedData
                                                              .billedItem) {
                                                        saleController.items
                                                            .add(returnedData
                                                                .billedItem);
                                                        saleController.items
                                                            .refresh();
                                                        saleController
                                                            .recalculateForItems();
                                                        // Log.d(
                                                        //     "got  billed item ${returnedData.billedItem.toJson()}");
                                                      }
                                                    }
                                                  },
                                          ),
                                        ),
                                      ),
                                    ],
                                  )),
                            ),

                            //===============================================Total Amount
                            Card(
                              elevation: 2,
                              child: Column(
                                children: [
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                        vertical: 4, horizontal: 8),
                                    decoration: BoxDecoration(
                                        color: colorPrimaryLight,
                                        borderRadius: BorderRadius.only(
                                            topLeft: Radius.circular(4),
                                            topRight: Radius.circular(4))),
                                    child: Center(
                                        child: Text(
                                      "Bill Totals (जम्मा बिल)",
                                      style: TextStyle(
                                          color: Colors.white, fontSize: 16),
                                    )),
                                  ),
                                  SizedBox(
                                    height: 10,
                                  ),
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                      horizontal: 10,
                                    ),
                                    child: Column(
                                      children: [
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "उप कुल",
                                              style: labelStyle2,
                                            ),
                                            SizedBox(height: 5.0),
                                            FormBuilderTextField(
                                              name: "txn_subtotal",
                                              readOnly:
                                                  saleController.readOnlyFlag
                                                      ? true
                                                      : (0 <
                                                              saleController
                                                                  .items.length)
                                                          ? true
                                                          : false,
                                              autocorrect: false,
                                              keyboardType: TextInputType
                                                  .numberWithOptions(
                                                      decimal: true),
                                              textInputAction:
                                                  TextInputAction.done,
                                              style: formFieldTextStyle,
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .allow(RegExp(
                                                        r'^(\d+)?\.?\d{0,2}'))
                                              ],
                                              maxLength: 10,
                                              decoration:
                                                  formFieldStyle.copyWith(
                                                      hintText: "Sub Total",
                                                      counterText: ''),
                                              textAlign: TextAlign.end,
                                              controller: saleController
                                                  .subTotalAmountCtrl,
                                              onChanged: (value) {
                                                if (value != null ||
                                                    (value != null &&
                                                        value.isNotEmpty)) {
                                                  saleController
                                                          .subTotalAmountCtrl
                                                          .selection =
                                                      TextSelection.fromPosition(
                                                          TextPosition(
                                                              offset: saleController
                                                                  .subTotalAmountCtrl
                                                                  .text
                                                                  .length));
                                                  saleController
                                                      .onSubTotalIndividualChange(
                                                          value,
                                                          editorTag:
                                                              'txn_subtotal');
                                                }
                                              },
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 20,
                                        ),

                                        // =============================================Discount
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "छुट (Discount)",
                                              style: labelStyle2,
                                            ),
                                            SizedBox(height: 5.0),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Container(
                                                  width: 80,
                                                  child: FormBuilderTextField(
                                                    // focusNode: saleController.node,
                                                    name:
                                                        "txn_discount_percent",
                                                    readOnly: saleController
                                                            .readOnlyFlag ||
                                                        (!hasSubTotal),
                                                    autocorrect: false,
                                                    keyboardType: TextInputType
                                                        .numberWithOptions(
                                                            decimal: true),
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            suffix: Text("%"),
                                                            labelText: "%"),
                                                    textAlign: TextAlign.end,
                                                    controller: saleController
                                                        .discountPercentageCtrl,
                                                    onChanged: (value) {
                                                      if (value!.isEmpty) {
                                                        //remove keyboard
                                                        FocusScope.of(context)
                                                            .unfocus();
                                                      }
                                                      saleController
                                                              .discountPercentageCtrl
                                                              .selection =
                                                          TextSelection.fromPosition(
                                                              TextPosition(
                                                                  offset: saleController
                                                                      .discountPercentageCtrl
                                                                      .text
                                                                      .length));
                                                      saleController
                                                          .updateDiscountPercentage(
                                                        value ?? "",
                                                        editorTag:
                                                            'txn_discount_percent',
                                                        // context: context
                                                      );
                                                    },
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: 20,
                                                ),
                                                Expanded(
                                                  child: Container(
                                                    child: FormBuilderTextField(
                                                      name:
                                                          "txn_discount_amount",
                                                      readOnly: saleController
                                                              .readOnlyFlag ||
                                                          (!hasSubTotal),
                                                      autocorrect: false,
                                                      keyboardType: TextInputType
                                                          .numberWithOptions(
                                                              decimal: true),
                                                      textInputAction:
                                                          TextInputAction.done,
                                                      style: formFieldTextStyle,
                                                      decoration: formFieldStyle
                                                          .copyWith(
                                                              labelText:
                                                                  "छुट रकम (Dis. Amount)"),
                                                      textAlign: TextAlign.end,
                                                      controller: saleController
                                                          .discountAmountCtrl,
                                                      onChanged: (value) {
                                                        saleController
                                                                .discountAmountCtrl
                                                                .selection =
                                                            TextSelection.fromPosition(
                                                                TextPosition(
                                                                    offset: saleController
                                                                        .discountAmountCtrl
                                                                        .text
                                                                        .length));
                                                        if (value != null ||
                                                            (value != null &&
                                                                value
                                                                    .isNotEmpty)) {
                                                          saleController
                                                              .updateDiscountAmount(
                                                                  value,
                                                                  editorTag:
                                                                      "txn_discount_amount");
                                                        }
                                                      },
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 25,
                                        ),

                                        // =============================================VAT
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Container(
                                                  width: 20,
                                                  height: 20,
                                                  child: Checkbox(
                                                    activeColor: colorPrimary,
                                                    checkColor: Colors.white,
                                                    value: saleController
                                                        .isVatEnabled,
                                                    onChanged: (saleController
                                                                .readOnlyFlag ||
                                                            (!(hasSubTotal)))
                                                        ? null
                                                        : (value) {
                                                            saleController
                                                                .onToggleVat(
                                                                    value!);
                                                          },
                                                  ),
                                                ),
                                                Text("  मु.अ. कर (VAT)",
                                                    style: labelStyle2)
                                              ],
                                            ),
                                            SizedBox(height: 10.0),
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                Container(
                                                  width: 80,
                                                  child: FormBuilderTextField(
                                                    name: "txn_tax_percent",
                                                    readOnly: true,
                                                    autocorrect: false,
                                                    keyboardType: TextInputType
                                                        .numberWithOptions(
                                                            decimal: true),
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            suffix: Text("%"),
                                                            labelText: "%"),
                                                    textAlign: TextAlign.end,
                                                    controller: saleController
                                                        .vatPercentCtrl,
                                                    onChanged: (value) {
                                                      saleController
                                                          .onvatPercentChange(
                                                              value ?? "0.00",
                                                              editorTag:
                                                                  'txn_tax_percent');
                                                    },
                                                  ),
                                                ),
                                                SizedBox(
                                                  width: 20,
                                                ),
                                                Expanded(
                                                  child: Container(
                                                    child: FormBuilderTextField(
                                                      name: "txn_tax_amount",
                                                      readOnly: true,
                                                      autocorrect: false,
                                                      keyboardType: TextInputType
                                                          .numberWithOptions(
                                                              decimal: true),
                                                      textInputAction:
                                                          TextInputAction.done,
                                                      style: formFieldTextStyle,
                                                      decoration: formFieldStyle
                                                          .copyWith(
                                                              labelText:
                                                                  "मु.अ. कर रकम (VAT Amount) "),
                                                      textAlign: TextAlign.end,
                                                      controller: saleController
                                                          .vatAmountCtrl,
                                                      onChanged: (value) {},
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 5,
                                        ),

                                        Divider(
                                          height: 5,
                                        ),
                                        Divider(
                                          height: 0,
                                        ),
                                        SizedBox(
                                          height: 15,
                                        ),

                                        // =============================================Total Amount
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "कुल रकम",
                                              style: labelStyle2,
                                            ),
                                            SizedBox(
                                              height: 5,
                                            ),
                                            Container(
                                              child: FormBuilderTextField(
                                                  name: "txn_total",
                                                  readOnly: true,
                                                  autocorrect: false,
                                                  keyboardType: TextInputType
                                                      .numberWithOptions(
                                                          decimal: true),
                                                  textInputAction:
                                                      TextInputAction.done,
                                                  style: formFieldTextStyle,
                                                  decoration:
                                                      formFieldStyle.copyWith(
                                                          labelText:
                                                              "Total Amount"),
                                                  textAlign: TextAlign.end,
                                                  controller: saleController
                                                      .totalAmountCtrl),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 25,
                                        ),

                                        // =============================================Paid Amount

                                        Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.end,
                                            children: [
                                              Expanded(
                                                child: Column(
                                                  // crossAxisAlignment:
                                                  //     CrossAxisAlignment.start,
                                                  children: [
                                                    Row(
                                                      children: [
                                                        Container(
                                                          width: 20,
                                                          height: 20,
                                                          child: Checkbox(
                                                            activeColor:
                                                                colorPrimary,
                                                            checkColor:
                                                                Colors.white,
                                                            value: isCashSale
                                                                ? true
                                                                : (saleController
                                                                    .isReceived),
                                                            onChanged: (saleController
                                                                        .readOnlyFlag ||
                                                                    isCashSale ||
                                                                    (!hasSubTotal))
                                                                ? null
                                                                : (value) {
                                                                    saleController
                                                                            .isReceived =
                                                                        value!;
                                                                    if (value) {
                                                                      saleController
                                                                          .receivedAmountCtrl
                                                                          .text = (saleController.transaction.value.txnTotalAmount ??
                                                                              0.0)
                                                                          .toStringAsFixed(
                                                                              2);
                                                                      saleController
                                                                          .dueAmountCtrl
                                                                          .text = "0.00";

                                                                      saleController
                                                                          .transaction
                                                                          .value
                                                                          .txnCashAmount = (saleController
                                                                              .transaction
                                                                              .value
                                                                              .txnTotalAmount ??
                                                                          0.0);

                                                                      saleController
                                                                          .transaction
                                                                          .value
                                                                          .txnBalanceAmount = 0.00;
                                                                    } else {
                                                                      saleController
                                                                          .receivedAmountCtrl
                                                                          .text = "0.00";
                                                                    }
                                                                  },
                                                          ),
                                                        ),
                                                        Text("  प्राप्त रकम",
                                                            style: labelStyle2)
                                                      ],
                                                    ),
                                                    SizedBox(
                                                      height: 10,
                                                    ),
                                                    PaymentModeSelector(
                                                      onChangedFn: (v) {
                                                        saleController
                                                            .transaction
                                                            .value
                                                            .txnPaymentTypeId = v;

                                                        saleController
                                                                .transaction
                                                                .value
                                                                .txnPaymentReference =
                                                            null;
                                                        saleController
                                                                .transaction
                                                                .value
                                                                .chequeIssueDateBS =
                                                            null;
                                                        saleController
                                                            .transaction
                                                            .refresh();
                                                      },
                                                      paymentModeID:
                                                          saleController
                                                              .transaction
                                                              .value
                                                              .txnPaymentTypeId,
                                                      enableFlag: saleController
                                                          .readOnlyFlag,
                                                    ),
                                                  ],
                                                ),
                                              ),
                                              SizedBox(
                                                width: 10,
                                              ),
                                              Expanded(
                                                child: FormBuilderTextField(
                                                  name: "txn_cash_amount",
                                                  readOnly: (saleController
                                                          .readOnlyFlag ||
                                                      !hasSubTotal ||
                                                      isCashSale),
                                                  autocorrect: false,
                                                  keyboardType: TextInputType
                                                      .numberWithOptions(
                                                          decimal: true),
                                                  textInputAction:
                                                      TextInputAction.done,
                                                  inputFormatters: [
                                                    FilteringTextInputFormatter
                                                        .allow(RegExp(
                                                            r'^\d*\.?\d*$')),
                                                  ],
                                                  style: formFieldTextStyle,
                                                  decoration:
                                                      formFieldStyle.copyWith(
                                                          labelText:
                                                              "Received Amount"),
                                                  textAlign: TextAlign.end,
                                                  controller: saleController
                                                      .receivedAmountCtrl,
                                                  onChanged: (value) {
                                                    saleController
                                                        .changeReceivedAmount(
                                                            value ?? "0.00",
                                                            editorTag:
                                                                'txn_cash_amount');
                                                  },
                                                ),
                                              )
                                            ]),

                                        ...(saleController.transaction.value
                                                    .txnPaymentTypeId ==
                                                PAYMENT_MODE_CHEQUE_ID)
                                            ? [
                                                SizedBox(
                                                  height: 25,
                                                ),
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text("चेक/भौचर न.",
                                                        style: labelStyle2),
                                                    SizedBox(
                                                      height: 10,
                                                    ),
                                                    TextField(
                                                        autocorrect: false,
                                                        readOnly: saleController
                                                            .readOnlyFlag,
                                                        style:
                                                            formFieldTextStyle,
                                                        decoration: formFieldStyle
                                                            .copyWith(
                                                                labelText:
                                                                    "Cheque/Voucher No."),
                                                        controller:
                                                            saleController
                                                                .paymentRefCtrl,
                                                        onChanged: (v) {
                                                          saleController
                                                              .transaction
                                                              .value
                                                              .txnPaymentReference = v;
                                                          saleController
                                                              .transaction
                                                              .refresh();
                                                        }),
                                                  ],
                                                ),
                                              ]
                                            : [],
                                        if (saleController.transaction.value
                                                .txnPaymentTypeId ==
                                            PAYMENT_MODE_CHEQUE_ID) ...[
                                          SizedBox(
                                            height: 25,
                                          ),
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text("चेक मिति",
                                                  style: labelStyle2),
                                              SizedBox(
                                                height: 10,
                                              ),
                                              CustomDatePickerTextField(
                                                labelText: "Cheque Date",
                                                readOnly:
                                                    saleController.readOnlyFlag,
                                                // maxBSDate: NepaliDateTime.now(),
                                                initialValue: saleController
                                                    .transaction
                                                    .value
                                                    .chequeIssueDateBS,
                                                onChange: (selectedDate) {
                                                  saleController
                                                          .transaction
                                                          .value
                                                          .chequeIssueDateBS =
                                                      selectedDate;
                                                },
                                              ),
                                            ],
                                          )
                                        ],

                                        SizedBox(
                                          height: 25,
                                        ),

                                        // =============================================Balance Amount
                                        ...isCashSale
                                            ? []
                                            : [
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text("बाँकी रहेको रकम",
                                                        style: labelStyle2),
                                                    SizedBox(
                                                      height: 10,
                                                    ),
                                                    FormBuilderTextField(
                                                        name:
                                                            "txn_balance_amount",
                                                        readOnly: true,
                                                        autocorrect: false,
                                                        keyboardType: TextInputType
                                                            .numberWithOptions(
                                                                decimal: true),
                                                        textInputAction:
                                                            TextInputAction
                                                                .done,
                                                        style:
                                                            formFieldTextStyle,
                                                        decoration: formFieldStyle
                                                            .copyWith(
                                                                labelText:
                                                                    "Balance Due"),
                                                        textAlign:
                                                            TextAlign.end,
                                                        controller:
                                                            saleController
                                                                .dueAmountCtrl),
                                                  ],
                                                ),
                                                SizedBox(
                                                  height: 20,
                                                ),
                                              ],
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            //===============================================Image
                            Container(
                              width: double.infinity,
                              child: Card(
                                elevation: 2,
                                child: Container(
                                  child: Container(
                                    // color: Colors.red,
                                    height: 140,
                                    width: 100,
                                    // width: ,
                                    // child: (null==state.selectImage)?
                                    child: saleController.fileUploadPermission
                                        ? FormBuilderImagePicker(
                                            name: "image_picker",
                                            bottomSheetPadding:
                                                EdgeInsets.all(0),
                                            decoration: InputDecoration(
                                              border: InputBorder.none,
                                            ),
                                            maxImages: 2,
                                            iconColor: colorPrimaryLight,
                                            initialValue:
                                                saleController.imageList,
                                            // validators: [],
                                            onChanged: (_fls) async {
                                              if (_fls != null &&
                                                  _fls.isNotEmpty) {
                                                print(
                                                    "this is type ${_fls.runtimeType}}");
                                                saleController.files.clear();
                                                _fls.forEach((element) {
                                                  // print(
                                                  //     "this is kera _${element.runtimeType}");
                                                });
                                              }
                                              // saleController.files = _fls;
                                            },
                                          )
                                        : Container(
                                            height: 130,
                                            width: 130,
                                            decoration: BoxDecoration(
                                              color: Colors.black12,
                                              borderRadius:
                                                  BorderRadius.circular(10.0),
                                            ),
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                const Icon(
                                                  Icons
                                                      .image_not_supported_outlined,
                                                  size: 50.0,
                                                  color: Colors.black26,
                                                ),
                                                const SizedBox(height: 10.0),
                                                Text(
                                                  "File Upload \nNot Allowed",
                                                  style: TextStyle(
                                                    color: Colors.black54,
                                                    fontSize: 12.0,
                                                    fontFamily:
                                                        'HelveticaRegular',
                                                  ),
                                                ),
                                              ],
                                            )),
                                  ),
                                ),
                              ),
                            ),

                            //===============================================Description
                            Card(
                              elevation: 2,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 10),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "कैफियत",
                                      style: labelStyle2,
                                    ),
                                    SizedBox(height: 5.0),
                                    FormBuilderTextField(
                                      name: "description",
                                      readOnly: saleController.readOnlyFlag,
                                      autocorrect: false,
                                      textAlign: TextAlign.start,
                                      textInputAction: TextInputAction.newline,
                                      style: formFieldTextStyle,
                                      decoration: formFieldStyle.copyWith(
                                          labelText: "Remarks"),
                                      minLines: 4,
                                      maxLines: 4,
                                      controller: saleController.descCtrl,
                                      onChanged: (value) {
                                        saleController.transaction.value
                                            .txnDescription = value;
                                        saleController.transaction.refresh();
                                      },
                                      // validators: [],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),

          //=================================================Save button
          bottomNavigationBar: BottomSaveCancelButton(
            shadow: false,
            hasNew: !saleController.editFlag,
            onSaveAndNewBtnPressedFn: () {
              this.onSave(context, forNew: true);
            },
            enableFlag: !saleController.readOnlyFlag,
            onSaveBtnPressedFn: (saleController.readOnlyFlag)
                ? null
                : () async {
                    this.onSave(context);
                  },
          ),
        ));
      }
    });
  }
}

Widget getItemListView(
  BuildContext context,
  List<LineItemDetailModel> _items,
  SaleController saleController,
) {
  // return Container(height: 40, color: Colors.red);
  ScrollController scrollController = ScrollController();
  var listView = Scrollbar(
    controller: scrollController,
    isAlwaysShown: true,
    child: ListView.builder(
        controller: scrollController,
        itemCount: _items.length,
        shrinkWrap: true,
        itemBuilder: (context, int index) {
          LineItemDetailModel _item = _items[index];
          return Row(children: [
            Expanded(
                child: GestureDetector(
              onTap: saleController.readOnlyFlag
                  ? null
                  : () async {
                      var returnedData = await showDialog(
                          context: context,
                          useRootNavigator: true,
                          barrierDismissible: false,
                          builder: (d_c) {
                            return AlertDialog(
                                insetPadding: EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 10),
                                contentPadding: EdgeInsets.zero,
                                clipBehavior: Clip.hardEdge,
                                content: Container(
                                  width: MediaQuery.of(context).size.width - 20,
                                  child: AddSaleBilledItemScreenView(
                                    lineItemModel: _item,
                                  ),
                                ));
                          });
                      if (null != returnedData) {
                        if (returnedData.deleteFlag) {
                          saleController.items.removeAt(index);
                        } else if (null != returnedData.billedItem) {
                          saleController.items.replaceRange(
                              index, 1, [returnedData.billedItem]);
                          saleController.recalculateForItems();
                        }
                        saleController.items.refresh();
                        saleController.recalculateForItems();
                      }
                    },
              child: Card(
                elevation: 2,
                margin: EdgeInsets.only(
                    left: 5,
                    right: 5,
                    top: (0 == index) ? 15 : 8,
                    bottom: ((_items.length - 1) == index) ? 20 : 8),
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                  decoration: BoxDecoration(color: Colors.black12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // =============================================item name
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              _item.itemName ?? "",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: colorPrimaryDark),
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          Container(
                              width: 15,
                              child: GestureDetector(
                                onTap: saleController.readOnlyFlag
                                    ? null
                                    : () {
                                        saleController.items.removeAt(index);
                                        saleController.items.refresh();
                                        saleController.recalculateForItems();
                                      },
                                child: Icon(Icons.delete,
                                    color: (saleController.readOnlyFlag)
                                        ? Colors.black54
                                        : Colors.red),
                              )),
                        ],
                      ),
                      SizedBox(
                        height: 5,
                      ),

                      // =============================================gross amount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            width: 75,
                            child: Text(
                              "Amount",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  color: Colors.black54),
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              "${_item.quantity} ${_item.lineItemUnitName ?? ""} X ${formatCurrencyAmount(_item.pricePerUnit ?? 0.00)} = ${formatCurrencyAmount(_item.grossAmount ?? 0.00, false)}",
                              textAlign: TextAlign.right,
                              style: TextStyle(fontSize: 12, color: textColor),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 5,
                      ),

                      // =============================================Discount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            width: 100,
                            child: Text(
                              "Discount(%): ${_item.discountPercent}",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  color: Colors.black54),
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              " = ${formatCurrencyAmount(_item.discountAmount ?? 0.00, false)}",
                              textAlign: TextAlign.right,
                              style: TextStyle(fontSize: 12, color: textColor),
                            ),
                          ),
                        ],
                      ),
                      Divider(
                        height: 5,
                      ),
                      Divider(
                        height: 0,
                      ),

                      // =============================================netAmount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              formatCurrencyAmount(
                                  _item.totalAmount ?? 0.00, false),
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: colorPrimary),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            )),
          ]);
        }),
  );

  return listView;
}
