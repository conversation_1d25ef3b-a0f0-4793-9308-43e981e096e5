// ignore_for_file: prefer_final_fields

import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/registration_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/registration_detail_repository.dart';
import 'package:tuple/tuple.dart';

class RegistrationDetailController extends GetxController {
  RegistrationDetailController() {
    onInit();
  }
  final String tag = "RegistrationDetailController";

  var _isLoading = false.obs;
  get isLoading => _isLoading.value;

  var _registrationDetail = RegistrationDetailModel().obs;
  RegistrationDetailModel get registrationDetail => _registrationDetail.value;

  var _logo = ImageModel().obs;
  ImageModel get logo => _logo.value;

  // Cache flag to prevent unnecessary reloading
  var _isDataLoaded = false.obs;
  bool get isDataLoaded => _isDataLoaded.value;

  RegistrationDetailRepository _registrationDetailRepository =
      RegistrationDetailRepository();

  @override
  Future<void> onInit() async {
    // Only load data if not already loaded
    if (!_isDataLoaded.value) {
      await loadData();
    }
    super.onInit();
  }

  // Separate method to load data
  Future<void> loadData() async {
    _isLoading(true);
    _isLoading.refresh();

    try {
      Tuple2<RegistrationDetailModel, ImageModel> dt =
          await _registrationDetailRepository.getRegistrationDetailWithImage();
      _registrationDetail.value = dt.item1;
      _registrationDetail.refresh();
      _logo.value = dt.item2;
      _logo.refresh();
      _isDataLoaded(true);
      _isDataLoaded.refresh();
    } catch (e) {
      print("Error loading registration data: $e");
    } finally {
      _isLoading(false);
      _isLoading.refresh();
    }
  }

  // Method to refresh data when needed (e.g., after profile update)
  Future<void> refreshData() async {
    _isDataLoaded(false);
    await loadData();
  }
}
