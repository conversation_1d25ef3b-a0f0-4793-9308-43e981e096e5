import 'dart:convert';

import 'package:flutter/material.dart';

// DropdownItemRenderer defaltItemRenderer = ({dynamic optionItem}) {
//   return Container(
//       padding: EdgeInsets.all(11),
//       child: Text(
//         optionItem != null ? optionItem['value'] ?? "" : "",
//         // style: widget.style ?? style,
//       ));
// };

// typedef DropdownItemRenderer = Widget Function({dynamic optionItem});

class CustomDropdown extends StatefulWidget {
  final List<dynamic>? options;
  final Function? onChange;
  final Function? itemRenderer;
  final dynamic value;
  final bool isSearchable;
  final bool allowClear;
  final String placeholder;
  final InputDecoration? decoration;
  final TextStyle? style;
  final bool? readOnly;
  final bool? borderless;

  const CustomDropdown(
      {Key? key,
      this.options,
      this.borderless = false,
      this.onChange,
      this.value,
      this.isSearchable = true,
      this.allowClear = true,
      this.placeholder = "Select Item",
      this.decoration,
      this.style,
      this.itemRenderer, //= defaltItemRenderer,
      this.readOnly = false})
      : super(key: key);

  @override
  _CustomDropdownState createState() => _CustomDropdownState();
}

class _CustomDropdownState extends State<CustomDropdown> {
  final FocusNode _focusNode = FocusNode();

  OverlayEntry? _overlayEntry;
  List<dynamic> results = [];
  final TextEditingController _displayController = TextEditingController();
  final TextEditingController _searchController = TextEditingController();

  final LayerLink _layerLink = LayerLink();

  InputDecoration? decoration;
  InputDecoration? displayDecoration;

  TextStyle style = const TextStyle(color: Colors.black87);

  @override
  void didUpdateWidget(covariant CustomDropdown oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.options?.length != widget.options?.length ||
        jsonEncode(oldWidget.options) != jsonEncode(widget.options)) {
      setState(() {
        results = widget.options ?? [];
      });

      if (_overlayEntry != null) {
        try {
          _overlayEntry!.markNeedsBuild();
        } catch (e) {
          // If there's an error, recreate the overlay entry
          _overlayEntry?.remove();
          _overlayEntry = null;
        }
      }
    }

    // Update display text if value changed
    if (oldWidget.value != widget.value) {
      for (var e in (widget.options ?? [])) {
        if (e['key'] == widget.value) {
          _displayController.text = e['value'] ?? "";
          break;
        }
      }
    }
  }

  @override
  void initState() {
    super.initState();
    results = widget.options ?? [];
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        _overlayEntry?.remove();
        _overlayEntry = null;
      }
    });

    decoration = widget.decoration ??
        const InputDecoration(
          contentPadding: EdgeInsets.all(11),
          floatingLabelBehavior: FloatingLabelBehavior.never,
          enabledBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(4)),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(4)),
          ),
        );
  }

  _showOptions() {
    if (null == _overlayEntry) {
      _overlayEntry = _createOverlayEntry();
      Overlay.of(context).insert(_overlayEntry!);
      _focusNode.requestFocus();
    } else {
      _overlayEntry?.remove();
      _overlayEntry = null;
    }
  }

  Widget _defaultItemRenderer(dynamic optionItem) {
    return Container(
        padding: const EdgeInsets.all(11),
        child: Text(
          optionItem != null ? optionItem['value'] ?? "" : "",
          style: widget.style ?? style,
        ));
  }

  OverlayEntry _createOverlayEntry() {
    final renderBox = context.findRenderObject();

    Function itemRenderer = widget.itemRenderer ?? _defaultItemRenderer;

    return OverlayEntry(
        builder: (context) => Positioned(
              width: MediaQuery.of(context).size.width * .92,
              child: CompositedTransformFollower(
                link: _layerLink,
                showWhenUnlinked: false,
                offset: Offset(0.0, MediaQuery.of(context).size.height * 0),
                child: Material(
                  elevation: 4.0,
                  borderRadius: BorderRadius.circular(4),
                  child: GestureDetector(
                    onTap: () {},
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(5),
                          height: 55,
                          child: TextFormField(
                            enabled: !widget.readOnly!,
                            readOnly: (widget.readOnly!)
                                ? true
                                : widget.isSearchable
                                    ? false
                                    : true,
                            controller: _searchController,
                            focusNode: _focusNode,
                            style: widget.style ?? style,
                            decoration: widget.decoration ?? decoration,
                            onChanged: (val) {
                              setState(() {
                                results = widget.options!
                                    .where((element) => element['value']
                                        .toString()
                                        .toLowerCase()
                                        .contains(val.toLowerCase()))
                                    .toList();
                              });
                              _overlayEntry!.markNeedsBuild();
                            },
                          ),
                        ),
                        Container(
                          constraints: const BoxConstraints(maxHeight: 200),
                          child: ListView(
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            children: <Widget>[
                              ...results.map((optionItem) {
                                return Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    onTap: () {
                                      widget.onChange!(optionItem!['key']);
                                      _displayController.text =
                                          optionItem['value'] ?? "";

                                      setState(() {
                                        results = widget.options!;
                                      });
                                      _overlayEntry?.remove();
                                      _overlayEntry = null;
                                      _searchController.text = "";
                                    },
                                    child: itemRenderer(optionItem),
                                  ),
                                );
                              })
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ));
  }

  @override
  Widget build(BuildContext context) {
    String? selectedValueText;
    for (var e in (widget.options ?? [])) {
      if (e['key'] == widget.value) {
        selectedValueText = _displayController.text = e['value'];
        break;
      }
    }

    bool hideDropdownIcon = (!widget.allowClear)
        ? false
        // ignore: unnecessary_null_comparison
        : selectedValueText == null
            ? false
            : true;
    return CompositedTransformTarget(
      link: _layerLink,
      child: Stack(children: [
        InkWell(
          onTap: () {
            if (!widget.readOnly!) _showOptions();
          },
          child: AbsorbPointer(
            child: TextFormField(
                enabled: !widget.readOnly!,
                readOnly: widget.readOnly!,
                controller: _displayController,
                style: widget.style ?? style,
                decoration: decoration!.copyWith(
                  hintStyle: const TextStyle(color: Colors.black54),
                  hintText: widget.placeholder,
                  border: widget.borderless! ? InputBorder.none : null,
                  suffixIcon: hideDropdownIcon
                      ? const SizedBox()
                      : const Icon(
                          Icons.arrow_drop_down,
                          color: Colors.black54,
                        ),
                )),
          ),
        ),
        if (!widget.readOnly! && widget.allowClear)
          Positioned(
            right: 10,
            bottom: MediaQuery.of(context).size.width * 0.04,
            child: Container(
              alignment: Alignment.center,
              child: InkWell(
                onTap: () {
                  FocusScope.of(context).requestFocus();
                  widget.onChange!(null);
                  _displayController.text = "";
                },
                child: const Icon(
                  Icons.close,
                  color: Colors.black54,
                  size: 20,
                ),
              ),
            ),
          )
      ]),
    );
  }
}

class CustomDropdownField extends FormField<dynamic> {
  CustomDropdownField({
    FormFieldSetter<dynamic>? onSaved,
    final validator,
    dynamic initialValue = '',
    String placeholder = "Select Item",
    List<dynamic>? options,
  }) : super(
            onSaved: onSaved,
            validator: validator,
            initialValue: initialValue,
            builder: (FormFieldState<dynamic> state) {
              return CustomDropdown(
                options: options,
                value: initialValue,
                onChange: (val) {
                  state.didChange(val);
                },
              );
            });
}
