// ignore_for_file: must_be_immutable, library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/payment_mode_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import '../../utilities/logger.dart';

class PaymentModeSelector extends StatefulWidget {
  bool? enableFlag;
  String? labelText;
  Function? onChangedFn;
  String? paymentModeID;
  PaymentModeSelector({
    Key? key,
    this.enableFlag = true,
    this.labelText,
    this.onChangedFn,
    this.paymentModeID,
  }) : super(key: key);
  @override
  _PaymentModeSelectorState createState() => _PaymentModeSelectorState();
}

class _PaymentModeSelectorState extends State<PaymentModeSelector> {
  final paymetModeListController = PaymetModeListController();
  @override
  void initState() {
    super.initState();
    paymetModeListController.fetchAll();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (paymetModeListController.isLoading) {
        return Container();
      }

      // print("llllllllllllllllllll");

      // print(paymetModeListController.modes[0].pmtTypeId);
      // print(paymetModeListController.modes[1].pmtTypeId);
      //
      // print("ppppppppppppppppppppp");

      // print(widget.paymentModeID);

      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          FormBuilderDropdown(
            name: 'txn_payment_type_id',
            style: formFieldTextStyle,
            decoration: formFieldStyle.copyWith(labelText: "Mode"),
            items: [
              ...paymetModeListController.modes
                  .map((e) => DropdownMenuItem(
                      value: e.pmtTypeId, child: Text(e.pmtTypeShortName!)))
                  .toList()
            ],

            // readOnly: !widget.enableFlag,
            initialValue: widget.paymentModeID,
            onChanged: widget.enableFlag == true
                ? null
                : (value) {
                    // Log.d("changed value $value");
                    if (null != widget.onChangedFn) {
                      widget.onChangedFn!(value);
                    }
                    // itemAdjustmentController.itemAdj.itemAdjType = value;
                  },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return "(Please select Payment Mode)";
              }
              return null;
            },
          ),
        ],
      );
    });
  }
}
