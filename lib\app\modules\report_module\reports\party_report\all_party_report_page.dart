// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_dropdown.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/all_party_statement_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/party_report/all_party_report_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

/**
 * ALL PARTY REPORT PAGE
 *
 * This page displays a comprehensive report of all parties (customers/suppliers)
 * showing their current balance status and allowing filtering by due type.
 *
 * FEATURES:
 * - Filter by due type (All, Receivable, Payable)
 * - Display party names with balance amounts
 * - Visual indicators for receivable (green) vs payable (red)
 * - Summary totals at bottom
 * - Print functionality for reports
 */
class AllPartyReportPage extends StatefulWidget {
  const AllPartyReportPage({Key? key}) : super(key: key);

  @override
  _AllPartyReportPageState createState() => _AllPartyReportPageState();
}

class _AllPartyReportPageState extends State<AllPartyReportPage> {
  final _controller = AllPartyReportController();

  // Filter state for due type selection
  String? dueType;

  @override
  void initState() {
    super.initState();
    // Initialize data on page load
    this.generate();
  }

  /**
   * GENERATE REPORT DATA
   *
   * Triggers data fetch based on current filter settings
   */
  generate() {
    _controller.getLedgersFor(dueType: dueType);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          elevation: 0,
          titleSpacing: -5.0,
          backgroundColor: colorPrimary,
          title: Text(
            "सबै पार्टी रिपोर्ट (All Party Report)",
            style: TextStyle(
              fontSize: 17,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold,
            ),
          ),
          actions: [
            PrintButton(
              onPressed: () {
                // Navigate to print page with current ledger data
                Navigator.pushNamed(
                  context,
                  '/printPartyStatement',
                  arguments: AllPartyStatementPrintPage(
                    ledgers: _controller.ledgers,
                  ),
                );
              },
            )
          ],
        ),
        body: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(new FocusNode()),
          child: Container(
            color: Colors.black12,
            child: Column(
              children: [
                // FILTER SECTION - Due Type Selection
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 10),
                  child: CustomDropdown(
                    borderless: true,
                    style: formFieldTextStyle,
                    decoration: formFieldStyle,
                    value: dueType,
                    allowClear: false,
                    placeholder: "Select Due Type",
                    options: [
                      {'key': null, 'value': 'All'},
                      {'key': 'receivable', 'value': 'Receivable (लिनुपर्ने)'},
                      {'key': 'payable', 'value': 'Payable (तिर्नुपर्ने)'},
                    ],
                    onChange: (value) {
                      // Update filter and regenerate data
                      dueType = value;
                      this.setState(() {});
                      generate();
                    },
                  ),
                ),
                Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                // MAIN CONTENT SECTION
                Obx(
                  () {
                    // Show loading indicator
                    if (_controller.isLoading) {
                      return Container(
                          color: Colors.white,
                          child: Center(child: CircularProgressIndicator()));
                    }

                    // Show no records message
                    if (_controller.ledgers.isEmpty) {
                      return Container(
                        color: Colors.white,
                        width: double.infinity,
                        child: Center(
                          child: Text(
                            "No Records",
                            style: TextStyle(color: Colors.black54),
                          ),
                        ),
                      );
                    } else {
                      // Show party list
                      return Expanded(
                        child: _PartyListView(_controller.ledgers),
                      );
                    }
                  },
                ),
              ],
            ),
          ),
        ),

        // BOTTOM SUMMARY BAR
        bottomNavigationBar: Container(
          height: 45, // Back to original height
          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          color: colorPrimary,
          child: SingleChildScrollView(
            child: Obx(
              () {
                return DefaultTextStyle(
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text("No. of Party: " +
                            _controller.ledgers.length.toString()),
                        flex: 1,
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          "Total: " +
                              formatCurrencyAmount(
                                  _controller.totalAmount.value, false),
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

/**
 * PARTY LIST VIEW WIDGET
 *
 * Displays the list of parties with their balance information
 * Uses visual indicators to show receivable vs payable status
 */
class _PartyListView extends StatelessWidget {
  final List<LedgerDetailModel> _ledgers;

  _PartyListView(this._ledgers);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: _ledgers.length,
      itemBuilder: (context, int index) {
        LedgerDetailModel ledger = _ledgers[index];

        return InkWell(
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                DefaultTextStyle(
                  style: TextStyle(fontSize: 14, color: colorPrimary),
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // PARTY NAME SECTION
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${ledger.ledgerTitle ?? 'Unknown Party'}",
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                                style: TextStyle(
                                    fontSize: 14, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),

                        // BALANCE AMOUNT SECTION - FIXED null safety
                        Expanded(
                          flex: 2,
                          child: _amountWidget(ledger.balanceAmount ?? 0.0),
                        ),
                      ],
                    ),
                  ),
                ),
                Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                // Add space for last element (for better scrolling)
                if (_ledgers.length - 1 == index) ...{
                  SizedBox(height: 10),
                },
              ],
            ),
          ),
        );
      },
    );
  }
}

/**
 * AMOUNT WIDGET WITH VISUAL INDICATORS
 *
 * Displays balance amount with appropriate visual cues:
 * - Green arrow + "लिनुपर्ने" for receivables (money owed TO us)
 * - Red arrow + "तिर्नुपर्ने" for payables (money owed BY us)
 *
 * @param balanceAmount - The balance amount (positive = receivable, negative = payable)
 */
Widget _amountWidget(double balanceAmount) {
  // Determine if this is a receivable (positive) or payable (negative)
  bool isReceivable = (balanceAmount >= 0);

  return Row(
    mainAxisSize: MainAxisSize.min,
    children: [
      // Visual indicator arrow
      Transform.rotate(
        angle: (isReceivable) ? (3.14 / 1.3) : (-3.14 / 4),
        alignment: Alignment.center,
        child: CircleAvatar(
          radius: 8,
          backgroundColor: (isReceivable) ? colorGreenDark : colorRedLight,
          child: Icon(
            Icons.arrow_forward,
            color: Colors.white,
            size: 12,
          ),
        ),
      ),
      SizedBox(width: 5),

      // Amount text with label
      RichText(
        text: TextSpan(
          // Nepali labels for better user understanding
          text: (isReceivable) ? "लिनुपर्ने: " : "तिर्नुपर्ने: ",
          style: TextStyle(
            color: (isReceivable) ? colorGreenDark : colorRedLight,
            fontSize: 14,
          ),
          children: [
            TextSpan(
              // Always show absolute value since the label indicates the type
              text: "${formatCurrencyAmount(balanceAmount.abs(), false)}",
            )
          ],
        ),
      ),
    ],
  );
}
