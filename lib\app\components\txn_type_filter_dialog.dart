// ignore_for_file: library_private_types_in_public_api

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class TxnTypeFilterDialog extends StatefulWidget {
  final List<int>? initialSelectedTypes;
  final Function? onChange;
  const TxnTypeFilterDialog(
      {super.key, this.initialSelectedTypes, this.onChange});

  @override
  _TxnTypeFilterDialogState createState() => _TxnTypeFilterDialogState();
}

class _TxnTypeFilterDialogState extends State<TxnTypeFilterDialog> {
  List<int> selectedTypes = [];
  @override
  void initState() {
    super.initState();
    setState(() {
      selectedTypes =
          jsonDecode(jsonEncode(widget.initialSelectedTypes ?? [])).cast<int>();
    });
    // Log.d("state init ${widget.initialSelectedTypes}");
    // Log.d("statet init");
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(10),
      child: Column(
          // mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.only(bottom: 6),
              child: Row(
                children: [
                  Text(
                    "Select Transaction Types",
                    style: labelStyle2.copyWith(fontSize: 20),
                  ),
                ],
              ),
            ),
            Expanded(
              child: ListView(
                  children: TxnType.financialTxnTypeList
                      .map((e) => Container(
                            padding: const EdgeInsets.all(2.0),
                            child: Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () {
                                  setState(() {
                                    selectedTypes.contains(e)
                                        ? selectedTypes.remove(e)
                                        : selectedTypes.add(e);
                                    // widget.onSelectionChanged(selectedChoices); // +added
                                  });
                                },
                                child: Row(
                                  children: [
                                    AbsorbPointer(
                                      child: Checkbox(
                                        onChanged: (checked) {},
                                        value: selectedTypes.contains(e),
                                        checkColor:
                                            Theme.of(context).primaryColor,
                                        activeColor:
                                            Theme.of(context).primaryColorLight,
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 10,
                                    ),
                                    Text(
                                      TxnType.txnTypeText[e]!,
                                      style: labelStyle2.copyWith(
                                          color: selectedTypes.contains(e)
                                              ? Theme.of(context).primaryColor
                                              : Colors.black87),
                                    )
                                  ],
                                ),
                              ),
                            ),
                            // child: ChoiceChip(
                            //   label: Text(e['text']),
                            //   selected: selectedTypes.contains(e['value']),
                            //   onSelected: (selected) {
                            //     setState(() {
                            //       selectedTypes.contains(e['value'])
                            //           ? selectedTypes.remove(e['value'])
                            //           : selectedTypes.add(e['value']);
                            //       // widget.onSelectionChanged(selectedChoices); // +added
                            //     });
                            //   },
                            // ),
                          ))
                      .toList()),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                ElevatedButton(
                  child: const Text("Select All"),
                  onPressed: () {
                    setState(() {
                      selectedTypes = TxnType.allTypes;
                    });
                  },
                ),
                ElevatedButton(
                  child: const Text("Clear All"),
                  onPressed: () {
                    setState(() {
                      selectedTypes = [];
                    });
                  },
                ),
                ElevatedButton(
                  child: const Text("Submit"),
                  onPressed: () {
                    widget.onChange!(selectedTypes);
                  },
                )
              ],
            )
          ]),
    );
  }
}

showTxnTypeFilterDialog(
    {required List<int> selectedTypes, required BuildContext context}) async {
  return Scaffold.of(context).showBottomSheet((BuildContext context) {
    //Here we will build the content of the dialog
    return TxnTypeFilterDialog(
      initialSelectedTypes: selectedTypes,
      onChange: (List<int> types) {
        // onChangeSelection(types);
        Navigator.of(context).pop(types);
      },
    );
  });
}
