// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mobile_khaata_v2/app/model/others/youtube_turorial_model.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:share/share.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class YoutubeVideoItem extends StatefulWidget {
  final YoutubeTutorialModal? tutorialModal;
  const YoutubeVideoItem({Key? key, this.tutorialModal}) : super(key: key);

  @override
  _YoutubeVideoItemState createState() => _YoutubeVideoItemState();
}

class _YoutubeVideoItemState extends State<YoutubeVideoItem> {
  YoutubePlayerController? _controller;
  PlayerState? _playerState;
  YoutubeMetaData? _videoMetaData;
  bool _isPlayerReady = false;

  @override
  void initState() {
    super.initState();
    _controller = YoutubePlayerController(
      initialVideoId:
          YoutubePlayer.convertUrlToId(widget.tutorialModal!.link ?? "")!,
      flags: const YoutubePlayerFlags(
        mute: false,
        // autoPlay: true,
        disableDragSeek: false,
        loop: false,
        isLive: false,
        forceHD: false,
        enableCaption: true,
      ),
    )..addListener(listener);
    _videoMetaData = const YoutubeMetaData();
    _playerState = PlayerState.unknown;
  }

  void listener() {
    if (_isPlayerReady && mounted && !_controller!.value.isFullScreen) {
      setState(() {
        _playerState = _controller!.value.playerState;
        _videoMetaData = _controller!.metadata;
      });
    }
  }

  @override
  void deactivate() {
    // Pauses video while navigating to next page.
    _controller!.pause();
    super.deactivate();
  }

  @override
  void dispose() {
    _controller!.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      margin: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
      child: Column(children: [
        Container(
          padding: const EdgeInsets.only(left: 10, right: 10, top: 10),
          child: YoutubePlayer(
            controller: _controller!,
            showVideoProgressIndicator: false,
            progressIndicatorColor: Colors.blueAccent,
            bottomActions: [
              Expanded(child: Container()),
              IconButton(
                icon: const Icon(
                  FontAwesomeIcons.youtube,
                  color: Color(0xFFEB3223),
                  size: 25.0,
                ),
                onPressed: () {
                  launchUrl(Uri.parse(widget.tutorialModal!.link ?? ""));
                },
              ),
            ],
            // topActions: <Widget>[
            //   const SizedBox(width: 8.0),
            //   Expanded(
            //     child: Text(
            //       _controller.metadata.title,
            //       style: const TextStyle(
            //         color: Colors.white,
            //         fontSize: 18.0,
            //       ),
            //       overflow: TextOverflow.ellipsis,
            //       maxLines: 1,
            //     ),
            //   ),
            //   IconButton(
            //     icon: const Icon(
            //       Icons.settings,
            //       color: Colors.white,
            //       size: 25.0,
            //     ),
            //     onPressed: () {},
            //   ),
            // ],
            onReady: () {
              _isPlayerReady = true;
            },
          ),
        ),
        Container(
          width: double.infinity,
          // alignment: Alignment.center,
          padding: const EdgeInsets.all(10),
          // color: Theme.of(context).primaryColor,
          child: Text(
            widget.tutorialModal!.title ?? "",
            style: labelStyle2.copyWith(),
          ),
        ),
        Container(
          width: double.infinity,
          alignment: Alignment.centerRight,

          // color: Theme.of(context).primaryColor,
          child: InkWell(
              onTap: () {
                Share.share(widget.tutorialModal!.link ?? "");
              },
              child: Container(
                // color: Colors.red,
                padding: const EdgeInsets.all(10),
                child: Row(mainAxisSize: MainAxisSize.min, children: const [
                  Icon(Icons.share, size: 14),
                  SizedBox(width: 8),
                  Text("Share")
                ]),
              )),
        ),
        // Container(
        //   padding: EdgeInsets.all(10),
        //   child: Text(
        //     widget.tutorialModal.title ?? "",
        //     style: labelStyle2,
        //   ),
        // )
      ]),
    );
  }
}
