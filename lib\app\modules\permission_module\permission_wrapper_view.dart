import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

/**
 * PERMISSION WRAPPER VIEW - UI COMPONENT EXPLANATION:
 *
 * This widget acts as a wrapper around any feature that requires permission checking.
 * It provides three different UI states based on permission checking results.
 *
 * HOW THE UI WORKS:
 * 1. LOADING STATE: Shows loading spinner while checking permissions
 * 2. ERROR STATE: Shows error screen if permission is denied or other issues occur
 * 3. SUCCESS STATE: Shows the actual feature (child widget) if permission is granted
 *
 * USAGE EXAMPLE:
 * PermissionWrapper(
 *   child: YourFeatureWidget(),
 *   requiredPermission: "feature_name"
 * )
 *
 * PERMISSION FLOW IN UI:
 * 1. Widget created -> Controller initialized -> Loading screen shown
 * 2. If admin user -> Loading stops immediately -> Feature shown
 * 3. If regular user -> API call made -> Based on response:
 *    - Success: Show feature
 *    - Error: Show error screen with retry option
 */

// ignore: must_be_immutable
class PermissionWrapper extends StatelessWidget {
  final Widget child; // The actual feature widget to show if permission granted
  final String
      requiredPermission; // Permission string required for this feature

  var permissionWrapperController = PermissionWrapperController();

  PermissionWrapper(
      {super.key, required this.child, required this.requiredPermission}) {
    // INITIALIZE PERMISSION CHECKING:
    // This triggers the permission validation process
    // For admin users: This completes immediately
    // For regular users: This starts API call to check permissions
    permissionWrapperController.init(forPermission: requiredPermission);
  }

  @override
  Widget build(BuildContext context) {
    // REACTIVE UI: Uses Obx to rebuild UI when controller state changes
    return Obx(() {
      // STATE 1: LOADING STATE
      // Shown while permission checking is in progress
      // For admin users: This state is very brief (almost instant)
      // For regular users: This shows until API response is received
      if (permissionWrapperController.isLoading) {
        return SafeArea(
            child: Scaffold(
          body: Center(
              child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(
                height: 20,
              ),
              Text("Checking for permision $requiredPermission")
            ],
          )),
        ));
      }

      // STATE 2: ERROR STATE
      // Shown when permission is denied or other errors occur
      // Admin users should never see this state (they bypass all checks)
      // Regular users see this when:
      // - They don't have required permission
      // - Their account is expired/inactive
      // - Multi-user mode is disabled
      // - Network errors occur
      else if (permissionWrapperController.hasError) {
        return SafeArea(
            child: Scaffold(
          appBar: AppBar(
            backgroundColor: colorPrimary,
            // COMMENTED OUT: Old title implementation
            // title: const Text(
            //   "Permission Error",
            //   style: TextStyle(
            //       fontSize: 18,
            //       color: Colors.white,
            //       fontFamily: 'HelveticaRegular',
            //       fontWeight: FontWeight.bold),
            // ),
          ),
          body: Padding(
            padding: const EdgeInsets.all(20.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  // flex: 2,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    // crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      const SizedBox(
                        height: 40,
                      ),

                      // ERROR ICON: Visual indicator of permission denial
                      Icon(
                        // ignore: deprecated_member_use
                        FontAwesomeIcons.exclamationTriangle,
                        color: colorRedLight,
                        size: 60,
                      ),
                      const SizedBox(
                        height: 40,
                      ),

                      // ERROR MESSAGE: Displays specific error from controller
                      // Messages can include:
                      // - "You don't have permission for this action"
                      // - "Your account is expired"
                      // - "Your account is inactive"
                      // - "Multi User mode is turned off"
                      // - Network error messages
                      Text(
                        permissionWrapperController.errorMessage,
                        style: TextStyle(color: colorPrimary, fontSize: 15),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(
                        height: 20,
                      ),

                      // RETRY BUTTON: Allows user to retry permission check
                      // Useful for network errors or when permissions might have changed
                      Center(
                        child: ElevatedButton(
                            // COMMENTED OUT: Old button styling
                            // color: colorPrimary,
                            // elevation: 10,
                            // shape: RoundedRectangleBorder(
                            //   borderRadius: BorderRadius.circular(10.0),
                            // ),
                            // splashColor: colorPrimaryLightest,
                            child: const Padding(
                              padding: EdgeInsets.symmetric(
                                  vertical: 10, horizontal: 20),
                              child: Text(
                                "Re-try",
                                style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 15,
                                    fontWeight: FontWeight.bold),
                              ),
                            ),
                            onPressed: () async {
                              // RETRY LOGIC: Re-initialize permission checking
                              // This will go through the same flow again:
                              // Admin users -> Immediate access
                              // Regular users -> New API call
                              permissionWrapperController.init(
                                  forPermission: requiredPermission);
                            }),
                      ),
                    ],
                  ),
                ),

                // FOOTER: App logo at bottom of error screen
                Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Image.asset(
                      'images/logo-bottom.png',
                      height: MediaQuery.of(context).size.width * 0.2,
                      fit: BoxFit.cover,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ));
      }

      // STATE 3: SUCCESS STATE
      // Shown when permission is granted
      // This displays the actual feature (child widget)
      // Admin users: Always reach this state (immediate)
      // Regular users: Reach this state only after successful permission validation
      else {
        return child;
      }
    });
  }
}

/**
 * PERMISSION MODULE SUMMARY:
 *
 * CONTROLLER (PermissionWrapperController):
 * - Handles permission checking logic
 * - Makes API calls for regular users
 * - Bypasses checks for admin users
 * - Manages loading and error states
 *
 * VIEW (PermissionWrapper):
 * - Provides UI for three states: loading, error, success
 * - Shows loading screen during permission checks
 * - Shows error screen with retry option for failures
 * - Shows actual feature when permission is granted
 *
 * ADMIN EXPERIENCE:
 * - No loading delays
 * - No permission restrictions
 * - Immediate access to all features
 *
 * REGULAR USER EXPERIENCE:
 * - Brief loading screen for permission checks
 * - Clear error messages if access denied
 * - Retry option for network issues
 * - Access only to permitted features
 */
