import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_image_picker/form_builder_image_picker.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/components/expense_category_autocomplete.dart';
import 'package:mobile_khaata_v2/app/components/ledger_autocomplete%20_%20textfield_with_add.dart';
import 'package:mobile_khaata_v2/app/components/payment_mode_selector.dart';
import 'package:mobile_khaata_v2/app/model/database/expanse_category_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_module/add_edit_expense/add_edit_expense_controller.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_module/detail_expense/detail_express_page.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import '../../bank_module/bank_account_list/bank_account_list_controller.dart';

class AddEditExpensesPage extends StatelessWidget {
  final String tag = "Expense Add/Edit Page";

  final String? expensID;
  final bool? reaOnlyFlag;
  final bankController =
      Get.put(BankAccountListController(), tag: "BankAccountListController");

  final expenseController = AddEditExpenseController();

  AddEditExpensesPage({this.expensID, this.reaOnlyFlag}) {
    expenseController.onInit();

    if (null != expensID) {
      // initiate edit functionality
      expenseController.initEdit(expensID, this.reaOnlyFlag ?? false);
    } else {
      expenseController.initialize();
    }
  }

  onSave(BuildContext context, {bool forNew = false}) async {
    FocusScope.of(context).unfocus();
    if (expenseController.formKey.currentState!.validate()) {
      Log.d(expenseController.transaction.value.toJson());

      if (expenseController.transaction.value.expenseCategoryId == null &&
          (expenseController.transaction.value.expenseCategoryName == null ||
              expenseController
                  .transaction.value.expenseCategoryName!.isEmpty)) {
        showToastMessage(context,
            message:
                "खर्च शीर्षक खाली राख्न मिल्दैन |\nPlease fill Expense Title",
            alertType: AlertType.Error);
        return;
      }

      if (expenseController.transaction.value.txnDateBS!.isEmpty) {
        showToastMessage(context,
            message: "मिति खाली राख्न मिल्दैन |\nPlease fill the date",
            alertType: AlertType.Error);
        return;
      }
      if (null == expenseController.transaction.value.ledgerId) {
        showToastMessage(context,
            message:
                "विक्रेता/पाउने खाली राख्न मिल्दैन |\nVendor/Receiver cannot be empty.",
            alertType: AlertType.Error);
        return;
      }

      if (100 < parseDouble(expenseController.discountPercentageCtrl.text)! ||
          parseDouble(expenseController.subTotalAmountCtrl.text)! <
              parseDouble(expenseController.discountAmountCtrl.text)!) {
        showToastMessage(context,
            message:
                "छुट १००% भन्दा ठूलो हुन सक्दैन | \nDiscount can't be greater than 100%.",
            alertType: AlertType.Error);
        return;
      }

      if (expenseController.totalAmountCtrl.text.isEmpty) {
        showToastMessage(context,
            message: "कुल रकम खाली हुन सक्दैन | \nTotal amount can't be empty.",
            alertType: AlertType.Error);
        return;
      }

      if (expenseController.transaction.value.txnTotalAmount == 0.0) {
        showToastMessage(context,
            message: "कुल रकम शून्य हुन सक्दैन।| \nTotal amount can't be zero.",
            alertType: AlertType.Error);
        return;
      }

      if (0.0 > parseDouble(expenseController.totalAmountCtrl.text)!) {
        showToastMessage(context,
            message:
                "कुल रकम नकारात्मक हुन सक्दैन | \nTotal amount can't be negative.",
            alertType: AlertType.Error);
        return;
      }

      if (expenseController.transaction.value.txnCashAmount! >
          expenseController.transaction.value.txnTotalAmount!) {
        showToastMessage(context,
            message:
                "भुक्तानी गरिएको रकम बिल रकम भन्दा ठूलो हुन सक्दैन | \nPaid amount can't be greater than bill amount.",
            alertType: AlertType.Error);
        return;
      }

      if (expenseController.transaction.value.txnPaymentTypeId !=
              PAYMENT_MODE_CASH_ID &&
          !(expenseController.transaction.value.txnCashAmount! > 0)) {
        showToastMessage(context,
            message:
                "चेक वा बैंक मा भुक्तानी रकम 0 राख्न मिल्दैन | \nPaid amount cannot be 0 for Cheque and Bank",
            alertType: AlertType.Error);
        return;
      }

      if (expenseController.transaction.value.txnPaymentTypeId ==
              PAYMENT_MODE_CHEQUE_ID &&
          (null == expenseController.transaction.value.txnPaymentReference ||
              "" == expenseController.transaction.value.txnPaymentReference)) {
        showToastMessage(context,
            message:
                "चेक/भौचर न. खाली राख्न मिल्दैन | \nPlease fill the cheque/voucher no",
            alertType: AlertType.Error);
        return;
      }

      if (expenseController.transaction.value.txnPaymentTypeId ==
              PAYMENT_MODE_CHEQUE_ID &&
          (null == expenseController.transaction.value.chequeIssueDateBS ||
              "" == expenseController.transaction.value.chequeIssueDateBS)) {
        showToastMessage(context,
            message: "चेक खाली राख्न मिल्दैन | \nPlease fill the cheque date",
            alertType: AlertType.Error);
        return;
      }
      if (expenseController.transaction.value.txnPaymentTypeId !=
              PAYMENT_MODE_CASH_ID &&
          expenseController.transaction.value.txnPaymentTypeId !=
              PAYMENT_MODE_CHEQUE_ID) {
        if (bankController.bankList
                .where((element) =>
                    element.pmtTypeId ==
                    expenseController.transaction.value.txnPaymentTypeId)
                .first
                .pmtTypeCurrentBalance! <
            expenseController.transaction.value.txnCashAmount!) {
          print(
              "चयन गरिएको बैंक खातामा अपर्याप्त मौज्दात रकम। कृपया अर्को बैंक खाता चयन गर्नुहोस् वा भुक्तानी मोड परिवर्तन गर्नुहोस्। | \nInsufficient balance in selected bank account. Please select another bank account or change payment mode.");
          showToastMessage(
            context,
            alertType: AlertType.Error,
            message:
                "चयन गरिएको बैंक खातामा अपर्याप्त मौज्दात रकम। कृपया अर्को बैंक खाता चयन गर्नुहोस् वा भुक्तानी मोड परिवर्तन गर्नुहोस्। | \nInsufficient balance in selected bank account. Please select another bank account or change payment mode.",
          );
          return;
        }
      }

      ProgressDialog progressDialog = ProgressDialog(context,
          type: ProgressDialogType.normal, isDismissible: false);
      progressDialog.update(message: "Saving data. Please wait....");
      await progressDialog.show();

      bool isLargeFile =
          await expenseController.checkLargeImage(expenseController.files);
      if (isLargeFile) {
        await progressDialog.hide();
        showToastMessage(context,
            message: MAX_IMAGE_SIZE_MESSAGE, alertType: AlertType.Error);
        return;
      }

      //dont check for purchase
      bool isDuplicate = false;
      // await expenseController
      //     .checkDuplicateBillNo();

      bool status = false;
      String? txnID;
      try {
        if (!expenseController.editFlag) {
          txnID = await expenseController.addExpenses();
          status = (null != txnID);
        } else {
          status = await expenseController.updateExpenses();
        }
      } catch (e, trace) {
        Log.e(tag, e.toString() + trace.toString());
      }
      await progressDialog.hide();

      if (status) {
        // Navigator.pop(context, true);
        String message = (expenseController.editFlag)
            ? "Expense Updated Successfully."
            : "Expense Created Successfully.";
        if (forNew) {
          Navigator.of(context).pushReplacementNamed('/addExpenses');
        } else {
          if (expenseController.editFlag) {
            Navigator.of(context).pop();
            Navigator.of(context).pushReplacementNamed('/detailExpenses',
                arguments: DetailExpensesPage(
                  expensID: this.expensID,
                ));
          } else {
            Navigator.of(context).pop();
            if (null != txnID) {
              TransactionHelper.goToPrintPage(context, txnID, TxnType.expense);
            }
          }
        }

        TransactionHelper.refreshPreviousPages();
        showToastMessage(context, message: message, duration: 2);
      } else {
        showToastMessage(context,
            alertType: AlertType.Error,
            message: "Failed to process operation",
            duration: 2);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      bool iscashExpenses = expenseController.transaction.value.ledgerId ==
          CASH_EXPENSE_LEDGER_ID;
      bool hasSubTotal =
          (expenseController.transaction.value.txnSubTotalAmount != null &&
              expenseController.transaction.value.txnSubTotalAmount! > 0.0);

      if (expenseController.isLoading) {
        return Container(
            color: Colors.white,
            child: Center(child: CircularProgressIndicator()));
      } else {
        return SafeArea(
            child: Scaffold(
                resizeToAvoidBottomInset: true,
                appBar: AppBar(
                  toolbarHeight: 60,
                  backgroundColor: colorPrimary,
                  elevation: 4,
                  leading: BackButton(
                    onPressed: () => Navigator.pop(context, false),
                  ),
                  centerTitle: false,
                  titleSpacing: -5.0,
                  title: Text(
                    "खर्च (Expense) ",
                    style: TextStyle(
                        fontSize: 20,
                        color: Colors.white,
                        fontFamily: 'HelveticaRegular',
                        fontWeight: FontWeight.bold),
                  ),
                ),

                //===========================================================================Body Part
                body: Center(
                  child: Container(
                    color: backgroundColorShade,
                    child: GestureDetector(
                      onTap: () => FocusScope.of(context).unfocus(),
                      child: Container(
                        child: Form(
                          key: expenseController.formKey,
                          child: SingleChildScrollView(
                            child: Container(
                              child: Column(
                                children: [
                                  Card(
                                    elevation: 2,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 15),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          //===========================Bill No.
                                          Expanded(
                                            flex: 1,
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "बिल/भौचर न.",
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                FormBuilderTextField(
                                                  name: "bill_no",
                                                  readOnly: expenseController
                                                      .readOnlyFlag,
                                                  autocorrect: false,
                                                  keyboardType:
                                                      TextInputType.text,
                                                  textInputAction:
                                                      TextInputAction.done,
                                                  textAlign: TextAlign.right,
                                                  style: formFieldTextStyle,
                                                  decoration:
                                                      formFieldStyle.copyWith(
                                                          labelText:
                                                              "Bill/Voucher No."),
                                                  controller: expenseController
                                                      .billNoCtrl,
                                                  onChanged: (value) {
                                                    expenseController
                                                            .transaction
                                                            .value
                                                            .txnRefNumberChar =
                                                        value;
                                                    expenseController
                                                        .transaction
                                                        .refresh();
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),

                                          SizedBox(
                                            width: 20,
                                          ),

                                          //===========================Transaction Date
                                          Expanded(
                                            flex: 1,
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "मिति",
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                CustomDatePickerTextField(
                                                  readOnly: expenseController
                                                      .readOnlyFlag,
                                                  maxBSDate:
                                                      NepaliDateTime.now(),
                                                  initialValue:
                                                      expenseController
                                                          .transaction
                                                          .value
                                                          .txnDateBS,
                                                  onChange: (selectedDate) {
                                                    expenseController
                                                            .transaction
                                                            .value
                                                            .txnDateBS =
                                                        selectedDate;
                                                  },
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),

                                  Card(
                                    elevation: 2,
                                    child: Container(
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 10, vertical: 10),
                                      child: Column(
                                        children: [
                                          //===============================================Expense Category
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'खर्च शीर्षक',
                                                style: labelStyle2,
                                              ),
                                              SizedBox(height: 5.0),
                                              ExpensesCategoryAutoComplete(
                                                  enableFlag: !expenseController
                                                      .readOnlyFlag,
                                                  labelText: "Expense Title",
                                                  controller: expenseController
                                                      .expenseCategoryCtrl,
                                                  onChangedFn: (value) {
                                                    Log.d(
                                                        "called i text change $value");
                                                    expenseController
                                                        .onChangeCategory(null);
                                                  },
                                                  categoryID: expenseController
                                                      .transaction
                                                      .value
                                                      .expenseCategoryId,
                                                  onSuggestionSelectedFn:
                                                      (ExpenseCategoryModel
                                                          category) {
                                                    expenseController
                                                        .onChangeCategory(
                                                            category);
                                                    FocusScope.of(context)
                                                        .unfocus();
                                                  }),
                                              const SizedBox(
                                                height: 25,
                                              ),
                                            ],
                                          ),

                                          //===============================================Party Balance

                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Row(
                                                children: [
                                                  Container(
                                                    width: 20,
                                                    height: 20,
                                                    child: Checkbox(
                                                      activeColor: colorPrimary,
                                                      checkColor: Colors.white,
                                                      value: expenseController
                                                          .isCashExpensesSelected,
                                                      onChanged:
                                                          expenseController
                                                                  .readOnlyFlag
                                                              ? null
                                                              : (value) {
                                                                  expenseController
                                                                          .setisCashExpensesSelected =
                                                                      value!;
                                                                  if (value) {
                                                                    expenseController.onChangeParty(LedgerDetailModel(
                                                                        ledgerId:
                                                                            CASH_EXPENSE_LEDGER_ID,
                                                                        ledgerTitle:
                                                                            CASH_EXPENSE_LEDGER_NAME));
                                                                    expenseController
                                                                            .receivedAmountCtrl
                                                                            .text =
                                                                        expenseController
                                                                            .totalAmountCtrl
                                                                            .text;
                                                                  } else {
                                                                    expenseController.onChangeParty(LedgerDetailModel(
                                                                        ledgerId:
                                                                            null));
                                                                  }
                                                                },
                                                      // onChanged:
                                                      //     expenseController
                                                      //             .readOnlyFlag
                                                      //         ? null
                                                      //         : (value) {
                                                      //
                                                      //             expenseController
                                                      //                     .setisCashExpensesSelected =
                                                      //                 value!;
                                                      //             if (value) {
                                                      //               expenseController.onChangeParty(LedgerDetailModel(
                                                      //                   ledgerId:
                                                      //                       CASH_EXPENSE_LEDGER_ID,
                                                      //                   ledgerTitle:
                                                      //                       CASH_EXPENSE_LEDGER_NAME));
                                                      //               expenseController
                                                      //                   .onToggleVat(
                                                      //                       false);
                                                      //             } else {
                                                      //               expenseController.onChangeParty(
                                                      //                   LedgerDetailModel(
                                                      //                       ledgerId:
                                                      //                           null));
                                                      //             }
                                                      //           },
                                                    ),
                                                  ),
                                                  Text("  खुद्रा खर्च",
                                                      style: labelStyle2)
                                                ],
                                              ),
                                              RichText(
                                                textAlign: TextAlign.right,
                                                text: TextSpan(
                                                    text: "पुरानो बाँकी: ",
                                                    style: TextStyle(
                                                        color: textColor),
                                                    children: [
                                                      if (null !=
                                                          expenseController
                                                              .transaction
                                                              .value
                                                              .ledgerId) ...{
                                                        TextSpan(
                                                          text:
                                                              "${expenseController.selectedLedger.value.balanceAmount ?? 0}",
                                                          style: TextStyle(
                                                              color: ((expenseController
                                                                              .selectedLedger
                                                                              .value
                                                                              .balanceAmount ??
                                                                          0) >=
                                                                      0.0)
                                                                  ? colorGreenDark
                                                                  : colorRedLight),
                                                        )
                                                      }
                                                    ]),
                                              )
                                            ],
                                          ),

                                          Container(
                                            height: 15,
                                          ),
                                          //===============================================Party Field
                                          if (!expenseController
                                              .isCashExpensesSelected)
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  'विक्रेता / पाउने को नाम',
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                LedgerAutoCompleteTextFieldWithAdd(
                                                    excludedIDS: [
                                                      CASH_SALES_LEDGER_ID
                                                    ],
                                                    enableFlag:
                                                        !expenseController
                                                            .readOnlyFlag,
                                                    labelText:
                                                        "Vendor / Receiver Name",
                                                    controller:
                                                        expenseController
                                                            .partyNameCtrl,
                                                    onChangedFn: (value) {
                                                      Log.d(
                                                          "called i text change");
                                                    },
                                                    ledgetID: expenseController
                                                        .transaction
                                                        .value
                                                        .ledgerId,
                                                    onSuggestionSelectedFn:
                                                        (LedgerDetailModel
                                                            ledger) {
                                                      expenseController
                                                          .onChangeParty(
                                                              ledger);
                                                    })
                                              ],
                                            ),
                                          ...iscashExpenses
                                              ? [
                                                  const SizedBox(
                                                    height: 10,
                                                  ),
                                                  Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        "खुद्रा विक्रेता / पाउने को नाम",
                                                        style: labelStyle2,
                                                      ),
                                                      SizedBox(height: 5.0),
                                                      FormBuilderTextField(
                                                        name: "display_text",
                                                        // readOnly:
                                                        //     (null == state.selectedLedger.ledgerId)
                                                        //         ? false
                                                        //         : true,
                                                        readOnly:
                                                            expenseController
                                                                .readOnlyFlag,
                                                        autocorrect: false,
                                                        textInputAction:
                                                            TextInputAction
                                                                .done,
                                                        style:
                                                            formFieldTextStyle,
                                                        decoration: formFieldStyle
                                                            .copyWith(
                                                                labelText:
                                                                    "Vendor / Receiver Name"),
                                                        controller:
                                                            expenseController
                                                                .displayTextCtrl,
                                                        onChanged: (value) {
                                                          expenseController
                                                                  .transaction
                                                                  .value
                                                                  .txnDisplayName =
                                                              value;
                                                          expenseController
                                                              .transaction
                                                              .refresh();
                                                        },
                                                      ),
                                                    ],
                                                  )
                                                ]
                                              : [],

                                          const SizedBox(
                                            height: 10,
                                          ),
                                          if (!iscashExpenses)
                                            Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                //===================================Mobile
                                                Container(
                                                  width: MediaQuery.of(context)
                                                          .size
                                                          .width *
                                                      0.3,
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        'फोन नम्बर',
                                                        style: labelStyle2,
                                                      ),
                                                      SizedBox(height: 5.0),
                                                      FormBuilderTextField(
                                                          name: "mobile",
                                                          readOnly: true,
                                                          autocorrect: false,
                                                          keyboardType:
                                                              TextInputType
                                                                  .number,
                                                          textInputAction:
                                                              TextInputAction
                                                                  .done,
                                                          inputFormatters: [
                                                            FilteringTextInputFormatter
                                                                .digitsOnly
                                                          ],
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration: formFieldStyle.copyWith(
                                                              labelText:
                                                                  "Contact no.",
                                                              hintText:
                                                                  "Contact no"),
                                                          controller:
                                                              expenseController
                                                                  .mobileCtrl),
                                                    ],
                                                  ),
                                                ),

                                                //===================================Address
                                                Container(
                                                  width: MediaQuery.of(context)
                                                          .size
                                                          .width *
                                                      0.5,
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text(
                                                        'ठेगाना',
                                                        style: labelStyle2,
                                                      ),
                                                      SizedBox(height: 5.0),
                                                      FormBuilderTextField(
                                                        name: "address",
                                                        readOnly: true,
                                                        autocorrect: false,
                                                        keyboardType:
                                                            TextInputType.text,
                                                        textInputAction:
                                                            TextInputAction
                                                                .done,
                                                        style:
                                                            formFieldTextStyle,
                                                        decoration: formFieldStyle
                                                            .copyWith(
                                                                labelText:
                                                                    "Address"),
                                                        controller:
                                                            expenseController
                                                                .addressCtrl,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          if (!iscashExpenses)
                                            const SizedBox(height: 10.0),

                                          //=====================================PAN No Field
                                          if (!iscashExpenses)
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text(
                                                  "पान / मु. अ. कर नम्बर",
                                                  style: labelStyle2,
                                                ),
                                                SizedBox(height: 5.0),
                                                FormBuilderTextField(
                                                    name: "pan_no",
                                                    readOnly: true,
                                                    autocorrect: false,
                                                    keyboardType:
                                                        TextInputType.number,
                                                    inputFormatters: [
                                                      FilteringTextInputFormatter
                                                          .digitsOnly
                                                    ],
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            labelText:
                                                                "PAN/VAT No."),
                                                    controller:
                                                        expenseController
                                                            .panNoCtrl),
                                              ],
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),

                                  //===============================================Description
                                  Card(
                                    elevation: 2,
                                    child: Container(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 10, vertical: 10),
                                        child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                "खर्च विवरण",
                                                style: labelStyle2,
                                              ),
                                              SizedBox(height: 5.0),
                                              FormBuilderTextField(
                                                name: "description",
                                                readOnly: expenseController
                                                    .readOnlyFlag,
                                                autocorrect: false,
                                                textAlign: TextAlign.start,
                                                textInputAction:
                                                    TextInputAction.newline,
                                                style: formFieldTextStyle,
                                                decoration:
                                                    formFieldStyle.copyWith(
                                                        labelText:
                                                            "Expense Description"),
                                                minLines: 4,
                                                maxLines: 4,
                                                controller:
                                                    expenseController.descCtrl,
                                                onChanged: (value) {
                                                  expenseController
                                                      .transaction
                                                      .value
                                                      .txnDescription = value;
                                                  expenseController.transaction
                                                      .refresh();
                                                },
                                                // validators: [],
                                              ),
                                            ])),
                                  ),

                                  //===============================================Total Amount
                                  Card(
                                    elevation: 2,
                                    child: Column(
                                      children: [
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                              vertical: 6, horizontal: 8),
                                          decoration: BoxDecoration(
                                              color: colorPrimaryLight,
                                              borderRadius: BorderRadius.only(
                                                  topLeft: Radius.circular(4),
                                                  topRight:
                                                      Radius.circular(4))),
                                          child: Center(
                                              child: Text(
                                            "Bill Totals (जम्मा बिल)",
                                            style: TextStyle(
                                                color: Colors.white,
                                                fontSize: 16),
                                          )),
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Container(
                                          padding: EdgeInsets.symmetric(
                                            horizontal: 10,
                                          ),
                                          child: Column(
                                            children: [
                                              // =============================================Sub Total
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    "रकम",
                                                    style: labelStyle2,
                                                  ),
                                                  SizedBox(height: 5.0),
                                                  FormBuilderTextField(
                                                    name: "txn_subtotal",
                                                    readOnly: expenseController
                                                        .readOnlyFlag,
                                                    autocorrect: false,
                                                    keyboardType: TextInputType
                                                        .numberWithOptions(
                                                            decimal: true),
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    inputFormatters: [
                                                      FilteringTextInputFormatter
                                                          .allow(RegExp(
                                                              r'^(\d+)?\.?\d{0,2}'))
                                                    ],
                                                    maxLength: 10,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            hintText:
                                                                "Sub Total",
                                                            counterText: ''),
                                                    textAlign: TextAlign.end,
                                                    controller:
                                                        expenseController
                                                            .subTotalAmountCtrl,
                                                    onChanged: (value) {
                                                      if (value != null ||
                                                          (value != null &&
                                                              value
                                                                  .isNotEmpty)) {
                                                        expenseController
                                                                .subTotalAmountCtrl
                                                                .selection =
                                                            TextSelection
                                                                .fromPosition(
                                                          TextPosition(
                                                            offset: expenseController
                                                                .subTotalAmountCtrl
                                                                .text
                                                                .length,
                                                          ),
                                                        );
                                                        expenseController
                                                            .onSubTotalIndividualChange(
                                                          value,
                                                          editorTag:
                                                              'txn_subtotal',
                                                        );
                                                      }

                                                      if (expenseController
                                                          .isReceived) {
                                                        expenseController
                                                                .setIsReceived =
                                                            false;
                                                      }
                                                    },
                                                  ),
                                                  // FormBuilderTextField(
                                                  //   name: "txn_subtotal",
                                                  //   readOnly: expenseController
                                                  //       .readOnlyFlag,
                                                  //   autocorrect: false,
                                                  //   keyboardType: TextInputType
                                                  //       .numberWithOptions(
                                                  //           decimal:
                                                  //               true),
                                                  //   textInputAction:
                                                  //       TextInputAction.done,
                                                  //   style: formFieldTextStyle,
                                                  //   inputFormatters: [
                                                  //     FilteringTextInputFormatter
                                                  //         .allow(RegExp(
                                                  //             r'^(\d+)?\.?\d{0,2}'))
                                                  //   ],
                                                  //   maxLength: 10,
                                                  //   decoration:
                                                  //       formFieldStyle.copyWith(
                                                  //           hintText: "Amount",
                                                  //           counterText: ''),
                                                  //   textAlign: TextAlign.end,
                                                  //   controller:
                                                  //       expenseController
                                                  //           .subTotalAmountCtrl,
                                                  //   onChanged: (value) {
                                                  //     if (value != null ||
                                                  //         (value != null &&
                                                  //             value
                                                  //                 .isNotEmpty)) {
                                                  //       expenseController
                                                  //               .subTotalAmountCtrl
                                                  //               .selection =
                                                  //           TextSelection.fromPosition(
                                                  //               TextPosition(
                                                  //                   offset: expenseController
                                                  //                       .subTotalAmountCtrl
                                                  //                       .text
                                                  //                       .length));
                                                  //
                                                  //       expenseController
                                                  //           .onSubTotalIndividualChange(
                                                  //               value!,
                                                  //               editorTag:
                                                  //                   'txn_subtotal');
                                                  //
                                                  //       if (expenseController
                                                  //           .isCashExpensesSelected) {
                                                  //         expenseController
                                                  //             .receivedAmountCtrl
                                                  //             .text = value!;
                                                  //
                                                  //
                                                  //       }
                                                  //     }
                                                  //     // expenseController.transaction
                                                  //     //     .refresh();
                                                  //   },
                                                  // ),
                                                ],
                                              ),
                                              SizedBox(
                                                height: 20,
                                              ),

                                              // =============================================Discount
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    "छुट (Discount)",
                                                    style: labelStyle2,
                                                  ),
                                                  SizedBox(height: 5.0),
                                                  Row(
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .spaceBetween,
                                                    children: [
                                                      Container(
                                                        width: 80,
                                                        child:
                                                            FormBuilderTextField(
                                                          // focusNode: saleController.node,
                                                          name:
                                                              "txn_discount_percent",
                                                          readOnly: expenseController
                                                                  .readOnlyFlag ||
                                                              (!hasSubTotal),
                                                          autocorrect: false,
                                                          keyboardType:
                                                              TextInputType
                                                                  .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                          textInputAction:
                                                              TextInputAction
                                                                  .done,
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration:
                                                              formFieldStyle
                                                                  .copyWith(
                                                                      suffix: Text(
                                                                          "%"),
                                                                      labelText:
                                                                          "%"),
                                                          textAlign:
                                                              TextAlign.end,
                                                          controller:
                                                              expenseController
                                                                  .discountPercentageCtrl,
                                                          onChanged: (value) {
                                                            if (value!
                                                                .isEmpty) {
                                                              //remove keyboard
                                                              FocusScope.of(
                                                                      context)
                                                                  .unfocus();
                                                            }
                                                            expenseController
                                                                    .discountPercentageCtrl
                                                                    .selection =
                                                                TextSelection.fromPosition(TextPosition(
                                                                    offset: expenseController
                                                                        .discountPercentageCtrl
                                                                        .text
                                                                        .length));
                                                            expenseController
                                                                .updateDiscountPercentage(
                                                              value ?? "",
                                                              editorTag:
                                                                  'txn_discount_percent',
                                                              // context: context
                                                            );
                                                          },
                                                        ),
                                                        // child:
                                                        //     FormBuilderTextField(
                                                        //   name:
                                                        //       "txn_discount_percent",
                                                        //   readOnly: expenseController
                                                        //           .readOnlyFlag ||
                                                        //       (!hasSubTotal),
                                                        //   autocorrect: false,
                                                        //   keyboardType:
                                                        //       TextInputType
                                                        //           .numberWithOptions(
                                                        //               decimal:
                                                        //                   true),
                                                        //   textInputAction:
                                                        //       TextInputAction
                                                        //           .done,
                                                        //   style:
                                                        //       formFieldTextStyle,
                                                        //   decoration:
                                                        //       formFieldStyle
                                                        //           .copyWith(
                                                        //               suffix: Text(
                                                        //                   "%"),
                                                        //               labelText:
                                                        //                   "%"),
                                                        //   textAlign:
                                                        //       TextAlign.end,
                                                        //   controller:
                                                        //       expenseController
                                                        //           .discountPercentageCtrl,
                                                        //   onChanged: (value) {
                                                        //     if (value!
                                                        //         .isEmpty) {
                                                        //       //remove keyboard
                                                        //       FocusScope.of(
                                                        //               context)
                                                        //           .unfocus();
                                                        //     }
                                                        //     if (expenseController
                                                        //         .isCashExpensesSelected) {
                                                        //       expenseController
                                                        //           .receivedAmountCtrl
                                                        //           .text = (expenseController
                                                        //                   .transaction
                                                        //                   .value
                                                        //                   .txnTotalAmount ??
                                                        //               0.0)
                                                        //           .toStringAsFixed(
                                                        //               2);
                                                        //     }
                                                        //
                                                        //     expenseController
                                                        //             .discountPercentageCtrl
                                                        //             .selection =
                                                        //         TextSelection.fromPosition(TextPosition(
                                                        //             offset: expenseController
                                                        //                 .discountPercentageCtrl
                                                        //                 .text
                                                        //                 .length));
                                                        //     expenseController
                                                        //         .updateDiscountPercentage(
                                                        //             value ?? "",
                                                        //             editorTag:
                                                        //                 'txn_discount_percent');
                                                        //   },
                                                        // ),
                                                      ),
                                                      SizedBox(
                                                        width: 20,
                                                      ),
                                                      Expanded(
                                                        child: Container(
                                                          child:
                                                              FormBuilderTextField(
                                                                  name:
                                                                      "txn_discount_amount",
                                                                  readOnly: expenseController
                                                                          .readOnlyFlag ||
                                                                      (!hasSubTotal),
                                                                  autocorrect:
                                                                      false,
                                                                  keyboardType: TextInputType.numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                                  textInputAction:
                                                                      TextInputAction
                                                                          .done,
                                                                  style:
                                                                      formFieldTextStyle,
                                                                  decoration:
                                                                      formFieldStyle.copyWith(
                                                                          labelText:
                                                                              "छुट रकम (Dis. Amount)"),
                                                                  textAlign:
                                                                      TextAlign
                                                                          .end,
                                                                  controller:
                                                                      expenseController
                                                                          .discountAmountCtrl,
                                                                  onChanged:
                                                                      (value) {
                                                                    expenseController
                                                                            .discountAmountCtrl
                                                                            .selection =
                                                                        TextSelection.fromPosition(TextPosition(
                                                                            offset:
                                                                                expenseController.discountAmountCtrl.text.length));
                                                                    expenseController.updateDiscountAmount(
                                                                        value!,
                                                                        editorTag:
                                                                            'txn_discount_amount');
                                                                  }),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                              SizedBox(
                                                height: 25,
                                              ),

                                              //====================================================VAT
                                              ...[
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Row(
                                                      children: [
                                                        Container(
                                                          width: 20,
                                                          height: 20,
                                                          child: Checkbox(
                                                            activeColor:
                                                                colorPrimary,
                                                            checkColor:
                                                                Colors.white,
                                                            value:
                                                                expenseController
                                                                    .isVatEnabled,
                                                            onChanged: (expenseController
                                                                        .readOnlyFlag ||
                                                                    (!hasSubTotal))
                                                                ? null
                                                                : (value) {
                                                                    expenseController
                                                                        .onToggleVat(
                                                                            value!);
                                                                  },
                                                          ),
                                                        ),
                                                        Text("  मु.अ. कर (VAT)",
                                                            style: labelStyle2)
                                                      ],
                                                    ),
                                                    SizedBox(height: 10.0),
                                                    Row(
                                                      mainAxisAlignment:
                                                          MainAxisAlignment
                                                              .spaceBetween,
                                                      children: [
                                                        Container(
                                                          width: 80,
                                                          child:
                                                              FormBuilderTextField(
                                                            name:
                                                                "txn_tax_percent",
                                                            readOnly: true,
                                                            autocorrect: false,
                                                            keyboardType:
                                                                TextInputType
                                                                    .numberWithOptions(
                                                                        decimal:
                                                                            true),
                                                            textInputAction:
                                                                TextInputAction
                                                                    .done,
                                                            style:
                                                                formFieldTextStyle,
                                                            decoration: formFieldStyle
                                                                .copyWith(
                                                                    suffix: Text(
                                                                        "%"),
                                                                    labelText:
                                                                        "%"),
                                                            textAlign:
                                                                TextAlign.end,
                                                            controller:
                                                                expenseController
                                                                    .vatPercentCtrl,
                                                            onChanged: (value) {
                                                              expenseController
                                                                  .onvatPercentChange(
                                                                      value!,
                                                                      editorTag:
                                                                          'txn_tax_percent');
                                                            },
                                                          ),
                                                        ),
                                                        SizedBox(
                                                          width: 20,
                                                        ),
                                                        Expanded(
                                                          child: Container(
                                                            child:
                                                                FormBuilderTextField(
                                                              name:
                                                                  "txn_tax_amount",
                                                              readOnly: true,
                                                              autocorrect:
                                                                  false,
                                                              keyboardType: TextInputType
                                                                  .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                              textInputAction:
                                                                  TextInputAction
                                                                      .done,
                                                              style:
                                                                  formFieldTextStyle,
                                                              decoration: formFieldStyle
                                                                  .copyWith(
                                                                      labelText:
                                                                          "मु.अ. कर रकम (VAT Amount) "),
                                                              textAlign:
                                                                  TextAlign.end,
                                                              controller:
                                                                  expenseController
                                                                      .vatAmountCtrl,
                                                              onChanged:
                                                                  (value) {
                                                                expenseController
                                                                    .onvatAmountChange(
                                                                        value!,
                                                                        editorTag:
                                                                            'txn_tax_amount');
                                                              },
                                                            ),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ],
                                                ),
                                                SizedBox(
                                                  height: 5,
                                                ),
                                              ],

                                              Divider(
                                                height: 5,
                                              ),
                                              Divider(
                                                height: 0,
                                              ),
                                              SizedBox(
                                                height: 15,
                                              ),

                                              // =============================================Total Amount
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    "कुल रकम",
                                                    style: labelStyle2,
                                                  ),
                                                  SizedBox(
                                                    height: 5,
                                                  ),
                                                  Container(
                                                      child: FormBuilderTextField(
                                                          name: "txn_total",
                                                          readOnly: true,
                                                          autocorrect: false,
                                                          keyboardType:
                                                              TextInputType.numberWithOptions(
                                                                  decimal:
                                                                      true),
                                                          textInputAction:
                                                              TextInputAction
                                                                  .done,
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration: formFieldStyle
                                                              .copyWith(
                                                                  labelText:
                                                                      "Total Amount"),
                                                          textAlign:
                                                              TextAlign.end,
                                                          controller:
                                                              expenseController
                                                                  .totalAmountCtrl)),
                                                ],
                                              ),
                                              SizedBox(
                                                height: 25,
                                              ),

                                              // =============================================Received Amount
                                              Row(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.end,
                                                  children: [
                                                    Expanded(
                                                      child: Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Row(
                                                            children: [
                                                              Container(
                                                                width: 20,
                                                                height: 20,
                                                                child: Checkbox(
                                                                    activeColor:
                                                                        colorPrimary,
                                                                    checkColor:
                                                                        Colors
                                                                            .white,
                                                                    value: iscashExpenses
                                                                        ? true
                                                                        : (expenseController
                                                                            .isReceived),
                                                                    onChanged: (expenseController.readOnlyFlag ||
                                                                            (!hasSubTotal))
                                                                        ? null
                                                                        : (value) {
                                                                            expenseController.setIsReceived =
                                                                                value!;
                                                                            if (value!) {
                                                                              expenseController.receivedAmountCtrl.text = (expenseController.transaction.value.txnTotalAmount ?? 0.0).toStringAsFixed(2);
                                                                              expenseController.dueAmountCtrl.text = "0.00";

                                                                              expenseController.transaction.value.txnCashAmount = (expenseController.transaction.value.txnTotalAmount ?? 0.0);

                                                                              expenseController.transaction.value.txnBalanceAmount = 0.00;
                                                                            } else {
                                                                              expenseController.receivedAmountCtrl.text = "0.00";
                                                                            }
                                                                          }),
                                                              ),
                                                              Text(
                                                                  " भुक्तानी रकम",
                                                                  style:
                                                                      labelStyle2)
                                                            ],
                                                          ),
                                                          SizedBox(
                                                            height: 10,
                                                          ),
                                                          PaymentModeSelector(
                                                              onChangedFn: (v) {
                                                                expenseController
                                                                    .transaction
                                                                    .value
                                                                    .txnPaymentTypeId = v;

                                                                expenseController
                                                                    .transaction
                                                                    .value
                                                                    .txnPaymentReference = null;
                                                                expenseController
                                                                    .transaction
                                                                    .value
                                                                    .chequeIssueDateBS = null;
                                                                expenseController
                                                                    .transaction
                                                                    .refresh();
                                                              },
                                                              paymentModeID:
                                                                  expenseController
                                                                      .transaction
                                                                      .value
                                                                      .txnPaymentTypeId,
                                                              enableFlag:
                                                                  (expenseController
                                                                      .readOnlyFlag))
                                                        ],
                                                      ),
                                                    ),
                                                    SizedBox(
                                                      width: 10,
                                                    ),
                                                    Expanded(
                                                      child:
                                                          FormBuilderTextField(
                                                        name: "txn_cash_amount",
                                                        readOnly: (false),
                                                        autocorrect: false,
                                                        keyboardType: TextInputType
                                                            .numberWithOptions(
                                                                decimal: true),
                                                        textInputAction:
                                                            TextInputAction
                                                                .done,
                                                        inputFormatters: [
                                                          FilteringTextInputFormatter
                                                              .allow(RegExp(
                                                                  r'^(\d+)?\.?\d{0,2}'))
                                                        ],
                                                        style:
                                                            formFieldTextStyle,
                                                        decoration: formFieldStyle
                                                            .copyWith(
                                                                labelText:
                                                                    "Paid Amount"),
                                                        textAlign:
                                                            TextAlign.end,
                                                        controller:
                                                            expenseController
                                                                .receivedAmountCtrl,
                                                        onChanged: (value) {
                                                          expenseController
                                                                  .receivedAmountCtrl
                                                                  .selection =
                                                              TextSelection.fromPosition(
                                                                  TextPosition(
                                                                      offset: expenseController
                                                                          .receivedAmountCtrl
                                                                          .text
                                                                          .length));
                                                          expenseController
                                                              .changeReceivedAmount(
                                                                  value ??
                                                                      "0.00",
                                                                  editorTag:
                                                                      'txn_cash_amount');
                                                        },
                                                      ),
                                                    )
                                                  ]),
                                              ...(expenseController
                                                          .transaction
                                                          .value
                                                          .txnPaymentTypeId ==
                                                      PAYMENT_MODE_CHEQUE_ID)
                                                  ? [
                                                      SizedBox(
                                                        height: 25,
                                                      ),
                                                      Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text("चेक/भौचर न.",
                                                              style:
                                                                  labelStyle2),
                                                          SizedBox(
                                                            height: 10,
                                                          ),
                                                          TextField(
                                                              autocorrect:
                                                                  false,
                                                              readOnly:
                                                                  expenseController
                                                                      .readOnlyFlag,
                                                              style:
                                                                  formFieldTextStyle,
                                                              decoration: formFieldStyle
                                                                  .copyWith(
                                                                      labelText:
                                                                          "Cheque/Voucher No."),
                                                              controller:
                                                                  expenseController
                                                                      .paymentRefCtrl,
                                                              onChanged: (v) {
                                                                expenseController
                                                                    .transaction
                                                                    .value
                                                                    .txnPaymentReference = v;
                                                                expenseController
                                                                    .transaction
                                                                    .refresh();
                                                              }),
                                                        ],
                                                      ),
                                                    ]
                                                  : [],
                                              if (expenseController.transaction
                                                      .value.txnPaymentTypeId ==
                                                  PAYMENT_MODE_CHEQUE_ID) ...[
                                                SizedBox(
                                                  height: 25,
                                                ),
                                                Column(
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.start,
                                                  children: [
                                                    Text("चेक मिति",
                                                        style: labelStyle2),
                                                    SizedBox(
                                                      height: 10,
                                                    ),
                                                    CustomDatePickerTextField(
                                                      labelText: "Cheque Date",
                                                      readOnly:
                                                          expenseController
                                                              .readOnlyFlag,
                                                      // maxBSDate: NepaliDateTime.now(),
                                                      initialValue:
                                                          expenseController
                                                              .transaction
                                                              .value
                                                              .chequeIssueDateBS,
                                                      onChange: (selectedDate) {
                                                        expenseController
                                                                .transaction
                                                                .value
                                                                .chequeIssueDateBS =
                                                            selectedDate;
                                                      },
                                                    ),
                                                  ],
                                                )
                                              ],

                                              SizedBox(
                                                height: 25,
                                              ),

                                              // =============================================Balance Amount
                                              ...iscashExpenses
                                                  ? []
                                                  : [
                                                      Column(
                                                        crossAxisAlignment:
                                                            CrossAxisAlignment
                                                                .start,
                                                        children: [
                                                          Text(
                                                              "बाँकी रहेको रकम",
                                                              style:
                                                                  labelStyle2),
                                                          SizedBox(
                                                            height: 10,
                                                          ),
                                                          FormBuilderTextField(
                                                              name:
                                                                  "txn_balance_amount",
                                                              readOnly: true,
                                                              autocorrect:
                                                                  false,
                                                              keyboardType: TextInputType
                                                                  .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                              textInputAction:
                                                                  TextInputAction
                                                                      .done,
                                                              style:
                                                                  formFieldTextStyle,
                                                              decoration: formFieldStyle
                                                                  .copyWith(
                                                                      labelText:
                                                                          "Balance Due"),
                                                              textAlign:
                                                                  TextAlign.end,
                                                              controller:
                                                                  expenseController
                                                                      .dueAmountCtrl),
                                                        ],
                                                      ),
                                                      SizedBox(
                                                        height: 20,
                                                      ),
                                                    ],
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),

                                  //===============================================Image
                                  Container(
                                    width: double.infinity,
                                    child: Card(
                                      elevation: 2,
                                      child: Container(
                                        child: Container(
                                            // color: Colors.red,
                                            height: 140,
                                            width: 100,
                                            // width: ,
                                            // child: (null==state.selectImage)?
                                            child: FormBuilderImagePicker(
                                                name: "image_picker",
                                                bottomSheetPadding:
                                                    EdgeInsets.all(0),
                                                // imageHeight: 100,
                                                // imageWidth: 100,

                                                // // maxHeight: 100,
                                                // // maxWidth: 100,
                                                // imageMargin: EdgeInsets.symmetric(
                                                //     horizontal: 10),
                                                enabled: !expenseController
                                                    .readOnlyFlag,
                                                decoration: InputDecoration(
                                                  border: InputBorder.none,
                                                ),
                                                maxImages: 2,
                                                iconColor: colorPrimaryLight,
                                                // validators: [],
                                                initialValue:
                                                    expenseController.files,
                                                onChanged: (_fls) async {
                                                  // expenseController.files.value =
                                                  //     _fls.cast<File>();
                                                  // expenseController.files.refresh();

                                                  // bool isLargeFile =
                                                  //     await expenseController
                                                  //         .checkLargeImage(_fls);
                                                  // if (isLargeFile) {
                                                  //   showToastMessage(context,
                                                  //       message:
                                                  //           MAX_IMAGE_SIZE_MESSAGE,
                                                  //       alertType: AlertType.Error);
                                                  //   return;
                                                  // }
                                                })

                                            //     :customImageBox(
                                            //     state.selectImage,
                                            //     onCancel: (expenseController.readOnlyFlag)?  null : () => state.imageCancelButtonOnClickHandler()
                                            // ),
                                            ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                //=================================================Save button
                bottomNavigationBar: BottomSaveCancelButton(
                  shadow: false,
                  hasNew: !expenseController.editFlag,
                  onSaveAndNewBtnPressedFn: () {
                    this.onSave(context, forNew: true);
                  },
                  enableFlag: !expenseController.readOnlyFlag,
                  onSaveBtnPressedFn: (expenseController.readOnlyFlag)
                      ? null
                      : () async {
                          this.onSave(context);
                        },
                )));
      }
    });
  }
}
