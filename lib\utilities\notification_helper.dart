import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:rxdart/subjects.dart';
import 'package:timezone/timezone.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:timezone/timezone.dart' as tz;

import '../main.dart';
import 'constants.dart';

class ReminderNotification {
  final int? id;
  final String? title;
  final String? body;
  final String? payload;

  ReminderNotification({
    @required this.id,
    @required this.title,
    @required this.body,
    @required this.payload,
  });
}

class Reminder {
  final String time;
  final RepeatInterval repeat;
  final String name;

  const Reminder({
    required this.time,
    required this.repeat,
    required this.name,
  });

  static Reminder? fromJson(dynamic json) {
    return json != null
        ? Reminder(
            time: json["time"],
            repeat: parseRepeatIntervalToValue(json["repeat"]),
            name: json["name"])
        : null;
  }

  dynamic toJson() {
    return {
      "time": time,
      "repeat": parseRepeatIntervalToString(repeat),
      "name": name,
    };
  }

  static String parseRepeatIntervalToString(RepeatInterval repeat) {
    switch (repeat) {
      case RepeatInterval.daily:
        return "Daily";
      case RepeatInterval.everyMinute:
        return "EveryMinute";
      case RepeatInterval.hourly:
        return "Hourly";
      case RepeatInterval.weekly:
        return "Weekly";
      default:
        return "Daily";
    }
  }

  static RepeatInterval parseRepeatIntervalToValue(String repeat) {
    switch (repeat) {
      case "Daily":
        return RepeatInterval.daily;
      case "EveryMinute":
        return RepeatInterval.everyMinute;
      case "Hourly":
        return RepeatInterval.hourly;
      case "Weekly":
        return RepeatInterval.weekly;
      default:
        return RepeatInterval.weekly;
    }
  }
}

final BehaviorSubject<ReminderNotification> didReceiveLocalNotificationSubject =
    BehaviorSubject<ReminderNotification>();

final BehaviorSubject<String> selectNotificationSubject =
    BehaviorSubject<String>();

Future<void> initNotifications(
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin) async {
  var initializationSettingsAndroid =
      // AndroidInitializationSettings('@mipmap/ic_launcher');
      const AndroidInitializationSettings(
          '@drawable/ic_stat_onesignal_default');

  var initializationSettingsIOS = DarwinInitializationSettings(
      requestAlertPermission: false,
      requestBadgePermission: false,
      requestSoundPermission: false,
      onDidReceiveLocalNotification:
          (int? id, String? title, String? body, String? payload) async {
        didReceiveLocalNotificationSubject.add(ReminderNotification(
            id: id, title: title, body: body, payload: payload));
      });

  var initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid, iOS: initializationSettingsIOS);

  await flutterLocalNotificationsPlugin.initialize(
    initializationSettings,
    // onSelectNotification: (String? payload) async {
    //   // Log.d("notification clicked");
    //   navigatorKey.currentState!.pushNamed('/notifications');
    //   if (payload != null) {
    //     if (payload == "reminder") {
    //       // Navigator.pushNamed(context, "/reminderList");
    //     }
    //     // debugLog.d('notification payload: ' + payload);
    //   }
    //   selectNotificationSubject.add(payload ?? "");
    // },
  );
}

Future initializetimezone() async {
  tz.initializeTimeZones();
}

Future<void> showNotificationAt(
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin, int id,
    {String title = "MobileKhaata",
    required String body,
    required DateTime scheduledNotificationDateTime}) async {
  var androidPlatformChannelSpecifics = AndroidNotificationDetails(
    id.toString(),
    'Reminder notifications \n Remember about it',
    // icon: '@mipmap/ic_launcher',
    importance: Importance.max,
    priority: Priority.high,
  );
  await initializetimezone();

  var iOSPlatformChannelSpecifics = const DarwinNotificationDetails();
  var platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics);
  tz.TZDateTime zonedTime = tz.TZDateTime.local(
          scheduledNotificationDateTime.year,
          scheduledNotificationDateTime.month,
          scheduledNotificationDateTime.day,
          scheduledNotificationDateTime.hour,
          scheduledNotificationDateTime.minute)
      .subtract(
    scheduledNotificationDateTime.timeZoneOffset,
  );
  await flutterLocalNotificationsPlugin.zonedSchedule(
    id,
    title,
    body,
    zonedTime,
    platformChannelSpecifics,
    androidAllowWhileIdle: true,
    payload: "reminder",
    uiLocalNotificationDateInterpretation:
        UILocalNotificationDateInterpretation.absoluteTime,
  );
}

Future<void> showNotification(
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin,
    {int id = 1,
    String title = APP_NAME,
    String body = "Notification Body",
    String payload = ""}) async {
  var androidPlatformChannelSpecifics = const AndroidNotificationDetails(
      '0', 'Natalia your channel description',
      importance: Importance.max, priority: Priority.high, ticker: 'ticker');
  var iOSPlatformChannelSpecifics = const DarwinNotificationDetails();
  var platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics);
  await flutterLocalNotificationsPlugin
      .show(id, title, body, platformChannelSpecifics, payload: payload);
}

Future<void> turnOffNotification(
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin) async {
  await flutterLocalNotificationsPlugin.cancelAll();
}

Future<void> turnOffNotificationById(
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin,
    num id) async {
  await flutterLocalNotificationsPlugin.cancel(id.toInt());
}

Future<void> scheduleNotification(
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin,
    int id,
    String body,
    DateTime scheduledNotificationDateTime) async {
  var androidPlatformChannelSpecifics = AndroidNotificationDetails(
    id.toString(),
    'Reminder notifications \n Remember about it',
    icon: '@mipmap/ic_launcher',
    importance: Importance.max,
    priority: Priority.high,
  );
  await initializetimezone();

  var iOSPlatformChannelSpecifics = const DarwinNotificationDetails();
  var platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics);
  tz.TZDateTime zonedTime = tz.TZDateTime.local(
          scheduledNotificationDateTime.year,
          scheduledNotificationDateTime.month,
          scheduledNotificationDateTime.day,
          scheduledNotificationDateTime.hour,
          scheduledNotificationDateTime.minute)
      .subtract(
    scheduledNotificationDateTime.timeZoneOffset,
  );
  await flutterLocalNotificationsPlugin.zonedSchedule(
    id,
    'Mobile Khata',
    body,
    zonedTime,
    platformChannelSpecifics,
    androidAllowWhileIdle: true,
    payload: "reminder",
    uiLocalNotificationDateInterpretation:
        UILocalNotificationDateInterpretation.absoluteTime,
  );
}

Future<void> scheduleNotificationPeriodically(
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin,
    String id,
    String body,
    RepeatInterval interval) async {
  var androidPlatformChannelSpecifics = AndroidNotificationDetails(
    id,
    'Reminder notifications \n Remember about it',
    icon: 'smile_icon',
  );
  var iOSPlatformChannelSpecifics = const DarwinNotificationDetails();
  var platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: iOSPlatformChannelSpecifics);
  await flutterLocalNotificationsPlugin.periodicallyShow(
      0, 'Reminder', body, interval, platformChannelSpecifics);
}

void requestIOSPermissions(
    FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin) {
  flutterLocalNotificationsPlugin
      .resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin>()
      ?.requestPermissions(
        alert: true,
        badge: true,
        sound: true,
      );
}
