import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/payment_type_model.dart';
import 'package:mobile_khaata_v2/app/repository/payment_type_repository.dart';

class PaymetModeListController extends GetxController {
  final _isLoading = true.obs;

  bool get isLoading => _isLoading.value;

  set isLoading(bool flag) => _isLoading.value = flag;

  List<PaymentTypeModel> modes = [];

  PaymentTypeRepository paymentTypeRepository = PaymentTypeRepository();

  List<PaymentTypeModel> get getModes => modes;

  @override
  onInit() async {
    _isLoading(true);
    await fetchAll();
    _isLoading(false);
    super.onInit();
  }

  fetchAll() async {
    _isLoading(true);
    modes.addAll(await paymentTypeRepository.getAll());
    update();
    // Log.d("fetched all datta");
    _isLoading(false);
  }
}
