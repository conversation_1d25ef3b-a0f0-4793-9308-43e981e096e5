// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_dropdown.dart';
import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
import 'package:mobile_khaata_v2/app/model/report/party_satement_report_model.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/party_list/party_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/party_statement_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/party_report/party_statement_report_controller.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';

/**
 * PARTY STATEMENT REPORT - COMPLETE REWRITE
 *
 * This report shows financial transactions between business and a specific party.
 * Displays in standard accounting ledger format with proper running balance.
 *
 * ACCOUNTING LOGIC:
 * - Debit (Dr): Amounts that increase what party owes us
 * - Credit (Cr): Amounts that reduce what party owes us
 * - Balance: Running total showing net position
 *
 * CALCULATION METHOD:
 * - Start with opening balance
 * - Each transaction: Balance = Previous Balance + (Dr - Cr)
 * - Positive Balance = Party owes us (Receivable)
 * - Negative Balance = We owe party (Payable)
 */

class PartyStatementReport extends StatefulWidget {
  const PartyStatementReport({super.key});

  @override
  _PartyStatementReportState createState() => _PartyStatementReportState();
}

class _PartyStatementReportState extends State<PartyStatementReport> {
  // CONTROLLERS
  final PartyStatementReportController _controller =
      PartyStatementReportController();
  final PartyListController _partyController = PartyListController();

  // STATE VARIABLES
  String startDate = currentDate;
  String endDate = currentDate;
  String? selectedLedgerID;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void dispose() {
    _partyController.dispose();
    super.dispose();
  }

  /**
   * INITIALIZE DATA
   * Load party list for dropdown
   */
  Future<void> _initializeData() async {
    await _partyController.init();
    setState(() {});
  }

  /**
   * GENERATE REPORT
   * Create party statement for selected party and date range
   */
  void _generateReport() {
    if (selectedLedgerID == null) return;

    _controller.generatePartyReport(
      startDate: startDate,
      endDate: endDate,
      types: [], // Include all transaction types
      ledgerID: selectedLedgerID,
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: _buildAppBar(),
        body: _buildBody(),
        bottomNavigationBar: _buildClosingBalanceBar(),
      ),
    );
  }

  /**
   * BUILD APP BAR
   */
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      elevation: 0,
      titleSpacing: -5.0,
      backgroundColor: colorPrimary,
      title: const Text(
        "पार्टी खाता विवरण (Party Statement)",
        style: TextStyle(
          fontSize: 17,
          color: Colors.white,
          fontFamily: 'HelveticaRegular',
          fontWeight: FontWeight.bold,
        ),
      ),
      actions: [
        PrintButton(
          onPressed: selectedLedgerID == null ? null : _handlePrint,
        ),
      ],
    );
  }

  /**
   * BUILD MAIN BODY
   */
  Widget _buildBody() {
    return GestureDetector(
      onTap: () => FocusScope.of(context).unfocus(),
      child: Container(
        color: Colors.black12,
        child: Column(
          children: [
            _buildDateFilters(),
            const Divider(height: 4, color: Colors.black54),
            _buildPartySelector(),
            const Divider(height: 4, color: Colors.black54),
            _buildReportContent(),
          ],
        ),
      ),
    );
  }

  /**
   * BUILD DATE FILTERS
   */
  Widget _buildDateFilters() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Row(
        children: [
          // FROM DATE
          Expanded(
            flex: 2,
            child: ReportCustomDatePickerTextField(
              initialValue: toDateBS(DateTime.parse(startDate)),
              hintText: "From Date",
              onChange: (selectedDate) {
                startDate = toDateAD(NepaliDateTime.parse(selectedDate));
                setState(() {});
                _generateReport();
              },
            ),
          ),

          // SEPARATOR
          Expanded(
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Text(
                "TO",
                style: labelStyle2,
                textAlign: TextAlign.center,
              ),
            ),
          ),

          // TO DATE
          Expanded(
            flex: 2,
            child: ReportCustomDatePickerTextField(
              initialValue: toDateBS(DateTime.parse(endDate)),
              hintText: "To Date",
              onChange: (selectedDate) {
                endDate = toDateAD(NepaliDateTime.parse(selectedDate));
                setState(() {});
                _generateReport();
              },
            ),
          ),
        ],
      ),
    );
  }

  /**
   * BUILD PARTY SELECTOR
   */
  Widget _buildPartySelector() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10),
      child: Obx(() {
        if (_partyController.partyLoading) {
          return Container(
            height: 48,
            decoration: BoxDecoration(
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(4),
            ),
            child: const Center(child: CircularProgressIndicator()),
          );
        }

        return CustomDropdown(
          borderless: true,
          style: formFieldTextStyle,
          decoration: formFieldStyle,
          value: selectedLedgerID,
          allowClear: false,
          placeholder: "Select Party",
          options: _partyController.filteredLedgers.map((ledger) {
            return {
              'key': ledger.ledgerId,
              'value': ledger.ledgerTitle,
            };
          }).toList(),
          onChange: (value) {
            selectedLedgerID = value;
            setState(() {});
            _generateReport();
          },
        );
      }),
    );
  }

  /**
   * BUILD REPORT CONTENT
   */
  Widget _buildReportContent() {
    return Obx(() {
      if (_controller.txnLoading) {
        return Container(
          color: Colors.white,
          child: const Center(child: CircularProgressIndicator()),
        );
      }

      if (_controller.transactions.isEmpty) {
        return Container(
          color: Colors.white,
          width: double.infinity,
          child: const Center(
            child: Text(
              "No Records Found",
              style: TextStyle(color: Colors.black54),
            ),
          ),
        );
      }

      return Expanded(child: _buildTransactionList());
    });
  }

  /**
   * BUILD TRANSACTION LIST WITH PROPER RUNNING BALANCE
   */
  Widget _buildTransactionList() {
    return ListView.builder(
      itemCount: _controller.transactions.length + 1, // +1 for header
      itemBuilder: (context, index) {
        // HEADER ROW
        if (index == 0) {
          return _buildHeaderRow();
        }

        // DATA ROWS
        final transaction = _controller.transactions[index - 1];
        final runningBalance = _calculateRunningBalance(index - 1);

        return _buildTransactionRow(transaction, runningBalance);
      },
    );
  }

  /**
   * BUILD HEADER ROW
   */
  Widget _buildHeaderRow() {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    "Particulars",
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.left,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    "Dr",
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.right,
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Text(
                    "Cr",
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.right,
                  ),
                ),
                Expanded(
                  flex: 3,
                  child: Text(
                    "Balance",
                    style: TextStyle(fontWeight: FontWeight.bold),
                    textAlign: TextAlign.right,
                  ),
                ),
              ],
            ),
          ),
          const Divider(height: 4, color: Colors.black54),
        ],
      ),
    );
  }

  /**
   * BUILD TRANSACTION ROW
   */
  Widget _buildTransactionRow(
      PartyStatementReportModel transaction, double runningBalance) {
    return Container(
      color: Colors.white,
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // PARTICULARS
                Expanded(
                  flex: 3,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        transaction.description ?? "Unknown Transaction",
                        style: const TextStyle(fontWeight: FontWeight.w500),
                      ),
                      const SizedBox(height: 3),
                      Text(
                        transaction.txnDateBS ?? "",
                        style: const TextStyle(
                            fontSize: 12, color: Colors.black54),
                      ),
                      if (transaction.txnRefNumberChar?.isNotEmpty == true) ...[
                        const SizedBox(height: 2),
                        Text(
                          "Ref: #${transaction.txnRefNumberChar}",
                          style: const TextStyle(
                              fontSize: 11, color: Colors.black45),
                        ),
                      ],
                    ],
                  ),
                ),

                // DEBIT
                Expanded(
                  flex: 2,
                  child: Text(
                    _formatAmount(transaction.drAmount),
                    textAlign: TextAlign.right,
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ),

                // CREDIT
                Expanded(
                  flex: 2,
                  child: Text(
                    _formatAmount(transaction.crAmount),
                    textAlign: TextAlign.right,
                    style: const TextStyle(fontFamily: 'monospace'),
                  ),
                ),

                // BALANCE
                Expanded(
                  flex: 3,
                  child: Text(
                    formatCurrencyAmount(runningBalance, false),
                    textAlign: TextAlign.right,
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      fontFamily: 'monospace',
                      color: runningBalance >= 0
                          ? Colors.green[700]
                          : Colors.red[700],
                    ),
                  ),
                ),
              ],
            ),
          ),
          Divider(height: 1, color: Colors.grey[300]),
        ],
      ),
    );
  }

  /**
   * CALCULATE RUNNING BALANCE UP TO SPECIFIC INDEX
   * Uses the SAME logic as PartyStatementReportController
   */
  double _calculateRunningBalance(int upToIndex) {
    if (_controller.transactions.isEmpty) return 0.0;

    // Start with opening balance from first transaction (same as controller)
    double balance = _controller.transactions.first.txnBalanceAmount ?? 0.0;

    // Add subsequent transaction effects using SAME logic as controller
    for (int i = 1; i <= upToIndex; i++) {
      final txn = _controller.transactions[i];
      final drAmount = txn.drAmount ?? 0.0;
      final crAmount = txn.crAmount ?? 0.0;
      // Use EXACT same calculation as PartyStatementReportController
      balance += (drAmount - crAmount);
    }

    return balance;
  }

  /**
   * FORMAT AMOUNT FOR DISPLAY
   * Only show non-zero amounts, empty string for zero
   */
  String _formatAmount(double? amount) {
    if (amount == null || amount == 0.0) return "0.00";
    return formatCurrencyAmount(amount, false);
  }

  /**
   * BUILD CLOSING BALANCE BAR
   *
   * CALCULATION EXPLANATION:
   * - Uses the totalClosingBalance from the controller which includes opening balance
   * - This matches the calculation method used in the parent party detail page
   * - Shows absolute value with proper receivable/payable label
   */
  Widget _buildClosingBalanceBar() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
      color: colorPrimary,
      child: Obx(() {
        // Use the controller's totalClosingBalance which includes opening balance
        final actualClosingBalance = _controller.totalClosingBalance.value;

        final isReceivable = actualClosingBalance >= 0;

        return Row(
          children: [
            Expanded(
              child: Text(
                isReceivable ? "Closing (Receivable)" : "Closing (Payable)",
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Text(
              formatCurrencyAmount(actualClosingBalance.abs(), false),
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                fontFamily: 'monospace',
              ),
            ),
          ],
        );
      }),
    );
  }

  /**
   * HANDLE PRINT FUNCTIONALITY
   */
  void _handlePrint() {
    String partyText = "Unknown Party";

    try {
      final selectedLedger = _partyController.filteredLedgers
          .firstWhere((ledger) => ledger.ledgerId == selectedLedgerID);
      partyText = selectedLedger.ledgerTitle ?? "Unknown Party";
    } catch (e) {
      // Handle case where ledger is not found
    }

    Navigator.pushNamed(
      context,
      '/printSinglePartyStatement',
      arguments: PartyStatementPrintPage(
        transactions: _controller.transactions,
        partyText: partyText,
        startDate: startDate,
        endDate: endDate,
      ),
    );
  }
}
