import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/controllers/registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/registration_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/annex_item_model.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

class AnnexReportPrintPage extends StatelessWidget {
  final String? pageTitle;
  final List<AnnexItemModel>? transactions;
  final String? endDate;
  final String? startDate;

  const AnnexReportPrintPage(
      {super.key,
      this.pageTitle = "Annex 13 Report",
      this.transactions,
      required this.startDate,
      required this.endDate});

  @override
  Widget build(BuildContext context) {
    String startDate =
        NepaliDateTime.parse(toDateBS(DateTime.parse(this.startDate!)))
            .format("y-MM-dd");
    String endDate =
        NepaliDateTime.parse(toDateBS(DateTime.parse(this.endDate!)))
            .format("y-MM-dd");

    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        toolbarHeight: 60,
        backgroundColor: colorPrimary,
        elevation: 4,
        leading: BackButton(
          onPressed: () => Navigator.pop(context, false),
        ),
        centerTitle: false,
        titleSpacing: -5.0,
        title: Text(
          pageTitle ?? "",
          style: const TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
        actions: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10)),
                  backgroundColor: colorPrimary,
                  foregroundColor: colorPrimaryLightest,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Column(
                  children: [
                    const Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                    const Text(
                      "Cancel",
                      style: TextStyle(color: Colors.white, fontSize: 10),
                    ),
                  ],
                )),
          ),
        ],
      ),
      body: PdfPreview(
        useActions: true,
        canChangePageFormat: false,
        // allowPrinting: false,
        // allowSharing: false,
        initialPageFormat: defaultPdfPageFormat,
        maxPageWidth: 700,
        // actions: actions,
        pdfFileName: "$pageTitle From $startDate to $endDate .pdf",
        build: (format) {
          return generateReport(defaultPdfPageFormat,
              transactions: transactions!,
              billTitle: pageTitle!,
              startDate: this.startDate!,
              endDate: this.endDate!);
        },
      ),
    ));
  }
}

Future<Uint8List> generateReport(PdfPageFormat pageFormat,
    {List<AnnexItemModel>? transactions,
    String billTitle = "",
    String? startDate,
    String? endDate}) async {
  double total = 0.0;
  double totalTaxable = 0.0;
  double totalTax = 0.0;

  final products = <Product>[
    if (transactions != null && transactions.isNotEmpty)
      ...transactions.map((txn) {
        // ENHANCED: Better null safety and formatting
        final openingBalance = txn.openingBalance ?? 0.0;
        final salesTaxable = txn.salesTaxableTotal ?? 0.0;
        final purchaseTaxable = txn.purchaseTaxableTotal ?? 0.0;
        final salesReturnTaxable = txn.salesReturnTaxableTotal ?? 0.0;
        final purchaseReturnTaxable = txn.purchaseReturnTaxableTotal ?? 0.0;
        final closingBalance = txn.closingBalance ?? 0.0;

        return Product(
          txn.tinNo ?? "",
          txn.ledgerName ?? "Unknown Party",
          _safeFormatAmount(openingBalance),
          _safeFormatAmount(salesTaxable),
          _safeFormatAmount(purchaseTaxable),
          _safeFormatAmount(salesReturnTaxable),
          _safeFormatAmount(purchaseReturnTaxable),
          _safeFormatAmount(closingBalance),
        );
      }).toList()
  ];

  // ENHANCED: Calculate totals with proper null safety
  double totalSales = 0.0;
  double totalPurchase = 0.0;
  double totalSalesReturn = 0.0;
  double totalPurchaseReturn = 0.0;

  if (transactions != null && transactions.isNotEmpty) {
    for (var txn in transactions) {
      totalSales += txn.salesTaxableTotal ?? 0.0;
      totalPurchase += txn.purchaseTaxableTotal ?? 0.0;
      totalSalesReturn += txn.salesReturnTaxableTotal ?? 0.0;
      totalPurchaseReturn += txn.purchaseReturnTaxableTotal ?? 0.0;
    }
  }
  totalSales = _safeFormatAmount(totalSales);
  totalPurchase = _safeFormatAmount(totalPurchase);
  totalSalesReturn = _safeFormatAmount(totalSalesReturn);
  totalPurchaseReturn = _safeFormatAmount(totalPurchaseReturn);

  RegistrationDetailController _registrationDetailController =
      Get.find<RegistrationDetailController>(
          tag: "RegistrationDetailController");
  ImageModel sellerImageModel = _registrationDetailController.logo;

  RegistrationDetailModel myDetail =
      _registrationDetailController.registrationDetail;

  final invoice = Invoice(
    myDetail: myDetail,
    sellerImage: sellerImageModel,
    startDate: NepaliDateTime.parse(toDateBS(DateTime.parse(startDate ?? "")))
        .format("y/MM/dd"),
    endDate: NepaliDateTime.parse(toDateBS(DateTime.parse(endDate ?? "")))
        .format("y/MM/dd"),
    products: products,
    totalSales: totalSales,
    totalPurchase: totalPurchase,
    totalSalesReturn: totalSalesReturn,
    totalPurchaseReturn: totalPurchaseReturn,
    reportTitle: "Annex-13 (Mis-match Report) ",
    baseColor: PdfColors.lightBlue,
    accentColor: PdfColors.blueGrey900,
  );

  return await invoice.buildPdf(pageFormat);
}

class Invoice {
  Invoice(
      {this.myDetail,
      this.sellerImage,
      this.products,
      this.baseColor,
      this.accentColor,
      this.startDate,
      this.endDate,
      this.totalPurchaseReturn,
      this.totalSales,
      this.totalSalesReturn,
      this.totalPurchase,
      this.partyName,
      this.txnType,
      this.reportTitle});

  final RegistrationDetailModel? myDetail;
  final ImageModel? sellerImage;

  final List<Product>? products;
  final PdfColor? baseColor;
  final PdfColor? accentColor;
  final String? startDate;
  final String? endDate;
  final String? partyName;
  final String? txnType;
  final String? reportTitle;
  double? totalSales;
  double? totalPurchase;
  double? totalSalesReturn;
  double? totalPurchaseReturn;

  static const _darkColor = PdfColors.blueGrey800;
  static const _lightColor = PdfColors.black;
  static const _VedColor = PdfColor.fromInt(0xFF3560AF);
  PdfColor get _baseTextColor =>
      baseColor!.luminance < 0.5 ? _lightColor : _darkColor;

  var currencyInWords = NepaliNumberFormat(
    inWords: true,
    language: Language.english,
    isMonetory: true,
    decimalDigits: 2,
  );

  String? _logo;

  Future<Uint8List> buildPdf(PdfPageFormat pageFormat) async {
    // Create a PDF document.
    final doc = pw.Document();

    final font1 = await rootBundle.load('assets/roboto1.ttf');
    final font2 = await rootBundle.load('assets/roboto1.ttf');
    final font3 = await rootBundle.load('assets/roboto3.ttf');

    _logo = await rootBundle.loadString('assets/mobilekhata.svg');

    // Add page to the PDF
    doc.addPage(
      pw.MultiPage(
        // pageTheme: _buildTheme(
        //   pageFormat,
        //   pw.Font.ttf(font1),
        //   pw.Font.ttf(font2),
        //   pw.Font.ttf(font3),
        // ),
        header: _buildHeader,
        footer: _buildFooter,
        build: (context) => [
          _header(context),
          _newHeader(context),
          _contentTable(context),
          _tableTotal(context),
          pw.SizedBox(height: 20),
          // _contentFooter(context),
          pw.SizedBox(height: 20),
        ],
      ),
    );

    // Return the PDF file content
    return doc.save();
  }

  pw.Widget _tableTotal(pw.Context context) {
    return pw.Container(
        padding: const pw.EdgeInsets.only(top: 10),
        child: pw.Column(children: [
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Text("Total Sales : ${formatCurrencyAmount((totalSales!))}")
          ]),
          pw.SizedBox(height: 10),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Text(
                "Total Purchase : ${formatCurrencyAmount((totalPurchase!))}")
          ]),
          pw.SizedBox(height: 10),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Text(
                "Total Sales Return : ${formatCurrencyAmount((totalSalesReturn!))}")
          ]),
          pw.SizedBox(height: 10),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Text(
                "Total Purchase Return : ${formatCurrencyAmount((totalPurchaseReturn!))}")
          ]),
        ]));
  }

  pw.Widget _buildHeader(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Expanded(
              child: pw.Column(
                mainAxisSize: pw.MainAxisSize.min,
                children: [
                  // ignore: unnecessary_null_comparison
                  if (null != sellerImage!.imageBitmap) ...{
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.center,
                        children: [
                          pw.Container(
                            margin: const pw.EdgeInsets.only(top: -20),
                            alignment: pw.Alignment.center,
                            height: 60,
                            width: 60,
                            child: sellerImage!.imageBitmap != null
                                ? pw.Image(pw.MemoryImage(Uint8List.fromList(
                                    sellerImage!.imageBitmap!)))
                                : pw.PdfLogo(),
                          ),
                        ])
                  },
                  // pw.Container(
                  //   color: baseColor,
                  //   padding: pw.EdgeInsets.only(top: 3),
                  // ),
                ],
              ),
            ),
          ],
        ),
        pw.Row(crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
          pw.Expanded(
              child: pw.Column(mainAxisSize: pw.MainAxisSize.min, children: [
            pw.Container(
              margin: const pw.EdgeInsets.only(top: 10),
              alignment: pw.Alignment.center,
              child: pw.Text(
                myDetail?.businessName ?? "",
                style: pw.TextStyle(
                  color: _VedColor,
                  fontWeight: pw.FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            pw.Container(
                margin: const pw.EdgeInsets.only(),
                child: pw.Text(
                  myDetail?.businessAddress ?? "",
                  style: pw.TextStyle(
                    color: accentColor,
                    fontSize: 8,
                  ),
                )),
            if (null != myDetail!.tinNo && "" != myDetail!.tinNo)
              pw.Container(
                  margin: const pw.EdgeInsets.only(),
                  child: pw.Text(
                    "${myDetail!.tinFlag ?? ''} No. :${myDetail!.tinNo ?? ''}",
                    style: pw.TextStyle(
                      color: accentColor,
                      fontSize: 8,
                    ),
                  )),
          ]))
        ]),
        pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _header(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Expanded(
              child: pw.Column(
                children: [
                  pw.Container(
                    margin: const pw.EdgeInsets.only(bottom: 20, top: 10),
                    alignment: pw.Alignment.center,
                    child: pw.Text(reportTitle!,
                        style: pw.TextStyle(
                            color: PdfColors.red,
                            fontSize: 15,
                            fontWeight: pw.FontWeight.bold)),
                  )
                ],
              ),
            ),
          ],
        ),
        pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _buildFooter(pw.Context context) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: [
        // pw.Container(
        //   height: 20,
        //   width: 100,
        //   child: pw.BarcodeWidget(
        //     barcode: pw.Barcode.pdf417(),
        //     data: 'Invoice# $invoiceNumber',
        //   ),
        // ),
        pw.Text(
          'Page ${context.pageNumber}/${context.pagesCount}',
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
        pw.Text(
          FOOTER_PRINT_TEXT,
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
      ],
    );
  }

  // pw.PageTheme _buildTheme(
  //     PdfPageFormat pageFormat, pw.Font base, pw.Font bold, pw.Font italic) {
  //   return pw.PageTheme(
  //     pageFormat: pageFormat,
  //     theme: pw.ThemeData.withFont(
  //       base: base,
  //       bold: bold,
  //       italic: italic,
  //     ),
  //     buildBackground: (context) => pw.FullPage(
  //       ignoreMargins: true,
  //     ),
  //   );
  // }

  pw.Widget _newHeader(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // pw.Container(
              //   height: 15,
              //   child: pw.Text(
              //     'Party Name:\r $partyName',
              //     style: pw.TextStyle(
              //       color: PdfColors.black,
              //       lineSpacing: 10,
              //       fontSize: 10,
              //       fontWeight: pw.FontWeight.bold,
              //     ),
              //   ),
              // ),
              // pw.Container(
              //   height: 15,
              //   child: pw.Text(
              //     'Transaction type:\r $txnType',
              //     style: pw.TextStyle(
              //       color: PdfColors.black,
              //       lineSpacing: 5,
              //       fontSize: 10,
              //       fontWeight: pw.FontWeight.bold,
              //     ),
              //   ),
              // ),

              pw.Container(
                height: 20,
                child: pw.Text(
                  'Duration:\rFrom\r$startDate\rTo\r$endDate',
                  style: pw.TextStyle(
                    color: PdfColors.black,
                    lineSpacing: 5,
                    fontSize: 10,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  pw.Widget _contentTable(pw.Context context) {
    const tableHeaders = [
      'PAN/VAT No',
      'Name',
      'Opening',
      'Purchase',
      'Sales',
      'Purchase Return',
      'Sales Return',
      'Closing',
    ];

    return pw.Table.fromTextArray(
      border: null,
      cellAlignment: pw.Alignment.centerLeft,
      headerDecoration: const pw.BoxDecoration(
        color: PdfColors.blue100,
      ),
      headerHeight: 24,
      cellHeight: 24,
      cellAlignments: {
        0: pw.Alignment.centerLeft,
        1: pw.Alignment.centerRight,
        2: pw.Alignment.centerRight,
        3: pw.Alignment.centerRight,
        4: pw.Alignment.centerRight,
        5: pw.Alignment.centerRight,
        6: pw.Alignment.centerRight,
        7: pw.Alignment.centerRight,
      },
      headerStyle: pw.TextStyle(
        color: _baseTextColor,
        fontSize: 9.5,
        fontWeight: pw.FontWeight.bold,
      ),
      cellStyle: pw.TextStyle(
        color: accentColor,
        fontSize: 8,
      ),
      rowDecoration: pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: accentColor!,
            width: .5,
          ),
        ),
      ),
      headers: List<String>.generate(
        tableHeaders.length,
        (col) => tableHeaders[col],
      ),
      data: List<List<String>>.generate(
        products!.length,
        (row) => List<String>.generate(
          tableHeaders.length,
          (col) => products![row].getIndex(col),
        ),
      ),
    );
  }
}

String _formatDate(DateTime date) {
  final format = DateFormat.yMMMd('en_US');
  return format.format(date);
}

class Product {
  const Product(
      this.tinNo,
      this.ledgerName,
      this.opening,
      this.totalSales,
      this.totalPurchase,
      this.totalSalesReturn,
      this.totalPurchaseReturn,
      this.closing);

  final String tinNo;
  final String ledgerName;
  final double opening;
  final double totalSales;
  final double totalPurchase;
  final double totalSalesReturn;
  final double totalPurchaseReturn;
  final double closing;

  String getIndex(int index) {
    switch (index) {
      case 0:
        return tinNo;
      case 1:
        return ledgerName;
      case 2:
        return getNumber(opening);
      case 3:
        return getNumber(totalPurchase);
      case 4:
        return getNumber(totalSales); // FIXED: Was showing totalPurchase again
      case 5:
        return getNumber(totalPurchaseReturn);
      case 6:
        return getNumber(totalSalesReturn);
      case 7:
        return getNumber(closing);
    }
    return '';
  }
}

String getNumber([double d = 0.0]) {
  // if (d == 0.0) {
  //   return "-";
  // }
  if (d < 0) {
    return "( " + d.abs().toString() + " )";
  }
  return d.toString();
}

/**
 * SAFE AMOUNT FORMATTING
 *
 * Ensures consistent decimal formatting for all amounts
 */
double _safeFormatAmount(double amount) {
  return parseDouble(amount.toStringAsFixed(2)) ?? 0.0;
}
