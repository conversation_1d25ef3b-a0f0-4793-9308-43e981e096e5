import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/registration_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/report/party_satement_report_model.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

class PartyStatementPrintPage extends StatelessWidget {
  final List<PartyStatementReportModel>? transactions;
  final String? partyText;
  final String? endDate;
  final String? startDate;

  const PartyStatementPrintPage(
      {super.key,
      this.transactions,
      this.endDate,
      this.startDate,
      this.partyText});

  @override
  Widget build(BuildContext context) {
    debugPrint("transactions Wallah habibi");
    debugPrint(transactions.toString());

    String startDate =
        NepaliDateTime.parse(toDateBS(DateTime.parse(this.startDate ?? "")))
            .format("y-MM-dd");
    String endDate =
        NepaliDateTime.parse(toDateBS(DateTime.parse(this.endDate ?? "")))
            .format("y-MM-dd");

    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        toolbarHeight: 60,
        elevation: 4,
        leading: BackButton(
          onPressed: () => Navigator.pop(context, false),
        ),
        centerTitle: false,
        backgroundColor: colorPrimary,
        titleSpacing: -5.0,
        title: const Text(
          "Party Statement",
          style: TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
        actions: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12),
            child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10)),
                  elevation: 8,
                  padding: EdgeInsets.zero,
                  backgroundColor: colorPrimary,
                  foregroundColor: colorPrimaryLight,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Column(
                  children: const [
                    Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                    Text(
                      "Cancel",
                      style: TextStyle(color: Colors.white, fontSize: 10),
                    ),
                  ],
                )),
          ),
        ],
      ),
      body: PdfPreview(
        useActions: true,
        canChangePageFormat: false,
        // allowPrinting: false,
        // allowSharing: false,
        initialPageFormat: defaultPdfPageFormat,
        pdfFileName: "$partyText Statement From $startDate To $endDate .pdf",
        maxPageWidth: 700,
        // actions: actions,
        build: (format) {
          return generatePartyStatementReport(defaultPdfPageFormat,
              transactions: transactions!,
              partyText: partyText!,
              startDate: this.startDate!,
              endDate: this.endDate!);
        },
      ),
    ));
  }
}

Future<Uint8List> generatePartyStatementReport(PdfPageFormat pageFormat,
    {List<PartyStatementReportModel>? transactions,
    String partyText = "All Party",
    String? startDate,
    String? endDate}) async {
  debugPrint("Transactionss haruu");
  debugPrint(transactions.toString());

  // CRITICAL FIX: The issue is that we need to respect the actual balance amounts
  // from the controller instead of recalculating them incorrectly
  double closingAmount = 0.0;

  if (transactions != null && transactions.isNotEmpty) {
    // The controller has already calculated the correct closing balance
    // We should use the final running balance from the last transaction

    // Method 1: Calculate the same way as controller
    closingAmount =
        transactions.first.txnBalanceAmount ?? 0.0; // Opening balance

    // Add transaction effects exactly as the controller does
    for (int i = 1; i < transactions.length; i++) {
      final txn = transactions[i];
      closingAmount += (txn.drAmount ?? 0.0) - (txn.crAmount ?? 0.0);
    }
  }

  final products = <Product>[
    ...transactions!.asMap().entries.map((entry) {
      final index = entry.key;
      final e = entry.value;

      // CRITICAL FIX: Calculate running balance with proper 2 decimal precision
      double runningBalance;
      if (index == 0) {
        // First transaction is opening balance - use exactly as stored
        runningBalance = e.txnBalanceAmount ?? 0.0;
      } else {
        // Calculate cumulative balance with proper rounding
        runningBalance = transactions.first.txnBalanceAmount ?? 0.0;
        for (int i = 1; i <= index; i++) {
          final txn = transactions[i];
          double txnEffect = (txn.drAmount ?? 0.0) - (txn.crAmount ?? 0.0);
          runningBalance += txnEffect;
          // Ensure 2 decimal precision at each step to avoid floating point errors
          runningBalance = double.parse(runningBalance.toStringAsFixed(2));
        }
      }

      return Product(
          e.txnDateBS ?? "",
          e.description ?? "",
          e.txnRefNumberChar ?? "",
          double.parse((e.drAmount ?? 0.0).toStringAsFixed(2)),
          double.parse((e.crAmount ?? 0.0).toStringAsFixed(2)),
          double.parse(runningBalance.toStringAsFixed(2)));
    }).toList()
  ];

  RegistrationDetailController registrationDetailController =
      Get.find(tag: "RegistrationDetailController");
  ImageModel sellerImageModel = registrationDetailController.logo;

  RegistrationDetailModel myDetail =
      registrationDetailController.registrationDetail;

  final invoice = PDFPartyStatement(
    myDetail: myDetail,
    sellerImage: sellerImageModel,
    // txnType: txnTypeText,
    partyName: partyText,
    startDate: NepaliDateTime.parse(toDateBS(DateTime.parse(startDate ?? "")))
        .format("y/MM/dd"),
    endDate: NepaliDateTime.parse(toDateBS(DateTime.parse(endDate ?? "")))
        .format("y/MM/dd"),
    products: products,
    closingAmount: closingAmount,
    reportTitle: "Party Statement",
    baseColor: PdfColors.lightBlue,
    accentColor: PdfColors.blueGrey900,
  );

  return await invoice.buildPdf(pageFormat);
}

class PDFPartyStatement {
  PDFPartyStatement(
      {this.myDetail,
      this.sellerImage,
      this.products,
      this.baseColor,
      this.accentColor,
      this.startDate,
      this.closingAmount,
      this.endDate,
      this.partyName,
      this.reportTitle});

  final RegistrationDetailModel? myDetail;
  final ImageModel? sellerImage;

  final List<Product>? products;
  final PdfColor? baseColor;
  final PdfColor? accentColor;
  final String? startDate;
  final String? endDate;
  final String? partyName;
  final String? reportTitle;

  final double? closingAmount;

  static const _darkColor = PdfColors.blueGrey800;
  static const _lightColor = PdfColors.black;
  // ignore: constant_identifier_names
  static const _VedColor = PdfColor.fromInt(0xFF3560AF);
  PdfColor get _baseTextColor =>
      baseColor!.luminance < 0.5 ? _lightColor : _darkColor;

  var currencyInWords = NepaliNumberFormat(
    inWords: true,
    language: Language.english,
    isMonetory: true,
    decimalDigits: 2,
  );

  String? _logo;

  Future<Uint8List> buildPdf(PdfPageFormat pageFormat) async {
    debugPrint("products.toString()=========");
    debugPrint(products.toString());
    // Create a PDF document.
    final doc = pw.Document();

    final font1 = await rootBundle.load('assets/roboto1.ttf');
    final font2 = await rootBundle.load('assets/roboto1.ttf');
    final font3 = await rootBundle.load('assets/roboto3.ttf');

    _logo = await rootBundle.loadString('assets/mobilekhata.svg');

    // Add page to the PDF
    doc.addPage(
      pw.MultiPage(
        // pageTheme: _buildTheme(
        //   pageFormat,
        //   pw.Font.ttf(font1),
        //   pw.Font.ttf(font2),
        //   pw.Font.ttf(font3),
        // ),
        header: _buildHeader,
        footer: _buildFooter,
        build: (context) => [
          _header(context),
          _newHeader(context),
          _contentTable(context),
          _tableTotal(context),
          pw.SizedBox(height: 20),
          pw.SizedBox(height: 20),
        ],
      ),
    );

    // Return the PDF file content
    return doc.save();
  }

  pw.Widget _tableTotal(pw.Context context) {
    return pw.Container(
        padding: const pw.EdgeInsets.only(top: 10),
        child: pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
          pw.Text(closingAmount! >= 0
              ? "Closing (Receivable): Rs. ${closingAmount!.toStringAsFixed(2)}"
              : "Closing (Payable): Rs. ${closingAmount!.toStringAsFixed(2)}")
        ]));
  }

  pw.Widget _buildHeader(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Expanded(
              child: pw.Column(
                mainAxisSize: pw.MainAxisSize.min,
                children: [
                  if (null != sellerImage?.imageBitmap) ...{
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.center,
                        children: [
                          pw.Container(
                            margin: const pw.EdgeInsets.only(top: -20),
                            alignment: pw.Alignment.center,
                            height: 60,
                            width: 60,
                            // child: pw.PdfLogo(),
                            child: sellerImage?.imageBitmap != null
                                ? pw.Image(pw.MemoryImage(Uint8List.fromList(
                                    sellerImage!.imageBitmap!)))
                                : pw.PdfLogo(),
                          ),
                        ])
                  },
                ],
              ),
            ),
          ],
        ),
        pw.Row(crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
          pw.Expanded(
              child: pw.Column(mainAxisSize: pw.MainAxisSize.min, children: [
            pw.Container(
              margin: const pw.EdgeInsets.only(top: 10),
              alignment: pw.Alignment.center,
              child: pw.Text(
                myDetail!.businessName ?? "",
                style: pw.TextStyle(
                  color: _VedColor,
                  fontWeight: pw.FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            pw.Container(
                margin: const pw.EdgeInsets.only(),
                child: pw.Text(
                  myDetail?.businessAddress ?? "",
                  style: pw.TextStyle(
                    color: accentColor,
                    fontSize: 8,
                  ),
                )),
            if (myDetail != null &&
                null != myDetail!.tinNo &&
                "" != myDetail!.tinNo)
              pw.Container(
                  margin: const pw.EdgeInsets.only(),
                  child: pw.Text(
                    "${myDetail?.tinFlag ?? ''} No. :${myDetail?.tinNo ?? ''}",
                    style: pw.TextStyle(
                      color: accentColor,
                      fontSize: 8,
                    ),
                  )),
          ]))
        ]),
        if (context.pageNumber > 1) pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _header(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Expanded(
              child: pw.Column(
                children: [
                  pw.Container(
                    margin: const pw.EdgeInsets.only(bottom: 20, top: 10),
                    alignment: pw.Alignment.center,
                    child: pw.Text(reportTitle ?? "",
                        style: pw.TextStyle(
                            color: PdfColors.red,
                            fontSize: 15,
                            fontWeight: pw.FontWeight.bold)),
                  )
                ],
              ),
            ),
          ],
        ),
        pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _buildFooter(pw.Context context) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: [
        pw.Text(
          'Page ${context.pageNumber}/${context.pagesCount}',
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
        pw.Text(
          FOOTER_PRINT_TEXT,
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
      ],
    );
  }

  // pw.PageTheme _buildTheme(
  //     PdfPageFormat pageFormat, pw.Font base, pw.Font bold, pw.Font italic) {
  //   return pw.PageTheme(
  //     pageFormat: pageFormat,
  //     theme: pw.ThemeData.withFont(
  //       base: base,
  //       bold: bold,
  //       italic: italic,
  //     ),
  //     buildBackground: (context) => pw.FullPage(
  //       ignoreMargins: true,
  //     ),
  //   );
  // }

  pw.Widget _newHeader(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Container(
                height: 15,
                child: pw.Text(
                  'Party Name:\r $partyName',
                  style: pw.TextStyle(
                    color: PdfColors.black,
                    lineSpacing: 10,
                    fontSize: 10,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.Container(
                height: 20,
                child: pw.Text(
                  'Duration:\rFrom\r$startDate\rTo\r$endDate',
                  style: pw.TextStyle(
                    color: PdfColors.black,
                    lineSpacing: 5,
                    fontSize: 10,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  pw.Widget _contentTable(pw.Context context) {
    const tableHeaders = [
      'Date',
      'Particulars',
      'Ref No',
      'Dr',
      'Cr',
      'Balance'
    ];

    return pw.Table.fromTextArray(
      border: null,
      cellAlignment: pw.Alignment.centerLeft,
      headerDecoration: const pw.BoxDecoration(
        color: PdfColors.blue100,
      ),
      headerHeight: 24,
      cellHeight: 24,
      cellAlignments: {
        0: pw.Alignment.centerLeft,
        1: pw.Alignment.centerLeft,
        2: pw.Alignment.centerLeft,
        3: pw.Alignment.centerRight,
        4: pw.Alignment.centerRight,
        5: pw.Alignment.centerRight,
        6: pw.Alignment.centerRight,
      },
      headerStyle: pw.TextStyle(
        color: _baseTextColor,
        fontSize: 9.5,
        fontWeight: pw.FontWeight.bold,
      ),
      cellStyle: pw.TextStyle(
        color: accentColor,
        fontSize: 8,
      ),
      rowDecoration: pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: accentColor!,
            width: .5,
          ),
        ),
      ),
      headers: List<String>.generate(
        tableHeaders.length,
        (col) => tableHeaders[col],
      ),
      data: List<List<String>>.generate(
        products!.length,
        (row) => List<String>.generate(
          tableHeaders.length,
          (col) => products![row].getIndex(col),
        ),
      ),
    );
  }
}

class Product {
  const Product(this.date, this.description, this.refNo, this.drAmount,
      this.crAmount, this.balanceAmount);

  final String date;
  final String description;
  final String refNo;
  final double drAmount;
  final double crAmount;
  final double balanceAmount;

  String getIndex(int index) {
    switch (index) {
      case 0:
        return date;
      case 1:
        return description;
      case 2:
        return refNo;
      case 3:
        return drAmount.toStringAsFixed(2);
      case 4:
        return crAmount.toStringAsFixed(2);
      case 5:
        return balanceAmount.toStringAsFixed(2);
    }
    return '';
  }
}
