import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/report/party_satement_report_model.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/app/repository/report_repository.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class PartyStatementReportController extends GetxController {
  final String tag = "PartyStatementReportController";

  final ReportRepository _reportRepository = ReportRepository();

  final _txnLoading = false.obs;
  bool get txnLoading => _txnLoading.value;

  List<PartyStatementReportModel> transactions = [];

  var totalClosingBalance = 0.00.obs;

  generatePartyReport(
      {required String startDate,
      required String endDate,
      List<int>? types,
      String? ledgerID}) async {
    _txnLoading(true);

    transactions.clear();

    double openingForDate = await LedgerRepository()
        .getOpeningBalanceForPartyForDate(startDate, ledgerID ?? "");

    PartyStatementReportModel opnTxn = PartyStatementReportModel(
      txnDateBS: toDateBS(DateTime.parse(startDate)),
      txnDate: startDate,
      txnBalanceAmount: openingForDate,
      drAmount: 0.00,
      crAmount: 0.00,
    );
    if (openingForDate >= 0) {
      opnTxn.txnType = TxnType.openingReceive;
      opnTxn.description = "Balance B/F (Receivable)";
    } else {
      opnTxn.txnType = TxnType.openingPay;
      opnTxn.description = "Balance B/F (Payable)";
    }
    opnTxn.txnTypeText = TxnType.txnTypeText[opnTxn.txnType];
    transactions.add(opnTxn);

    // Start running balance with opening balance
    double runningBalance = openingForDate;

    List<Map<String, dynamic>> txnDataListJson =
        await _reportRepository.getPartyTransactions(
            startDate: startDate,
            endDate: endDate,
            types: types,
            ledgerID: ledgerID);

    debugPrint(txnDataListJson[0].toString());

    for (int i = 0; i < txnDataListJson.length && i < 100; i++) {
      PartyStatementReportModel txn =
          PartyStatementReportModel.fromJson(txnDataListJson[i]);

      // Calculate running balance properly using Dr/Cr amounts
      runningBalance += (txn.drAmount ?? 0.0) - (txn.crAmount ?? 0.0);

      transactions.add(txn);
    }

    // Update the total closing balance with final running balance
    totalClosingBalance.value = runningBalance;

    _txnLoading(false);
  }
}
