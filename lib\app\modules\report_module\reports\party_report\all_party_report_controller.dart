import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';

/**
 * ALL PARTY REPORT CONTROLLER
 *
 * This controller manages the all party report which shows:
 * - List of all parties (customers/suppliers)
 * - Their current balance amounts (receivable/payable)
 * - Filter options by due type (receivable, payable, or all)
 *
 * BALANCE INTERPRETATION:
 * - Positive Balance: Party owes money to business (Receivable - लिनुपर्ने)
 * - Negative Balance: Business owes money to party (Payable - तिर्नुपर्ने)
 */
class AllPartyReportController extends GetxController {
  // Loading state for UI feedback
  var _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  // FIXED: Separate totals for better financial reporting
  var totalReceivable = 0.00.obs; // Total amount we should receive
  var totalPayable = 0.00.obs; // Total amount we should pay
  var netBalance = 0.00.obs; // Net position (receivable - payable)

  // DEPRECATED: This was calculating incorrect totals by summing absolute values
  // Keeping for backward compatibility but should use separate totals above
  var totalAmount = 0.00.obs;

  // Party ledgers list
  List<LedgerDetailModel> _ledgers = [];
  List<LedgerDetailModel> get ledgers => _ledgers;

  LedgerRepository _ledgerRepository = LedgerRepository();

  /**
   * GET LEDGERS FOR SPECIFIC DUE TYPE
   *
   * Fetches all general ledgers with their current balance and calculates totals
   *
   * @param dueType - Filter type: 'receivable', 'payable', or null for all
   */
  getLedgersFor({String? dueType}) async {
    _isLoading(true);

    // Fetch ledgers from repository with optional filtering
    List<LedgerDetailModel> _ls =
        await _ledgerRepository.getAllGeneralLedgerWithDues(dueType: dueType);

    // Update ledgers list
    _ledgers.clear();
    _ledgers.addAll(_ls);

    // FIXED: Calculate proper financial totals
    _calculateTotals();

    _isLoading(false);
  }

  /**
   * CALCULATE FINANCIAL TOTALS
   *
   * Properly separates receivables from payables for accurate reporting
   */
  void _calculateTotals() {
    totalReceivable.value = 0.00;
    totalPayable.value = 0.00;
    totalAmount.value = 0.00; // For backward compatibility

    for (var ledger in _ledgers) {
      // FIXED: Added null safety check
      final balance = ledger.balanceAmount ?? 0.0;

      if (balance > 0) {
        // Positive balance = Money owed TO us (Receivable)
        totalReceivable.value += balance;
      } else if (balance < 0) {
        // Negative balance = Money owed BY us (Payable)
        totalPayable.value += balance.abs();
      }

      // DEPRECATED: Keep old calculation for backward compatibility
      // This sums absolute values which doesn't represent true financial position
      totalAmount.value += balance.abs();
    }

    // Calculate net balance (positive = net receivable, negative = net payable)
    netBalance.value = totalReceivable.value - totalPayable.value;
  }
}
