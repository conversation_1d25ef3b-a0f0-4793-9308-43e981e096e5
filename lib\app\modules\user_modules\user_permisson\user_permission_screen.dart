// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/model/others/user_modal.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/update_registration_detail/update_registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/user_list/user_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/user_permisson/user_permission_controller.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:mobile_khaata_v2/utilities/ui_helper.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:tuple/tuple.dart';

class UserPermissionScreen extends StatelessWidget {
  final UserModel? user;
  final userPermissionController = UserPermissionController();
  final updateRegistrationDetailController =
      Get.put(UpdateRegistrationDetailController());
  UserPermissionScreen({super.key, this.user}) {
    userPermissionController.init(user!);
  }
  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        if (updateRegistrationDetailController.isLoading) {
          return CircularProgressIndicator(
            color: colorPrimary,
          );
        }
        return SafeArea(
            child: Scaffold(
          appBar: AppBar(
            toolbarHeight: 60,
            elevation: 4,
            centerTitle: false,
            backgroundColor: colorPrimary,
            titleSpacing: -5.0,
            title: const Text("User Permissions"),
            actions: [
              Container(
                padding: const EdgeInsets.symmetric(vertical: 12),
                child: InkWell(
                    onTap: () {
                      userPermissionController.readOnlyFlag =
                          !userPermissionController.readOnlyFlag;
                    },
                    child: (userPermissionController.readOnlyFlag)
                        ? Column(
                            children: const [
                              Icon(
                                Icons.mode_edit,
                                color: Colors.white,
                              ),
                              Text(
                                "Click here to Edit",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          )
                        : Column(
                            children: const [
                              Icon(
                                Icons.close,
                                color: Colors.white,
                              ),
                              Text(
                                "Cancel",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          )),
              ),
              mWidthSpan,
            ],
          ),
          body: ListView(
            padding: const EdgeInsets.symmetric(vertical: 20, horizontal: 10),
            children: [
              Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: userPermissionController.readOnlyFlag
                      ? null
                      : () {
                          if (userPermissionController.permissions.length ==
                              PermissionManager.permissionTypeList.length) {
                            //remove  all
                            userPermissionController.clearAllPermission();
                          } else {
                            //select all
                            userPermissionController.onSelectAllPermission(
                                PermissionManager.getAllPermissionValues());
                          }
                        },
                  child: Container(
                    color: colorPrimary,
                    child: Row(
                      children: [
                        Theme(
                          data: ThemeData(
                            primarySwatch: Colors.blue,
                            unselectedWidgetColor: Colors.white, // Your color
                          ),
                          child: AbsorbPointer(
                            child: Checkbox(
                              value: userPermissionController
                                      .permissions.length ==
                                  PermissionManager.permissionTypeList.length,
                              onChanged: (value) {},
                              activeColor: Colors.white,
                              checkColor: colorPrimary,
                            ),
                          ),
                        ),
                        const SizedBox(
                          width: 0,
                        ),
                        Text(
                          "All",
                          style: labelStyle2.copyWith(color: Colors.white),
                        )
                      ],
                    ),
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              ...PermissionManager.permissionGroupName.entries.map((entry) {
                int groupID = entry.key;
                String groupName = entry.value;
                List<dynamic> groupChilds =
                    PermissionManager.permissionsForGroup(groupID);

                if (groupChilds.isEmpty) {
                  return const SizedBox(
                    height: 0,
                    width: 0,
                  );
                }
                return Container(
                  decoration: BoxDecoration(
                      border: Border.all(color: Colors.black12, width: 0.5)),
                  child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Material(
                          color: Colors.transparent,
                          child: InkWell(
                            onTap: userPermissionController.readOnlyFlag
                                ? null
                                : () {
                                    userPermissionController
                                        .onSelectGroupPermission(groupID);
                                  },
                            child: Container(
                              color: colorPrimaryLightest,
                              child: Row(
                                children: [
                                  AbsorbPointer(
                                    child: Checkbox(
                                      value: userPermissionController
                                          .isAllExistForGroup(groupID),
                                      onChanged: (value) {},
                                      activeColor: Colors.white,
                                      checkColor: colorPrimary,
                                    ),
                                  ),
                                  Text(
                                    groupName,
                                    style: labelStyle2.copyWith(
                                        color: Colors.white),
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 40.0),
                          child: Wrap(
                            spacing: 20.0,
                            children: [
                              ...groupChilds.map((groupChildItem) {
                                return Material(
                                  child: InkWell(
                                    onTap: userPermissionController.readOnlyFlag
                                        ? null
                                        : () {
                                            userPermissionController
                                                .onSelectSinglePermission(
                                                    groupChildItem['value']
                                                        .toString());
                                          },
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        AbsorbPointer(
                                          child: SizedBox(
                                            width: 30,
                                            child: Checkbox(
                                              value: userPermissionController
                                                  .permissions
                                                  .contains(
                                                      groupChildItem['value']),
                                              onChanged: (value) {},
                                              activeColor: colorPrimary,
                                            ),
                                          ),
                                        ),
                                        Text(
                                          groupChildItem['text'],
                                          style: TextStyle(
                                              fontSize: 16,
                                              color: colorPrimary,
                                              fontWeight: FontWeight.bold),
                                          // style: labelStyle2,
                                        )
                                      ],
                                    ),
                                  ),
                                );
                              }).toList(),
                            ],
                          ),
                        ),
                      ]),
                );
              }),
              // Text(userPermissionController.permissions.toString())
            ],
          ),
          bottomNavigationBar: BottomSaveCancelButton(
            shadow: false,
            enableFlag: !userPermissionController.readOnlyFlag,
            onSaveBtnPressedFn: userPermissionController.readOnlyFlag
                ? null
                : () async {
                    ProgressDialog progressDialog = ProgressDialog(context,
                        type: ProgressDialogType.normal, isDismissible: false);
                    progressDialog.update(
                        message: "Updating user's permission. Please wait....");
                    await progressDialog.show();

                    Tuple2<bool, String> toggleResp =
                        await userPermissionController.updateUserPermission();
                    await progressDialog.hide();
                    if (toggleResp.item1) {
                      Navigator.of(context).pop();
                      //update user list
                      if (Get.isRegistered<UserListController>(
                          tag: "UserListController")) {
                        UserListController userListController =
                            Get.find(tag: "UserListController");
                        userListController.init();
                      }
                      showToastMessage(context,
                          message: toggleResp.item2, duration: 2);
                    } else {
                      showToastMessage(context,
                          alertType: AlertType.Error,
                          message: toggleResp.item2,
                          duration: 2);
                    }
                  },
          ),
        ));
      },
    );
  }
}
