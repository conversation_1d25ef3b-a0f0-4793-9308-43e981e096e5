import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/modules/subscription_module/payment_subscription/payment_response_view.dart';

class PaymentEsewa extends StatefulWidget {
  PaymentEsewa({super.key, required this.url, required this.postData});

  final String url;
  final Map<String, String> postData;

  @override
  State<PaymentEsewa> createState() => _PaymentEsewaState();
}

class _PaymentEsewaState extends State<PaymentEsewa> {
  late InAppWebViewController _webViewController;
  bool _hasNavigated = false;

  void _loadPostData() {
    String formHtml = '''
      <html>
        <head>
          <style>
            #loadingMessage {
              position: fixed;
              top: 50%;
              left: 50%;
              transform: translate(-50%, -50%);
              background: rgba(0, 0, 0, 0.6);
              color: white;
              padding: 10px 20px;
              border-radius: 5px;
              font-size: 18px;
              display: none;
            }
          </style>
        </head>
        <body>
          <div id="loadingMessage">Submitting payment... Please wait...</div>
          <form id="paymentForm" method="POST" action="${widget.url}">
            ${widget.postData.entries.map((entry) => '<input type="hidden" name="${entry.key}" value="${entry.value}" />').join()}
          </form>
          <script type="text/javascript">
            window.onload = function() {
              document.getElementById('loadingMessage').style.display = 'block';
              document.getElementById('paymentForm').submit();
            };
          </script>
        </body>
      </html>
    ''';

    _webViewController.loadData(
        data: formHtml, mimeType: 'text/html', encoding: 'utf-8');
  }

  @override
  Widget build(BuildContext context) {
    print(widget.postData);
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(title: const Text('eSewa Payment')),
      body: InAppWebView(
        initialUrlRequest: URLRequest(url: Uri.parse('about:blank')),
        initialOptions: InAppWebViewGroupOptions(
          crossPlatform: InAppWebViewOptions(
            javaScriptEnabled: true,
          ),
        ),
        onWebViewCreated: (controller) {
          print(
            widget.postData,
          );
          _webViewController = controller;
          _loadPostData();
        },
        onLoadStop: (controller, url) async {
          if (_hasNavigated) return;

          try {
            String pageContent = await controller.evaluateJavascript(
                source: "document.body.innerText");

            pageContent = pageContent.trim();
            print("== Page Content ==");
            print(pageContent);

            final decoded = jsonDecode(pageContent);

            if (decoded is Map && decoded['message'] != null) {
              String message = decoded['message'].toString().toLowerCase();

              _hasNavigated = true; // prevent multiple navigations

              if (message.contains("payment successful")) {
                showToastMessage(context, message: message, duration: 2);
                if (!context.mounted) return;
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                      builder: (context) => PaymentResponseView(
                          isSuccess: true, url: url ?? Uri.parse(''))),
                );
              } else {
                showToastMessage(context,
                    alertType: AlertType.Error, message: message, duration: 2);
                if (!context.mounted) return;
                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                      builder: (context) => PaymentResponseView(
                          isSuccess: false, url: url ?? Uri.parse(''))),
                );
              }
            }
          } catch (e) {
            print("Error reading response: $e");
          }
        },
        onProgressChanged: (controller, progress) {},
        // onReceivedHttpError: (controller, url, error) {},
        onReceivedServerTrustAuthRequest: (controller, challenge) async {
          return ServerTrustAuthResponse(
              action: ServerTrustAuthResponseAction.PROCEED);
        },
        onUpdateVisitedHistory: (controller, url, androidIsReload) {
          print("==failure URL ETA XAA HAIIII====");
          print(url);
          print("==failure URL ETA XAA HAIIII====");
          if (url.toString().contains("/failure")) {
            Navigator.pop(context);
          }
        },
      ),
    ));
    ;
  }

  Future<void> _submitForm() async {
    String jsScript = '''
      var form = document.getElementById('paymentForm');
      var loadingMessage = document.getElementById('loadingMessage');
      if (form) {
        form.submit();
        loadingMessage.style.display = 'block'; // Show loading message
      }
    ''';

    await _webViewController.evaluateJavascript(source: jsScript);
  }

  // Handle success redirection

  // Function to show an error message in the WebView if something goes wrong
  void _showErrorPage(String message) {
    String errorHtml = '''
      <html>
        <body style="background-color: #f8d7da; color: #721c24; text-align: center; padding: 50px;">
          <h2>Error</h2>
          <p>${message}</p>
        </body>
      </html>
    ''';

    _webViewController.loadData(
        data: errorHtml, mimeType: 'text/html', encoding: 'utf-8');
  }
}
