import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/app/components/ledger_sms_call_reminder_group.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/party_detail/party_detail_page.dart';
import 'package:mobile_khaata_v2/app/modules/payment_module/add_edit_payment_screen.dart';
import 'package:mobile_khaata_v2/app/modules/receipt_module/add_edit_receipt_page.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

// ignore: must_be_immutable
class PartyCardView extends StatelessWidget {
  final LedgerDetailModel ledger;
  final bool? showBothPayReceiveBtn;
  bool? _isReceivable;

  PartyCardView({
    super.key,
    required this.ledger,
    this.showBothPayReceiveBtn = true,
  }) {
    _isReceivable = (ledger.balanceAmount! >= 0) ? true : false;
  }

  Widget _amountWidget(double balanceAmount) {
    bool isReceivable = (balanceAmount >= 0) ? true : false;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Transform.rotate(
          angle: (isReceivable) ? (3.14 / 1.3) : (-3.14 / 4),
          alignment: Alignment.center,
          child: CircleAvatar(
            radius: 8,
            backgroundColor: (isReceivable) ? colorGreenDark : colorRedLight,
            child: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
              size: 12,
            ),
          ),
        ),
        const SizedBox(
          width: 5,
        ),
        RichText(
          text: TextSpan(
            text: (isReceivable) ? "लिनुपर्ने: " : "तिर्नुपर्ने: ",
            style: TextStyle(
              color: (isReceivable) ? colorGreenDark : colorRedLight,
              fontSize: 13,
            ),
            children: [
              TextSpan(
                text: formatCurrencyAmount(balanceAmount.abs()),
              )
            ],
          ),
        ),
      ],
    );
  }

  Widget _receiveBtn(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: colorGreenLight,
        foregroundColor: colorGreenDark,
      ),
      onPressed: () {
        Navigator.pushNamed(
          context,
          '/creditReceive',
          arguments: AddEditReceiptPage(
            ledgerId: ledger.ledgerId,
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
        child: const Text(
          "लिनुहोस् (Take)\n[Cr.]",
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _payBtn(BuildContext context) {
    return ElevatedButton(
      style: ElevatedButton.styleFrom(
        backgroundColor: colorRedLight,
        foregroundColor: colorRedDark,
      ),
      onPressed: () {
        Navigator.pushNamed(
          context,
          '/creditPay',
          arguments: AddEditPaymentPage(
            ledgerId: ledger.ledgerId,
          ),
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
        child: const Text(
          "दिनुहोस् (Give)\n[Dr.]",
          textAlign: TextAlign.center,
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget _showBothPayReceiveBtn(BuildContext context) {
    return Row(
      children: [
        _receiveBtn(context),
        const SizedBox(
          width: 20,
        ),
        _payBtn(context),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    onTap: () {
                      Navigator.pushNamed(
                        context,
                        '/partyLedgerDetail',
                        arguments: PartyDetailPage(
                          ledgerId: ledger.ledgerId!,
                        ),
                      );
                    },
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "${ledger.ledgerTitle}",
                          style: TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 14,
                              color: textColor),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        _amountWidget(ledger.balanceAmount!),
                        const SizedBox(
                          height: 10,
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  width: 20,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    if (null != ledger.mobileNo)
                      Text(
                        "${ledger.mobileNo}",
                        textAlign: TextAlign.center,
                      ),
                    LedgerSmsCallReminderGroup(
                      ledger: ledger,
                    ),
                  ],
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: <Widget>[
                if (showBothPayReceiveBtn!)
                  _showBothPayReceiveBtn(context)
                else if (_isReceivable!)
                  _receiveBtn(context)
                else
                  _payBtn(context)
              ],
            ),
          ],
        ),
      ),
    );
  }
}
