// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/expense_category_list_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/expanse_category_model.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_category_module/add_edit_expense_category/add_edit_expense_category_dialog.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class ExpenseCategoryListPage extends StatefulWidget {
  const ExpenseCategoryListPage({super.key});

  @override
  _ExpenseCategoryListPageState createState() =>
      _ExpenseCategoryListPageState();
}

class _ExpenseCategoryListPageState extends State<ExpenseCategoryListPage> {
  final String tag = "ExpenseCategoryListPage";

  final expensesCategoryListController = ExpensesCategoryListController();
  bool showSearchBar = false;
  final FocusNode searchBoxFocus = FocusNode();
  final TextEditingController searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    expensesCategoryListController.onInit();
  }

  searchBoxOnChangeHandler(String searchString) {
    expensesCategoryListController.searchCategory(searchString);
    setState(() {});
  }

  searchButtonOnPressedHandler({bool? shouldShow}) {
    showSearchBar = (shouldShow) ?? !showSearchBar;

    if (showSearchBar) {
      searchController.text = "";
      searchBoxFocus.requestFocus();
    }

    expensesCategoryListController.searchCategory("");
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: false,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        backgroundColor: colorPrimary,
        toolbarHeight: 60,
        elevation: 0,
        leading: BackButton(
          onPressed: () => Navigator.pop(context, false),
        ),
        titleSpacing: -5.0,
        centerTitle: false,
        title: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          (showSearchBar)
              ? SizedBox(
                  width: MediaQuery.of(context).size.width * 0.56,
                  height: 50,
                  child: FormBuilderTextField(
                    name: "searchBox",
                    autocorrect: false,
                    keyboardType: TextInputType.text,
                    textInputAction: TextInputAction.done,
                    focusNode: searchBoxFocus,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 18,
                    ),
                    controller: searchController,
                    decoration: InputDecoration(
                        hintText: "खर्च शीर्षक (Expenses Category)",
                        contentPadding: EdgeInsets.zero,
                        prefixStyle: formFieldTextStyle,
                        hintStyle: const TextStyle(
                            color: Colors.white60, fontSize: 18),
                        border: InputBorder.none),
                    onChanged: (searchString) =>
                        searchBoxOnChangeHandler(searchString ?? ""),
                  ),
                )
              : const Text(
                  "खर्च शीर्षक (Expenses Category)",
                  style: TextStyle(
                      fontSize: 18,
                      color: Colors.white,
                      fontFamily: 'HelveticaRegular',
                      fontWeight: FontWeight.bold),
                ),
        ]),
        actions: <Widget>[
          IconButton(
            icon: (showSearchBar)
                ? const Icon(
                    Icons.cancel,
                    color: Colors.white,
                  )
                : const Icon(
                    Icons.search,
                    color: Colors.white,
                  ),
            onPressed: () => searchButtonOnPressedHandler(),
          )
        ],
      ),
      body: Container(
        // height: MediaQuery.of(context).size.height,
        decoration: BoxDecoration(
          color: backgroundColorShade,
        ),

        child: _ExpenseTitleList(
            expensesCategoryListController, searchButtonOnPressedHandler),
      ),
      floatingActionButton: SizedBox(
        width: 45,
        child: FloatingActionButton(
          onPressed: () async {
            final expenseItem =
                await displayAddEditExpenseCategoryDialog(context);
            if (expenseItem == null) return;
            expensesCategoryListController.onInit();
          },
          backgroundColor: colorPrimary,
          elevation: 6,
          child: const Icon(
            Icons.add,
            size: 25,
            color: Colors.white,
          ),
        ),
      ),
    ));
  }
}

class _ExpenseTitleList extends StatelessWidget {
  final ExpensesCategoryListController expensesCategoryListController;
  final Function searchButtonOnPressedHandler;

  const _ExpenseTitleList(
      this.expensesCategoryListController, this.searchButtonOnPressedHandler);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (expensesCategoryListController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }
      if (expensesCategoryListController.filteredCategories.isEmpty) {
        return const SizedBox(
            width: double.infinity,
            child: Center(
                child: Text(
              "No Records",
              style: TextStyle(color: Colors.black54),
            )));
      } else {
        return ListView.builder(
          itemCount: expensesCategoryListController.filteredCategories.length,
          // shrinkWrap: true,
          padding: const EdgeInsets.only(bottom: 60),
          itemBuilder: (context, int index) {
            ExpenseCategoryModel expeseCategory =
                expensesCategoryListController.filteredCategories[index];

            return Card(
              margin: EdgeInsets.only(
                  left: 5,
                  right: 5,
                  top: (0 == index) ? 15 : 0,
                  bottom: ((expensesCategoryListController
                                  .filteredCategories.length -
                              1) ==
                          index)
                      ? 20
                      : 10),
              child: ListTile(
                  visualDensity:
                      const VisualDensity(horizontal: -4, vertical: -4),
                  contentPadding: const EdgeInsets.all(10),
                  title: Text("${expeseCategory.expenseTitle}",
                      style: labelStyle2.copyWith()),
                  trailing: Row(mainAxisSize: MainAxisSize.min, children: [
                    IconButton(
                      icon: const Icon(
                        Icons.edit,
                        size: 20,
                        color: Colors.black54,
                      ),
                      onPressed: () async {
                        ExpenseCategoryModel? newExp =
                            await displayAddEditExpenseCategoryDialog(context,
                                categoryId: expeseCategory.expenseCategoryId);
                        if (newExp == null) return;
                        expensesCategoryListController.onInit();
                        searchButtonOnPressedHandler(shouldShow: false);
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.delete,
                        size: 20,
                        color: colorRedDark,
                      ),
                      onPressed: () {
                        expensesCategoryListController
                            .deleteButtonOnPressedHandler(context,
                                expeseCategory.expenseCategoryId ?? "");
                      },
                    ),
                  ])),
            );
          },
        );
      }
    });
  }
}
