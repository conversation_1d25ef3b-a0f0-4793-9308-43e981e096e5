import 'package:intl/intl.dart';

import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class LedgerModel {
  LedgerModel(
      {this.ledgerId,
      this.ledgerTitle,
      this.contactPersonName,
      this.ledgerType = 'G',
      this.countryCode = "+977",
      this.mobileNo,
      this.email,
      this.address,
      this.ledgerPhoto,
      this.tinNo,
      this.tinFlag,
      this.openingBalance,
      this.openingDate,
      this.openingDateBS,
      this.openingType,
      this.lastActivityType,
      this.lastActivityAt,
      this.lastActivityBy});

  String? ledgerId;
  String? ledgerTitle;
  String? contactPersonName;
  String? ledgerType;
  String? countryCode;
  String? mobileNo;
  String? email;
  String? address;
  String? ledgerPhoto;
  String? tinNo;
  String? tinFlag;

  double? openingBalance;
  String? openingDate;
  String? openingDateBS;
  int? openingType;

  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;

  factory LedgerModel.from<PERSON>son(Map<String, dynamic> json) {
    DateTime? openingDateTimeObj =
        DateTime.tryParse(json["opening_date"] ?? "");

    return LedgerModel(
        ledgerId: json["ledger_id"],
        ledgerTitle: json["ledger_title"],
        contactPersonName: json["contact_person_name"],
        ledgerType: json["ledger_type"],
        countryCode: json["country_code"],
        mobileNo: json["mobile_no"],
        email: json["email"],
        address: json["address"],
        ledgerPhoto: json["ledger_photo"],
        tinNo: json["tin_no"],
        tinFlag: json["tin_flag"],
        openingBalance: json["opening_balance"],
        openingDate: (null != openingDateTimeObj)
            ? DateFormat('y-MM-dd').format(openingDateTimeObj)
            : '',
        openingDateBS:
            (null != openingDateTimeObj) ? toDateBS(openingDateTimeObj) : '',
        openingType: json["opening_type"],
        lastActivityType: json["last_activity_type"],
        lastActivityAt: json["last_activity_at"],
        lastActivityBy: json['last_activity_by']);
  }

  Map<String, dynamic> toJson() => {
        "ledger_id": ledgerId,
        "ledger_title": ledgerTitle,
        "contact_person_name": contactPersonName,
        "ledger_type": 'G',
        "country_code": countryCode,
        "mobile_no": mobileNo,
        "email": email,
        "address": address,
        "ledger_photo": ledgerPhoto,
        "tin_no": tinNo,
        "tin_flag": tinFlag,
        "opening_balance": openingBalance,
        "opening_date": openingDate,
        "opening_type": openingType,
        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        "last_activity_by": lastActivityBy
      };
}
