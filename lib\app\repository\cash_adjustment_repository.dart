import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/cash_adjustment_model.dart';
import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/model/others/cash_txn_model.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';

import 'package:sqflite/sqflite.dart';
import 'package:tuple/tuple.dart';

import '../../utilities/logger.dart';

class CashAdjustmentRepository {
  final String tag = "CashAdjustmentRepository";
  final String tableName = "mk_cash_adjustments";
  DatabaseHelper databaseHelper = DatabaseHelper();
  QueryRepository queryRepository = QueryRepository();

  //========================================================================================= SYNCING ACTIONS
  Future<String> insert(CashAdjustmentModel adjustment,
      {dynamic dbClient, String? batchID}) async {
    String adjustmentId;

    dbClient ??= await databaseHelper.database;

    String primaryKeyPrefix = await getPrimaryKeyPrefix();
    adjustmentId = primaryKeyPrefix + uuidV4;
    adjustment.cashAdjId = adjustmentId;

    adjustment.lastActivityAt = currentDateTime;
    adjustment.lastActivityBy = await getLastActivityBy();
    adjustment.lastActivityType = LastActivityType.New;

    await dbClient.insert(tableName, adjustment.toJson());

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.insert,
      data: adjustment.toJson(),
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    pushPendingQueries(
        singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

    return adjustmentId;
  }

  Future<bool> update(CashAdjustmentModel adjustment,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    adjustment.lastActivityAt = currentDateTime;
    adjustment.lastActivityBy = await getLastActivityBy();
    adjustment.lastActivityType = LastActivityType.Edit;

    String whereClause = "cash_adj_id = ?";
    List<dynamic> whereArgs = [adjustment.cashAdjId];

    await dbClient.update(tableName, adjustment.toJson(),
        where: whereClause, whereArgs: whereArgs);

    QueryModel newQueryModel = QueryModel(
        tableName: tableName,
        queryType: QueryType.update,
        data: adjustment.toJson(),
        whereArgs: whereArgs,
        whereClause: whereClause);
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    pushPendingQueries(
        singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

    status = true;

    return status;
  }

  Future<Tuple2<bool, String>> delete(String txnID,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;
    String message = "";

    dbClient ??= await databaseHelper.database;
    String whereClause = "cash_adj_id = ?";
    List<dynamic> whereArgs = [txnID];
    try {
      // can soft delete for given id
      dbClient.update(
          tableName, {"last_activity_type": LastActivityType.Delete},
          where: whereClause, whereArgs: whereArgs);

      QueryModel newQueryModel = QueryModel(
          tableName: tableName,
          queryType: QueryType.update,
          whereArgs: whereArgs,
          whereClause: whereClause,
          data: {"last_activity_type": LastActivityType.Delete});

      await queryRepository.pushQuery(newQueryModel,
          batchID: batchID, dbClient: dbClient);
      status = true;
      pushPendingQueries(
          singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

      message = "Cash Adjustment deleted successfully";
    } catch (e) {
      message =
          "Cannot delete Cash Adjustment at this moment. Please try again later";
    }

    return Tuple2(status, message);
  }

  //========================================================================================= NON SYNCING ACTIONS
  Future<CashAdjustmentModel> getAdjustmentById(String adjustmentId,
      {dynamic dbClient}) async {
    CashAdjustmentModel adjustment = CashAdjustmentModel();
    try {
      dbClient ??= await databaseHelper.database;

      Map<String, dynamic> json = (await dbClient.rawQuery(
              "SELECT * FROM mk_cash_adjustments WHERE last_activity_type!=? AND cash_adj_id = ?",
              [LastActivityType.Delete, adjustmentId]))
          .first;
      adjustment = CashAdjustmentModel.fromJson(json);
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return adjustment;
  }

  String cashInHandBaseQuery(
      {String? openingDate, String? closingDate, String? date}) {
    String query = "SELECT  "
        "cash_adj_id AS txn_id,  "
        "cash_adj_date AS txn_date,  "
        "cash_adj_type AS txn_type,  "
        "'Cash' AS bank_name, "
        "'cash' AS mode,  "
        "cash_adj_description AS txn_desc,  "
        "CASE WHEN cash_adj_type=${TxnType.reduceCash} THEN -cash_adj_amount "
        'ELSE cash_adj_amount END AS txn_amount,  '
        "last_activity_at  "
        "FROM mk_cash_adjustments  "
        "WHERE last_activity_type<>${LastActivityType.Delete}  ";

    if (null != openingDate && null != closingDate) {
      query += " AND cash_adj_date BETWEEN '$openingDate' AND '$closingDate' ";
    } else if (null != openingDate) {
      query += " AND cash_adj_date < '$openingDate'  ";
    } else if (null != closingDate) {
      query += " AND cash_adj_date > '$closingDate'  ";
    } else if (null != date) {
      query += " AND cash_adj_date = '$date'  ";
    }

    query += "UNION ALL  "
        "SELECT  "
        "bankAdj.bank_adj_id AS txn_id,  "
        "bankAdj.bank_adj_date AS txn_date,  "
        "bankAdj.bank_adj_type AS txn_type,  "
        "bank.pmt_type_short_name AS bank_name, "
        "'bank' AS mode,  "
        "bankAdj.bank_adj_description AS txn_desc,  "
        "CASE WHEN bankAdj.bank_adj_type=${TxnType.bankDeposit} THEN -bankAdj.bank_adj_amount "
        "ELSE bankAdj.bank_adj_amount END AS txn_amount,  "
        "bankAdj.last_activity_at  "
        "FROM mk_bank_adjustments  AS bankAdj "
        "INNER JOIN mk_payment_types AS bank ON bank.pmt_type_id=bankAdj.bank_adj_bank_id AND bank.last_activity_type<>${LastActivityType.Delete}  "
        "WHERE (bankAdj.bank_adj_type=${TxnType.bankDeposit} OR bankAdj.bank_adj_type=${TxnType.bankWithdrawn}) "
        "AND bankAdj.last_activity_type<>${LastActivityType.Delete} ";

    if (null != openingDate && null != closingDate) {
      query +=
          " AND bankAdj.bank_adj_date BETWEEN '$openingDate' AND '$closingDate' ";
    } else if (null != openingDate) {
      query += " AND bankAdj.bank_adj_date < '$openingDate'  ";
    } else if (null != closingDate) {
      query += " AND bankAdj.bank_adj_date > '$closingDate'  ";
    } else if (null != date) {
      query += " AND bankAdj.bank_adj_date = '$date'  ";
    }

    query += "UNION ALL  "
        "SELECT  "
        "txn.txn_id,  "
        "txn.txn_date,  "
        "txn.txn_type,  "
        "from_bank.pmt_type_short_name AS bank_name, "
        "CASE  "
        "WHEN txn.txn_payment_type_id='$PAYMENT_MODE_CASH_ID' THEN 'cash' "
        "WHEN txn.txn_payment_type_id='$PAYMENT_MODE_CHEQUE_ID' THEN 'cheque'  "
        "ELSE 'bank' END AS mode,  "
        "CASE WHEN txn.txn_display_name IS NULL OR txn.txn_display_name = '' "
        "THEN lm.ledger_title ELSE txn.txn_display_name END AS txn_desc,  "
        "CASE  "
        "WHEN txn.txn_type=${TxnType.sales} THEN txn.txn_cash_amount  "
        "WHEN txn.txn_type=${TxnType.purchase} THEN -txn.txn_cash_amount  "
        "WHEN txn.txn_type=${TxnType.paymentIn} THEN txn.txn_cash_amount  "
        "WHEN txn.txn_type=${TxnType.paymentOut} THEN -txn.txn_cash_amount  "
        "WHEN txn.txn_type=${TxnType.expense} THEN -txn.txn_cash_amount  "
        "WHEN txn.txn_type=${TxnType.salesReturn} THEN -txn.txn_cash_amount  "
        "WHEN txn.txn_type=${TxnType.purchaseReturn} THEN txn.txn_cash_amount  "
        "END AS txn_amount, "
        "txn.last_activity_at  "
        "FROM mk_transactions txn  "
        "INNER JOIN mk_ledger_master lm ON lm.ledger_id=txn.ledger_id  AND lm.last_activity_type<>${LastActivityType.Delete} "
        "LEFT JOIN mk_payment_types AS from_bank ON from_bank.pmt_type_id=txn.txn_payment_type_id  "
        "WHERE (txn.txn_payment_type_id='$PAYMENT_MODE_CASH_ID' OR txn.cheque_transferred_to_acc_id='$PAYMENT_MODE_CASH_ID') "
        "AND txn.txn_cash_amount>0 AND txn.last_activity_type<>${LastActivityType.Delete}  ";

    if (null != openingDate && null != closingDate) {
      query += " AND txn.txn_date BETWEEN '$openingDate' AND '$closingDate' ";
    } else if (null != openingDate) {
      query += " AND txn.txn_date < '$openingDate'  ";
    } else if (null != closingDate) {
      query += " AND txn.txn_date > '$closingDate'  ";
    } else if (null != date) {
      query += " AND txn.txn_date = '$date'  ";
    }

    query += "ORDER BY txn_date DESC, last_activity_at DESC  ";

    return query;
  }

  Future<double> getTotalCashInHand({dynamic dbClient}) async {
    double total = 0.00;
    try {
      dbClient ??= await databaseHelper.database;

      Map<String, dynamic> json = (await dbClient.rawQuery(
              "SELECT IFNULL(SUM(txn_amount),0.00) AS total_cash_in_hand FROM (${cashInHandBaseQuery()})"))
          .first;

      total = json["total_cash_in_hand"];
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return total;
  }

  Future<List<CashTxnModel>> getAllCashInHandTxn({dynamic dbClient}) async {
    List<CashTxnModel> txnList = [];
    try {
      dbClient ??= await databaseHelper.database;

      List<Map<String, dynamic>> json =
          await dbClient.rawQuery(cashInHandBaseQuery());

      txnList = json.map((row) => CashTxnModel.fromJson(row)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return txnList;
  }

  Future<Tuple2<double, List<CashTxnModel>>> getAllCashInHandTxnWithTotal(
      {dynamic dbClient}) async {
    print("Eta chittio 1");
    List<CashTxnModel> txnList = [];
    double totalCashInHand = 0.00;

    // try {
    dbClient ??= await databaseHelper.database;

    List<Map<String, dynamic>> json =
        await dbClient.rawQuery(cashInHandBaseQuery());
    print("Eta chittio 2");
    print(json);
    txnList = json.map((row) {
      CashTxnModel txn = CashTxnModel.fromJson(row);
      totalCashInHand += parseDouble(txn.txnAmount)!;
      print("Eta chittio 2");
      return txn;
    }).toList();
    // } catch (e) {
    //   Log.e(tag, e.toString());
    //   e.printError();
    // }

    return Tuple2(totalCashInHand, txnList);
  }

  Future<double> getOpeningCashInHandBalance(
      {required String openingDate}) async {
    double balanceAmount = 0.00;
    try {
      Database? dbClient = await databaseHelper.database;

      String query =
          "SELECT IFNULL(SUM(txn_amount),0.00) AS opening_cash_in_hand FROM (${cashInHandBaseQuery(openingDate: openingDate)})";
      Map<String, dynamic> json = (await dbClient!.rawQuery(query)).first;

      // Log.d("got cash balance ${json}");

      balanceAmount = parseDouble(json["opening_cash_in_hand"])!;
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return balanceAmount;
  }
}
