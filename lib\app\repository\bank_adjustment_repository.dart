import 'package:mobile_khaata_v2/app/model/database/bank_adjustment_modal.dart';
import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/model/others/bank_transaction_model.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';

import 'package:sqflite/sqflite.dart';
import 'package:tuple/tuple.dart';

class BankAdjustmentRepository {
  final String tag = "BankAdjustmentRepository";
  final String tableName = "mk_bank_adjustments";
  DatabaseHelper databaseHelper = DatabaseHelper();
  QueryRepository queryRepository = QueryRepository();

  //========================================================================================= SYNCING ACTIONS
  Future<String> insert(BankAdjustmentModel adjustment,
      {dynamic dbClient, String? batchID}) async {
    String adjustmentId;

    dbClient ??= await databaseHelper.database;

    String primaryKeyPrefix = await getPrimaryKeyPrefix();
    adjustmentId = primaryKeyPrefix + uuidV4;
    adjustment.bankAdjId = adjustmentId;

    adjustment.lastActivityAt = currentDateTime;
    adjustment.lastActivityBy = await getLastActivityBy();
    adjustment.lastActivityType = LastActivityType.New;

    await dbClient.insert(tableName, adjustment.toJson());

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.insert,
      data: adjustment.toJson(),
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    pushPendingQueries(
        singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

    return adjustmentId;
  }

  Future<bool> update(BankAdjustmentModel adjustment,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    adjustment.lastActivityAt = currentDateTime;
    adjustment.lastActivityBy = await getLastActivityBy();
    adjustment.lastActivityType = LastActivityType.Edit;

    String whereClause = "bank_adj_id = ?";
    List<dynamic> whereArgs = [adjustment.bankAdjId];

    await dbClient.update(tableName, adjustment.toJson(),
        where: whereClause, whereArgs: whereArgs);

    QueryModel newQueryModel = QueryModel(
        tableName: tableName,
        queryType: QueryType.update,
        data: adjustment.toJson(),
        whereArgs: whereArgs,
        whereClause: whereClause);
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    pushPendingQueries(
        singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

    status = true;

    return status;
  }

  Future<Tuple2<bool, String>> delete(String txnID,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;
    String message = "";

    dbClient ??= await databaseHelper.database;
    String whereClause = "bank_adj_id = ?";
    List<dynamic> whereArgs = [txnID];
    try {
      // can soft delete for given id
      dbClient.update(
          tableName, {"last_activity_type": LastActivityType.Delete},
          where: whereClause, whereArgs: whereArgs);

      QueryModel newQueryModel = QueryModel(
          tableName: tableName,
          queryType: QueryType.update,
          whereArgs: whereArgs,
          whereClause: whereClause,
          data: {"last_activity_type": LastActivityType.Delete});

      await queryRepository.pushQuery(newQueryModel,
          batchID: batchID, dbClient: dbClient);
      status = true;
      pushPendingQueries(
          singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

      message = "Bank Adjustment deleted successfully";
    } catch (e) {
      message =
          "Cannot delete Bank Adjustment at this moment. Please try again later";
    }

    return Tuple2(status, message);
  }

  //========================================================================================= NON SYNCING ACTIONS
  Future<BankAdjustmentModel> getAdjustmentById(String adjustmentId,
      {dynamic dbClient}) async {
    BankAdjustmentModel adjustment = BankAdjustmentModel();
    try {
      dbClient ??= await databaseHelper.database;

      Map<String, dynamic> json = (await dbClient.rawQuery(
              "SELECT * FROM $tableName WHERE last_activity_type!=? AND bank_adj_id = ?",
              [LastActivityType.Delete, adjustmentId]))
          .first;
      adjustment = BankAdjustmentModel.fromJson(json);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return adjustment;
  }

  String bankBaseQuery(
      {String? bankId,
      String? openingDate,
      String? closingDate,
      String? date}) {
    String query = "SELECT "
        "bank1.pmt_type_id AS bank_id, "
        "bank1.pmt_type_short_name AS bank_short_name, "
        "CASE WHEN bankAdj.bank_adj_type=18 THEN bank2.pmt_type_id END AS bank_id_2, "
        "CASE WHEN bankAdj.bank_adj_type=18 THEN bank2.pmt_type_short_name END AS bank_name_2, "
        "bankAdj.bank_adj_id AS txn_id, "
        "bankAdj.bank_adj_date AS txn_date, "
        "bankAdj.bank_adj_type AS txn_type, "
        "bankAdj.bank_adj_description AS txn_desc, "
        "bankAdj.last_activity_at, "
        "CASE "
        "WHEN bankAdj.bank_adj_type=14 THEN bankAdj.bank_adj_amount "
        "WHEN bankAdj.bank_adj_type=15 THEN -bankAdj.bank_adj_amount "
        "WHEN bankAdj.bank_adj_type=16 THEN bankAdj.bank_adj_amount "
        "WHEN bankAdj.bank_adj_type=17 THEN -bankAdj.bank_adj_amount "
        "WHEN bankAdj.bank_adj_type=18 AND bankAdj.bank_adj_bank_id=bank1.pmt_type_id THEN -bankAdj.bank_adj_amount "
        "WHEN bankAdj.bank_adj_type=18 AND bankAdj.bank_adj_to_bank_id=bank1.pmt_type_id THEN bankAdj.bank_adj_amount "
        "END AS txn_amount, "
        "CASE WHEN bankAdj.bank_adj_type=18 THEN 'bank' ELSE 'cash' END AS txn_mode "
        "FROM mk_payment_types AS bank1 "
        "INNER JOIN mk_bank_adjustments AS bankAdj ON (bankAdj.bank_adj_bank_id=bank1.pmt_type_id OR bankAdj.bank_adj_to_bank_id=bank1.pmt_type_id) AND bankAdj.last_activity_type<>3 "
        "LEFT JOIN mk_payment_types AS bank2 ON (CASE "
        "WHEN bankAdj.bank_adj_type=18 AND bankAdj.bank_adj_bank_id=bank1.pmt_type_id THEN bankAdj.bank_adj_to_bank_id=bank2.pmt_type_id "
        "WHEN bankAdj.bank_adj_type=18 AND bankAdj.bank_adj_to_bank_id=bank1.pmt_type_id THEN bankAdj.bank_adj_bank_id=bank2.pmt_type_id "
        "END) AND bank2.last_activity_type<>3 "
        "WHERE ";

    if (null != bankId) {
      query += " bank1.pmt_type_id='$bankId' AND ";
    }
    query +=
        " bank1.pmt_type_type='BANK' AND bank1.last_activity_type<>${LastActivityType.Delete}  ";

    if (null != openingDate && null != closingDate) {
      query +=
          " AND bankAdj.bank_adj_date BETWEEN '$openingDate' AND '$closingDate' ";
    } else if (null != openingDate) {
      query += " AND bankAdj.bank_adj_date < '$openingDate'  ";
    } else if (null != closingDate) {
      query += " AND bankAdj.bank_adj_date > '$closingDate'  ";
    } else if (null != date) {
      query += " AND bankAdj.bank_adj_date = '$date'  ";
    }

    query += "UNION ALL "
        "SELECT "
        "bank1.pmt_type_id AS bank_id, "
        "bank1.pmt_type_short_name AS bank_short_name, "
        "bank1.pmt_type_id AS bank_id_2, "
        "bank1.pmt_type_short_name AS bank_name_2, "
        "txn.txn_id AS txn_id, "
        "txn.txn_date AS txn_date, "
        "txn.txn_type AS txn_type,  "
        "CASE WHEN txn.txn_display_name IS NULL OR txn.txn_display_name = '' THEN lm.ledger_title ELSE txn.txn_display_name END AS txn_desc, "
        "txn.last_activity_at AS last_activity_at, "
        "CASE "
        "WHEN txn.txn_type=1 THEN txn.txn_cash_amount "
        "WHEN txn.txn_type=2 THEN -txn.txn_cash_amount "
        "WHEN txn.txn_type=3 THEN txn.txn_cash_amount "
        "WHEN txn.txn_type=4 THEN -txn.txn_cash_amount "
        "WHEN txn.txn_type=7 THEN -txn.txn_cash_amount "
        "WHEN txn.txn_type=8 THEN -txn.txn_cash_amount "
        "WHEN txn.txn_type=9 THEN txn.txn_cash_amount "
        "END AS txn_amount, "
        "CASE WHEN txn.txn_payment_type_id=bank1.pmt_type_id THEN 'bank' ELSE 'cheque' END AS txn_mode "
        "FROM mk_payment_types AS bank1 "
        "INNER JOIN mk_transactions txn ON (bank1.pmt_type_id=txn.txn_payment_type_id OR bank1.pmt_type_id=txn.cheque_transferred_to_acc_id) AND txn.txn_cash_amount>0 AND txn.last_activity_type<>3 "
        "LEFT JOIN mk_ledger_master lm ON lm.ledger_id=txn.ledger_id AND lm.last_activity_type<>3 "
        "WHERE ";

    if (null != bankId) {
      query += " bank1.pmt_type_id='$bankId' AND ";
    }
    query +=
        " bank1.pmt_type_type='BANK' AND bank1.last_activity_type<>${LastActivityType.Delete}  ";

    if (null != openingDate && null != closingDate) {
      query += " AND txn.txn_date BETWEEN '$openingDate' AND '$closingDate' ";
    } else if (null != openingDate) {
      query += " AND txn.txn_date < '$openingDate'  ";
    } else if (null != closingDate) {
      query += " AND txn.txn_date > '$closingDate'  ";
    } else if (null != date) {
      query += " AND txn.txn_date = '$date'  ";
    }

    query += "ORDER BY txn.txn_date DESC, txn.last_activity_at DESC ";

    return query;
  }

  Future<List<BankTransactionModel>> getTransactionsForBank(
      String bankId) async {
    List<BankTransactionModel> list = [];
    try {
      Database? dbClient = await databaseHelper.database;

      List<Map<String, dynamic>> jsonItems =
          await dbClient!.rawQuery(bankBaseQuery(bankId: bankId));

      // Log.d("got transaction ${jsonItems}");

      list = jsonItems.map((e) => BankTransactionModel.fromJson(e)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return list;
  }

  Future<double> getCurrentBankBalance(String bankId) async {
    double balanceAmount = 0.00;
    try {
      Database? dbClient = await databaseHelper.database;
      String query = "SELECT "
          "IFNULL(mk_payment_types.pmt_type_opening_balance, 0.00) AS pmt_type_opening_balance, "
          "IFNULL(SUM(txn_amount),0.00) AS total_bank "
          "FROM mk_payment_types "
          "LEFT JOIN (${bankBaseQuery(bankId: bankId)}) AS bal ON bal.bank_id=mk_payment_types.pmt_type_id "
          "WHERE mk_payment_types.pmt_type_id='$bankId' AND mk_payment_types.last_activity_type<>3 ";

      Map<String, dynamic> json = (await dbClient!.rawQuery(query)).first;

      // Log.d("got bank current balance ${json}");
      if (json["total_bank"] is double) {
        balanceAmount = parseDouble((parseDouble(json["total_bank"]!)! +
                parseDouble(json["pmt_type_opening_balance"])!)
            .toStringAsFixed(2))!;
      }
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return balanceAmount;
  }

  Future<double> getOpeningBankBalance(
      {required String bankId, required String openingDate}) async {
    double balanceAmount = 0.00;
    try {
      Database? dbClient = await databaseHelper.database;
      String query = "SELECT "
          "IFNULL(mk_payment_types.pmt_type_opening_balance, 0.00) AS pmt_type_opening_balance, "
          "IFNULL(SUM(txn_amount),0.00) AS total_bank "
          "FROM mk_payment_types "
          "LEFT JOIN (${bankBaseQuery(bankId: bankId, openingDate: openingDate)}) AS bal ON bal.bank_id=mk_payment_types.pmt_type_id "
          "WHERE mk_payment_types.pmt_type_id='$bankId' AND mk_payment_types.last_activity_type<>3 ";

      Map<String, dynamic> json = (await dbClient!.rawQuery(query)).first;

      // Log.d("got transaction ${json}");
      if (json["total_bank"] is double) {
        balanceAmount = parseDouble((parseDouble(json["total_bank"]!)! +
                parseDouble(json["pmt_type_opening_balance"])!)
            .toStringAsFixed(2))!;
      }
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return balanceAmount;
  }
}
