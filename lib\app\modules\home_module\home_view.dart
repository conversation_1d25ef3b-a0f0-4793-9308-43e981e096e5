import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_speed_dial/flutter_speed_dial.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/contact_us_dialog.dart';
import 'package:mobile_khaata_v2/app/components/dashboard_item_view.dart';
import 'package:mobile_khaata_v2/app/controllers/registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/model/others/company_model.dart';
import 'package:mobile_khaata_v2/app/modules/backup_module/backup_controller.dart';
import 'package:mobile_khaata_v2/app/modules/home_module/home_screen_controller.dart';
import 'package:mobile_khaata_v2/core/base_view.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/login_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:share/share.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tuple/tuple.dart';

import '../../../utilities/device_info_helper.dart';
import '../../../utilities/shared_pref_helper1.dart';
import '../../../utilities/transaction_helper.dart';
import '../../model/others/login_model.dart';
import '../auth_modules/otp_verification.dart';
import '../auth_modules/select_company_module/select_company_controller.dart';

class HomeScreenView extends BaseView<HomeScreen, HomeScreenController> {
  HomeScreenView({required HomeScreenController state}) : super(state);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      resizeToAvoidBottomInset: true,
      body: Container(
        color: Color(0xFFFDFAE3),
        child: Column(
          children: [
            //=============================================AppBar
            AppBarHome(
              state: state,
            ),

            //=============================================Company Info
            DashboardViewItem(),
            // ElevatedButton(
            //   onPressed: () {
            //     Map<String, dynamic> data = {
            //       "txn_id": "23352-09632f67-43f5-4720-b6e9-5ba2594b60fc",
            //       "txn_date": "2024-06-14",
            //       "txn_ref_number_char": "25143",
            //       "txn_type": 3,
            //       "expense_category_id": null,
            //       "ledger_id": "23352-7720705f-6722-4a7f-b693-55164d9bf239",
            //       "txn_cash_amount": 235145.0,
            //       "txn_balance_amount": 0.0,
            //       "txn_discount_percent": 0.0,
            //       "txn_discount_amount": 0.0,
            //       "txn_tax_percent": 0.0,
            //       "txn_tax_amount": 0.0,
            //       "txn_description": "hhhi",
            //       "txn_display_name": null,
            //       "last_activity_type": 1,
            //       "last_activity_at": "2024-06-14 10:01:54",
            //       "last_activity_by": "Demo Prietors-9843631160",
            //       "txn_payment_type_id": "1",
            //       "txn_payment_reference": null,
            //       "cheque_issue_date": null
            //     };
            //
            //     List<Map<String, dynamic>> pending_queries = [
            //       {
            //         "batch_id": "23352-374b126e-d7cf-4264-be24-a570f1ff4df2",
            //         "queries": [
            //           {
            //             "query_type": "insert",
            //             "table_name": "mk_transactions",
            //             "where_clause": null,
            //             "data": jsonEncode(data),
            //             "where_args": "null",
            //             "created_at": "2024-06-14 10:01:54",
            //             "query_id": "2",
            //             "batch_id": "23352-374b126e-d7cf-4264-be24-a570f1ff4df2"
            //           }
            //         ]
            //       }
            //     ];
            //
            //     Map<String, dynamic> body = {
            //       "pending_queries": jsonEncode(pending_queries),
            //       "synced_batch_id": []
            //     };
            //
            //     Logger logger = Logger(
            //       printer: PrettyPrinter(methodCount: 0, errorMethodCount: 5, lineLength: 200, colors: true, printEmojis: true, printTime: false),
            //     );
            //     logger.f(jsonEncode(body));
            //   },
            //   style: ElevatedButton.styleFrom(
            //     primary: Colors.blue, // Background color
            //     onPrimary: Colors.white, // Text color
            //     padding: EdgeInsets.symmetric(horizontal: 30, vertical: 15),
            //     textStyle: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            //   ),
            //   child: Text('Click Me'),
            // ),

            //=============================================Home Menu
            Expanded(child: HomeMenuBox())
          ],
        ),
      ),

      //=============================================Right Navigation
      // endDrawer: RightNavigationDrawer(state),
      drawer: RightNavigationDrawer(state),

      //=============================================Reminder Floating Icon
      floatingActionButton: SpeedDial(
        // marginBottom: 10,
        // marginRight: 10,
        elevation: 8,
        backgroundColor: colorPrimary,
        overlayColor: colorPrimaryLightest,
        child: Container(
          width: double.infinity,
          child: Image.asset(
            "images/reminder-white.png",
            fit: BoxFit.contain,
            height: 36,
          ),
        ),
        children: [
          SpeedDialChild(
              child: Icon(
                Icons.add,
                size: 30,
              ),
              label: "नयाँ रिमाइन्डर (New Reminder)",
              backgroundColor: colorGreen,
              onTap: () => Navigator.pushNamed(context, "/addEditReminder")),
          SpeedDialChild(
              child: Icon(
                Icons.list,
                size: 30,
              ),
              label: "रिमाइन्डर सूची (Reminder List)",
              backgroundColor: colorOrangeLight,
              onTap: () => Navigator.pushNamed(context, "/reminderList")),
        ],
      ),
    ));
  }
}

class AppBarHome extends StatefulWidget {
  final HomeScreenController state;

  AppBarHome({required this.state});

  @override
  _AppBarHomeState createState() => _AppBarHomeState();
}

class _AppBarHomeState extends State<AppBarHome> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(left: 10, top: 0, bottom: 0, right: 10),
      // height: 110.0,
      decoration: BoxDecoration(
        color: colorPrimary,
        boxShadow: downShadow,
      ),
      child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                InkWell(
                  child: Image.asset(
                    'images/logo-appbar.png',
                    height: 45.0,
                    fit: BoxFit.contain,
                  ),
                  onTap: () {
                    Scaffold.of(context).openDrawer();
                  },
                ),
                Row(
                  // crossAxisAlignment: CrossAxisAlignment.baseline,
                  children: [
                    Text(
                      "mobile",
                      style: TextStyle(
                          fontSize: 18,
                          color: Colors.white,
                          fontFamily: 'ArialBlack'),
                    ),
                    Text(
                      " खाता",
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontWeight: FontWeight.w800,
                        fontFamily: 'ArialBlack',
                      ),
                    ),
                  ],
                ),
              ],
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(top: 15),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    InkWell(
                      onTap: () {
                        Scaffold.of(context).openDrawer();
                      },
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: Colors.white,
                        ),
                        width: 35,
                        height: 35,
                        child: Icon(
                          Icons.menu,
                          color: colorPrimary,
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 10,
                    ),
                    Obx(() {
                      return InkWell(
                        onTap: () {
                          Navigator.pushNamed(context, '/notifications');
                        },
                        child: Container(
                          width: 35,
                          height: 35,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(10),
                            color: Colors.white,
                          ),
                          child: Container(
                            padding: EdgeInsets.all(5),
                            decoration: BoxDecoration(
                                color: Colors.white, shape: BoxShape.circle),
                            child: Icon(
                              widget.state.hasNotification.value
                                  ? Icons.notifications_active
                                  : Icons.notifications_none,
                              color: widget.state.hasNotification.value
                                  ? colorOrangeDark
                                  : colorPrimary,
                              size: 25,
                            ),
                          ),
                        ),
                      );
                    }),
                    SizedBox(
                      width: 10,
                    ),
                    InkWell(
                      onTap: () async {
                        ProgressDialog progressDialog = ProgressDialog(context,
                            type: ProgressDialogType.normal,
                            isDismissible: false);
                        progressDialog.update(
                            message: "Syncing data. Please wait....");
                        await progressDialog.show();
                        // bool status = await BackupController().syncAllData();
                        Tuple2<bool, String> combinedResp =
                            await BackupController().syncAllDataWithMessage();

                        await progressDialog.hide();

                        print(combinedResp.item2);
                        showAlertDialog(context,
                            alertType: combinedResp.item1
                                ? AlertType.Success
                                : AlertType.Error,
                            alertTitle:
                                "ब्याकअप जानकारी \n(Backup Information)",
                            message: combinedResp.item2);
                      },
                      child: Container(
                        width: 35,
                        height: 35,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: Colors.white,
                        ),
                        child: Icon(
                          Icons.cloud_upload_sharp,
                          color: colorPrimary,
                          size: 25,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ].reversed.toList()),
    );
  }
}

class HomeMenuBox extends StatelessWidget {
  final SharedPrefHelper1 prefsHelper = SharedPrefHelper1();
  _homeMenuOption(
      {@required onClick,
      @required nepTitle,
      @required engTitle,
      @required image}) {
    return Container(
      width: 130,
      height: 150,
      padding: EdgeInsets.zero,
      decoration: BoxDecoration(
        border: Border.all(color: colorPrimary),
        borderRadius: BorderRadius.circular(17),
      ),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          backgroundColor: colorPrimaryLighter,
          elevation: 8,
          surfaceTintColor: Colors.white,
          padding: EdgeInsets.zero,
        ),
        onPressed: onClick,
        child: Container(
          child: Stack(
            children: [
              Container(
                width: double.infinity,
                alignment: Alignment.bottomCenter,
                padding: EdgeInsets.only(bottom: 6),
                decoration: BoxDecoration(
                  color: colorPrimary,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  engTitle,
                  style: TextStyle(
                    fontSize: 15,
                    color: Colors.white,
                    fontFamily: 'HelveticaRegular',
                  ),
                ),
              ),
              Container(
                width: double.infinity,
                margin: EdgeInsets.only(bottom: 30),
                padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Column(
                  children: [
                    Text(
                      nepTitle,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 15,
                          color: colorPrimary,
                          fontFamily: 'HelveticaRegular',
                          fontWeight: FontWeight.bold),
                    ),
                    SizedBox(
                      height: 5,
                    ),
                    Image.asset(
                      'images/$image',
                      height: 60.0,
                      fit: BoxFit.scaleDown,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.symmetric(vertical: 20),
        child: Wrap(
          alignment: WrapAlignment.center,
          spacing: 50,
          runSpacing: 30,
          children: [
            _homeMenuOption(
              nepTitle: "बिक्री (आम्दानी)",
              engTitle: "Sales/Income",
              image: "sales-blue.png",
              onClick: () {
                prefsHelper.checkPermission("Sales").then((value) {
                  if (value) {
                    // Navigator.of(context).pop();
                    Navigator.pushNamed(context, "/addSale");
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "Sales/Income (बिक्री (आम्दानी))",
                        message:
                            "You don't have access to Sales/Income \n(तपाईंलाई बिक्री (आम्दानी) हेर्न अनुमति छैन।)");
                  }
                });
              },
            ),
            _homeMenuOption(
              nepTitle: "खरिद",
              engTitle: "Purchase",
              image: "purchase-blue.png",
              onClick: () {
                prefsHelper.checkPermission("Purchase").then((value) {
                  if (value) {
                    // Navigator.of(context).pop();
                    Navigator.pushNamed(context, "/addPurchase");
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "Purchase(खरिद)",
                        message:
                            "You don't have access to Purchase \n(तपाईंलाई खरिद हेर्न अनुमति छैन।)");
                  }
                });
              },
            ),
            _homeMenuOption(
              nepTitle: "उधारो",
              engTitle: "Credit",
              image: "credit-small.png",
              onClick: () {
                Navigator.pushNamed(context, "/creditList");
              },
            ),
            _homeMenuOption(
              nepTitle: "खर्च",
              engTitle: "Expenses",
              image: "payment-blue.png",
              onClick: () {
                prefsHelper.checkPermission("Expenses").then((value) {
                  if (value) {
                    // Navigator.of(context).pop();
                    Navigator.pushNamed(context, "/listExpenses");
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "Expenses(खर्च)",
                        message:
                            "You don't have access to Expenses \n(तपाईंलाई खर्च हेर्न अनुमति छैन।)");
                  }
                });
              },
            ),
            _homeMenuOption(
              nepTitle: "रिपोर्ट",
              engTitle: "Report",
              image: "report-small.png",
              onClick: () {
                prefsHelper.checkPermission("Reports").then((value) {
                  if (value) {
                    // Navigator.of(context).pop();
                    Navigator.pushNamed(context, "/reportList");
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "Report(रिपोर्ट)",
                        message:
                            "You don't have access to Report \n(तपाईंलाई रिपोर्ट हेर्न अनुमति छैन।)");
                  }
                });
              },
            ),
          ],
        ),
      ),
    );
  }
}

//============================================================Right Navigation
class RightNavigationDrawer extends StatefulWidget {
  final HomeScreenController state;

  RightNavigationDrawer(this.state);

  @override
  State<RightNavigationDrawer> createState() => _RightNavigationDrawerState();
}

class _RightNavigationDrawerState extends State<RightNavigationDrawer> {
  // Use Get.find to get existing controller or Get.put with permanent flag
  RegistrationDetailController registrationDetailController =
      Get.put(RegistrationDetailController(), permanent: true);

  final SharedPrefHelper1 prefsHelper = SharedPrefHelper1();

  final SelectCompanyController companyController = SelectCompanyController();

  _customSubListTile(
      {String iconType = "image",
      String? leadingImageIcon,
      Widget? leadingIcon,
      required String title,
      String subTitle = "",
      required Function onClick}) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          onClick();
        },
        child: Container(
          padding: EdgeInsets.only(bottom: 8, top: 8, right: 16, left: 54),
          child: Row(
            children: [
              Container(
                alignment: Alignment.centerLeft,
                child: ("image" == iconType)
                    ? Image.asset(
                        leadingImageIcon!,
                        height: 25,
                        width: 25,
                        fit: BoxFit.fitHeight,
                      )
                    : leadingIcon,
                width: 40,
              ),
              SizedBox(
                  // width: 20,
                  ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    textAlign: TextAlign.left,
                    style: TextStyle(color: colorPrimary),
                  ),
                  SizedBox(
                    height: 1,
                  ),
                  if ((subTitle.isNotEmpty))
                    Text(
                      "${subTitle}",
                      textAlign: TextAlign.left,
                      style: TextStyle(color: textColor, fontSize: 12),
                    )
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  _customListTile(
      {String iconType = "image",
      String? leadingImageIcon,
      Widget? leadingIcon,
      required String title,
      String subTitle = "",
      required Function onClick}) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          onClick();
        },
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 16),
          child: Row(
            children: [
              Container(
                alignment: Alignment.centerLeft,
                child: ("image" == iconType)
                    ? Image.asset(
                        leadingImageIcon!,
                        height: 25,
                        width: 25,
                        fit: BoxFit.fitHeight,
                      )
                    : leadingIcon,
                width: 40,
              ),
              SizedBox(
                  // width: 20,
                  ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    textAlign: TextAlign.left,
                    style: TextStyle(color: colorPrimary),
                  ),
                  SizedBox(
                    height: 1,
                  ),
                  if ((subTitle.isNotEmpty))
                    Text(
                      "${subTitle}",
                      textAlign: TextAlign.left,
                      style: TextStyle(color: textColor, fontSize: 12),
                    )
                ],
              )
            ],
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    setState(() {});
    return Drawer(
      child: ListView(
        padding: EdgeInsets.zero,
        shrinkWrap: true,
        children: <Widget>[
          Container(
            padding: EdgeInsets.only(top: 40, bottom: 10, left: 5, right: 5),
            decoration: BoxDecoration(color: colorPrimary),
            constraints: BoxConstraints(minHeight: 200),
            child: Obx(
              () {
                if (registrationDetailController.isLoading) {
                  return Container(
                    child: Center(
                      child: CircularProgressIndicator(
                        backgroundColor: Colors.white,
                        valueColor: AlwaysStoppedAnimation<Color>(colorPrimary),
                      ),
                    ),
                  );
                }
                return Column(
                  children: [
                    Container(
                      width: double.infinity,
                      child: Stack(
                        children: [
                          if (isAdmin)
                            Positioned(
                              right: 0,
                              child: Container(
                                width: 40,
                                decoration: BoxDecoration(boxShadow: upShadow),
                                child: ElevatedButton(
                                  child: Icon(
                                    Icons.edit,
                                    color: colorPrimary,
                                  ),
                                  style: ElevatedButton.styleFrom(
                                    shape: CircleBorder(),
                                    backgroundColor: Colors.white,
                                    foregroundColor: colorPrimaryLighter,
                                    padding: EdgeInsets.zero,
                                  ),
                                  onPressed: () {
                                    Navigator.of(context).pop();
                                    Navigator.pushNamed(
                                        context, "/userBasicInfo");
                                  },
                                ),
                              ),
                            ),
                          Center(
                            child: Center(
                              child: getImageWidget(
                                  context,
                                  registrationDetailController.logo.imageBitmap,
                                  registrationDetailController
                                          .registrationDetail.businessName ??
                                      " ",
                                  shapeColor: Colors.white,
                                  fontColor: colorPrimary),
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 10.0),
                    Text(
                      registrationDetailController
                              .registrationDetail.businessName ??
                          "",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: 16,
                          fontFamily: "HelveticaBold",
                          color: Colors.white),
                    ),
                    SizedBox(height: 5.0),
                    Text(
                      registrationDetailController
                              .registrationDetail.businessAddress ??
                          "",
                      textAlign: TextAlign.center,
                      style: TextStyle(fontSize: 14, color: Colors.white),
                    ),
                    SizedBox(height: 15.0),
                    Container(
                      child: Obx(
                        () {
                          return Text(
                            "Proprietor: " +
                                (registrationDetailController
                                        .registrationDetail.fullName ??
                                    ""),
                            textAlign: TextAlign.center,
                            style: TextStyle(fontSize: 14, color: Colors.white),
                          );
                        },
                      ),
                    ),
                    SizedBox(height: 10.0),
                    Container(
                      child: Text(
                        "(${(isAdmin) ? "Admin" : "SubUser"})",
                        textAlign: TextAlign.center,
                        style: TextStyle(fontSize: 14, color: Colors.white),
                      ),
                    )
                  ],
                );
              },
            ),
          ),
          SizedBox(
            height: 10,
          ),

          _customListTile(
              leadingImageIcon: 'images/user-blue.png',
              title: "पार्टी/ग्राहक खाता",
              subTitle: "Party Ledger",
              onClick: () {
                prefsHelper.checkPermission("Party Ledger").then((value) {
                  if (value) {
                    Navigator.of(context).pop();
                    Navigator.pushNamed(context, "/partyLedgerList");
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "Party Ledger (पार्टी खाता)",
                        message:
                            "You don't have access to Party Ledger \n(तपाईंलाई पार्टी खाता हेर्न अनुमति छैन।)");
                  }
                });
              }),

          _customListTile(
              leadingImageIcon: 'images/product-blue.png',
              title: "सामानहरु",
              subTitle: "Items",
              onClick: () {
                prefsHelper.checkPermission("Items").then((value) {
                  if (value) {
                    Navigator.of(context).pop();
                    Navigator.pushNamed(context, "/itemList");
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "Items (सामानहरु)",
                        message:
                            " You don't have access to Party Ledger \n(तपाईंलाई सामानहरु हेर्न अनुमति छैन।)");
                  }
                });
              }),

          _customListTile(
              leadingIcon: Icon(
                Icons.list,
                color: colorPrimary,
              ),
              iconType: 'icon',
              title: "सबै लेनदेन",
              subTitle: "All Transactions",
              onClick: () {
                prefsHelper.checkPermission("All Transactions").then((value) {
                  if (value) {
                    Navigator.of(context).pop();
                    Navigator.pushNamed(context, "/allTransaction");
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "All Transactions (सबै लेनदेन)",
                        message:
                            " You don't have access to All Transactions\n(तपाईंलाई सबै लेनदेन हेर्न अनुमति छैन।)");
                  }
                });
              }),

          _customListTile(
              leadingImageIcon: 'images/sales-small.png',
              title: "बिक्री फिर्ता",
              subTitle: "Sales Return",
              onClick: () {
                prefsHelper.checkPermission("Sales Return").then((value) {
                  if (value) {
                    Navigator.of(context).pop();
                    Navigator.pushNamed(context, "/addSaleReturn");
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "Sales Return (िक्री फिर्ता)",
                        message:
                            " You don't have access to Sales Return\n(तपाईंलाई िक्री फिर्ता हेर्न अनुमति छैन।)");
                  }
                });
              }),

          _customListTile(
              leadingImageIcon: 'images/purchase-small.png',
              title: "खरिद फिर्ता",
              subTitle: "Purchase Return",
              onClick: () {
                prefsHelper.checkPermission("Purchase Return").then((value) {
                  if (value) {
                    Navigator.of(context).pop();
                    Navigator.pushNamed(context, "/addPurchaseReturn");
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "Purchase Return (खरिद फिर्ता)",
                        message:
                            " You don't have access to Purchase Return\n(तपाईंलाई खरिद फिर्ता हेर्न अनुमति छैन।)");
                  }
                });
              }),

          Divider(
            color: colorPrimary,
          ),
          _customListTile(
              leadingImageIcon: 'images/cash_in_hand-blue.png',
              title: "नगद मौज्दात",
              subTitle: "Cash In Hand",
              onClick: () {
                prefsHelper.checkPermission("Cash in Hand").then((value) {
                  if (value) {
                    Navigator.of(context).pop();
                    Navigator.pushNamed(context, "/cashInHandDetail");
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "Cash In Hand(नगद मौज्दात)",
                        message:
                            " You don't have access to Cash In Hand\n(तपाईंलाई नगद मौज्दात हेर्न अनुमति छैन।)");
                  }
                });
              }),
          _customListTile(
              leadingIcon: Icon(
                Icons.account_balance,
                color: colorPrimary,
              ),
              iconType: 'icon',
              title: "बैंकहरू/सहकारीहरू",
              subTitle: "Banks/Co-operatives",
              onClick: () {
                prefsHelper
                    .checkPermission("Banks/Co-operatives")
                    .then((value) {
                  if (value) {
                    Navigator.of(context).pop();
                    Navigator.pushNamed(context, "/banks");
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "Banks/Co-operatives(ैंकहरू/सहकारीहरू)",
                        message:
                            " You don't have access to Banks/Co-operatives\n(तपाईंलाई ैंकहरू/सहकारीहरू हेर्न अनुमति छैन।)");
                  }
                });
              }),
          _customListTile(
              leadingIcon: Icon(
                Icons.money,
                color: colorPrimary,
              ),
              iconType: 'icon',
              title: "चेकहरु",
              subTitle: "Cheques",
              onClick: () {
                prefsHelper.checkPermission("Cheques").then((value) {
                  if (value) {
                    Navigator.of(context).pop();
                    Navigator.pushNamed(context, "/cheque");
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "Cheques(चेकहरु)",
                        message:
                            " You don't have access to Cheques\n(तपाईंलाई चेकहरु हेर्न अनुमति छैन।)");
                  }
                });
              }),
          Divider(
            color: colorPrimary,
          ),
          _customListTile(
              leadingImageIcon: 'images/expense-small.png',
              title: "खर्च शीर्षक",
              subTitle: "Expense Category",
              onClick: () {
                prefsHelper.checkPermission("Expense Category").then((value) {
                  if (value) {
                    Navigator.of(context).pop();
                    Navigator.pushNamed(context, "/listExpensesCategory");
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "Expense Category(खर्च शीर्षक)",
                        message:
                            " You don't have access to Expense Category\n(तपाईंलाई खर्च शीर्षक हेर्न अनुमति छैन।)");
                  }
                });
              }),
          Theme(
            data: ThemeData(
              accentColor: colorPrimary,
              unselectedWidgetColor: colorPrimary,
            ),
            child: ExpansionTile(
              title: Row(
                children: [
                  Icon(
                    FontAwesomeIcons.toolbox,
                    // Icons.toolbox
                    color: colorPrimary,
                  ),
                  SizedBox(
                    width: 16,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "उपयोगिताहरु",
                        textAlign: TextAlign.left,
                        style: TextStyle(color: colorPrimary),
                      ),
                      SizedBox(
                        height: 1,
                      ),
                      Text(
                        "Utilities",
                        textAlign: TextAlign.left,
                        style: TextStyle(color: textColor, fontSize: 12),
                      )
                    ],
                  )
                ],
              ),
              children: [
                Container(
                  color: Colors.black.withOpacity(0.15),
                  child: Column(
                    children: [
                      _customSubListTile(
                        iconType: "icon",
                        leadingIcon: Icon(
                          FontAwesomeIcons.fileExcel,
                          color: colorPrimary,
                        ),
                        title: "पार्टी खाताहरु ईम्पोर्ट गर्नुस्",
                        subTitle: "Import Parties Account",
                        onClick: () async {
                          prefsHelper
                              .checkPermission("Utilities")
                              .then((value) {
                            if (value) {
                              Navigator.of(context).pop();
                              Navigator.pushNamed(context, "/importParty");
                            } else {
                              showAlertDialog(context,
                                  alertType: AlertType.Error,
                                  alertTitle: "Utilities(उपयोगिताहरु)",
                                  message:
                                      " You don't have access to Utilities\n(तपाईंलाई उपयोगिताहरु हेर्न अनुमति छैन।)");
                            }
                          });

                          Navigator.of(context).pop();
                          Navigator.pushNamed(context, "/importParty");
                        },
                      ),
                      _customSubListTile(
                          iconType: "icon",
                          leadingIcon: Icon(
                            FontAwesomeIcons.fileExcel,
                            color: colorPrimary,
                          ),
                          title: "सामानहरु ईम्पोर्ट गर्नुस्",
                          subTitle: "Import Products/Items",
                          onClick: () {
                            prefsHelper
                                .checkPermission("Utilities")
                                .then((value) {
                              if (value) {
                                Navigator.of(context).pop();
                                Navigator.pushNamed(context, "/importItem");
                              } else {
                                showAlertDialog(context,
                                    alertType: AlertType.Error,
                                    alertTitle: "Utilities(उपयोगिताहरु)",
                                    message:
                                        " You don't have access to Utilities\n(तपाईंलाई उपयोगिताहरु हेर्न अनुमति छैन।)");
                              }
                            });
                          }),
                    ],
                  ),
                )
              ],
            ),
          ),
          _customListTile(
              leadingImageIcon: 'images/lock-blue.png',
              title: "सुरक्षा",
              subTitle: "Security",
              onClick: () {
                prefsHelper.checkPermission("Security").then((value) {
                  if (value) {
                    Navigator.of(context).pop();
                    Navigator.pushNamed(context, "/securitySetting");
                  } else {
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "Security(सुरक्षा)",
                        message:
                            " You don't have access to Security\n(तपाईंलाई सुरक्षा हेर्न अनुमति छैन।)");
                  }
                });
              }),
          if (isAdmin)
            _customListTile(
                iconType: 'icon',
                leadingIcon: Icon(
                  Icons.supervised_user_circle,
                  color: colorPrimary,
                ),
                title: "प्रयोगकर्ताहरू",
                subTitle: "Users",
                onClick: () {
                  prefsHelper.checkPermission("Users").then((value) {
                    if (value) {
                      Navigator.of(context).pop();
                      Navigator.pushNamed(context, "/allUsers");
                    } else {
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "Users(प्रयोगकर्ताहरू)",
                          message:
                              " You don't have access to Users\n(तपाईंलाई प्रयोगकर्ताहरू हेर्न अनुमति छैन।)");
                    }
                  });
                }),

          if (isAdmin)
            Theme(
              data: ThemeData(
                accentColor: colorPrimary,
                unselectedWidgetColor: colorPrimary,
              ),
              child: ExpansionTile(
                title: Row(
                  children: [
                    Icon(
                      Icons.apartment,
                      // Icons.toolbox
                      color: colorPrimary,
                    ),
                    SizedBox(
                      width: 16,
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "व्यवसायहरू",
                          textAlign: TextAlign.left,
                          style: TextStyle(color: colorPrimary),
                        ),
                        SizedBox(
                          height: 1,
                        ),
                        Text(
                          "Companies",
                          textAlign: TextAlign.left,
                          style: TextStyle(color: textColor, fontSize: 12),
                        )
                      ],
                    )
                  ],
                ),
                children: [
                  Container(
                    color: Colors.black.withOpacity(0.15),
                    child: FutureBuilder<List<CompanyModel>>(
                      future: companyController.getCompanies(context),
                      builder: (context, snapshot) {
                        if (snapshot.hasData) {
                          List<CompanyModel> companies = snapshot.data!;
                          return ListView.builder(
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: companies.length,
                            itemBuilder: (context, index) {
                              if (companies[index].registrationDetail != null &&
                                  companies[index]
                                          .registrationDetail!
                                          .businessName! ==
                                      registrationDetailController
                                          .registrationDetail.businessName) {
                                return SizedBox();
                              } else {
                                return _customSubListTile(
                                  iconType: "icon",
                                  // leadingImageIcon: 'images/company-blue.png',
                                  title: companies[index].registrationDetail !=
                                          null
                                      ? companies[index]
                                          .registrationDetail!
                                          .businessName!
                                      : companies[index].userName!,
                                  // subTitle: companies[index].registrationDetail!.businessAddress ?? "",
                                  leadingIcon: Icon(Icons.apartment,
                                      color: colorPrimary),
                                  onClick: () async {
                                    DeviceInfoHelper deviceInfo =
                                        new DeviceInfoHelper();
                                    await deviceInfo.init();

                                    // Navigator.of(context).pop();

                                    _doLogin(
                                      LoginModel(
                                        parentId:
                                            companies[index].parentId != null
                                                ? int.parse(companies[index]
                                                    .parentId!
                                                    .toString())
                                                : null,
                                        deviceId: deviceInfo.deviceId,
                                        deviceManufacturer:
                                            deviceInfo.manufacturer,
                                        deviceModel: deviceInfo.model,
                                        devicePlatform: deviceInfo.platform,
                                        deviceName: deviceInfo.name,
                                        isAdmin: isAdmin,
                                        password: companies[index].userName,
                                        username: companies[index].userName,
                                        subusername: "",
                                      ),
                                      context,
                                    );
                                  },
                                );
                              }
                            },
                          );
                        } else if (snapshot.hasError) {
                          return Center(
                            child: Text("Error loading companies"),
                          );
                        } else {
                          return Center(
                            child: Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: CircularProgressIndicator(),
                            ),
                          );
                        }
                      },
                    ),
                  ),
                ],
              ),
            ),
          // _customListTile(
          //     iconType: 'icon',
          //     leadingIcon: Icon(
          //       Icons.apartment,
          //       color: colorPrimary,
          //     ),
          //     title: "संगठन",
          //     subTitle: "Companies",
          //     onClick: () {
          //       // Navigator.of(context).pop();
          //       // Navigator.of(context).push(
          //       //   MaterialPageRoute(
          //       //     builder: (context) => CompaniesScreen(),
          //       //   ),
          //       // );
          //     }),

          if (isAdmin)
            _customListTile(
                iconType: 'icon',
                leadingIcon: Icon(
                  Icons.add_home,
                  color: colorPrimary,
                ),
                title: "कम्पनी थप्नुहोस्",
                subTitle: "Add Company",
                onClick: () => showCompanyFormDialog(context)),

          _customListTile(
              iconType: "icon",
              leadingIcon: Icon(
                Icons.backup,
                color: colorPrimary,
              ),
              title: "ब्याकअप जानकारी",
              subTitle: "Backup Information",
              onClick: () {
                Navigator.of(context).pop();
                Navigator.pushNamed(context, "/backup");
              }),
          Divider(
            color: colorPrimary,
          ),
          _customListTile(
              iconType: "icon",
              leadingIcon: Container(
                height: 30,
                width: 30,
                padding: EdgeInsets.only(right: 2, bottom: 2),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: colorOrangeDark),
                alignment: Alignment.center,
                child: Icon(
                  FontAwesomeIcons.crown,
                  color: Colors.white,
                  size: 13,
                ),
              ),
              title: "सदस्यता जानकारी",
              subTitle: "Subscription Information",
              onClick: () {
                Navigator.of(context).pop();
                Navigator.pushNamed(context, "/subscription");
              }),
          Divider(
            color: colorPrimary,
          ),
          _customListTile(
              iconType: "icon",
              leadingIcon: Icon(
                Icons.share,
                color: colorPrimary,
              ),
              title: "शेयर गर्नुहोस्",
              subTitle: "Share",
              onClick: () async {
                Share.share(SHARE_APP_MSG);
              }),
          _customListTile(
              iconType: "icon",
              leadingIcon: Icon(
                Icons.star,
                color: colorPrimary,
              ),
              title: "रेटिङ दिनुहोस्",
              subTitle: "Rating",
              onClick: () {
                final InAppReview inAppReview = InAppReview.instance;
                inAppReview.openStoreListing(appStoreId: APP_STORE_ID);
              }),
          _customListTile(
              iconType: "icon",
              leadingIcon: Icon(
                Icons.video_collection,
                color: colorPrimary,
              ),
              title: "mobile खाता चलाऊन सिक्नुहोस्",
              subTitle: "How to use mobile खाता ?",
              onClick: () {
                Navigator.of(context).pop();
                Navigator.pushNamed(context, "/tutorials");
              }),
          _customListTile(
              iconType: "icon",
              leadingIcon: Icon(
                Icons.info_outline,
                color: colorPrimary,
              ),
              title: "हाम्रोबारे",
              subTitle: "About Us",
              onClick: () {
                Navigator.of(context).pop();
                Navigator.pushNamed(context, "/aboutUs");
              }),
          _customListTile(
              iconType: "icon",
              leadingIcon: Icon(
                FontAwesomeIcons.phoneSquareAlt,
                color: colorPrimary,
              ),
              title: "सम्पर्क गर्नुहोस ",
              subTitle: 'Contact Us',
              onClick: () {
                Navigator.of(context).pop();
                displayContactUsDialog(context);
              }),
          Divider(
            color: colorPrimary,
          ),
          _customListTile(
              leadingImageIcon: 'images/logout-blue.png',
              title: "लग-आउट",
              subTitle: "Logout ",
              onClick: () => widget.state.logoutButtonOnClickHandler()),
          SizedBox(
            height: 20,
          ),
        ],
      ),
    );
  }

  void showCompanyFormDialog(BuildContext context) {
    final TextEditingController companyNameController = TextEditingController();
    final TextEditingController addressController = TextEditingController();
    final GlobalKey<FormState> formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text("Enter Company Details"),
          content: Form(
            key: formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFormField(
                  controller: companyNameController,
                  decoration: InputDecoration(labelText: "Company Name"),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return "Please enter company name";
                    }
                    return null;
                  },
                ),
                TextFormField(
                  controller: addressController,
                  decoration: InputDecoration(labelText: "Company Address"),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return "Please enter company address";
                    }
                    return null;
                  },
                ),
              ],
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text("Cancel"),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                if (formKey.currentState!.validate()) {
                  // You can use companyNameController.text and addressController.text here
                  print("Company: ${companyNameController.text}");
                  print("Address: ${addressController.text}");
                  bool result = await addCompany(
                    companyNameController.text,
                    addressController.text,
                    context,
                  );

                  print(context.mounted.toString() + " 4 ");
                  print(result.toString() + " result");
                }
              },
              child: Text("Submit"),
            ),
          ],
        );
      },
    );
  }

  Future<bool> addCompany(
      String companyName, String address, BuildContext context) async {
    try {
      ProgressDialog _progressDialog = ProgressDialog(context,
          type: ProgressDialogType.normal, isDismissible: false);
      _progressDialog.update(message: "Adding company...");
      await _progressDialog.show();

      ApiBaseHelper apiBaseHelper = ApiBaseHelper();
      Map<String, dynamic> body = {
        'company_name': companyName,
        'parent_id':
            registrationDetailController.registrationDetail.userId ?? "",
        'address': address,
      };

      print("BODY: $body");

      ApiResponse apiResponse = await apiBaseHelper
          .post(apiBaseHelper.REGISTER_CHILD_COMPANY, body, accessToken: true);

      await _progressDialog.hide();

      if (!context.mounted) return false;

      if (apiResponse.status) {
        showToastMessage(context,
            message: "Company added successfully", duration: 2);
        // Navigator.of(context).pop(); // Close the dialog
        // Close the dialog
        return true;
      } else {
        showAlertDialog(context,
            alertType: AlertType.Error,
            alertTitle: "Error",
            message: apiResponse.msg ?? "Unknown error");
        return false;
      }
    } catch (e) {
      print("Error adding company: $e");
      showAlertDialog(context,
          alertType: AlertType.Error,
          alertTitle: "Error",
          message: "Failed to add company. Please try again.");
      return false;
    }
  }

  Future<void> _doLogin(LoginModel login, BuildContext context) async {
    try {
      ProgressDialog _progressDialog = ProgressDialog(context,
          type: ProgressDialogType.normal, isDismissible: false);
      _progressDialog.update(message: "Please wait....");
      await _progressDialog.show();

      print(context.mounted.toString() + " 1 ");

      ApiBaseHelper apiBaseHelper = ApiBaseHelper();
      print(context.mounted.toString() + " 2 ");

      ApiResponse apiResponse = await apiBaseHelper.post(
          apiBaseHelper.ACTION_AUTH_LOGIN, login.toJson(),
          accessToken: false);
      print(context.mounted.toString() + " 3 ");

      await _progressDialog.hide();

      if (!context.mounted) {
        print("context not mounted 1");
        return;
      }
      ; // <- added

      print(apiResponse.status.toString() + " " + apiResponse.msg.toString());

      if (apiResponse.status) {
        LoginApiResponseModel _loginApiResponse =
            LoginApiResponseModel.fromJson(apiResponse.data);

        if (_loginApiResponse.operation == 'validate-otp') {
          if (!context.mounted) return;
          Navigator.pushReplacementNamed(context, "/otp_verify",
              arguments: OtpVerifyScreenArguments(_loginApiResponse.token!,
                  _loginApiResponse.registeredMobileNo!));

          showToastMessage(context, message: apiResponse.msg!, duration: 4);
        } else {
          bool status = await LoginHelper().login(
            permissions: _loginApiResponse.permissions ?? [],
            accessToken: _loginApiResponse.accessToken!,
            userName: _loginApiResponse.username!,
            fullName: _loginApiResponse.fullName!,
            subdata: {
              'expiry_info': apiResponse.data['expiry_info'],
              'agent_detail': apiResponse.data['agent_detail']
            },
            multiUserFlag: _loginApiResponse.multiUserFlag!,
            isExpired: _loginApiResponse.isAccountExpired!,
          );

          if (!context.mounted) {
            print("context not mounted");
            return;
          }
          ;

          if (status) {
            await refreshGlobalVariables();
            TransactionHelper.refreshPreviousPages();

            if (_loginApiResponse.isFirstLogin == 1) {
              showAlertDialog(
                context,
                alertType: AlertType.Success,
                alertTitle: "Welcome to Mobile खाता",
                barrierDismissible: false,
                onCloseButtonPressed: () {
                  if (!context.mounted) return;
                  Navigator.of(context).pop();
                  Navigator.of(context).pushNamedAndRemoveUntil(
                      '/home', (Route<dynamic> route) => false);
                },
                message: apiResponse.msg ?? "",
              );
            } else {
              // print("login status not 1st");
              print("Permissions eta ca hai" +
                  await SharedPreferences.getInstance().then((prefs) =>
                      prefs.getStringList(Permissions)?.toString() ?? "[]"));

              // Navigator.of(context).pop();
              Navigator.pushNamedAndRemoveUntil(
                  context, "/home", (Route<dynamic> route) => false);

              showToastMessage(context,
                  message: apiResponse.msg ?? "", duration: 2);
            }
          }
        }
      } else {
        showAlertDialog(context,
            alertType: AlertType.Error,
            alertTitle: "Error",
            message: apiResponse.msg ?? "Unknown error");
      }
    } catch (e, trace) {
      print("Error aaio hai: $e\n$trace");
    }
  }
}

Widget getImageWidget(BuildContext context, List<int>? logo, String name,
    {Color? shapeColor, Color? fontColor}) {
  shapeColor = (null == shapeColor) ? colorPrimary : shapeColor;

  double screenWidth = MediaQuery.of(context).size.width;

  debugPrint(logo.toString());

  if (null != logo) {
    return CircleAvatar(
        radius: screenWidth * 0.119,
        backgroundColor: shapeColor,
        child: CircleAvatar(
          backgroundImage: MemoryImage(Uint8List.fromList(logo)),
          radius: screenWidth * 0.114,
        ));
  } else {
    fontColor = (null == fontColor) ? Colors.white : fontColor;
    return CircleAvatar(
      radius: 45,
      backgroundColor: Colors.white,
      child: Text(
        name[0],
        style: TextStyle(
          color: colorPrimary,
          fontSize: 28,
          fontWeight: FontWeight.w900,
        ),
      ),
    );
  }
}
