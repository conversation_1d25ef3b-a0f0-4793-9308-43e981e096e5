class LineItemDetailModel {
  LineItemDetailModel({
    this.txnId,
    this.sno,
    this.itemId,
    this.itemName,
    this.quantity,
    this.pricePerUnit,
    this.totalAmount,
    this.lineItemUnitId,
    this.lineItemUnitName,
    this.lineItemUnitConversionFactor,
    this.discountPercent,
    this.discountAmount,
    this.taxPercent,
    this.taxAmount,
    this.grossAmount,
  });

  String? txnId;
  int? sno;
  String? itemId;
  String? itemName;
  double? quantity;
  double? pricePerUnit;
  double? totalAmount; //Net Amount
  String? lineItemUnitId;
  String? lineItemUnitName;
  double? lineItemUnitConversionFactor;

  double? discountPercent;
  double? discountAmount;
  double? taxPercent;
  double? taxAmount;
  double? grossAmount;

  factory LineItemDetailModel.fromJson(Map<String, dynamic> json) {
    double grossAmount = (json["total_amount"] ?? 0).toDouble() +
        (json["line_item_discount_amount"] ?? 0).toDouble() -
        (json["line_item_tax_amount"] ?? 0).toDouble();

    return LineItemDetailModel(
      txnId: json["txn_id"],
      sno: json["sno"],
      itemId: json["item_id"],
      quantity: (json["quantity"] ?? 0).toDouble(),
      pricePerUnit: (json["price_per_unit"] ?? 0).toDouble(),
      totalAmount: (json["total_amount"] ?? 0).toDouble(),
      lineItemUnitId: json["line_item_unit_id"],
      lineItemUnitConversionFactor:
          (json["line_item_unit_conversion_factor"] ?? 0).toDouble(),
      itemName: json["item_name"],
      lineItemUnitName: json['unit_name'],
      discountPercent: (json['line_item_discount_percent'] ?? 0).toDouble(),
      discountAmount: (json['line_item_discount_amount'] ?? 0).toDouble(),
      taxPercent: (json['line_item_tax_percent'] ?? 0).toDouble(),
      taxAmount: (json['line_item_tax_amount'] ?? 0).toDouble(),
      grossAmount: grossAmount,
    );
  }

  Map<String, dynamic> toJson() => {
        "txn_id": txnId,
        "sno": sno,
        "item_id": itemId,
        "quantity": quantity,
        "price_per_unit": pricePerUnit,
        "total_amount": totalAmount,
        "line_item_unit_id": lineItemUnitId,
        "line_item_unit_conversion_factor": lineItemUnitConversionFactor,
        "item_name": itemName,
        "unit_name": lineItemUnitName,
        "line_item_discount_percent": discountPercent,
        "line_item_discount_amount": discountAmount,
        "line_item_tax_percent": taxPercent,
        "line_item_tax_amount": taxAmount,
      };
}
