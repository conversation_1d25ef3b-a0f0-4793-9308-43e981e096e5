// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/unit_modal.dart';
import 'package:mobile_khaata_v2/app/model/others/item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/item_list/item_unit_list_controller.dart';
import 'package:mobile_khaata_v2/database/item_type.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class ItemUnitListPage extends StatefulWidget {
  const ItemUnitListPage({super.key});

  @override
  _ItemUnitListPageState createState() => _ItemUnitListPageState();
}

class _ItemUnitListPageState extends State<ItemUnitListPage>
    with SingleTickerProviderStateMixin {
  final String tag = "ItemListPage";

  final itemUnitListController =
      Get.put(ItemUnitListController(), tag: "ItemUnitListController");

  bool showSearchBar = false;
  final FocusNode searchBoxFocus = FocusNode();
  String searchBoxPlaceholder = "Search";

  TabController? tabController;

  @override
  void initState() {
    itemUnitListController.initItem();
    itemUnitListController.initUnit();
    tabController = TabController(length: 2, vsync: this)
      ..addListener(tabBarOnChangeHandler);
    super.initState();
  }

  @override
  void dispose() {
    itemUnitListController.onClose();
    tabController!.dispose();
    super.dispose();
  }

  searchBoxOnChangeHandler(String searchString) {
    if (0 == tabController!.index) {
      itemUnitListController.searchItem(searchString);
    } else {
      itemUnitListController.searchUnit(searchString);
    }

    setState(() {});
  }

  searchButtonOnPressedHandler() {
    showSearchBar = !showSearchBar;

    if (showSearchBar) searchBoxFocus.requestFocus();

    if (0 == tabController!.index) {
      searchBoxPlaceholder = "सामान (Item)";
      itemUnitListController.searchItem("");
    } else {
      searchBoxPlaceholder = "एकाइ (Unit)";
      itemUnitListController.searchUnit("");
    }

    setState(() {});
  }

  tabBarOnChangeHandler() {
    if (showSearchBar) showSearchBar = false;

    if (0 == tabController!.previousIndex) {
      itemUnitListController.searchItem("");
    } else {
      itemUnitListController.searchUnit("");
    }

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: false,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        toolbarHeight: 60,
        backgroundColor: colorPrimary,
        elevation: 0,
        leading: BackButton(
          onPressed: () => Navigator.pop(context, false),
        ),
        titleSpacing: -5.0,
        centerTitle: false,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            (showSearchBar)
                ? SizedBox(
                    width: MediaQuery.of(context).size.width * 0.56,
                    height: 50,
                    child: FormBuilderTextField(
                      name: "searchBox",
                      autocorrect: false,
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.done,
                      focusNode: searchBoxFocus,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                      ),
                      decoration: InputDecoration(
                          hintText: searchBoxPlaceholder,
                          contentPadding: EdgeInsets.zero,
                          prefixStyle: formFieldTextStyle,
                          hintStyle: const TextStyle(
                              color: Colors.white60, fontSize: 18),
                          border: InputBorder.none),
                      onChanged: (searchString) =>
                          searchBoxOnChangeHandler(searchString ?? ""),
                    ),
                  )
                : const Text(
                    "सामान/एकाइ सूची  (Item/Unit List)",
                    style: TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontFamily: 'HelveticaRegular',
                        fontWeight: FontWeight.bold),
                  ),
          ],
        ),
        actions: <Widget>[
          IconButton(
            icon: (showSearchBar)
                ? const Icon(
                    Icons.cancel,
                    color: Colors.white,
                  )
                : const Icon(
                    Icons.search,
                    color: Colors.white,
                  ),
            onPressed: () => searchButtonOnPressedHandler(),
          )
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Container(
              height: MediaQuery.of(context).size.height,
              decoration: BoxDecoration(
                color: backgroundColorShade,
              ),
              child: Column(
                children: [
                  Container(
                    height: 50,
                    decoration: BoxDecoration(color: colorPrimary),
                    child: TabBar(
                      controller: tabController,
                      indicatorColor: Colors.white,
                      labelStyle: const TextStyle(
                        fontSize: 14,
                      ),
                      unselectedLabelColor: Colors.white54,
                      indicator: const BoxDecoration(
                          border: Border(
                              bottom:
                                  BorderSide(color: Colors.white, width: 5))),
                      tabs: const [
                        Tab(
                            child: Text(
                          "सामान\n(Products/Items)",
                          textAlign: TextAlign.center,
                        )),
                        Tab(
                            child: Text("एकाइ\n(Units)",
                                textAlign: TextAlign.center)),
                      ],
                    ),
                  ),
                  Expanded(
                      child: TabBarView(
                    controller: tabController,
                    children: [
                      _ItemListView(itemUnitListController),
                      _UnitListView(itemUnitListController),
                    ],
                  )),
                ],
              ),
            ),
          ),
        ],
      ),

      floatingActionButton: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          const SizedBox(
            width: 10,
          ),

          ElevatedButton(
            onPressed: () async {
              final res = await itemUnitListController
                  .addUnitButtonOnPressedHandler(context);

              if (res) {
                Future.delayed(
                  const Duration(milliseconds: 500),
                  () {
                    tabController!.animateTo(1);
                  },
                );
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: colorPrimary,
              foregroundColor: colorPrimaryLightest,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(18.0),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Row(
                children: const [
                  Icon(
                    Icons.add_circle,
                    color: Colors.white,
                    size: 20,
                  ),
                  Text(
                    ' Add Unit',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(
            width: 10,
          ),

          ElevatedButton(
            onPressed: () async {
              final res = await itemUnitListController
                  .addItemButtonOnPressedHandler(context);
              if (res == true) {
                itemUnitListController.initItem();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: colorPrimary,
              foregroundColor: colorPrimaryLightest,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(18.0),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Row(
                children: const [
                  Icon(
                    Icons.add_circle,
                    color: Colors.white,
                    size: 20,
                  ),
                  Text(
                    ' Add Product/Item',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          ),

          // FloatingActionButton.extended(
          //   onPressed: () => itemUnitListController.addUnitButtonOnPressedHandler(context),
          //   backgroundColor: colorPrimary,
          //   elevation: 6,
          //   heroTag: 'addUnit',
          //   icon: Icon(Icons.add_circle),
          //   label: Text('Add Unit'),
          // ),

          // FloatingActionButton.extended(
          //   onPressed: () => itemUnitListController.addItemButtonOnPressedHandler(context),
          //   backgroundColor: colorPrimary,
          //   elevation: 6,
          //   heroTag: 'addItem',
          //   icon: Icon(Icons.add_circle),
          //   label: Text('Add Product/Item'),
          // ),
        ],
      ),
    ));
  }
}

class _UnitListView extends StatelessWidget {
  final ItemUnitListController itemUnitListController;

  const _UnitListView(this.itemUnitListController);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (itemUnitListController.unitLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }
      if (itemUnitListController.filteredUnits.isEmpty) {
        return const SizedBox(
            width: double.infinity,
            child: Center(
                child: Text(
              "No Records",
              style: TextStyle(color: Colors.black54),
            )));
      } else {
        return ListView.builder(
          padding: const EdgeInsets.only(bottom: 50),
          itemCount: itemUnitListController.filteredUnits.length,
          shrinkWrap: true,
          itemBuilder: (context, int index) {
            UnitModel unit = itemUnitListController.filteredUnits[index];

            return Card(
              margin: EdgeInsets.only(
                  left: 5,
                  right: 5,
                  top: (0 == index) ? 15 : 0,
                  bottom: ((itemUnitListController.filteredUnits.length - 1) ==
                          index)
                      ? 20
                      : 10),
              child: ListTile(
                visualDensity:
                    const VisualDensity(horizontal: -4, vertical: -4),
                contentPadding: const EdgeInsets.all(10),
                title: Text(
                  unit.displayTitle,
                  style: TextStyle(color: textColor),
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(
                        Icons.edit,
                        size: 20,
                        color: Colors.black54,
                      ),
                      onPressed: () {
                        itemUnitListController.editUnitButtonOnPressedHandler(
                            context, unit.unitId ?? "");
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.delete,
                        size: 20,
                        color: colorRedDark,
                      ),
                      onPressed: () {
                        debugPrint("Hello uni deletee");
                        itemUnitListController.deleteUnitButtonOnPressedHandler(
                            context, unit.unitId ?? "");
                      },
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }
    });
  }
}

class _ItemListView extends StatelessWidget {
  final ItemUnitListController itemUnitListController;
  const _ItemListView(this.itemUnitListController);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (itemUnitListController.itemLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }
      if (itemUnitListController.filteredItems.isEmpty) {
        return const SizedBox(
            width: double.infinity,
            child: Center(
                child: Text(
              "No Records",
              style: TextStyle(color: Colors.black54),
            )));
      } else {
        return ListView.builder(
          itemCount: itemUnitListController.filteredItems.length,
          shrinkWrap: true,
          itemBuilder: (context, int index) {
            ItemDetailModel item = itemUnitListController.filteredItems[index];

            return GestureDetector(
              onTap: () {
                itemUnitListController.itemListOnTapHandler(
                    context, item.itemId ?? "");
              },
              child: Card(
                margin: EdgeInsets.only(
                    left: 5,
                    right: 5,
                    top: (0 == index) ? 15 : 0,
                    bottom:
                        ((itemUnitListController.filteredItems.length - 1) ==
                                index)
                            ? 20
                            : 10),
                child: Column(
                  children: <Widget>[
                    Container(
                      margin:
                          const EdgeInsets.only(left: 10, right: 10, top: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            item.itemName ?? "",
                            style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: textColor),
                          ),
                          Row(
                            children: [
                              //==============================Edit Button
                              SizedBox(
                                width: 35,
                                height: 35,
                                child: IconButton(
                                  icon: const Icon(
                                    Icons.edit,
                                    size: 20,
                                    color: Colors.black54,
                                  ),
                                  onPressed: () {
                                    itemUnitListController
                                        .editItemButtonOnPressedHandler(
                                            context, item.itemId ?? "");
                                  },
                                ),
                              ),

                              //==============================Adjustment Button
                              if (ItemType.product == item.itemType) ...{
                                SizedBox(
                                  width: 35,
                                  height: 35,
                                  child: RotatedBox(
                                    quarterTurns: 1,
                                    child: IconButton(
                                      icon: const Icon(
                                        Icons.tune,
                                        size: 20,
                                        color: Colors.black54,
                                      ),
                                      onPressed: () {
                                        itemUnitListController
                                            .adjustmentButtonOnPressedHandler(
                                                context, item.itemId ?? "");
                                      },
                                    ),
                                  ),
                                ),
                              }
                            ],
                          ),
                        ],
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            flex: 1,
                            child: Text(
                              "Location: ${item.itemLocation ?? ''}",
                              style:
                                  TextStyle(fontSize: 14, color: colorPrimary),
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: Text(
                              "Type: ${ItemType.itemTypeText[item.itemType]}",
                              textAlign: TextAlign.right,
                              style:
                                  TextStyle(fontSize: 14, color: colorPrimary),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                        margin: const EdgeInsets.only(
                          left: 10,
                          right: 10,
                        ),
                        child: const Divider(
                          thickness: 0.5,
                        )),
                    Container(
                      margin: const EdgeInsets.only(
                        left: 10,
                        right: 10,
                      ),
                      padding: const EdgeInsets.only(top: 5, bottom: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: <Widget>[
                          //===================================== Item type:- Service
                          if (ItemType.service == item.itemType) ...{
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text("Sale Price",
                                        style: TextStyle(
                                            color: Colors.black54,
                                            fontSize: 12)),
                                    Text(
                                        formatCurrencyAmount(item
                                                .itemSaleUnitPrice
                                                ?.toDouble() ??
                                            0),
                                        style: const TextStyle(
                                            color: Colors.black87,
                                            fontSize: 14)),
                                  ],
                                ),
                              ],
                            ),
                          }

                          //===================================== Item type:- Product
                          else ...{
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text("Stock Qty",
                                        style: TextStyle(
                                            color: Colors.black54,
                                            fontSize: 12)),
                                    Text("${item.balanceQuantity ?? 0}",
                                        style: const TextStyle(
                                            color: Colors.black87,
                                            fontSize: 14)),
                                  ],
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    const Text("Stock Value",
                                        style: TextStyle(
                                            color: Colors.black54,
                                            fontSize: 12)),
                                    Text(
                                        formatCurrencyAmount(((item
                                                    .balanceQuantity)!
                                                .toDouble()) *
                                            (item.itemPurchaseUnitPrice ?? 0)),
                                        style: const TextStyle(
                                            color: Colors.black87,
                                            fontSize: 14)),
                                  ],
                                ),
                              ],
                            ),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    const Text("Sale Price",
                                        style: TextStyle(
                                            color: Colors.black54,
                                            fontSize: 12)),
                                    Text(
                                        formatCurrencyAmount(
                                            (item.itemSaleUnitPrice ?? 0)
                                                .toDouble()),
                                        style: const TextStyle(
                                            color: Colors.black87,
                                            fontSize: 14)),
                                  ],
                                ),
                                const SizedBox(
                                  height: 15,
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    const Text("Purchase Price",
                                        style: TextStyle(
                                            color: Colors.black54,
                                            fontSize: 12)),
                                    Text(
                                        formatCurrencyAmount(
                                            (item.itemPurchaseUnitPrice ?? 0)
                                                .toDouble()),
                                        style: const TextStyle(
                                            color: Colors.black87,
                                            fontSize: 14)),
                                  ],
                                ),
                              ],
                            ),
                          }
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      }
    });
  }
}
