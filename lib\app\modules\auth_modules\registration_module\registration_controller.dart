import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/registration_model.dart';

import 'package:mobile_khaata_v2/http/api_base_helper.dart';

class NewRegistrationController extends GetxController {
  final String tag = "NewRegistrationController";

  final _registration = NewRegistrationModel().obs;
  NewRegistrationModel get registration => _registration.value;

  var termsConditionChecked = false.obs;

  Future<ApiResponse> doRegistration(BuildContext context) async {
    ApiResponse apiResponse = ApiResponse();
    try {
      registration.countryCode = '+977';
      if (null == registration.panVatNo ||
          (registration.panVatNo != null && registration.panVatNo!.isEmpty)) {
        registration.panVatFlag = null;
      }

      ApiBaseHelper apiBaseHelper = ApiBaseHelper();
      apiResponse = await apiBaseHelper.post(
          apiBaseHelper.ACTION_REG_REGISTRATION, registration.toJson(),
          accessToken: false);
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return apiResponse;
  }
}
