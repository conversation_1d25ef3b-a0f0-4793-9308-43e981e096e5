import 'package:mobile_khaata_v2/app/model/database/line_item_model.dart';
import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';

class LineItemRepository {
  final String tag = "LineItemRepository";
  final String tableName = 'mk_line_items';
  DatabaseHelper databaseHelper = DatabaseHelper();
  QueryRepository queryRepository = QueryRepository();

  //========================================================================================= SYNCING ACTIONS
  Future<bool> insert(LineItemModel lineItem,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    await dbClient.insert(tableName, lineItem.toJson());
    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.insert,
      data: lineItem.toJson(),
    );

    await queryRepository.pushQuery(newQueryModel,
        dbClient: dbClient, batchID: batchID);
    status = true;

    return status;
  }

  Future<bool> deleteLineItemsForTransaction(String txnID,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    String whereClause = 'txn_id = ?';
    List<dynamic> whereArgs = [txnID];

    await dbClient.delete(tableName, where: whereClause, whereArgs: whereArgs);

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.delete,
      whereArgs: whereArgs,
      whereClause: whereClause,
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    status = true;

    return status;
  }

  //=========================================================================================NON SYNCING ACTIONS

  Future<bool> setLineDetailItemsForTransaction(
      String txnID, List<LineItemDetailModel> items,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    ItemRepository itemRepository = ItemRepository();

    //check item id existance, create if null
    await Future.wait(items.map((lineItem) async {
      lineItem.itemId ??= await itemRepository.createIfNotExistByItemName(
          lineItem.itemName ?? "",
          dbClient: dbClient,
          batchID: batchID);
    }).toList());

    int lineItemSno = 1;
    await Future.wait(items.map((lineItem) async {
      lineItem.sno = lineItemSno;
      lineItemSno += 1;
      lineItem.txnId = txnID;

      await insert(LineItemModel.fromJson(lineItem.toJson()),
          dbClient: dbClient, batchID: batchID);
    }).toList());

    status = true;

    return status;
  }

  Future<List<LineItemDetailModel>> getLineDetailItemsForTransaction(
      String txnID,
      {dynamic dbClient}) async {
    List<LineItemDetailModel> items = [];

    dbClient ??= await databaseHelper.database;

    String query = "SELECT mkl.*, it.item_name AS item_name, "
        "un.unit_short_name  as unit_name "
        "FROM mk_line_items mkl "
        "LEFT JOIN mk_items it ON mkl.item_id = it.item_id "
        "LEFT JOIN mk_item_units un ON mkl.line_item_unit_id = un.unit_id "
        "WHERE mkl.txn_id=? ";

    List<Map<String, dynamic>> lineItetmDettailListJson =
        await dbClient.rawQuery(query, [txnID]);

    items = lineItetmDettailListJson
        .map((itemData) => LineItemDetailModel.fromJson(itemData))
        .toList();

    return items;
  }
}
