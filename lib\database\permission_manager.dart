class PermissionManager {
  static final String dashboardWidget = 'dashboard-widgets';

  static final String salesAdd = "sales-add";
  static final String salesEdit = 'sales-edit';
  static final String salesDelete = 'sales-delete';

  static final String salesReturnAdd = "sales-return-add";
  static final String salesReturnEdit = 'sales-return-edit';
  static final String salesReturnDelete = 'sales-return-delete';

  static final String purchaseAdd = 'purchase-add';
  static final String purchaseEdit = 'purchase-edit';
  static final String purchaseDelete = 'purchase-delete';

  static final String purchaseReturnAdd = 'purchase-return-add';
  static final String purchaseReturnEdit = 'purchase-return-edit';
  static final String purchaseReturnDelete = 'purchase-return-delete';

  static final String expenseAdd = 'expense-add';
  static final String expenseEdit = 'expense-edit';
  static final String expenseDelete = 'expense-delete';

  static final String partyAdd = 'party-add';
  static final String partyEdit = 'party-edit';
  static final String partyDelete = 'party-delete';

  static final String itemAdd = 'item-add';
  static final String itemEdit = 'item-edit';
  static final String itemDelete = 'item-delete';

  static final String itemAdjAdd = 'item-adjustment-add';
  static final String itemAdjEdit = 'item-adjustment-edit';
  static final String itemAdjDelete = 'item-adjustment-delete';

  static final String paymentAdd = 'payment-add';
  static final String paymentEdit = 'payment-edit';
  static final String paymentDelete = 'payment-delete';

  static final String receiptAdd = 'receipt-add';
  static final String receiptEdit = 'receipt-edit';
  static final String receiptDelete = 'receipt-delete';

  static final String expenseCategoryAdd = 'expense-category-add';
  static final String expenseCategoryEdit = 'expense-category-edit';
  static final String expenseCategoryDelete = 'expense-category-delete';

  static final String cashInHandView = 'cash-in-hand-view';
  static final String cashAdjAdd = 'cash-adjustment-add';
  static final String cashAdjEdit = 'cash-adjustment-edit';
  static final String cashAdjDelete = 'cash-adjustment-delete';

  static final String bankListView = 'bank-list-view';
  static final String bankAdd = 'bank-add';
  static final String bankEdit = 'bank-edit';
  static final String bankDelete = 'bank-delete';

  static final String bankDepositAdd = 'bank-deposit-add';
  static final String bankDepositEdit = 'bank-deposit-edit';
  static final String bankDepositDelete = 'bank-deposit-delete';

  static final String bankWithdrawAdd = 'bank-withdraw-add';
  static final String bankWithdrawEdit = 'bank-withdraw-edit';
  static final String bankWithdrawDelete = 'bank-withdraw-delete';

  static final String bankToBankTransferAdd = 'bank-to-bank-transfer-add';
  static final String bankToBankTransferEdit = 'bank-to-bank-transfer-edit';
  static final String bankToBankTransferDelete = 'bank-to-bank-transfer-delete';

  static final String bankAdjustmentAdd = 'bank-adjustment-add';
  static final String bankAdjustmentEdit = 'bank-adjustment-edit';
  static final String bankAdjustmentDelete = 'bank-adjustment-delete';

  static final String chequeListView = 'cheque-list-view';
  static final String chequeDepositWithdraw = 'cheque-deposit-withdraw';
  static final String chequeReopen = 'cheque-reopen';

  static final String reminderAdd = "reminder-add";
  static final String reminderEdit = "reminder-edit";
  static final String reminderDelete = "reminder-delete";

  static final String reportView = 'report-view';

  static final Map<String, String> permissionName = ({
    PermissionManager.dashboardWidget: "Dashboard Widgets",
    PermissionManager.salesAdd: "Add Sales",
    PermissionManager.salesEdit: "Edit Sales",
    PermissionManager.salesDelete: "Delete Sales",
    PermissionManager.salesReturnAdd: "Add Sales Return",
    PermissionManager.salesReturnEdit: "Edit Sales Return",
    PermissionManager.salesReturnDelete: "Delete Sales Return",
    PermissionManager.purchaseAdd: "Add Purchase",
    PermissionManager.purchaseEdit: "Edit Purchase",
    PermissionManager.purchaseDelete: "Delete Purchase",
    PermissionManager.purchaseReturnAdd: "Add Purchase Return",
    PermissionManager.purchaseReturnEdit: "Edit Purchase Return",
    PermissionManager.purchaseReturnDelete: "Delete Purchase Return",
    PermissionManager.expenseAdd: "Add Expense",
    PermissionManager.expenseEdit: "Edit Expense",
    PermissionManager.expenseDelete: "Delete Expense",
    PermissionManager.partyAdd: "Add Party",
    PermissionManager.partyEdit: "Edit Party",
    PermissionManager.partyDelete: "Delete Party",
    PermissionManager.itemAdd: "Add Item",
    PermissionManager.itemEdit: "Edit Item",
    PermissionManager.itemDelete: "Delete Item",
    PermissionManager.itemAdjAdd: "Add Item Adjustment",
    PermissionManager.itemAdjEdit: "Edit Item Adjustment",
    PermissionManager.itemAdjDelete: "Delete Item Adjustment",
    PermissionManager.paymentAdd: "Add Payment",
    PermissionManager.paymentEdit: "Edit Payment",
    PermissionManager.paymentDelete: "Delete Payment",
    PermissionManager.receiptAdd: "Add Receipt",
    PermissionManager.receiptEdit: "Edit Receipt",
    PermissionManager.receiptDelete: "Delete Receipt",
    PermissionManager.expenseCategoryAdd: "Add Expense Category",
    PermissionManager.expenseCategoryEdit: "Edit Expense Category",
    PermissionManager.expenseCategoryDelete: "Delete Expense Category",
    PermissionManager.cashInHandView: "Cash In Hand View",
    PermissionManager.cashAdjAdd: "Add Cash Adjustment",
    PermissionManager.cashAdjEdit: "Edit Cash Adjustment",
    PermissionManager.cashAdjDelete: "Delete Cash Adjustment",
    PermissionManager.bankListView: "Bank List",
    PermissionManager.bankAdd: "Add Bank",
    PermissionManager.bankEdit: "Edit Bank",
    PermissionManager.bankDelete: "Delete Bank",
    PermissionManager.bankDepositAdd: "Add Bank Deposit",
    PermissionManager.bankDepositEdit: "Edit Bank Deposit",
    PermissionManager.bankDepositDelete: "Delete Bank Deposit",
    PermissionManager.bankWithdrawAdd: "Add Bank Withdrawal",
    PermissionManager.bankWithdrawEdit: "Edit Bank Withdrawal",
    PermissionManager.bankWithdrawDelete: "Delete Bank Withdrawal",
    PermissionManager.bankToBankTransferAdd: "Add Bank To Bank Transfer",
    PermissionManager.bankToBankTransferEdit: "Edit Bank To Bank Transfer",
    PermissionManager.bankToBankTransferDelete: "Delete Bank To Bank Transfer",
    PermissionManager.bankAdjustmentAdd: "Add Bank Adjustment",
    PermissionManager.bankAdjustmentEdit: "Edit Bank Adjustment",
    PermissionManager.bankAdjustmentDelete: "Delete Bank Adjustment",
    PermissionManager.chequeListView: "Cheque List",
    PermissionManager.chequeDepositWithdraw: "Cheque Withdraw",
    PermissionManager.chequeReopen: "Cheque Re-open",
    PermissionManager.reportView: "View Reports",
    PermissionManager.reminderAdd: "Add Reminder",
    PermissionManager.reminderEdit: "Edit Reminder",
    PermissionManager.reminderDelete: "Delete Reminder"
  });

  static final Map<String, String> permissionShortName = ({
    PermissionManager.dashboardWidget: "Dashboard Widgets",
    PermissionManager.salesAdd: "Add",
    PermissionManager.salesEdit: "Edit",
    PermissionManager.salesDelete: "Delete",
    PermissionManager.salesReturnAdd: "Add",
    PermissionManager.salesReturnEdit: "Edit",
    PermissionManager.salesReturnDelete: "Delete",
    PermissionManager.purchaseAdd: "Add",
    PermissionManager.purchaseEdit: "Edit",
    PermissionManager.purchaseDelete: "Delete",
    PermissionManager.purchaseReturnAdd: "Add",
    PermissionManager.purchaseReturnEdit: "Edit",
    PermissionManager.purchaseReturnDelete: "Delete",
    PermissionManager.expenseAdd: "Add",
    PermissionManager.expenseEdit: "Edit",
    PermissionManager.expenseDelete: "Delete",
    PermissionManager.partyAdd: "Add",
    PermissionManager.partyEdit: "Edit",
    PermissionManager.partyDelete: "Delete",
    PermissionManager.itemAdd: "Add",
    PermissionManager.itemEdit: "Edit",
    PermissionManager.itemDelete: "Delete",
    PermissionManager.itemAdjAdd: "Add",
    PermissionManager.itemAdjEdit: "Edit",
    PermissionManager.itemAdjDelete: "Delete",
    PermissionManager.paymentAdd: "Add",
    PermissionManager.paymentEdit: "Edit",
    PermissionManager.paymentDelete: "Delete",
    PermissionManager.receiptAdd: "Add",
    PermissionManager.receiptEdit: "Edit",
    PermissionManager.receiptDelete: "Delete",
    PermissionManager.expenseCategoryAdd: "Add",
    PermissionManager.expenseCategoryEdit: "Edit",
    PermissionManager.expenseCategoryDelete: "Delete",
    PermissionManager.cashInHandView: "View",
    PermissionManager.cashAdjAdd: "Add",
    PermissionManager.cashAdjEdit: "Edit",
    PermissionManager.cashAdjDelete: "Delete",
    PermissionManager.bankListView: "List",
    PermissionManager.bankAdd: "Add",
    PermissionManager.bankEdit: "Edit",
    PermissionManager.bankDelete: "Delete",
    PermissionManager.bankDepositAdd: "Add",
    PermissionManager.bankDepositEdit: "Edit",
    PermissionManager.bankDepositDelete: "Delete",
    PermissionManager.bankWithdrawAdd: "Add",
    PermissionManager.bankWithdrawEdit: "Edit",
    PermissionManager.bankWithdrawDelete: "Delete",
    PermissionManager.bankToBankTransferAdd: "Add",
    PermissionManager.bankToBankTransferEdit: "Edit",
    PermissionManager.bankToBankTransferDelete: "Delete",
    PermissionManager.bankAdjustmentAdd: "Add",
    PermissionManager.bankAdjustmentEdit: "Edit",
    PermissionManager.bankAdjustmentDelete: "Delete",
    PermissionManager.chequeListView: "List",
    PermissionManager.chequeDepositWithdraw: "Deposit/Withdraw",
    PermissionManager.chequeReopen: "Re-open",
    PermissionManager.reportView: "View",
    PermissionManager.reminderAdd: "Add",
    PermissionManager.reminderEdit: "Edit",
    PermissionManager.reminderDelete: "Delete",
  });

  static final Map<int, String> permissionGroupName = ({
    1: "Dashboard",
    2: "Sales",
    12: "Sales Return",
    3: "Purchase",
    13: "Purchase Return",
    4: "Expense",
    5: "Party/Ledger",
    21: "Reminder",
    6: "Item",
    20: "Item Adjustment",
    7: "Payment",
    8: "Receipt",
    9: "Expense Category",
    10: "Cash In Hand [Adjustment]",
    14: "Bank",
    15: "Bank Deposit",
    16: "Bank Withdrawal",
    17: "Bank To Bank Transfer",
    18: "Bank Balance Adjustment",
    19: "Cheques",
    11: "Reports",
  });

  static List permissionsForGroup(int groupID) {
    List<dynamic> result = [];
    result = permissionTypeList
        .where((element) => (groupID == element['group_id']))
        .toList();
    return result;
  }

  static List<String> permissionValuesForGroup(int groupID) {
    List<dynamic> result = permissionsForGroup(groupID);
    List<String> _values = [];
    _values = result.map((e) => e['value'].toString()).toList();
    // Log.d("result ${result}");
    // Log.d(_values);
    return _values;
  }

  static List<String> getAllPermissionValues() {
    List<String> result = [];
    result = permissionTypeList.map((e) => e['value'].toString()).toList();
    return result;
  }

  static final List<dynamic> permissionTypeListOLD = [
    {
      "value": PermissionManager.dashboardWidget,
      "text":
          PermissionManager.permissionName[PermissionManager.dashboardWidget],
      "group_id": 1
    },
    {
      "value": PermissionManager.salesAdd,
      "text": PermissionManager.permissionName[PermissionManager.salesAdd],
      "group_id": 2
    },
    {
      "value": PermissionManager.salesEdit,
      "text": PermissionManager.permissionName[PermissionManager.salesEdit],
      "group_id": 2
    },
    {
      "value": PermissionManager.salesDelete,
      "text": PermissionManager.permissionName[PermissionManager.salesDelete],
      "group_id": 2
    },
    {
      "value": PermissionManager.salesReturnAdd,
      "text":
          PermissionManager.permissionName[PermissionManager.salesReturnAdd],
      "group_id": 12
    },
    {
      "value": PermissionManager.salesReturnEdit,
      "text":
          PermissionManager.permissionName[PermissionManager.salesReturnEdit],
      "group_id": 12
    },
    {
      "value": PermissionManager.salesReturnDelete,
      "text":
          PermissionManager.permissionName[PermissionManager.salesReturnDelete],
      "group_id": 12
    },
    {
      "value": PermissionManager.purchaseAdd,
      "text": PermissionManager.permissionName[PermissionManager.purchaseAdd],
      "group_id": 3
    },
    {
      "value": PermissionManager.purchaseEdit,
      "text": PermissionManager.permissionName[PermissionManager.purchaseEdit],
      "group_id": 3
    },
    {
      "value": PermissionManager.purchaseDelete,
      "text":
          PermissionManager.permissionName[PermissionManager.purchaseDelete],
      "group_id": 3
    },
    {
      "value": PermissionManager.purchaseReturnAdd,
      "text":
          PermissionManager.permissionName[PermissionManager.purchaseReturnAdd],
      "group_id": 13
    },
    {
      "value": PermissionManager.purchaseReturnEdit,
      "text": PermissionManager
          .permissionName[PermissionManager.purchaseReturnEdit],
      "group_id": 13
    },
    {
      "value": PermissionManager.purchaseReturnDelete,
      "text": PermissionManager
          .permissionName[PermissionManager.purchaseReturnDelete],
      "group_id": 13
    },
    {
      "value": PermissionManager.expenseAdd,
      "text": PermissionManager.permissionName[PermissionManager.expenseAdd],
      "group_id": 4
    },
    {
      "value": PermissionManager.expenseEdit,
      "text": PermissionManager.permissionName[PermissionManager.expenseEdit],
      "group_id": 4
    },
    {
      "value": PermissionManager.expenseDelete,
      "text": PermissionManager.permissionName[PermissionManager.expenseDelete],
      "group_id": 4
    },
    {
      "value": PermissionManager.partyAdd,
      "text": PermissionManager.permissionName[PermissionManager.partyAdd],
      "group_id": 5
    },
    {
      "value": PermissionManager.partyEdit,
      "text": PermissionManager.permissionName[PermissionManager.partyEdit],
      "group_id": 5
    },
    {
      "value": PermissionManager.partyDelete,
      "text": PermissionManager.permissionName[PermissionManager.partyDelete],
      "group_id": 5
    },
    {
      "value": PermissionManager.itemAdd,
      "text": PermissionManager.permissionName[PermissionManager.itemAdd],
      "group_id": 6
    },
    {
      "value": PermissionManager.itemEdit,
      "text": PermissionManager.permissionName[PermissionManager.itemEdit],
      "group_id": 6
    },
    {
      "value": PermissionManager.itemDelete,
      "text": PermissionManager.permissionName[PermissionManager.itemDelete],
      "group_id": 6
    },
    {
      "value": PermissionManager.itemAdjAdd,
      "text": PermissionManager.permissionName[PermissionManager.itemAdjAdd],
      "group_id": 20
    },
    {
      "value": PermissionManager.itemAdjEdit,
      "text": PermissionManager.permissionName[PermissionManager.itemAdjEdit],
      "group_id": 20
    },
    {
      "value": PermissionManager.itemAdjDelete,
      "text": PermissionManager.permissionName[PermissionManager.itemAdjDelete],
      "group_id": 20
    },
    {
      "value": PermissionManager.paymentAdd,
      "text": PermissionManager.permissionName[PermissionManager.paymentAdd],
      "group_id": 7
    },
    {
      "value": PermissionManager.paymentEdit,
      "text": PermissionManager.permissionName[PermissionManager.paymentEdit],
      "group_id": 7
    },
    {
      "value": PermissionManager.paymentDelete,
      "text": PermissionManager.permissionName[PermissionManager.paymentDelete],
      "group_id": 7
    },
    {
      "value": PermissionManager.receiptAdd,
      "text": PermissionManager.permissionName[PermissionManager.receiptAdd],
      "group_id": 8
    },
    {
      "value": PermissionManager.receiptEdit,
      "text": PermissionManager.permissionName[PermissionManager.receiptEdit],
      "group_id": 8
    },
    {
      "value": PermissionManager.receiptDelete,
      "text": PermissionManager.permissionName[PermissionManager.receiptDelete],
      "group_id": 8
    },
    {
      "value": PermissionManager.expenseCategoryAdd,
      "text": PermissionManager
          .permissionName[PermissionManager.expenseCategoryAdd],
      "group_id": 9
    },
    {
      "value": PermissionManager.expenseCategoryEdit,
      "text": PermissionManager
          .permissionName[PermissionManager.expenseCategoryEdit],
      "group_id": 9
    },
    {
      "value": PermissionManager.expenseCategoryDelete,
      "text": PermissionManager
          .permissionName[PermissionManager.expenseCategoryDelete],
      "group_id": 9
    },
    {
      "value": PermissionManager.cashInHandView,
      "text":
          PermissionManager.permissionName[PermissionManager.cashInHandView],
      "group_id": 10
    },
    {
      "value": PermissionManager.cashAdjAdd,
      "text": PermissionManager.permissionName[PermissionManager.cashAdjAdd],
      "group_id": 10
    },
    {
      "value": PermissionManager.cashAdjEdit,
      "text": PermissionManager.permissionName[PermissionManager.cashAdjEdit],
      "group_id": 10
    },
    {
      "value": PermissionManager.cashAdjDelete,
      "text": PermissionManager.permissionName[PermissionManager.cashAdjDelete],
      "group_id": 10
    },
    {
      "value": PermissionManager.reportView,
      "text": PermissionManager.permissionName[PermissionManager.reportView],
      "group_id": 11
    },
    {
      "value": PermissionManager.bankListView,
      "text": PermissionManager.permissionName[PermissionManager.bankListView],
      "group_id": 14
    },
    {
      "value": PermissionManager.bankAdd,
      "text": PermissionManager.permissionName[PermissionManager.bankAdd],
      "group_id": 14
    },
    {
      "value": PermissionManager.bankEdit,
      "text": PermissionManager.permissionName[PermissionManager.bankEdit],
      "group_id": 14
    },
    {
      "value": PermissionManager.bankDelete,
      "text": PermissionManager.permissionName[PermissionManager.bankDelete],
      "group_id": 14
    },
    {
      "value": PermissionManager.bankDepositAdd,
      "text":
          PermissionManager.permissionName[PermissionManager.bankDepositAdd],
      "group_id": 15
    },
    {
      "value": PermissionManager.bankDepositEdit,
      "text":
          PermissionManager.permissionName[PermissionManager.bankDepositEdit],
      "group_id": 15
    },
    {
      "value": PermissionManager.bankDepositDelete,
      "text":
          PermissionManager.permissionName[PermissionManager.bankDepositDelete],
      "group_id": 15
    },
    {
      "value": PermissionManager.bankWithdrawAdd,
      "text":
          PermissionManager.permissionName[PermissionManager.bankWithdrawAdd],
      "group_id": 16
    },
    {
      "value": PermissionManager.bankWithdrawEdit,
      "text":
          PermissionManager.permissionName[PermissionManager.bankWithdrawEdit],
      "group_id": 16
    },
    {
      "value": PermissionManager.bankWithdrawDelete,
      "text": PermissionManager
          .permissionName[PermissionManager.bankWithdrawDelete],
      "group_id": 16
    },
    {
      "value": PermissionManager.bankToBankTransferAdd,
      "text": PermissionManager
          .permissionName[PermissionManager.bankToBankTransferAdd],
      "group_id": 17
    },
    {
      "value": PermissionManager.bankToBankTransferEdit,
      "text": PermissionManager
          .permissionName[PermissionManager.bankToBankTransferEdit],
      "group_id": 17
    },
    {
      "value": PermissionManager.bankToBankTransferDelete,
      "text": PermissionManager
          .permissionName[PermissionManager.bankToBankTransferDelete],
      "group_id": 17
    },
    {
      "value": PermissionManager.bankAdjustmentAdd,
      "text":
          PermissionManager.permissionName[PermissionManager.bankAdjustmentAdd],
      "group_id": 18
    },
    {
      "value": PermissionManager.bankAdjustmentEdit,
      "text": PermissionManager
          .permissionName[PermissionManager.bankAdjustmentEdit],
      "group_id": 18
    },
    {
      "value": PermissionManager.bankAdjustmentDelete,
      "text": PermissionManager
          .permissionName[PermissionManager.bankAdjustmentDelete],
      "group_id": 18
    },
    {
      "value": PermissionManager.chequeListView,
      "text":
          PermissionManager.permissionName[PermissionManager.chequeListView],
      "group_id": 19
    },
    {
      "value": PermissionManager.chequeDepositWithdraw,
      "text": PermissionManager
          .permissionName[PermissionManager.chequeDepositWithdraw],
      "group_id": 19
    },
    {
      "value": PermissionManager.chequeReopen,
      "text": PermissionManager.permissionName[PermissionManager.chequeReopen],
      "group_id": 19
    },
  ];

  static final List<dynamic> permissionTypeList = [
    {
      "value": PermissionManager.dashboardWidget,
      "text": PermissionManager
          .permissionShortName[PermissionManager.dashboardWidget],
      "group_id": 1
    },
    {
      "value": PermissionManager.salesAdd,
      "text": PermissionManager.permissionShortName[PermissionManager.salesAdd],
      "group_id": 2
    },
    {
      "value": PermissionManager.salesEdit,
      "text":
          PermissionManager.permissionShortName[PermissionManager.salesEdit],
      "group_id": 2
    },
    {
      "value": PermissionManager.salesDelete,
      "text":
          PermissionManager.permissionShortName[PermissionManager.salesDelete],
      "group_id": 2
    },
    {
      "value": PermissionManager.salesReturnAdd,
      "text": PermissionManager
          .permissionShortName[PermissionManager.salesReturnAdd],
      "group_id": 12
    },
    {
      "value": PermissionManager.salesReturnEdit,
      "text": PermissionManager
          .permissionShortName[PermissionManager.salesReturnEdit],
      "group_id": 12
    },
    {
      "value": PermissionManager.salesReturnDelete,
      "text": PermissionManager
          .permissionShortName[PermissionManager.salesReturnDelete],
      "group_id": 12
    },
    {
      "value": PermissionManager.purchaseAdd,
      "text":
          PermissionManager.permissionShortName[PermissionManager.purchaseAdd],
      "group_id": 3
    },
    {
      "value": PermissionManager.purchaseEdit,
      "text":
          PermissionManager.permissionShortName[PermissionManager.purchaseEdit],
      "group_id": 3
    },
    {
      "value": PermissionManager.purchaseDelete,
      "text": PermissionManager
          .permissionShortName[PermissionManager.purchaseDelete],
      "group_id": 3
    },
    {
      "value": PermissionManager.purchaseReturnAdd,
      "text": PermissionManager
          .permissionShortName[PermissionManager.purchaseReturnAdd],
      "group_id": 13
    },
    {
      "value": PermissionManager.purchaseReturnEdit,
      "text": PermissionManager
          .permissionShortName[PermissionManager.purchaseReturnEdit],
      "group_id": 13
    },
    {
      "value": PermissionManager.purchaseReturnDelete,
      "text": PermissionManager
          .permissionShortName[PermissionManager.purchaseReturnDelete],
      "group_id": 13
    },
    {
      "value": PermissionManager.expenseAdd,
      "text":
          PermissionManager.permissionShortName[PermissionManager.expenseAdd],
      "group_id": 4
    },
    {
      "value": PermissionManager.expenseEdit,
      "text":
          PermissionManager.permissionShortName[PermissionManager.expenseEdit],
      "group_id": 4
    },
    {
      "value": PermissionManager.expenseDelete,
      "text": PermissionManager
          .permissionShortName[PermissionManager.expenseDelete],
      "group_id": 4
    },
    {
      "value": PermissionManager.partyAdd,
      "text": PermissionManager.permissionShortName[PermissionManager.partyAdd],
      "group_id": 5
    },
    {
      "value": PermissionManager.partyEdit,
      "text":
          PermissionManager.permissionShortName[PermissionManager.partyEdit],
      "group_id": 5
    },
    {
      "value": PermissionManager.partyDelete,
      "text":
          PermissionManager.permissionShortName[PermissionManager.partyDelete],
      "group_id": 5
    },
    {
      "value": PermissionManager.itemAdd,
      "text": PermissionManager.permissionShortName[PermissionManager.itemAdd],
      "group_id": 6
    },
    {
      "value": PermissionManager.itemEdit,
      "text": PermissionManager.permissionShortName[PermissionManager.itemEdit],
      "group_id": 6
    },
    {
      "value": PermissionManager.itemDelete,
      "text":
          PermissionManager.permissionShortName[PermissionManager.itemDelete],
      "group_id": 6
    },
    {
      "value": PermissionManager.itemAdjAdd,
      "text":
          PermissionManager.permissionShortName[PermissionManager.itemAdjAdd],
      "group_id": 20
    },
    {
      "value": PermissionManager.itemAdjEdit,
      "text":
          PermissionManager.permissionShortName[PermissionManager.itemAdjEdit],
      "group_id": 20
    },
    {
      "value": PermissionManager.itemAdjDelete,
      "text": PermissionManager
          .permissionShortName[PermissionManager.itemAdjDelete],
      "group_id": 20
    },
    {
      "value": PermissionManager.paymentAdd,
      "text":
          PermissionManager.permissionShortName[PermissionManager.paymentAdd],
      "group_id": 7
    },
    {
      "value": PermissionManager.paymentEdit,
      "text":
          PermissionManager.permissionShortName[PermissionManager.paymentEdit],
      "group_id": 7
    },
    {
      "value": PermissionManager.paymentDelete,
      "text": PermissionManager
          .permissionShortName[PermissionManager.paymentDelete],
      "group_id": 7
    },
    {
      "value": PermissionManager.receiptAdd,
      "text":
          PermissionManager.permissionShortName[PermissionManager.receiptAdd],
      "group_id": 8
    },
    {
      "value": PermissionManager.receiptEdit,
      "text":
          PermissionManager.permissionShortName[PermissionManager.receiptEdit],
      "group_id": 8
    },
    {
      "value": PermissionManager.receiptDelete,
      "text": PermissionManager
          .permissionShortName[PermissionManager.receiptDelete],
      "group_id": 8
    },
    {
      "value": PermissionManager.expenseCategoryAdd,
      "text": PermissionManager
          .permissionShortName[PermissionManager.expenseCategoryAdd],
      "group_id": 9
    },
    {
      "value": PermissionManager.expenseCategoryEdit,
      "text": PermissionManager
          .permissionShortName[PermissionManager.expenseCategoryEdit],
      "group_id": 9
    },
    {
      "value": PermissionManager.expenseCategoryDelete,
      "text": PermissionManager
          .permissionShortName[PermissionManager.expenseCategoryDelete],
      "group_id": 9
    },
    {
      "value": PermissionManager.cashInHandView,
      "text": PermissionManager
          .permissionShortName[PermissionManager.cashInHandView],
      "group_id": 10
    },
    {
      "value": PermissionManager.cashAdjAdd,
      "text":
          PermissionManager.permissionShortName[PermissionManager.cashAdjAdd],
      "group_id": 10
    },
    {
      "value": PermissionManager.cashAdjEdit,
      "text":
          PermissionManager.permissionShortName[PermissionManager.cashAdjEdit],
      "group_id": 10
    },
    {
      "value": PermissionManager.cashAdjDelete,
      "text": PermissionManager
          .permissionShortName[PermissionManager.cashAdjDelete],
      "group_id": 10
    },
    {
      "value": PermissionManager.reportView,
      "text":
          PermissionManager.permissionShortName[PermissionManager.reportView],
      "group_id": 11
    },
    {
      "value": PermissionManager.bankListView,
      "text":
          PermissionManager.permissionShortName[PermissionManager.bankListView],
      "group_id": 14
    },
    {
      "value": PermissionManager.bankAdd,
      "text": PermissionManager.permissionShortName[PermissionManager.bankAdd],
      "group_id": 14
    },
    {
      "value": PermissionManager.bankEdit,
      "text": PermissionManager.permissionShortName[PermissionManager.bankEdit],
      "group_id": 14
    },
    {
      "value": PermissionManager.bankDelete,
      "text":
          PermissionManager.permissionShortName[PermissionManager.bankDelete],
      "group_id": 14
    },
    {
      "value": PermissionManager.bankDepositAdd,
      "text": PermissionManager
          .permissionShortName[PermissionManager.bankDepositAdd],
      "group_id": 15
    },
    {
      "value": PermissionManager.bankDepositEdit,
      "text": PermissionManager
          .permissionShortName[PermissionManager.bankDepositEdit],
      "group_id": 15
    },
    {
      "value": PermissionManager.bankDepositDelete,
      "text": PermissionManager
          .permissionShortName[PermissionManager.bankDepositDelete],
      "group_id": 15
    },
    {
      "value": PermissionManager.bankWithdrawAdd,
      "text": PermissionManager
          .permissionShortName[PermissionManager.bankWithdrawAdd],
      "group_id": 16
    },
    {
      "value": PermissionManager.bankWithdrawEdit,
      "text": PermissionManager
          .permissionShortName[PermissionManager.bankWithdrawEdit],
      "group_id": 16
    },
    {
      "value": PermissionManager.bankWithdrawDelete,
      "text": PermissionManager
          .permissionShortName[PermissionManager.bankWithdrawDelete],
      "group_id": 16
    },
    {
      "value": PermissionManager.bankToBankTransferAdd,
      "text": PermissionManager
          .permissionShortName[PermissionManager.bankToBankTransferAdd],
      "group_id": 17
    },
    {
      "value": PermissionManager.bankToBankTransferEdit,
      "text": PermissionManager
          .permissionShortName[PermissionManager.bankToBankTransferEdit],
      "group_id": 17
    },
    {
      "value": PermissionManager.bankToBankTransferDelete,
      "text": PermissionManager
          .permissionShortName[PermissionManager.bankToBankTransferDelete],
      "group_id": 17
    },
    {
      "value": PermissionManager.bankAdjustmentAdd,
      "text": PermissionManager
          .permissionShortName[PermissionManager.bankAdjustmentAdd],
      "group_id": 18
    },
    {
      "value": PermissionManager.bankAdjustmentEdit,
      "text": PermissionManager
          .permissionShortName[PermissionManager.bankAdjustmentEdit],
      "group_id": 18
    },
    {
      "value": PermissionManager.bankAdjustmentDelete,
      "text": PermissionManager
          .permissionShortName[PermissionManager.bankAdjustmentDelete],
      "group_id": 18
    },
    {
      "value": PermissionManager.chequeListView,
      "text": PermissionManager
          .permissionShortName[PermissionManager.chequeListView],
      "group_id": 19
    },
    {
      "value": PermissionManager.chequeDepositWithdraw,
      "text": PermissionManager
          .permissionShortName[PermissionManager.chequeDepositWithdraw],
      "group_id": 19
    },
    {
      "value": PermissionManager.chequeReopen,
      "text":
          PermissionManager.permissionShortName[PermissionManager.chequeReopen],
      "group_id": 19
    },
    {
      "value": PermissionManager.reminderAdd,
      "text":
          PermissionManager.permissionShortName[PermissionManager.reminderAdd],
      "group_id": 21
    },
    {
      "value": PermissionManager.reminderEdit,
      "text":
          PermissionManager.permissionShortName[PermissionManager.reminderEdit],
      "group_id": 21
    },
    {
      "value": PermissionManager.reminderDelete,
      "text": PermissionManager
          .permissionShortName[PermissionManager.reminderDelete],
      "group_id": 21
    },
  ];
}
