import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/unit_list_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/item_modal.dart';
import 'package:mobile_khaata_v2/app/model/database/unit_modal.dart';
import 'package:mobile_khaata_v2/app/model/others/billed_item_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class AddEditPurchaseReturnBillItemController extends GetxController {
  final String tag = "AddEditPurchaseReturnBillItemController";
  UnitListController unitListController = Get.find();

  ItemRepository itemRepository = new ItemRepository();

  var _billedItem = LineItemDetailModel().obs;
  var _editFlag = false.obs;

  LineItemDetailModel get billedItem => _billedItem.value;

  bool get editFlag => _editFlag.value;
  final formKey = GlobalKey<FormState>();

  TextEditingController itemNameCtrl = TextEditingController();
  var selectedItem = new ItemModel().obs;
  TextEditingController qtyCtrl = TextEditingController();
  TextEditingController rateCtrl = TextEditingController();
  TextEditingController grossAmountCtrl = TextEditingController();
  TextEditingController discountPercentageCtrl = TextEditingController();
  TextEditingController discountAmountCtrl = TextEditingController();
  TextEditingController netAmountCtrl = TextEditingController();
  BilledItemUnit selectedUnit = new BilledItemUnit();
  var unitList = <BilledItemUnit>[];

  @override
  void onInit() async {
    super.onInit();
  }

  @override
  void dispose() {
    itemNameCtrl.dispose();
    qtyCtrl.dispose();
    rateCtrl.dispose();
    grossAmountCtrl.dispose();
    discountPercentageCtrl.dispose();
    discountAmountCtrl.dispose();
    netAmountCtrl.dispose();
    super.dispose();
  }

  init() async {}

  initEdit(LineItemDetailModel lineItem) async {
    _editFlag.value = true;

    ItemModel _item;
    if (null != lineItem.itemId) {
      _item = await itemRepository.getItemById(lineItem.itemId ?? "");
    } else {
      _item = ItemModel(itemName: lineItem.itemName);
    }

    itemOnSelectHandler(lineItem.itemId,
        item: _item, unitID: lineItem.lineItemUnitId ?? "");

    if (null != lineItem.lineItemUnitId)
      unitOnSelectHandler(lineItem.lineItemUnitId);

    qtyCtrl.text = lineItem.quantity.toString();
    rateCtrl.text = lineItem.pricePerUnit.toString();
    grossAmountCtrl.text = lineItem.grossAmount.toString();
    discountPercentageCtrl.text = lineItem.discountPercent.toString();
    discountAmountCtrl.text = lineItem.discountAmount.toString();
    netAmountCtrl.text = lineItem.totalAmount.toString();

    _billedItem.value = lineItem;
  }

  itemOnClearHandler() {
    itemNameCtrl.text = "";
  }

  itemOnSelectHandler(itemID, {String? unitID, required ItemModel item}) {
    itemNameCtrl.text = item.itemName ?? "";

    billedItem.itemName = item.itemName;

    selectedItem.value = item;
    selectedItem.refresh();

    unitList.clear();

    if (null != unitID) {
      UnitModel baseUnit =
          unitListController.units.firstWhere((unit) => unit.unitId == unitID);
      if (null != baseUnit)
        unitList.add(new BilledItemUnit(
            unitId: baseUnit.unitId,
            unitName: baseUnit.unitName,
            unitShortName: baseUnit.unitShortName,
            conversionFactor: 0.00,
            unitType: "base"));

      unitOnSelectHandler(unitID);
    } else {
      unitList = unitListController.units.map((e) {
        return BilledItemUnit(
          unitId: e.unitId,
          unitName: e.unitName,
          unitShortName: e.unitShortName,
          conversionFactor: 0.0,
          unitType: "base",
        );
      }).toList();
      debugPrint(unitListController.units.toString());
      debugPrint(unitList.toString());
      unitOnSelectHandler(unitListController.units.first.unitId!);
    }

    if (null != item.alternateUnitId) {
      UnitModel altUnit = unitListController.units
          .firstWhere((unit) => unit.unitId == item.alternateUnitId);
      if (null != altUnit)
        unitList.add(new BilledItemUnit(
            unitId: altUnit.unitId,
            unitName: altUnit.unitName,
            unitShortName: altUnit.unitShortName,
            conversionFactor: item.unitConversionFactor,
            unitType: "alt"));
    }
  }

  unitOnSelectHandler(value) {
    selectedUnit = unitList.firstWhere((element) => element.unitId == value,
        orElse: () => BilledItemUnit());

    _billedItem.value.lineItemUnitId = value;
    _billedItem.value.lineItemUnitName = selectedUnit.unitShortName;
    _billedItem.value.lineItemUnitConversionFactor =
        selectedUnit.conversionFactor;

    print("this is val ${selectedItem.value.itemPurchaseUnitPrice}");

    _billedItem.refresh();
    if ("alt" == selectedUnit.unitType)
      rateCtrl.text = ((selectedItem.value.itemPurchaseUnitPrice ?? 0.00) > 0)
          ? selectedItem.value.unitConversionFactor!.toStringAsFixed(2)
          : "0.00";
    else
      rateCtrl.text = ((selectedItem.value.itemPurchaseUnitPrice ?? 0.00) > 0)
          ? selectedItem.value.itemPurchaseUnitPrice!.toStringAsFixed(2)
          : "0.00";

    rateOnChangeHandler(rateCtrl.text);
  }

  qtyOnChangeHandler(value) {
    billedItem.quantity = (parseDouble(value) ?? 0.00);
    _billedItem.refresh();
    recalculateDataForItem(editorTAG: "qty");
  }

  rateOnChangeHandler(value) {
    billedItem.pricePerUnit = parseDouble(value);
    recalculateDataForItem(editorTAG: "rate");
  }

  amountOnChangeHandler(value) {
    billedItem.grossAmount = (parseDouble(value) ?? 0.00);
    recalculateDataForItem(editorTAG: "amount");
  }

  discountPercentOnChangeHandler(value) {
    debugPrint("===val===");
    debugPrint(value);
    billedItem.discountPercent = parseDouble(value);
    recalculateDataForItem(editorTAG: "discount-percent");
  }

  discountAmountOnChangeHandler(val) {
    billedItem.discountAmount = (parseDouble(val) ?? 0.00);
    final percent =
        (billedItem.discountAmount! / billedItem.grossAmount!) * 100;
    print("This is percent ${percent}");

    billedItem.discountPercent = percent;
    _billedItem.refresh();
    if (percent.toString() == "NaN") {
      print("This is percent 1212 ${percent}");

      discountPercentageCtrl.text = 0.00.toStringAsFixed(2);
    } else {
      print("This is percent 12121212 ${percent}");

      discountPercentageCtrl.text = percent.toStringAsFixed(2);
    }
  }

  recalculateDataForItem({String? editorTAG}) {
    try {
      if (editorTAG == "rate" || editorTAG == "qty") {
        billedItem.grossAmount =
            (billedItem.quantity ?? 0.00) * (billedItem.pricePerUnit ?? 0.00);
        grossAmountCtrl.text =
            (billedItem.grossAmount ?? 0.00).toStringAsFixed(2);
      }

      if (editorTAG == "amount") {
        if (billedItem.quantity != null && billedItem.quantity! > 0) {
          billedItem.pricePerUnit =
              (billedItem.grossAmount ?? 0.00) / (billedItem.quantity ?? 0.00);
          rateCtrl.text = (billedItem.pricePerUnit ?? 0.00).toStringAsFixed(2);
        }
      }

      billedItem.discountAmount = (billedItem.grossAmount! *
          (billedItem.discountPercent ?? 0.00) /
          100);
      discountAmountCtrl.text =
          (billedItem.discountAmount ?? 0.0).toStringAsFixed(2);

      billedItem.totalAmount =
          billedItem.grossAmount! - billedItem.discountAmount!;
      netAmountCtrl.text = (billedItem.totalAmount ?? 0.0).toStringAsFixed(2);
      if (editFlag == 'discount_amt') {
        print("hami ya xum");
      }

      // Log.d("billedItem ${billedItem.toJson()}");
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
  }
}
