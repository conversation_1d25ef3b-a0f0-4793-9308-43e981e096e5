import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:mobile_khaata_v2/app/model/others/expanse_model.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_module/expense_list/expense_list_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';

class ExpenseListSingleCategory extends StatelessWidget {
  final ExpensesListController? expensesListController;
  final String? categoryID;
  final String? categoryName;
  final double? categoryTotal;
  ExpenseListSingleCategory(
      {super.key,
      this.categoryID,
      this.categoryName,
      this.categoryTotal,
      this.expensesListController}) {
    expensesListController?.getExpensesForSingleCategory(categoryID ?? "");
  }
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        children: [
          Container(
            color: colorPrimary,
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 15),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "$categoryName",
                  style:
                      labelStyle2.copyWith(fontSize: 18, color: Colors.white),
                ),
                Text(
                  "Total: ${formatCurrencyAmount(categoryTotal ?? 0, true)}",
                  style: labelStyle2.copyWith(color: Colors.white),
                ),
              ],
            ),
          ),
          Expanded(
              child: expensesListController!.isLoadingForCategory
                  ? Container(
                      color: Colors.white,
                      child: const Center(child: CircularProgressIndicator()))
                  : expensesListController!.expensesForSingleCategory.isEmpty
                      ? const SizedBox(
                          width: double.infinity,
                          child: Center(
                              child: Text(
                            "No Records",
                            style: TextStyle(color: Colors.black54),
                          )))
                      : ListView.builder(
                          itemCount: expensesListController!
                              .expensesForSingleCategory.length,
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            ExpenseModel expense = expensesListController!
                                .expensesForSingleCategory[index];
                            return Material(
                              color: Colors.transparent,
                              child: InkWell(
                                onTap: () async {
                                  TransactionHelper.gotoTransactionEditPage(
                                      context,
                                      expense.txnId ?? "",
                                      expense.txnType!);
                                  // await showModalBottomSheet(
                                  //     context: context,
                                  //     builder: (_) {
                                  //       return ExpenseListSingleCategory(
                                  //         categoryID: expense.expenseCategoryId,
                                  //         categoryName:
                                  //             expense.expenseCategoryName,
                                  //         categoryTotal: expense.txnTotalAmount,
                                  //         expensesListController:
                                  //             expensesListController,
                                  //       );
                                  //     });
                                },
                                child: Container(
                                  padding: const EdgeInsets.only(
                                      left: 10, right: 10, top: 10, bottom: 10),
                                  decoration: BoxDecoration(
                                      border: Border(
                                          bottom: expensesListController!
                                                          .expensesForSingleCategory
                                                          .length -
                                                      1 ==
                                                  index
                                              ? BorderSide.none
                                              : BorderSide(
                                                  width: 1,
                                                  color: Colors.black
                                                      .withOpacity(0.1)))),
                                  child: Row(
                                    children: [
                                      SizedBox(
                                          width: 30,
                                          child: Text(
                                            "${index + 1}.",
                                            // style: labelStyle2.copyWith(),
                                          )),
                                      Expanded(
                                          flex: 1,
                                          child: Container(
                                            // color: Colors.red,
                                            width: double.infinity,
                                            alignment: Alignment.centerLeft,
                                            child: Text(
                                              expense.expenseCategoryName ?? "",
                                              // style: labelStyle2.copyWith(),
                                            ),
                                          )),
                                      Container(
                                          alignment: Alignment.centerRight,
                                          // width: 90,
                                          child: Text(
                                            formatCurrencyAmount(
                                                expense.txnTotalAmount ?? 0,
                                                true),
                                            // style: labelStyle2.copyWith(),
                                          )),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          })),
        ],
      );
    });
  }
}
