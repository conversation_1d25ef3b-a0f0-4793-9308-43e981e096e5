import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:sqflite/sqflite.dart';

class AllTransactionRepository {
  final String tag = "AllTransactionRepository";
  DatabaseHelper databaseHelper = DatabaseHelper();

  Future<List<AllTransactionModel>> getAllTransactions(
      {List<int>? types}) async {
    List<AllTransactionModel> list = [];
    if (null == types || types.isEmpty) {
      types = TxnType.allTypes;
    }

    try {
      Database? dbClient = await databaseHelper.database;
      String typesIN = types.join(",");

      List<Map<String, dynamic>> txnDataListJson = (await dbClient!.rawQuery(
          'SELECT mk_transactions.*, '
          'mk_expense_category.expense_title, '
          'mk_ledger_master.ledger_title, mk_ledger_master.address AS ledger_address, mk_ledger_master.tin_no, mk_ledger_master.tin_flag '
          'FROM mk_transactions '
          'LEFT JOIN mk_expense_category ON mk_expense_category.expense_category_id = mk_transactions.expense_category_id  '
          'LEFT JOIN mk_ledger_master ON mk_ledger_master.ledger_id = mk_transactions.ledger_id  '
          'WHERE mk_transactions.last_activity_type!=? AND mk_transactions.txn_type IN ($typesIN) '
          'ORDER BY txn_date DESC, last_activity_at DESC',
          [LastActivityType.Delete]));

      Log.d("this is data from database => ${txnDataListJson.length} ");

      list = txnDataListJson.map((txnData) {
        return AllTransactionModel.fromJson(txnData);
      }).toList();
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return list;
  }
}
