// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/ledger_list_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/ledger_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/add_party/add_edit_dialog_page.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

// ignore: must_be_immutable
class LedgerAutoCompleteTextFieldWithAdd extends StatefulWidget {
  bool? enableFlag;
  String? labelText;
  TextEditingController? controller;
  Function? onChangedFn;
  Function? onSuggestionSelectedFn;
  String? ledgetID;
  List<String>? excludedIDS;

  LedgerAutoCompleteTextFieldWithAdd(
      {super.key,
      this.enableFlag = true,
      this.labelText = "Select Person/Firm",
      this.controller,
      this.onChangedFn,
      this.onSuggestionSelectedFn,
      this.ledgetID,
      this.excludedIDS}) {
    // ledgerListController.fetchAll();
  }

  @override
  _LedgerAutoCompleteTextFieldWithAddState createState() =>
      _LedgerAutoCompleteTextFieldWithAddState();
}

class _LedgerAutoCompleteTextFieldWithAddState
    extends State<LedgerAutoCompleteTextFieldWithAdd> {
  final ledgerListController = Get.put(LedgerListController());
  LedgerRepository ledgerRepository = LedgerRepository();

  final TextEditingController _searchBoxCotroller = TextEditingController();

  final FocusNode searchFocuseNode = FocusNode();
  @override
  void initState() {
    ledgerListController.searchByTitleWithBalance("");

    searchFocuseNode.addListener(() {
      if (searchFocuseNode.hasFocus) {
      } else {
        Future.delayed(const Duration(milliseconds: 200), () {
          if (null == widget.ledgetID) {
            //ledger not selected, so remove text;
            widget.controller!.text = "";
            // widget.onSuggestionSelectedFn(LedgerDetailModel());
          }
        });

        // Log.d("Removed focus");
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    List<String> excluded = widget.excludedIDS ?? [];

    return TypeAheadField<LedgerDetailModel>(
      textFieldConfiguration: TextFieldConfiguration(
        focusNode: searchFocuseNode,
        enabled: widget.enableFlag!,
        controller: widget.controller,
        decoration: formFieldStyle.copyWith(labelText: widget.labelText),
        onChanged: (value) {
          // Log.d("Changed value $value");
          //if initially any ledger id is selected, then clear that selection on change textt
          if (null != widget.ledgetID) {
            widget.onSuggestionSelectedFn!(LedgerDetailModel());
          }
          if (null != widget.onChangedFn) {
            widget.onChangedFn!(value);
          }
        },
      ),
      getImmediateSuggestions: true,
      suggestionsCallback: (pattern) async {
        // Log.d("searching for $pattern");

        List<LedgerDetailModel> results = await ledgerRepository
            .getAllGeneralLedgersWithBalanceByTitle(pattern,
                includedIDs: widget.ledgetID != null ? [] : []);

        // await ledgerListController.searchByTitleWithBalance(pattern,
        //     included: widget.ledgetID != null ? [widget.ledgetID] : []);

        return results
            .where((element) => (!excluded.contains(element.ledgerId)));
      },
      noItemsFoundBuilder: (noItemCotext) {
        if (widget.controller!.text.isEmpty) {
          return const Padding(
            padding: EdgeInsets.all(8.0),
            child: Text("No Party Found"),
          );
        }
        return InkWell(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorPrimary,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                  foregroundColor: colorPrimaryLightest,
                ),
                child: const Padding(
                  padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                  child: Text(
                    "नयाँ पार्टी थप्नुहोस्  (Add New Party)",
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 15,
                    ),
                  ),
                ),
                onPressed: () async {
                  LedgerModel? returnedData = await displayPartyAddDialog(
                      context,
                      partyName: widget.controller!.text);
                  if (returnedData == null) return;
                  ledgerListController.fetchAllWithBalance();
                  widget.onSuggestionSelectedFn!(
                      LedgerDetailModel.fromJson(returnedData.toJson()));
                }),
            // child: Text(
            //   "Add  Party (\"${widget.controller.text}\"",
            //   style: labelStyle2,
            // ),
          ),
        );
      },
      transitionBuilder: (context, suggestionsBox, controller) {
        return suggestionsBox;
      },
      itemBuilder: (context, ledger) {
        bool isReceivable = ((ledger.balanceAmount ?? 0) >= 0) ? true : false;
        return Column(
          children: [
            ListTile(
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
              title: Text(
                "${ledger.ledgerTitle}",
                style:
                    const TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
              ),
              trailing: SizedBox(
                width: 80,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Expanded(
                        child: Text("${ledger.balanceAmount ?? 0} ",
                            style: TextStyle(
                                fontSize: 15,
                                color: (isReceivable)
                                    ? colorGreenDark
                                    : colorRedLight))),
                    Transform.rotate(
                      angle: (isReceivable) ? (3.14 / 1.3) : (-3.14 / 4),
                      alignment: Alignment.center,
                      child: CircleAvatar(
                        radius: 8,
                        backgroundColor:
                            (isReceivable) ? colorGreenDark : colorRedLight,
                        child: const Icon(Icons.arrow_forward,
                            color: Colors.white, size: 14),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const Divider(height: 0, thickness: 1),
          ],
        );
      },
      onSuggestionSelected: (ledger) => widget.onSuggestionSelectedFn!(ledger),
    );
  }
}
