import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
import 'package:mobile_khaata_v2/app/model/others/cash_txn_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/cash_in_hand_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/report_controllers/cash_report_controller.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';

// ignore: must_be_immutable
class CashStatementReport extends StatelessWidget {
  final CashReportController _controller = CashReportController();
  String startDate = currentDate;
  String endDate = currentDate;
  List<LedgerDetailModel> ledgers = [];

  CashStatementReport({super.key}) {
    generate();
  }

  generate() async {
    _controller.generateCashStatementReport(
      startDate: startDate,
      endDate: endDate,
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        elevation: 0,
        titleSpacing: -5.0,
        backgroundColor: colorPrimary,
        title: const Text(
          "नगद मौज्दात स्टेट्मेन्ट\n(Cash In Hand Statement)",
          style: TextStyle(
              fontSize: 17,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
        actions: [
          PrintButton(
            onPressed: () {
              Navigator.pushNamed(context, '/printCashStatement',
                  arguments: CashInHandPrintPage(
                    transactions: _controller.cashTransactions,
                    startDate: startDate,
                    endDate: endDate,
                  ));
            },
          )
        ],
      ),
      body: GestureDetector(
        onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
        child: Container(
          color: Colors.black12,
          child: Column(
            // mainAxisSize: MainAxisSize.min,
            children: [
              //=============================transaction date filter
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: ReportCustomDatePickerTextField(
                        initialValue: toDateBS(DateTime.parse(startDate)),
                        hintText: "From Date",
                        onChange: (selectedDate) {
                          startDate =
                              toDateAD(NepaliDateTime.parse(selectedDate));
                          // this.setState(() {});
                          generate();
                        },
                      ),
                    ),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Text(
                          "TO",
                          style: labelStyle2,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: ReportCustomDatePickerTextField(
                        initialValue: toDateBS(DateTime.parse(endDate)),
                        hintText: "To Date",
                        onChange: (selectedDate) {
                          endDate =
                              toDateAD(NepaliDateTime.parse(selectedDate));
                          generate();
                        },
                      ),
                    ),
                  ],
                ),
              ),

              const Divider(
                height: 4,
                color: Colors.black54,
              ),

              Container(
                color: Colors.white,
                child: Column(
                  children: [
                    DefaultTextStyle(
                      style: TextStyle(
                          fontSize: 12,
                          color: textColor,
                          fontWeight: FontWeight.bold),
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                            vertical: 10, horizontal: 5),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: const [
                            //====================================1st Column
                            Expanded(
                              flex: 3,
                              child: Text(
                                "Date",
                              ),
                            ),

                            //====================================2nd Column
                            Expanded(
                              flex: 3,
                              child: Text(
                                "Description",
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                            ),

                            //====================================3rd Column
                            Expanded(
                              flex: 3,
                              child: Text(
                                "Money-In",
                                textAlign: TextAlign.right,
                              ),
                            ),

                            //====================================4th Column
                            Expanded(
                              flex: 3,
                              child: Text(
                                "Money-Out",
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const Divider(
                      height: 4,
                      color: Colors.black54,
                    ),
                  ],
                ),
              ),

              Obx(() {
                if (_controller.txnLoading) {
                  return Container(
                      color: Colors.white,
                      child: const Center(child: CircularProgressIndicator()));
                }

                if (_controller.cashTransactions.isEmpty) {
                  return Container(
                      color: Colors.white,
                      width: double.infinity,
                      child: const Center(
                          child: Text(
                        "No Records",
                        style: TextStyle(color: Colors.black54),
                      )));
                } else {
                  return Expanded(
                      child: _TxnListView(_controller.cashTransactions));
                }
              }),
            ],
          ),
        ),
      ),
      // extendBody: true,
      bottomNavigationBar: Container(
        height: 45,
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        color: colorPrimary,
        child: SingleChildScrollView(
          child: Obx(() {
            return DefaultTextStyle(
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              child: Text(
                "Closing Balance: ${formatCurrencyAmount(_controller.closingBalance.value, false)}",
                textAlign: TextAlign.right,
              ),
            );
          }),
        ),
      ),
    ));
  }
}

class _TxnListView extends StatelessWidget {
  final List<CashTxnModel> _transactionList;

  const _TxnListView(this._transactionList);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: _transactionList.length,
      // shrinkWrap: true,
      itemBuilder: (context, int index) {
        CashTxnModel txn = _transactionList[index];

        return InkWell(
          // onTap: () => TransactionHelper.gotoTransactionEditPage(
          //     context, txn.txnId, txn.txnType),
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                DefaultTextStyle(
                  style: TextStyle(fontSize: 12, color: colorPrimary),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        //====================================1st Column
                        Expanded(
                          flex: 3,
                          child: Text(
                            "${txn.txnDateBS}",
                          ),
                        ),

                        //====================================2nd Column
                        Expanded(
                          flex: 3,
                          child: Text(
                            txn.cashTransactionDesc ?? '',
                            overflow: TextOverflow.ellipsis,
                            maxLines: 3,
                          ),
                        ),

                        //====================================3rd Column
                        Expanded(
                          flex: 3,
                          child: Text(
                            txn.cashTransactionType == TxnType.addCash
                                ? formatCurrencyAmount(txn.txnAmount!, false)
                                : "-",
                            textAlign: TextAlign.right,
                          ),
                        ),

                        //====================================4th Column
                        Expanded(
                          flex: 3,
                          child: Text(
                            txn.cashTransactionType == TxnType.reduceCash
                                ? formatCurrencyAmount(txn.txnAmount!, false)
                                : "-",
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                //Ad-d space if last element
                if (_transactionList.length - 1 == index) ...{
                  const SizedBox(
                      // height: 100,
                      )
                },
              ],
            ),
          ),
        );
      },
    );
  }
}
