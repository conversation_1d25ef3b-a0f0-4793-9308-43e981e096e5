import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/model/others/item_transaction_model.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/add_edit_item/add_edit_item_page.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/item_adjustment/item_adjustment_page.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/item_detail/item_detail_controller.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';
import 'package:mobile_khaata_v2/database/item_type.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:sticky_headers/sticky_headers.dart';
import 'package:tuple/tuple.dart';

class ItemDetailPage extends StatelessWidget {
  final String tag = "ItemDetailPage";

  final String itemId;
  final itemDetailController =
      Get.put(ItemDetailController(), tag: "ItemDetailController");

  ItemDetailPage({required this.itemId}) {
    itemDetailController.init(itemId);
  }

  searchBoxOnChangeHandler(String searchString) {
    itemDetailController.searchTransaction(searchString);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (itemDetailController.isLoading) {
        return Container(
            color: Colors.white,
            child: Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
        // resizeToAvoidBottomPadding: false,
        resizeToAvoidBottomInset: false,
        //==============================================AppBar
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          leading: BackButton(
            onPressed: () => Navigator.pop(context),
          ),
          centerTitle: false,
          titleSpacing: -5.0,
          backgroundColor: colorPrimary,
          title: Text(
            itemDetailController.item!.itemName ?? "",
            style: TextStyle(
                fontSize: 20,
                color: Colors.white,
                fontFamily: 'HelveticaRegular',
                fontWeight: FontWeight.bold),
          ),
          actions: [
            IconButton(
              icon: Icon(
                Icons.edit,
                size: 25,
                color: Colors.white,
              ),
              onPressed: () {
                Navigator.pushNamed(context, "/addItem",
                    arguments: AddEditItemPage(
                      itemId: itemDetailController.item!.itemId!,
                    ));
              },
            ),
            IconButton(
              icon: Icon(
                Icons.delete,
                size: 25,
                color: colorRedLight,
              ),
              onPressed: () {
                showAlertDialog(context,
                    okText: "YES",
                    hasCancel: true,
                    cancelText: "NO",
                    alertType: AlertType.Error,
                    alertTitle: "Confirm Delete",
                    onCloseButtonPressed: () async {
                  debugPrint("al bacchaa delete garde vaio k");
                  // Navigator.of(_).pop();
                  ProgressDialog progressDialog = ProgressDialog(context,
                      type: ProgressDialogType.normal, isDismissible: false);
                  progressDialog.update(
                      message: "Checking Permission. Please wait....");
                  await progressDialog.show();
                  Tuple2<bool, String> checkResp =
                      await PermissionWrapperController()
                          .requestForPermissionCheck(
                              forPermission: PermissionManager.itemDelete);
                  if (checkResp.item1) {
                    //has  permission
                    progressDialog.update(
                        message: "Deleting Data. Please wait....");

                    Tuple2<bool, String> deleteResp =
                        await ItemRepository().delete(this.itemId);
                    await progressDialog.hide();
                    if (deleteResp.item1) {
                      //  data deleted
                      TransactionHelper.refreshPreviousPages();
                      showAlertDialog(context,
                          barrierDismissible: false,
                          alertType: AlertType.Success,
                          alertTitle: "", onCloseButtonPressed: () {
                        // Navigator.of(_).pop();
                        Navigator.of(context).pop();
                        Navigator.pushReplacementNamed(context, '/itemList');
                      }, message: deleteResp.item2);
                    } else {
                      //cannot  delete  data
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "",
                          message: deleteResp.item2);
                    }
                  } else {
                    await progressDialog.hide();
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "",
                        message: checkResp.item2);
                  }
                }, message: "Are you sure you want to delete this item?");
                // Navigator.pushNamed(context, "/addItem",
                //     arguments: AddEditItemPage(
                //       itemId: itemDetailController.item.itemId,
                //     ));
              },
            )
          ],
        ),
        body: Stack(
          children: [
            //===================================================Second Panel (Transaction List)
            Container(
              child: ListView(
                padding: EdgeInsets.only(bottom: 100),
                children: [
                  Container(
                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                    decoration: BoxDecoration(
                      color: colorPrimaryLight,
                    ),
                    child: DefaultTextStyle(
                      style: TextStyle(color: Colors.white, fontSize: 14),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              //===================================== Item type:- Service
                              if (ItemType.service ==
                                  itemDetailController.item!.itemType!) ...{
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text("Sale Price",
                                            style: TextStyle(fontSize: 12)),
                                        Text(
                                          "${formatCurrencyAmount(itemDetailController.item!.itemSaleUnitPrice?.toDouble() ?? 0.00)}",
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              }

                              //===================================== Item type:- Product
                              else ...{
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text("Stock Qty",
                                            style: TextStyle(fontSize: 12)),
                                        Text(
                                            "${itemDetailController.item!.balanceQuantity ?? 0}"),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text("Stock Value",
                                            style: TextStyle(fontSize: 12)),
                                        Text(
                                            "${formatCurrencyAmount((itemDetailController.item!.balanceQuantity ?? 0) * (itemDetailController.item!.itemPurchaseUnitPrice != null ? itemDetailController.item!.itemPurchaseUnitPrice!.toDouble() : 0.00))}"),
                                      ],
                                    ),
                                  ],
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        Text("Sale Price",
                                            style: TextStyle(fontSize: 12)),
                                        Text(
                                            "${formatCurrencyAmount(itemDetailController.item!.itemSaleUnitPrice != null ? itemDetailController.item!.itemSaleUnitPrice!.toDouble() : 0.00)}"),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 15,
                                    ),
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end,
                                      children: [
                                        Text("Purchase Price",
                                            style: TextStyle(fontSize: 12)),
                                        Text(
                                            "${formatCurrencyAmount(itemDetailController.item!.itemPurchaseUnitPrice != null ? itemDetailController.item!.itemPurchaseUnitPrice!.toDouble() : 0.00)}"),
                                      ],
                                    ),
                                  ],
                                ),
                              }
                            ],
                          ),
                          Divider(
                            color: Colors.white,
                          ),
                          Text(
                              "Type: ${ItemType.itemTypeText[itemDetailController.item!.itemType!]}"),
                          SizedBox(
                            height: 5,
                          ),
                          Text(
                              "Location: ${itemDetailController.item!.itemLocation ?? ''}"),
                        ],
                      ),
                    ),
                  ),

                  //====================================================Border
                  Container(
                    height: 2,
                    color: Colors.black26,
                  ),
                  if (null != itemDetailController.item!.openingStock)
                    Container(
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(color: Colors.black26))),
                      padding: EdgeInsets.only(bottom: 5, top: 5),
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(vertical: 5, horizontal: 15),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            //============1st Column
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "${TxnType.txnTypeText[TxnType.openingStock]}",
                                    style: TextStyle(
                                        fontSize: 14,
                                        color: colorPrimaryDark,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  Text(
                                    "Qty: ${itemDetailController.item!.openingStock} ${itemDetailController.item!.unitName}",
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: colorPrimaryDark,
                                      height: 1.7,
                                    ),
                                  )
                                ],
                              ),
                            ),

                            SizedBox(
                              width: 20,
                            ),

                            //============2nd Column
                            Column(
                              children: [
                                Text(
                                  "${itemDetailController.item!.openingDateBS}",
                                  style: TextStyle(fontSize: 12),
                                ),
                              ],
                            ),
                            SizedBox(
                              width: 10,
                            ),
                          ],
                        ),
                      ),
                    ),
                  Container(
                    width: double.infinity,
                    decoration: BoxDecoration(color: Colors.white),
                    padding: EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                    // child: _ItemTxnListView(itemDetailController),
                    child: _ItemTxnListView(itemDetailController),
                  ),

                  //==============================================Bottom Button
                ],
              ),
            ),
            if (itemDetailController.item!.itemType == ItemType.product) ...{
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Container(
                  height: 65,
                  child: Column(
                    children: [
                      Container(
                        constraints: BoxConstraints(maxWidth: 160),
                        height: 40,
                        child: ElevatedButton(
                          // elevation: 6,
                          // textColor: Colors.black54,
                          // color: colorPrimary,
                          // padding: EdgeInsets.zero,
                          // splashColor: colorPrimaryLighter,
                          // shape: RoundedRectangleBorder(
                          //     borderRadius: BorderRadius.circular(50)),
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              "/itemAdjustment",
                              arguments: ItemAdjustmentPage(
                                itemId: itemId,
                              ),
                            );
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              RotatedBox(
                                  quarterTurns: 1,
                                  child: Icon(
                                    Icons.tune,
                                    size: 20,
                                    color: Colors.white,
                                  )),
                              SizedBox(
                                width: 8,
                              ),
                              Text(
                                "Adjust Stock",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 16),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            }
          ],
        ),
      ));
    });
  }
}

class _ItemTxnListView extends StatelessWidget {
  final ItemDetailController itemDetailController;

  _ItemTxnListView(this.itemDetailController);

  @override
  Widget build(BuildContext context) {
    // Log.d(itemDetailController.filteredTransactions.length);
    return Obx(() {
      if (itemDetailController.txnLoading) {
        return Container(
            padding: EdgeInsets.all(40),
            color: Colors.white,
            child: Center(child: CircularProgressIndicator()));
      }
      if (itemDetailController.transactions.isEmpty) {
        return Container(
            padding: EdgeInsets.all(40),
            width: double.infinity,
            child: Center(
                child: Text(
              "No Records",
              style: TextStyle(color: Colors.black54),
            )));
      } else {
        return StickyHeader(
            header: Container(
              width: MediaQuery.of(context).size.width,
              padding: EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 5),
              color: Colors.white,
              height: 55,
              child: FormBuilderTextField(
                name: "searchBox",
                autocorrect: false,
                keyboardType: TextInputType.text,
                textInputAction: TextInputAction.done,

                style: TextStyle(
                  // color: Colors.white,
                  fontSize: 18,
                ),
                // controller: searchController,
                decoration: formFieldStyle.copyWith(
                    // labelText: "Search For",
                    hintText: "Search For",
                    prefixIcon: Icon(Icons.search),
                    alignLabelWithHint: true),
                onChanged: (searchString) =>
                    itemDetailController.searchTransaction(searchString!),
              ),
            ),
            content: Column(children: [
              if (itemDetailController.filteredTransactions.length == 0)
                Container(
                    padding: EdgeInsets.all(40),
                    width: double.infinity,
                    child: Center(
                        child: Text(
                      "No Records for given query",
                      style: TextStyle(color: Colors.black54),
                    ))),
              ...itemDetailController.filteredTransactions
                  .map((ItemTransactionModel _itemTxn) {
                return Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () => {
                      TransactionHelper.gotoTransactionEditPage(
                          context, _itemTxn.txnId!, _itemTxn.txnType!),
                    },
                    child: Container(
                      decoration: BoxDecoration(
                          border: Border(
                              bottom: BorderSide(color: Colors.black26))),
                      padding: EdgeInsets.only(bottom: 5, top: 5),
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(vertical: 5, horizontal: 15),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            //============1st Column
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "${_itemTxn.txnTypeText}",
                                  style: TextStyle(
                                      fontSize: 14,
                                      color: colorPrimaryDark,
                                      fontWeight: FontWeight.bold),
                                ),
                                Text(
                                  "Qty: ${_itemTxn.quantity} ${_itemTxn.unitShortName}",
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: colorPrimaryDark,
                                    height: 1.7,
                                  ),
                                )
                              ],
                            ),

                            SizedBox(
                              width: 20,
                            ),

                            //============2nd Column
                            Column(
                              children: [
                                Text(
                                  "${_itemTxn.txnDateBS}",
                                  style: TextStyle(fontSize: 12),
                                ),
                              ],
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              }).toList(),
            ]));
      }
    });
  }
}
