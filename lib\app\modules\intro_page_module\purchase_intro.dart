import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_custom_clippers/flutter_custom_clippers.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class PurchaseIntro extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;

    return SafeArea(
      child: Container(
          decoration: BoxDecoration(color: Color(0xFFf5f8ff)),
          child: Stack(
            fit: StackFit.expand,
            children: [
              Positioned(
                left: 0,
                right: 0,
                top: screenWidth * 0.04,
                child: Text(
                  "खरिद",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: screenWidth * 0.10,
                      color: colorPrimary,
                      fontWeight: FontWeight.w800,
                      fontFamily: "ArialBlack"),
                ),
              ),
              Positioned(
                top: screenHeight * 0.09,
                left: 0,
                right: 0,
                bottom: screenHeight * 0.21,
                child: ClipPath(
                  clipper: DiagonalPathClipperTwo(),
                  child: Container(
                    width: double.infinity,
                    margin: EdgeInsets.symmetric(horizontal: 10),
                    padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                    decoration: BoxDecoration(
                        color: Colors.white, boxShadow: downShadow),
                    child: SingleChildScrollView(
                        physics: AlwaysScrollableScrollPhysics(),
                        child: Column(
                          children: [
                            Text(
                              "व्यवसायको सुरुवात अब\nmobile खाता बाट",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  fontSize: screenWidth * 0.06,
                                  fontWeight: FontWeight.w800,
                                  fontFamily: "ArialBlack"),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Image.asset(
                              'images/purchase-small.png',
                              height: screenWidth * 0.3,
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Text(
                              "खरिद गरिएका सम्पूर्ण सामाग्रीहरुको VAT/PAN सहित् को मुल्य तथा परीमाणको रेकर्ड राख्न सजिलो तथा भर्पर्दो माध्यम",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: screenWidth * 0.045,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                          ],
                        )),
                  ),
                ),
              ),
            ],
          )),
    );
  }
}
