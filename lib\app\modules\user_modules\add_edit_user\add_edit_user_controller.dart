import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/user_create_model.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:tuple/tuple.dart';

class AddEditUserController extends GetxController {
  final String tag = "AddEditUserController";

  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  final _editFlag = false.obs;
  bool get editFlag => _editFlag.value;

  final _readOnlyFlag = false.obs;
  bool get readOnlyFlag => _readOnlyFlag.value;
  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  final _user = UserCreateModel().obs;
  UserCreateModel get user => _user.value;
  final formKey = GlobalKey<FormState>();

  @override
  Future<void> onInit() async {
    // _isLoading(true);

    // unitMap = (await _unitRepository.getAllUnits())
    //     .map((e) => {"key": e.unitId, "value": e.displayTitle})
    //     .toList();
    // itemAdj.itemAdjDateBS = currentDateBS;
    // _isLoading(false);
    super.onInit();
  }

  initAdd() {
    _isLoading(false);
  }

  @override
  void onClose() {
    super.onClose();
  }

  initEdit(UserCreateModel userData) {
    _isLoading(true);
    _editFlag(true);
    _readOnlyFlag(true);
    _user.value = userData;
    _isLoading(false);
  }

  Future<Tuple2<bool, String>> createUser() async {
    bool status = false;
    String message = "";
    ApiBaseHelper apiBaseHelper = ApiBaseHelper();
    ApiResponse apiResponse = await apiBaseHelper
        .post(apiBaseHelper.ADD_USER, user.toJson(), accessToken: true);
    if (apiResponse.status) {
      status = true;
    } else {}
    message = apiResponse.msg ?? "";
    return Tuple2(status, message);
  }

  Future<Tuple2<bool, String>> updateUser(int userId) async {
    bool status = false;
    String message = "";
    ApiBaseHelper apiBaseHelper = ApiBaseHelper();
    ApiResponse apiResponse = await apiBaseHelper.post(
        apiBaseHelper.EDIT_USER + userId.toString(), user.toJson(),
        accessToken: true);
    if (apiResponse.status) {
      status = true;
    } else {}
    message = apiResponse.msg ?? "";

    return Tuple2(status, message);
  }
}
