import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/utilities/shared_pref_helper1.dart';

class UnauthorizedPage extends StatelessWidget {
  const UnauthorizedPage({Key? key}) : super(key: key);

  // final inactivePageController = InactivePageController();
  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(),
      body: Center(
        child: Column(
          children: [
            const Text("Unauthorized user"),
            const SizedBox(
              height: 40,
            ),
            Center(
              child: ElevatedButton(
                  // color: colorPrimary,
                  // elevation: 10,
                  // shape: RoundedRectangleBorder(
                  //   borderRadius: BorderRadius.circular(10.0),
                  // ),
                  // splashColor: colorPrimaryLightest,
                  child: const Padding(
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 40),
                    child: Text(
                      "Recheck From Network",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 15,
                          fontWeight: FontWeight.bold),
                    ),
                  ),
                  onPressed: () async {
                    SharedPrefHelper1().setUnauthorized(false);
                    // ProgressDialog progressDialog = ProgressDialog(context,
                    //     type: ProgressDialogType.Normal,
                    //     isDismissible: false);
                    // progressDialog.update(
                    //     message: "Checking status. Please wait....");
                    // await progressDialog.show();
                    // Tuple2<int, String> checkResponse =
                    //     await inactivePageController
                    //         .recheckActiveFromNetwork();
                    // await progressDialog.hide();
                    // if (checkResponse.item1 != 0) {
                    //   // Navigator.pop(context, true);

                    //   await setActiveUser(active: 1);
                    //   Navigator.popAndPushNamed(context, '/home');
                    //   showToastMessage(context,
                    //       message: checkResponse.item2, duration: 2);
                    //   // Navigator.of(context).pop();
                    // } else {
                    //   showToastMessage(context,
                    //       alertType: AlertType.Error,
                    //       message: checkResponse.item2,
                    //       duration: 2);
                    // }
                  }),
            ),
          ],
        ),
      ),
    ));
  }
}
