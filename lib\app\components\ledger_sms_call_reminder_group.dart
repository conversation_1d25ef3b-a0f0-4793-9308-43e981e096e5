import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/controllers/registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/reminder_module/add_edit_reminder/add_edit_reminder_page.dart';
import 'package:mobile_khaata_v2/database/reminder_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

// ignore: must_be_immutable
class LedgerSmsCallReminderGroup extends StatelessWidget {
  late LedgerDetailModel ledger;
  bool? _isReceivable;

  final RegistrationDetailController _registrationDetailController =
      Get.put(RegistrationDetailController());

  LedgerSmsCallReminderGroup({super.key, required this.ledger}) {
    _isReceivable = (ledger.balanceAmount! >= 0) ? true : false;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        SizedBox(
          width: 35,
          child: IconButton(
            icon: Icon(
              Icons.phone,
              size: 20,
              color: colorPrimary,
            ),
            onPressed: () {
              (null == ledger.mobileNo)
                  ? showAlertDialog(context,
                      alertType: AlertType.Error,
                      alertTitle: "Error",
                      message:
                          'सम्पर्क नम्बर थपिएको छैन |\n(Contact No. not added)')
                  : dialNumber(ledger.mobileNo!);
            },
          ),
        ),
        SizedBox(
          width: 35,
          child: IconButton(
            icon: Icon(
              Icons.message,
              size: 20,
              color: colorPrimary,
            ),
            onPressed: () {
              if (null == ledger.mobileNo) {
                showAlertDialog(context,
                    alertType: AlertType.Error,
                    alertTitle: "Error",
                    message:
                        'सम्पर्क नम्बर थपिएको छैन |\n(Contact No. not added)');
              } else {
                String message;
                if (_isReceivable == true) {
                  message = (DUE_RECEIVE_MSG.replaceFirst("{Amount}",
                          ledger.balanceAmount!.abs().toStringAsFixed(2)))
                      .replaceFirst(
                          "{shopName}",
                          _registrationDetailController
                                  .registrationDetail.businessName ??
                              "");
                } else {
                  message = (DUE_PAY_MSG.replaceFirst("{Amount}",
                          ledger.balanceAmount!.abs().toStringAsFixed(2)))
                      .replaceFirst(
                          "{shopName}",
                          _registrationDetailController
                                  .registrationDetail.businessName ??
                              "");
                }

                sendSMS(ledger.mobileNo!, message);
              }
            },
          ),
        ),
        SizedBox(
          width: 35,
          child: IconButton(
            icon: Icon(
              Icons.calendar_today_sharp,
              size: 20,
              color: colorPrimary,
            ),
            onPressed: () {
              String message =
                  "Give payment to ${ledger.ledgerTitle} of Rs. ${ledger.balanceAmount!.abs()}";
              int reminderType = ReminderType.payment;
              if (_isReceivable == true) {
                message =
                    "Collect payment from ${ledger.ledgerTitle} of Rs. ${ledger.balanceAmount!.abs()}";
              }
              Navigator.pushNamed(context, '/addEditReminder',
                  arguments: AddEditReminderPage(
                    reminderDesc: message,
                    reminderType: reminderType,
                    ledgerId: ledger.ledgerId,
                  ));
            },
          ),
        ),
      ],
    );
  }
}
