class RegistrationDetailModel {
  RegistrationDetailModel(
      {this.userId,
      this.businessName,
      this.businessAddress,
      this.wardNo,
      this.provinceId,
      this.provinceTitle,
      this.districtId,
      this.districtTitle,
      this.vdcMncId,
      this.vdcMncTitle,
      this.businessCategoryId,
      this.businessCategoryTitle,
      this.businessTypeId,
      this.businessTypeTitle,
      this.gpsLat,
      this.gpsLng,
      this.tinNo,
      this.tinFlag,
      this.fullName,
      this.countryCode,
      this.primaryMobileNo,
      this.gender,
      this.altCountryCode,
      this.altMobileNo,
      this.email,
      this.logoId,
      this.signatureId,
      this.lastActivityType,
      this.lastActivityAt,
      this.lastActivityBy});

  int? userId;
  String? businessName;
  String? businessAddress;

  String? wardNo;
  int? provinceId;
  String? provinceTitle;
  int? districtId;
  String? districtTitle;
  int? vdcMncId;
  String? vdcMncTitle;
  int? businessCategoryId;
  String? businessCategoryTitle;
  int? businessTypeId;
  String? businessTypeTitle;
  String? gpsLat;
  String? gpsLng;

  String? tinNo;
  String? tinFlag;
  String? fullName;
  String? countryCode;
  String? primaryMobileNo;
  String? gender;
  String? altCountryCode;
  String? altMobileNo;
  String? email;
  String? logoId;
  String? signatureId;

  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;

  factory RegistrationDetailModel.fromJson(Map<String, dynamic> json) =>
      RegistrationDetailModel(
        userId: json["user_id"],
        businessName: json["business_name"],
        businessAddress: json["business_address"],
        wardNo: json["ward_no"],
        provinceId: json["province_id"],
        provinceTitle: json["province_title"],
        districtId: json["district_id"],
        districtTitle: json["district_title"],
        vdcMncId: json["vdc_mnc_id"],
        vdcMncTitle: json["vdc_mnc_title"],
        businessCategoryId: json["business_category_id"],
        businessCategoryTitle: json["business_category_title"],
        businessTypeId: json["business_type_id"],
        businessTypeTitle: json["business_type_title"],
        gpsLat: json["gps_lat"],
        gpsLng: json["gps_lng"],
        tinNo: json["tin_no"],
        tinFlag: json["tin_flag"],
        fullName: json["full_name"],
        countryCode: json["country_code"],
        primaryMobileNo: json["primary_mobile_no"],
        gender: json["gender"],
        altCountryCode: json["alt_country_code"],
        altMobileNo: json["alt_mobile_no"],
        email: json["email"],
        logoId: json["logo_id"],
        signatureId: json["signature_id"],
        lastActivityType: json["last_activity_type"],
        lastActivityAt: json["last_activity_at"],
        lastActivityBy: json["last_activity_by"],
      );

  Map<String, dynamic> toJson() => {
        "user_id": userId,
        "business_name": businessName,
        "business_address": businessAddress,
        "ward_no": wardNo,
        "province_id": provinceId,
        "province_title": provinceTitle,
        "district_id": districtId,
        "district_title": districtTitle,
        "vdc_mnc_id": vdcMncId,
        "vdc_mnc_title": vdcMncTitle,
        "business_category_id": businessCategoryId,
        "business_category_title": businessCategoryTitle,
        "business_type_id": businessTypeId,
        "business_type_title": businessTypeTitle,
        "gps_lat": gpsLat,
        "gps_lng": gpsLng,
        "tin_no": tinNo,
        "tin_flag": tinFlag,
        "full_name": fullName,
        "country_code": countryCode,
        "primary_mobile_no": primaryMobileNo,
        "gender": gender,
        "alt_country_code": altCountryCode,
        "alt_mobile_no": altMobileNo,
        "email": email,
        "logo_id": logoId,
        "signature_id": signatureId,
        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        "last_activity_by": lastActivityBy,
      };
}
