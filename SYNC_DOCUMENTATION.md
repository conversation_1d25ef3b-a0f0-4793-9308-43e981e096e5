# Mobile Khata - Sync System Documentation

## Overview
This document outlines the current synchronization system between the Mobile Khata mobile app and web platform, identifies critical issues, and provides a roadmap for fixes.

## Current Sync Architecture

### 1. Sync Components
The current sync system consists of the following key components:

- **`sync_actions.dart`** - Main sync logic for push operations
- **`SyncedBatchRepository`** - Manages batch tracking for sync operations
- **`QueryRepository`** - Handles pending queries and batch operations
- **`BackupController`** - UI controller for backup/sync operations

### 2. Current Sync Flow

```
Mobile App Changes → Local Database → Query Batching → Push to Server
```

**Push Process:**
1. User performs actions (transactions, ledger updates, etc.)
2. Changes are queued as batched queries in local database
3. `pushPendingQueries()` sends batched changes to server
4. Server responds with success/failure status
5. Successfully synced batches are removed from local queue

### 3. Data Flow Architecture

```mermaid
graph TD
    A[Mobile App User Action] --> B[Local SQLite Database]
    B --> C[Query Repository - Batched Queries]
    C --> D[Sync Actions - Push Only]
    D --> E[Server API]
    E --> F[Web Platform Database]
    
    G[Web Platform User Action] --> F
    F --> H[No Pull Mechanism]
    H --> I[Mobile App Out of Sync]
```

## Critical Issues Identified

### Issue 1: App doesn't work if transactions are performed in web

**Problem:**
- Mobile app uses **unidirectional sync** (push-only)
- Pull functionality is completely disabled (commented out)
- Web-created transactions never reach mobile app

**Code Evidence:**
```dart
// In sync_actions.dart - Pull functionality is commented out
// Future<bool> pullPendingQueries(...) async { ... } // DISABLED
```

**Impact:**
- Mobile app operates on stale data
- Data inconsistencies between platforms
- Mobile users see incorrect balances and transaction histories

### Issue 2: Companies fail to load after doing any activity in web

**Problem:**
- Company/ledger data modified on web isn't synchronized to mobile
- Foreign key relationships break when local references don't match server state
- Mobile app continues to use outdated company information

**Technical Root Cause:**
```dart
// Mobile app never receives company updates from web
// No mechanism to refresh company data from server
```

**Impact:**
- Company selection screens show outdated data
- App crashes or freezes when accessing company-related features
- User unable to switch between companies

### Issue 3: Backup/sync fails after doing transactions from web

**Problem:**
- Batch ID tracking only works for mobile-originated changes
- No conflict resolution when web and mobile modify same data
- Server rejects conflicting changes causing sync failures

**Code Evidence:**
```dart
// In sync_actions.dart
List<String?> syncedBatchIDs = syncedBatchModels.map((e) => e.batchID).toList();
// These batch IDs only track mobile changes, not web changes
```

**Impact:**
- Complete sync failure
- Data loss risk
- Users unable to backup their data

## Current Sync Implementation Analysis

### Push Mechanism (Working)
```dart
// sync_actions.dart
Future<bool> pushPendingQueries({
  bool showNotificationAfterResult = false,
  String source = "BACKGROUND",
  bool all = false,
  String? singleBatchId,
  dynamic dbClient
}) async {
  // 1. Get pending local changes
  List<BatchedGroupModel> pendingBatchQueries = await QueryRepository()
      .getPendingQueries(all: all, dbClient: dbClient, singleBatchId: singleBatchId);
  
  // 2. Send to server
  ApiResponse apiResponse = await apiBaseHelper.post(
      apiBaseHelper.PUSH_PENDING_QUERY,
      ({"pending_queries": jsonEncode(pendingBatchQueries), "synced_batch_id": syncedBatchIDs}),
      accessToken: true);
  
  // 3. Clean up successful batches
  if (apiResponse.status) {
    await QueryRepository().deleteSyncedQueries(successBatchIds);
  }
}
```

### Pull Mechanism (Disabled)
```dart
// COMPLETELY COMMENTED OUT - This is the root cause
// Future<bool> pullPendingQueries(...) async {
//   // This would fetch web changes and apply to mobile
// }
```

### Batch Tracking System
```dart
// Tracks only mobile-originated changes
List<SyncedBatchModel> syncedBatchModels = 
    await SyncedBatchRepository().getSyncedBatchIDs(dbClient: dbClient);
```

## TODO: Comprehensive Fix Implementation

### Priority 1: Critical Fixes (Immediate)

#### TODO 1.1: Re-implement Pull Sync Mechanism
**File:** `lib/utilities/sync_actions.dart`

```dart
// TODO: Uncomment and fix pullPendingQueries function
Future<bool> pullPendingQueries({
  bool showNotificationAfterResult = false,
  String source = "BACKGROUND",
  int pullCount = 10,
  dynamic dbClient
}) async {
  // TODO: Implement proper conflict resolution
  // TODO: Add data validation before applying changes
  // TODO: Handle foreign key constraints
  // TODO: Add rollback mechanism for failed pulls
}
```

**Implementation Steps:**
1. Uncomment existing pull function
2. Add conflict detection logic
3. Implement data validation
4. Add proper error handling
5. Test with web-created transactions

#### TODO 1.2: Implement Bidirectional Sync
**File:** `lib/app/modules/backup_module/backup_controller.dart`

```dart
// TODO: Add bidirectional sync in BackupController
Future<Tuple2<bool, String>> syncAllDataBidirectional() async {
  // TODO: 1. Pull changes from server first
  Tuple2<bool, String> pullResult = await pullPendingQueriesWithMessage();
  
  // TODO: 2. Then push local changes
  Tuple2<bool, String> pushResult = await pushPendingQueriesWithMessage(all: true);
  
  // TODO: 3. Handle conflicts and errors
  // TODO: 4. Provide detailed sync status
}
```

#### TODO 1.3: Add Change Detection Mechanism
**New File:** `lib/utilities/change_detector.dart`

```dart
// TODO: Create change detection system
class ChangeDetector {
  // TODO: Compare local vs server timestamps
  // TODO: Detect data conflicts
  // TODO: Identify which records need updates
  // TODO: Flag potential data integrity issues
}
```

### Priority 2: Data Integrity Fixes

#### TODO 2.1: Implement Conflict Resolution
**New File:** `lib/utilities/conflict_resolver.dart`

```dart
// TODO: Create conflict resolution system
class ConflictResolver {
  // TODO: Last-write-wins strategy for simple conflicts
  // TODO: User-prompt strategy for complex conflicts
  // TODO: Backup conflicted data before resolution
  // TODO: Logging for all conflict resolutions
}
```

#### TODO 2.2: Add Data Validation Layer
**New File:** `lib/utilities/sync_validator.dart`

```dart
// TODO: Create data validation for sync operations
class SyncValidator {
  // TODO: Validate foreign key constraints
  // TODO: Check data type consistency
  // TODO: Verify required fields
  // TODO: Validate business logic constraints
}
```

#### TODO 2.3: Enhance Batch Tracking
**File:** `lib/app/repository/synced_batched_repository.dart`

```dart
// TODO: Extend batch tracking to include web changes
class SyncedBatchRepository {
  // TODO: Add server_batch_id tracking
  // TODO: Track web-originated changes
  // TODO: Implement batch conflict detection
  // TODO: Add batch rollback functionality
}
```

### Priority 3: Performance & Reliability

#### TODO 3.1: Implement Incremental Sync
**File:** `lib/utilities/sync_actions.dart`

```dart
// TODO: Add incremental sync based on timestamps
Future<bool> incrementalSync({
  DateTime? lastSyncTime,
  List<String>? specificTables
}) async {
  // TODO: Only sync changes after lastSyncTime
  // TODO: Support table-specific sync
  // TODO: Optimize for large datasets
}
```

#### TODO 3.2: Add Sync Queue Management
**New File:** `lib/utilities/sync_queue_manager.dart`

```dart
// TODO: Create intelligent sync queue management
class SyncQueueManager {
  // TODO: Prioritize critical data (companies, users)
  // TODO: Batch similar operations
  // TODO: Implement retry logic with exponential backoff
  // TODO: Handle network connectivity issues
}
```

#### TODO 3.3: Background Sync Improvements
**File:** `lib/main.dart`

```dart
// TODO: Improve background sync scheduling
// TODO: Add periodic pull sync (every 15 minutes)
// TODO: Implement smart sync based on user activity
// TODO: Add network-aware sync (WiFi vs mobile data)
```

### Priority 4: User Experience Enhancements

#### TODO 4.1: Sync Status Indicator
**New File:** `lib/app/widgets/sync_status_widget.dart`

```dart
// TODO: Create real-time sync status indicator
class SyncStatusWidget extends StatelessWidget {
  // TODO: Show sync progress
  // TODO: Display last sync time
  // TODO: Indicate conflicts requiring user attention
  // TODO: Provide manual sync trigger
}
```

#### TODO 4.2: Conflict Resolution UI
**New File:** `lib/app/modules/conflict_resolution/`

```dart
// TODO: Create UI for manual conflict resolution
// TODO: Show side-by-side data comparison
// TODO: Allow user to choose resolution strategy
// TODO: Provide conflict history
```

#### TODO 4.3: Enhanced Error Messaging
**File:** `lib/app/modules/backup_module/backup_controller.dart`

```dart
// TODO: Improve error messages for sync failures
// TODO: Provide actionable error descriptions
// TODO: Add troubleshooting suggestions
// TODO: Include error reporting mechanism
```

### Priority 5: Monitoring & Debugging

#### TODO 5.1: Sync Logging System
**New File:** `lib/utilities/sync_logger.dart`

```dart
// TODO: Comprehensive sync logging
class SyncLogger {
  // TODO: Log all sync operations with timestamps
  // TODO: Track sync performance metrics
  // TODO: Log conflict resolutions
  // TODO: Export logs for debugging
}
```

#### TODO 5.2: Sync Analytics
**New File:** `lib/utilities/sync_analytics.dart`

```dart
// TODO: Add sync analytics and monitoring
// TODO: Track sync success/failure rates
// TODO: Monitor sync performance
// TODO: Identify common sync issues
```

## Implementation Timeline

### Week 1: Critical Infrastructure
- [ ] TODO 1.1: Re-implement Pull Sync Mechanism
- [ ] TODO 1.2: Implement Bidirectional Sync
- [ ] TODO 2.1: Basic Conflict Resolution

### Week 2: Data Integrity
- [ ] TODO 2.2: Data Validation Layer
- [ ] TODO 2.3: Enhanced Batch Tracking
- [ ] TODO 1.3: Change Detection Mechanism

### Week 3: Performance & Reliability
- [ ] TODO 3.1: Incremental Sync
- [ ] TODO 3.2: Sync Queue Management
- [ ] TODO 3.3: Background Sync Improvements

### Week 4: User Experience
- [ ] TODO 4.1: Sync Status Indicator
- [ ] TODO 4.2: Conflict Resolution UI
- [ ] TODO 4.3: Enhanced Error Messaging

### Week 5: Monitoring & Testing
- [ ] TODO 5.1: Sync Logging System
- [ ] TODO 5.2: Sync Analytics
- [ ] Comprehensive testing across platforms

## Testing Strategy

### Unit Tests
- [ ] Test pull sync mechanism
- [ ] Test conflict resolution logic
- [ ] Test data validation
- [ ] Test batch tracking

### Integration Tests
- [ ] Test mobile-to-web sync
- [ ] Test web-to-mobile sync
- [ ] Test concurrent operations
- [ ] Test network failure scenarios

### End-to-End Tests
- [ ] Test complete sync workflows
- [ ] Test multi-device scenarios
- [ ] Test data integrity across platforms
- [ ] Test performance with large datasets

## Risk Assessment

### High Risk
- Data loss during sync conflicts
- Performance degradation with large datasets
- Network connectivity issues

### Medium Risk
- User confusion during conflict resolution
- Sync queue overflow
- Battery drain from background sync

### Low Risk
- UI/UX issues with sync indicators
- Minor logging performance impact

## Success Metrics

### Technical Metrics
- Sync success rate > 99%
- Average sync time < 30 seconds
- Zero data loss incidents
- Conflict resolution success rate > 95%

### User Experience Metrics
- User-reported sync issues < 1%
- App crash rate related to sync < 0.1%
- User satisfaction with sync reliability > 90%

## Conclusion

The current sync system's unidirectional design is the root cause of all reported issues. Implementing bidirectional sync with proper conflict resolution is critical for the app's multi-platform success. The proposed TODO roadmap provides a systematic approach to resolving these issues while maintaining data integrity and user experience.

---

**Document Version:** 1.0  
**Last Updated:** January 2025  
**Next Review:** After Priority 1 implementation
