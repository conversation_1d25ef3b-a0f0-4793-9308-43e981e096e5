import 'package:mobile_khaata_v2/app/model/database/transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/database/txn_image_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/purchase_model.dart';
import 'package:mobile_khaata_v2/app/repository/line_item_repository.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';
import 'package:mobile_khaata_v2/app/repository/txn_image_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:sqflite/sqflite.dart';

import 'package:tuple/tuple.dart';

class PurchaseRepository {
  final String tag = "PurchaseRepository";
  DatabaseHelper databaseHelper = DatabaseHelper();

  Future<List<PurchaseModel>> getAllPurchases() async {
    List<PurchaseModel> purchases = [];
    try {
      Database? dbClient = await databaseHelper.database;

      List<Map<String, dynamic>> txnDataListJson = (await dbClient!.rawQuery(
          'SELECT * FROM mk_transactions WHERE txn_type=? AND last_activity_type!=?',
          [TxnType.purchase, LastActivityType.Delete]));
      purchases = txnDataListJson.map((txnData) {
        return PurchaseModel.fromJson(txnData);
      }).toList();
      // txnData = TransactionModel.fromJson(txnDataJson);
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return purchases;
  }

  Future<Tuple3<PurchaseModel, List<LineItemDetailModel>, List<TxnImageModel>>>
      getPurchaseById(String txnID) async {
    TransactionModel? txnData = TransactionModel();
    List<LineItemDetailModel> items = [];
    List<TxnImageModel> images = [];
    TransactionRepository transactionRepository = TransactionRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();
    LineItemRepository lineItemRepository = LineItemRepository();

    try {
      // ignore: unnecessary_null_comparison
      if (null != txnID) {
        txnData = await transactionRepository.getTransactionByTxnId(txnID);
        items =
            await lineItemRepository.getLineDetailItemsForTransaction(txnID);
        images = await txnImageRepository.getImagesForTransaction(txnID);
      } else {
        // Throw dialog error for null id
      }
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return Tuple3(PurchaseModel.fromJson(txnData!.toJson()), items, images);
  }

  Future<String?> addPurchase(PurchaseModel purchaseModel,
      List<LineItemDetailModel> listItem, List<TxnImageModel> images) async {
    String? status;
    TransactionRepository transactionRepository = TransactionRepository();
    LineItemRepository lineItemRepository = LineItemRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();

    try {
      Database? dbClient = await databaseHelper.database;
      await dbClient!.transaction((batch) async {
        // var batch = txn.batch();

        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        String txnID = await transactionRepository.insert(
            TransactionModel.fromJson(purchaseModel.toJson()),
            dbClient: batch,
            batchID: batchID);

        await lineItemRepository.setLineDetailItemsForTransaction(
            txnID, listItem,
            dbClient: batch, batchID: batchID);

        await txnImageRepository.setImagesForTransaction(txnID, images,
            dbClient: batch, batchID: batchID);

        pushPendingQueries(singleBatchId: batchID, source: "TRIGGER");

        // await batch.commit(continueOnError: false, noResult: true);

        status = txnID;
      });
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }

  Future<bool> updatePurchase(PurchaseModel purchaseModel,
      List<LineItemDetailModel> listItem, List<TxnImageModel> images) async {
    bool status = false;
    TransactionRepository transactionRepository = TransactionRepository();
    LineItemRepository lineItemRepository = LineItemRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();

    try {
      Database? dbClient = await databaseHelper.database;
      await dbClient!.transaction((batch) async {
        // var batch = txn.batch();

        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        await transactionRepository.update(
            TransactionModel.fromJson(purchaseModel.toJson()),
            dbClient: batch,
            batchID: batchID);

        await lineItemRepository.deleteLineItemsForTransaction(
            purchaseModel.txnId ?? "",
            dbClient: batch,
            batchID: batchID);

        await lineItemRepository.setLineDetailItemsForTransaction(
            purchaseModel.txnId ?? "", listItem,
            dbClient: batch, batchID: batchID);

        await txnImageRepository.deleteImagesForTransaction(
            purchaseModel.txnId ?? "",
            dbClient: batch,
            batchID: batchID);

        await txnImageRepository.setImagesForTransaction(
            purchaseModel.txnId ?? "", images,
            dbClient: batch, batchID: batchID);

        pushPendingQueries(singleBatchId: batchID, source: "TRIGGER");
        // await batch.commit(continueOnError: false, noResult: true);

        status = true;
      });
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }

  Future<bool> isBillDuplicate(String billID) async {
    bool status = false;
    try {
      Database? dbClient = await databaseHelper.database;
      int? count = Sqflite.firstIntValue(await dbClient!.rawQuery(
          'SELECT COUNT(txn_id) AS total_txn FROM mk_transactions WHERE txn_type=? AND last_activity_type!=3 AND txn_ref_number_char=?',
          [TxnType.purchase, billID]));
      if (0 < count!) {
        // Bill id exist
        status = true;
      } else {
        status = false;
      }
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }
}
