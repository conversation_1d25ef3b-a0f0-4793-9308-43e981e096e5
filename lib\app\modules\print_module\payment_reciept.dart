import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:nepali_utils/nepali_utils.dart';

Future<Uint8List> receiptVoucher(PdfPageFormat pageFormat) async {
  final invoice = Invoice(
    sellerAddress: 'Hetauda-4',
    sellerName: 'Vedanta Technologies',
    receiverName: '<PERSON><PERSON><PERSON><PERSON>',
    receiptNumber: '1235665',
    remarks: 'Hello, Vedanta Technologies',
    receivedAmount: 108523.32,
    baseColor: PdfColors.lightBlue,
    accentColor: PdfColors.blueGrey900,
  );

  return await invoice.buildPdf(pageFormat);
}

class Invoice {
  Invoice(
      {this.sellerName,
      this.sellerAddress,
      this.receiverName,
      this.receiptNumber,
      this.receivedAmount,
      this.baseColor,
      this.accentColor,
      this.remarks});
  final String? sellerName;
  final String? sellerAddress;
  final String? receiverName;
  final String? receiptNumber;
  final double? receivedAmount;
  final PdfColor? baseColor;
  final PdfColor? accentColor;
  final String? remarks;

  static const _darkColor = PdfColors.blueGrey800;
  // ignore: constant_identifier_names
  static const _VedColor = PdfColor.fromInt(0xFF3560AF);

  var currencyInWords = NepaliNumberFormat(
    inWords: true,
    language: Language.english,
    isMonetory: true,
    decimalDigits: 2,
  );

  String? _logo;

  Future<Uint8List> buildPdf(PdfPageFormat pageFormat) async {
    // Create a PDF document.
    final doc = pw.Document();

    final font1 = await rootBundle.load('assets/roboto1.ttf');
    final font2 = await rootBundle.load('assets/roboto1.ttf');
    final font3 = await rootBundle.load('assets/roboto3.ttf');

    _logo = await rootBundle.loadString('assets/mobilekhata.svg');

    // Add page to the PDF
    doc.addPage(
      pw.MultiPage(
        // pageTheme: _buildTheme(
        //   pageFormat,
        //   pw.Font.ttf(font1),
        //   pw.Font.ttf(font2),
        //   pw.Font.ttf(font3),
        // ),
        header: _buildHeader,
        build: (context) => [
          _header(context),
          _newHeader(context),
          pw.SizedBox(height: 20),
          _contentFooter(context),
          _terms(context),
          pw.SizedBox(height: 20),
        ],
      ),
    );

    // Return the PDF file content
    return doc.save();
  }

  pw.Widget _buildHeader(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Expanded(
              child: pw.Column(
                mainAxisSize: pw.MainAxisSize.min,
                children: [
                  if (null != _logo) ...{
                    pw.Container(
                      margin: const pw.EdgeInsets.only(top: -20),
                      alignment: pw.Alignment.center,
                      height: 60,
                      child: _logo != null
                          ? pw.SvgImage(svg: _logo ?? "")
                          : pw.PdfLogo(),
                    ),
                  },
                  // pw.Container(
                  //   color: baseColor,
                  //   padding: pw.EdgeInsets.only(top: 3),
                  // ),
                ],
              ),
            ),
          ],
        ),
        if (context.pageNumber > 1) pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _header(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Expanded(
              child: pw.Column(
                children: [
                  pw.Container(
                    height: 30,
                    padding:
                        const pw.EdgeInsets.only(left: 0, top: 0, bottom: 0),
                    alignment: pw.Alignment.center,
                    child: pw.Text(
                      sellerName ?? "",
                      style: pw.TextStyle(
                        color: _VedColor,
                        fontWeight: pw.FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  pw.Container(
                      margin: const pw.EdgeInsets.only(),
                      child: pw.Text(
                        sellerAddress ?? "",
                        style: pw.TextStyle(
                          color: accentColor,
                          fontSize: 8,
                        ),
                      )),
                  pw.Container(
                    margin: const pw.EdgeInsets.only(bottom: 20, top: 10),
                    alignment: pw.Alignment.center,
                    child: pw.Text('Receipt Voucher',
                        style: pw.TextStyle(
                            color: PdfColors.red,
                            fontSize: 15,
                            fontWeight: pw.FontWeight.bold)),
                  )
                ],
              ),
            ),
          ],
        ),
        if (context.pageNumber > 1) pw.SizedBox(height: 20)
      ],
    );
  }

  // pw.PageTheme _buildTheme(
  //     PdfPageFormat pageFormat, pw.Font base, pw.Font bold, pw.Font italic) {
  //   return pw.PageTheme(
  //     pageFormat: pageFormat,
  //     theme: pw.ThemeData.withFont(
  //       base: base,
  //       bold: bold,
  //       italic: italic,
  //     ),
  //     buildBackground: (context) => pw.FullPage(
  //       ignoreMargins: true,
  //     ),
  //   );
  // }

  pw.Widget _newHeader(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Container(
                child: pw.Text(
                  'Received From:',
                  style: pw.TextStyle(
                    color: PdfColors.black,
                    lineSpacing: 10,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.Text(
                receiverName ?? "",
                style: pw.TextStyle(
                    fontSize: 10,
                    color: _darkColor,
                    fontWeight: pw.FontWeight.bold),
              ),
            ],
          ),
        ),
        pw.Expanded(
          flex: 1,
          child: pw.DefaultTextStyle(
            style: const pw.TextStyle(
              fontSize: 10,
              color: _darkColor,
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.end,
                  children: [
                    pw.Text(
                      'Voucher No.:\r',
                      style: pw.TextStyle(
                        color: PdfColors.black,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      receiptNumber ?? "",
                      style: pw.TextStyle(
                        color: PdfColors.black,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 5),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.end,
                  children: [
                    pw.Text(
                      'Date:\r',
                      style: pw.TextStyle(
                        color: PdfColors.black,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      _formatDate(DateTime.now()),
                      style: pw.TextStyle(
                        color: PdfColors.black,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  pw.Widget _contentFooter(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
                pw.Text(
                  'AMOUNT IN WORDS:',
                  style: const pw.TextStyle(
                    color: _VedColor,
                    fontSize: 9,
                  ),
                ),
              ]),
              pw.Row(children: [
                pw.Text(
                  '\r\r${currencyInWords.format(receivedAmount)}',
                  style: pw.TextStyle(
                    color: accentColor,
                    fontStyle: pw.FontStyle.italic,
                    fontSize: 9,
                  ),
                ),
              ]),
              pw.Container(
                margin: const pw.EdgeInsets.only(top: 40, bottom: 2),
                child: pw.Text(
                  'Remarks:',
                  style: pw.TextStyle(
                      color: PdfColors.red500,
                      fontWeight: pw.FontWeight.bold,
                      fontSize: 10),
                ),
              ),
              pw.Row(children: [
                pw.Text(remarks ?? "",
                    style: pw.TextStyle(color: accentColor, fontSize: 8.5))
              ])
            ],
          ),
        ),
        pw.Expanded(
          flex: 1,
          child: pw.DefaultTextStyle(
            style: const pw.TextStyle(
              fontSize: 10,
              color: _darkColor,
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text('Received:'),
                    pw.Text('Rs\r${(receivedAmount)}'),
                  ],
                ),
                pw.Divider(color: accentColor),
              ],
            ),
          ),
        ),
      ],
    );
  }

  pw.Widget _terms(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 1,
          child: pw.DefaultTextStyle(
              style: const pw.TextStyle(
                fontSize: 10,
                color: PdfColors.black,
              ),
              child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.SizedBox(height: 80),
                    pw.Divider(color: accentColor, endIndent: 70, indent: 70),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.center,
                        children: [
                          pw.Text('Paid By'),
                        ])
                  ])),
        ),
        pw.Expanded(
          flex: 1,
          child: pw.DefaultTextStyle(
              style: const pw.TextStyle(
                fontSize: 10,
                color: PdfColors.black,
              ),
              child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.SizedBox(height: 80),
                    pw.Divider(color: accentColor, endIndent: 70, indent: 70),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.center,
                        children: [
                          pw.Text('Received By'),
                        ])
                  ])),
        ),
      ],
    );
  }

  String _formatDate(DateTime date) {
    final format = DateFormat.yMMMd('en_US');
    return format.format(date);
  }
}
