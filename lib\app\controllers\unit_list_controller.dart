import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/unit_modal.dart';
import 'package:mobile_khaata_v2/app/repository/unit_repository.dart';

class UnitListController extends GetxController {
  var _isLoading = true.obs;

  bool get isLoading => _isLoading.value;

  set isLoading(bool flag) => _isLoading.value = flag;

  List<UnitModel> _units = [];

  UnitRepository _unitRepository = new UnitRepository();

  List<UnitModel> get units => _units;

  @override
  onInit() async {
    _isLoading(true);
    await fetchAll();
    _isLoading(false);
    super.onInit();
  }

  fetchAll() async {
    _isLoading(true);
    _units.clear();
    _units.addAll(await _unitRepository.getAllUnits());
    _isLoading(false);
  }

  // Future<List<UnitModel>>searchByTitle(String title) async {
  //   Log.d(title);
  //   return await _unitRepository.getUnitsByTitle(title);
  // }
}
