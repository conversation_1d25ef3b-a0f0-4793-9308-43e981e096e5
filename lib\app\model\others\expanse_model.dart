import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class ExpenseModel {
  ExpenseModel({
    this.txnId,
    this.txnDate,
    this.txnDateBS,
    this.txnRefNumberChar,
    this.txnType,
    this.ledgerId,
    this.txnCashAmount = 0.00,
    this.txnBalanceAmount = 0.00,
    this.txnDiscountPercent = 0.00,
    this.txnDiscountAmount = 0.00,
    this.txnTaxPercent = 0.00,
    this.txnTaxAmount = 0.00,
    this.txnDescription,
    this.txnDisplayName,
    this.lastActivityType,
    this.lastActivityAt,
    this.lastActivityBy,
    this.txnSubTotalAmount,
    this.txnTaxableTotalAmount,
    this.txnTotalAmount,
    this.expenseCategoryId,
    this.expenseCategoryName,
    this.txnPaymentTypeId,
    this.txnPaymentReference,
    this.chequeCurrentStatus,
    this.chequeIssueDate,
    this.chequeIssueDateBS,
    this.chequeTransferDate,
    this.chequeTransferredToAccId,
  });
  String? txnId;
  String? txnDate;
  String? txnDateBS;
  String? txnRefNumberChar;
  int? txnType;
  String? ledgerId;
  String? expenseCategoryId;
  String? expenseCategoryName;

  double? txnCashAmount;
  double? txnBalanceAmount;
  double? txnSubTotalAmount;
  double? txnTaxableTotalAmount;
  double? txnTotalAmount;

  double? txnDiscountPercent;
  double? txnDiscountAmount;
  double? txnTaxPercent;
  double? txnTaxAmount;
  String? txnDescription;
  String? txnDisplayName;

  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;

  String? txnPaymentTypeId;
  String? txnPaymentReference;
  int? chequeCurrentStatus;
  String? chequeIssueDate;
  String? chequeIssueDateBS;
  String? chequeTransferDate;
  String? chequeTransferredToAccId;

  factory ExpenseModel.fromJson(Map<String, dynamic> json) {
    // Log.d("json to expense model $json");
    DateTime txnDateTime = DateTime.parse(json["txn_date"]);
    String txnDate = DateFormat('y-MM-dd').format(txnDateTime);
    String txnDateBS = toDateBS(txnDateTime);

    String? chequeIssueBS;
    if (null != json["cheque_issue_date"]) {
      DateTime chequeIssueDateTime = DateTime.parse(json["cheque_issue_date"]);
      chequeIssueBS = toDateBS(chequeIssueDateTime);
    }
    double total = parseDouble(json['txn_balance_amount'])! +
        parseDouble(json['txn_cash_amount'])!;

    double taxableAmount = total - parseDouble(json['txn_tax_amount'])!;
    double subTotal = taxableAmount + parseDouble(json['txn_discount_amount'])!;

    return ExpenseModel(
        txnId: json["txn_id"],
        txnDate: txnDate,
        txnDateBS: txnDateBS,
        txnRefNumberChar: json["txn_ref_number_char"],
        txnType: json["txn_type"],
        ledgerId: json["ledger_id"],
        txnCashAmount: parseDouble(json["txn_cash_amount"]),
        txnBalanceAmount: parseDouble(json["txn_balance_amount"]),
        txnDiscountPercent: parseDouble(json["txn_discount_percent"]),
        txnDiscountAmount: parseDouble(json["txn_discount_amount"]),
        txnTaxPercent: parseDouble(json["txn_tax_percent"]),
        txnTaxAmount: parseDouble(json["txn_tax_amount"]),
        txnDescription: json["txn_description"],
        txnDisplayName: json["txn_display_name"],
        lastActivityType: json["last_activity_type"],
        lastActivityAt: json["last_activity_at"],
        lastActivityBy: json['last_activity_by'],
        txnSubTotalAmount: subTotal,
        txnTaxableTotalAmount: taxableAmount,
        expenseCategoryId: json["expense_category_id"],
        expenseCategoryName: json["expense_title"],
        txnTotalAmount: total,
        txnPaymentTypeId: json['txn_payment_type_id'].toString(),
        txnPaymentReference: json['txn_payment_reference'],
        chequeCurrentStatus: json['cheque_current_status'],
        chequeIssueDate: json['cheque_issue_date'],
        chequeTransferDate: json['cheque_transfer_date'],
        chequeTransferredToAccId: json['cheque_transferred_to_acc_id'],
        chequeIssueDateBS: chequeIssueBS);
  }

  Map<String, dynamic> toJson() => {
        "txn_id": txnId,
        "txn_date": txnDate,
        "txn_ref_number_char": txnRefNumberChar,
        "txn_type": txnType,
        "ledger_id": ledgerId,
        "txn_cash_amount": txnCashAmount,
        "txn_balance_amount": txnBalanceAmount,
        "txn_discount_percent": txnDiscountPercent,
        "txn_discount_amount": txnDiscountAmount,
        "txn_tax_percent": txnTaxPercent,
        "txn_tax_amount": txnTaxAmount,
        "txn_description": txnDescription,
        "txn_display_name": txnDisplayName,

        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        "last_activity_by": lastActivityBy,
        "txn_subtotal": txnSubTotalAmount,
        "txn_taxable_total": txnTaxableTotalAmount,
        "txn_total": txnTotalAmount,
        "expense_category_id": expenseCategoryId,
        "expense_title": expenseCategoryName,
        'txn_payment_type_id': txnPaymentTypeId,
        'txn_payment_reference': txnPaymentReference,
        // 'cheque_current_status': chequeCurrentStatus,
        'cheque_issue_date': chequeIssueDate,
      };
}
