import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_wise_transaction_model.dart';
import 'package:mobile_khaata_v2/app/repository/image_repository.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';

class PartyDetailController extends GetxController {
  final _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  final _txnLoading = true.obs;
  bool get txnLoading => _txnLoading.value;

  final _isSearching = true.obs;
  bool get isSearching => _isSearching.value;

  final _ledger = LedgerDetailModel().obs;
  LedgerDetailModel get ledger => _ledger.value;

  List<LedgerWiseTransactionModel> transactions = [];
  var _filteredTransactions = <LedgerWiseTransactionModel>[].obs;
  List<LedgerWiseTransactionModel> get filteredTransactions =>
      _filteredTransactions;

  // Use single image observable for consistency
  final Rx<ImageModel> image = ImageModel().obs;

  final LedgerRepository _ledgerRepository = LedgerRepository();
  final ImageRepository _imageRepository = ImageRepository();
  final TransactionRepository _transactionRepository = TransactionRepository();

  init(String ledgerId) async {
    _isLoading(true);
    try {
      // Load ledger details first, then load image based on ledger photo ID
      await loadLedgerDetail(ledgerId);
      await loadPartyImage();
      loadTransactions(ledgerId);
    } finally {
      _isLoading(false);
    }
  }

  Future<void> loadPartyImage() async {
    try {
      // Use the ledger photo ID from the loaded ledger details
      String? photoId = ledger.ledgerPhoto;

      if (photoId != null && photoId.isNotEmpty) {
        print('Fetching image for photo ID: $photoId');
        final imageData = await _imageRepository.getImageById(photoId);
        print(
            'Image data received: ${imageData.imageBitmap?.length ?? 'null'}');

        if (imageData != null &&
            imageData.imageBitmap != null &&
            imageData.imageBitmap!.isNotEmpty) {
          image.value = imageData;
          print(
              'Image set in controller: ${image.value.imageBitmap?.length ?? 'null'}');
        } else {
          print('No valid image data received from repository');
          image.value = ImageModel(); // Set empty image model
        }
      } else {
        print('No photo ID found in ledger details');
        image.value = ImageModel(); // Set empty image model
      }
    } catch (e) {
      print('Error loading party image: $e');
      image.value = ImageModel(); // Set empty image model on error
    }
  }

  loadLedgerDetail(String ledgerId) async {
    try {
      _ledger.value =
          await _ledgerRepository.getLedgerWithBalanceById(ledgerId);
      print('Ledger loaded with photo ID: ${ledger.ledgerPhoto}');
    } catch (e) {
      print('Error loading ledger detail: $e');
    }
  }

  loadTransactions(String ledgerId) async {
    _txnLoading(true);

    List<LedgerWiseTransactionModel> txns =
        await _transactionRepository.getLedgerWiseTransaction(ledgerId);

    transactions = txns;

    _filteredTransactions.clear();
    _filteredTransactions.addAll(transactions);

    _txnLoading(false);
  }

  searchTransaction(String searchString) {
    _isSearching(true);

    _filteredTransactions.clear();
    for (var item in transactions) {
      _filteredTransactions.addIf(
          item
              .toString()
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          item);
    }

    _isSearching(false);
  }

  reload() async {
    await init(ledger.ledgerId ?? "");
  }
}
