// ignore_for_file: unnecessary_null_comparison

import 'package:mobile_khaata_v2/app/model/database/transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/database/txn_image_model.dart';
import 'package:mobile_khaata_v2/app/model/others/expanse_model.dart';
import 'package:mobile_khaata_v2/app/repository/expense_category_repository.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';
import 'package:mobile_khaata_v2/app/repository/txn_image_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:sqflite/sqflite.dart';

import 'package:tuple/tuple.dart';

class ExpensesRepository {
  final String tag = "ExpensesRepository";
  DatabaseHelper databaseHelper = DatabaseHelper();

  //get from transaction repository and return from here
  Future<List<ExpenseModel>> getAllExpenses({String? forADDate}) async {
    List<ExpenseModel> expenses = [];
    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> txnDataListJson = (await dbClient!.rawQuery(
          'SELECT mk_transactions.*, '
          'SUM(txn_cash_amount) AS txn_cash_amount, '
          'SUM(txn_balance_amount) AS txn_balance_amount, '
          'mk_expense_category.expense_title '
          'FROM mk_transactions '
          'LEFT JOIN mk_expense_category ON mk_expense_category.expense_category_id =mk_transactions.expense_category_id  '
          'WHERE mk_transactions.txn_type=? AND mk_transactions.last_activity_type!=? AND mk_transactions.txn_date=? '
          'GROUP BY mk_expense_category.expense_category_id '
          'ORDER BY mk_transactions.txn_date DESC, mk_transactions.last_activity_at DESC ',
          [
            TxnType.expense,
            LastActivityType.Delete,
            forADDate ?? currentDate
          ]));
      expenses = txnDataListJson.map((txnData) {
        return ExpenseModel.fromJson(txnData);
      }).toList();
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return expenses;
  }

  Future<List<ExpenseModel>> getAllExpensesForCategoryID(String categoryID,
      {String? forADDate}) async {
    List<ExpenseModel> expenses = [];
    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> txnDataListJson = (await dbClient!.rawQuery(
          'SELECT mk_transactions.*, '
          // 'SUM(txn_cash_amount) AS txn_cash_amount, '
          // 'SUM(txn_balance_amount) AS txn_balance_amount, '
          'mk_expense_category.expense_title '
          'FROM mk_transactions '
          'LEFT JOIN mk_expense_category ON mk_expense_category.expense_category_id =mk_transactions.expense_category_id  '
          'WHERE mk_transactions.expense_category_id=? AND mk_transactions.txn_type=? AND mk_transactions.last_activity_type!=? AND mk_transactions.txn_date=? '
          'ORDER BY mk_transactions.txn_date DESC, mk_transactions.last_activity_at DESC ',
          [
            categoryID,
            TxnType.expense,
            LastActivityType.Delete,
            forADDate ?? currentDate
          ]));
      expenses = txnDataListJson.map((txnData) {
        return ExpenseModel.fromJson(txnData);
      }).toList();
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return expenses;
  }

  Future<List<ExpenseModel>> getTodaysExpenses() async {
    List<ExpenseModel> expenses = [];
    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> txnDataListJson = (await dbClient!.rawQuery(
          'SELECT mk_transactions.*, '
          'SUM(txn_cash_amount) AS txn_cash_amount, '
          'SUM(txn_balance_amount) AS txn_balance_amount, '
          'mk_expense_category.expense_title '
          'FROM mk_transactions '
          'LEFT JOIN mk_expense_category ON mk_expense_category.expense_category_id =mk_transactions.expense_category_id  '
          'WHERE mk_transactions.txn_type=? AND mk_transactions.last_activity_type!=? AND mk_transactions.txn_date=?'
          'GROUP BY mk_expense_category.expense_category_id',
          [TxnType.expense, LastActivityType.Delete, currentDate]));
      expenses = txnDataListJson.map((txnData) {
        return ExpenseModel.fromJson(txnData);
      }).toList();
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return expenses;
  }

  Future<Tuple2<ExpenseModel, List<TxnImageModel>>> getExpensesById(
      String txnID) async {
    TransactionModel txnData = TransactionModel();
    List<TxnImageModel> images = [];
    TransactionRepository transactionRepository = TransactionRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();

    try {
      if (null != txnID) {
        txnData = (await transactionRepository.getTransactionByTxnId(txnID))!;
        images = await txnImageRepository.getImagesForTransaction(txnID);
      } else {
        // Throw error for null id
      }
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return Tuple2(ExpenseModel.fromJson(txnData.toJson()), images);
  }

  Future<String?> addExpenses(
      ExpenseModel expenseModel, List<TxnImageModel> images) async {
    String? status;
    TransactionRepository transactionRepository = TransactionRepository();
    ExpensesCategoryRepository expensesCategoryRepository =
        ExpensesCategoryRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();

    try {
      Database? dbClient = await databaseHelper.database;
      await dbClient!.transaction((batch) async {
        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        expenseModel.expenseCategoryId ??=
            await expensesCategoryRepository.createIfNotExistByCategoryName(
                expenseModel.expenseCategoryName ?? "",
                batchID: batchID,
                dbClient: batch);

        String txnID = await transactionRepository.insert(
            TransactionModel.fromJson(expenseModel.toJson()),
            dbClient: batch,
            batchID: batchID);

        await txnImageRepository.setImagesForTransaction(txnID, images,
            dbClient: batch, batchID: batchID);

        // bool isSuccess = await pushPendingQueries(
        //     singleBatchId: batchID, source: "TRIGGER", dbClient: batch);

        // if (!isSuccess) {
        // throw CustomException("No Net");
        pushPendingQueries(
            singleBatchId: batchID, source: "TRIGGER", dbClient: batch);
        // }

        status = txnID;
      });
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }

  Future<bool> updateExpenses(
      ExpenseModel expenseModel, List<TxnImageModel> images) async {
    bool status = false;
    TransactionRepository transactionRepository = TransactionRepository();
    TxnImageRepository txnImageRepository = TxnImageRepository();
    ExpensesCategoryRepository expensesCategoryRepository =
        ExpensesCategoryRepository();

    try {
      Database? dbClient = await databaseHelper.database;

      await dbClient!.transaction((batch) async {
        // var batch = txn.batch();
        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        expenseModel.expenseCategoryId ??=
            await expensesCategoryRepository.createIfNotExistByCategoryName(
                expenseModel.expenseCategoryName ?? "",
                batchID: batchID,
                dbClient: batch);

        await transactionRepository.update(
            TransactionModel.fromJson(expenseModel.toJson()),
            dbClient: batch,
            batchID: batchID);

        await txnImageRepository.deleteImagesForTransaction(
            expenseModel.txnId ?? "",
            dbClient: batch,
            batchID: batchID);

        await txnImageRepository.setImagesForTransaction(
            expenseModel.txnId ?? "", images,
            dbClient: batch, batchID: batchID);

        // bool isSuccess = await pushPendingQueries(
        //     singleBatchId: batchID, source: "TRIGGER", dbClient: batch);
        // if (!isSuccess) {
        // throw CustomException("No Net");
        pushPendingQueries(
            singleBatchId: batchID, source: "TRIGGER", dbClient: batch);
        // }
        // await batch.commit(continueOnError: false, noResult: true);

        status = true;
      });
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }

  Future<bool> isBillDuplicate(String billID) async {
    bool status = false;
    try {
      Database? dbClient = await databaseHelper.database;

      int count = 0;

      if (billID.isNotEmpty) {
        count = Sqflite.firstIntValue(await dbClient!.rawQuery(
                'SELECT COUNT(txn_id) AS total_txn FROM mk_transactions WHERE txn_type=? AND last_activity_type!=3 AND txn_ref_number_char=? limit 1',
                [TxnType.expense, billID]))!
            .toInt();
      }

      if (0 < count) {
        // Bill id exist
        status = true;
      } else {
        status = false;
      }
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return status;
  }
}
