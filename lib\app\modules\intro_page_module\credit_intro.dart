import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_custom_clippers/flutter_custom_clippers.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class CreditIntro extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;

    return SafeArea(
      child: Container(
          decoration: BoxDecoration(color: Color(0xFFf5f8ff)),
          child: Stack(
            fit: StackFit.expand,
            children: [
              Positioned(
                left: 0,
                right: 0,
                top: screenWidth * 0.04,
                child: Text(
                  "उधारो",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: screenWidth * 0.10,
                      color: colorPrimary,
                      fontWeight: FontWeight.w800,
                      fontFamily: "ArialBlack"),
                ),
              ),
              Positioned(
                top: screenHeight * 0.09,
                left: 0,
                right: 0,
                bottom: screenHeight * 0.21,
                child: Clip<PERSON>ath(
                  clipper: DiagonalPathClipperTwo(),
                  child: Container(
                    width: double.infinity,
                    margin: EdgeInsets.symmetric(horizontal: 10),
                    padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                    decoration: BoxDecoration(
                        color: Colors.white, boxShadow: downShadow),
                    child: SingleChildScrollView(
                        physics: AlwaysScrollableScrollPhysics(),
                        child: Column(
                          children: [
                            Text(
                              "उठ्छ ! तुरुन्तै उठ्छ !!\nचिन्ता नलिनुहोस्  |",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  fontSize: screenWidth * 0.06,
                                  fontWeight: FontWeight.w800,
                                  fontFamily: "ArialBlack"),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Text(
                              "व्यवसाय र सम्बन्ध दुबैमा सुधार आउँछ\nढुक्क हुनुहोस् |",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  color: colorOrangeDark,
                                  fontSize: screenWidth * 0.045,
                                  fontWeight: FontWeight.w800,
                                  fontFamily: "ArialBlack"),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Image.asset(
                              'images/credit-small.png',
                              height: screenWidth * 0.3,
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Text(
                              "उधारो रहेका सम्पूर्ण रेकर्ड एकै ठाउँमा हेर्न सकिने  साथै भुक्तानीको लागि Reminder Message तथा Call गर्न सकिने",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: screenWidth * 0.045,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                          ],
                        )),
                  ),
                ),
              ),
            ],
          )),
    );
  }
}
