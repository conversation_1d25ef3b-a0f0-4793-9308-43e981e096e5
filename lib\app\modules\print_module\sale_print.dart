import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/controllers/registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/registration_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

Future<Uint8List> generateSaleBill(
  PdfPageFormat pageFormat, {
  String billTitle = "Sales Invoice",
  required AllTransactionModel transactionModel,
  required LedgerDetailModel ledgerDetailModel,
  List<LineItemDetailModel>? lineItems,
}) async {
  final lorem = pw.LoremText();
  final products = <Product>[
    ...lineItems!
        .map((e) => Product(
            e.sno.toString(),
            e.itemName ?? "",
            e.quantity ?? 0,
            e.pricePerUnit ?? 0,
            e.grossAmount ?? 0,
            e.discountPercent ?? 0,
            e.discountAmount ?? 0,
            e.totalAmount ?? 0,
            quantityName: e.lineItemUnitName))
        .toList(),
  ];

  RegistrationDetailController _registrationDetailController =
      Get.find(tag: "RegistrationDetailController");
  ImageModel sellerImageModel = _registrationDetailController.logo;

  RegistrationDetailModel myDetail =
      _registrationDetailController.registrationDetail;

  double total = 0.00;

  products.forEach((element) {
    total += element.netAmount;
  });

  final invoice = Invoice(
      myDetail: myDetail,
      partyModel: ledgerDetailModel,
      sellerImage: sellerImageModel,
      transaction: transactionModel,
      itemTotal: total,
      products: products,
      invoiceTitle: billTitle,
      baseColor: PdfColors.lightBlue,
      accentColor: PdfColors.black);

  return await invoice.buildPdf(pageFormat);
}

class Invoice {
  Invoice(
      {this.myDetail,
      this.transaction,
      this.products,
      this.itemTotal,
      this.partyModel,
      this.sellerImage,
      this.baseColor,
      this.accentColor,
      this.invoiceTitle});

  final String? invoiceTitle;
  final RegistrationDetailModel? myDetail;
  final ImageModel? sellerImage;
  final AllTransactionModel? transaction;
  final LedgerDetailModel? partyModel;
  final List<Product>? products;
  final PdfColor? baseColor;
  final PdfColor? accentColor;

  final double? itemTotal;

  static const _darkColor = PdfColors.blueGrey800;
  static const _lightColor = PdfColors.black;
  static const _VedColor = PdfColor.fromInt(0xFF3560AF);

  PdfColor get _baseTextColor =>
      baseColor!.luminance < 0.5 ? _lightColor : _darkColor;

  double get _total => transaction!.txnSubTotalAmount!;

  double get _grandTotal => transaction!.txnTotalAmount!;

  var currencyInWords = NepaliNumberFormat(
    inWords: true,
    language: Language.english,
    isMonetory: true,
    decimalDigits: 2,
  );

  String? _logo;

  Future<Uint8List> buildPdf(PdfPageFormat pageFormat) async {
    // Create a PDF document.
    final doc = pw.Document();

    final font1 = await rootBundle.load('assets/roboto1.ttf');
    final font2 = await rootBundle.load('assets/roboto1.ttf');
    final font3 = await rootBundle.load('assets/roboto3.ttf');

    _logo = await rootBundle.loadString('assets/mobilekhata.svg');

    // Add page to the PDF
    doc.addPage(
      pw.MultiPage(
        // margin: pw.EdgeInsets.all(200),
        // pageTheme: _buildTheme(
        //   pageFormat,
        //   // font1 != null ? pw.Font.ttf(font1) : null,
        //   // font2 != null ? pw.Font.ttf(font2) : null,
        //   // font3 != null ? pw.Font.ttf(font3) : null,
        // ),
        header: _buildHeader,
        footer: _buildFooter,
        build: (context) => [
          _header(context),
          _contentHeader(context),
          _contentTable(context),
          pw.SizedBox(height: 20),
          _contentFooter(context),
          _terms(context),
          pw.SizedBox(height: 20),
        ],
      ),
    );

    // Return the PDF file content
    return doc.save();
  }

  pw.Widget _buildHeader(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Expanded(
              child: pw.Column(
                mainAxisSize: pw.MainAxisSize.min,
                children: [
                  if (null != sellerImage!.imageBitmap) ...{
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.center,
                        children: [
                          pw.Container(
                            margin: pw.EdgeInsets.only(top: -20),
                            alignment: pw.Alignment.center,
                            height: 60,
                            width: 60,
                            child: sellerImage!.imageBitmap != null
                                ? pw.Image(pw.MemoryImage(Uint8List.fromList(
                                    sellerImage!.imageBitmap!)))
                                : pw.PdfLogo(),
                          ),
                        ])
                  },
                  // pw.Container(
                  //   color: baseColor,
                  //   padding: pw.EdgeInsets.only(top: 3),
                  // ),
                ],
              ),
            ),
          ],
        ),
        pw.Row(crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
          pw.Expanded(
              child: pw.Column(mainAxisSize: pw.MainAxisSize.min, children: [
            pw.Container(
              margin: pw.EdgeInsets.only(top: 10),
              alignment: pw.Alignment.center,
              child: pw.Text(
                myDetail!.businessName!,
                style: pw.TextStyle(
                  color: _VedColor,
                  fontWeight: pw.FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            pw.Container(
                margin: pw.EdgeInsets.only(),
                child: pw.Text(
                  myDetail!.businessAddress ?? '',
                  style: pw.TextStyle(
                    color: accentColor,
                    fontSize: 8,
                  ),
                )),
            if (null != myDetail!.tinNo && "" != myDetail!.tinNo)
              pw.Container(
                  margin: pw.EdgeInsets.only(),
                  child: pw.Text(
                    "${myDetail!.tinFlag ?? ''}" +
                        " No. :" +
                        "${myDetail!.tinNo ?? ''}",
                    style: pw.TextStyle(
                      color: accentColor,
                      fontSize: 8,
                    ),
                  )),
          ]))
        ]),
        if (context.pageNumber > 1) pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _header(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Expanded(
              child: pw.Column(
                children: [
                  pw.Container(
                    margin: pw.EdgeInsets.only(bottom: 20, top: 10),
                    alignment: pw.Alignment.center,
                    child: pw.Text(invoiceTitle!,
                        style: pw.TextStyle(
                            color: PdfColors.red,
                            fontSize: 15,
                            fontWeight: pw.FontWeight.bold)),
                  )
                ],
              ),
            ),
          ],
        ),
        pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _buildFooter(pw.Context context) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: [
        pw.Text(
          'Page ${context.pageNumber}/${context.pagesCount}',
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
        pw.Text(
          FOOTER_PRINT_TEXT,
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
      ],
    );
  }

  pw.PageTheme _buildTheme(
      PdfPageFormat pageFormat, pw.Font base, pw.Font bold, pw.Font italic) {
    return pw.PageTheme(
      pageFormat: pageFormat,
      theme: pw.ThemeData.withFont(
        base: base,
        bold: bold,
        italic: italic,
      ),
      buildBackground: (context) => pw.FullPage(
        ignoreMargins: true,
      ),
    );
  }

  pw.Widget _contentHeader(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Container(
                child: pw.Text(
                  'Invoice To: ${(null != transaction!.txnDisplayName && "" != transaction!.txnDisplayName) ? transaction!.txnDisplayName : partyModel!.ledgerTitle}',
                  style: pw.TextStyle(color: PdfColors.black, fontSize: 11),
                ),
              ),
              pw.Text(
                'Address: ${partyModel!.address ?? "N/A"}',
                style: pw.TextStyle(
                    fontSize: 10,
                    color: _darkColor,
                    fontWeight: pw.FontWeight.bold),
              ),
              if (null != partyModel!.tinNo && "" != partyModel!.tinNo)
                pw.Text(
                  '${partyModel!.tinFlag ?? ""} No.:\r${partyModel!.tinNo ?? ""}',
                  style: pw.TextStyle(
                      fontSize: 10,
                      color: _darkColor,
                      lineSpacing: 10,
                      fontWeight: pw.FontWeight.bold),
                ),
              pw.Container(margin: pw.EdgeInsets.only(bottom: 10))
            ],
          ),
        ),
        pw.Expanded(
          flex: 1,
          child: pw.DefaultTextStyle(
            style: const pw.TextStyle(
              fontSize: 10,
              color: _darkColor,
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.end,
                  children: [
                    pw.Text(
                      'Invoice No.: ${transaction!.txnRefNumberChar ?? "N/A"}',
                      style: pw.TextStyle(
                        color: PdfColors.black,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      '',
                      style: pw.TextStyle(
                        color: PdfColors.black,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                pw.SizedBox(height: 5),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.end,
                  children: [
                    pw.Text(
                      'Date:\r',
                      style: pw.TextStyle(
                        color: PdfColors.black,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                    pw.Text(
                      transaction!.txnDateBS!,
                      style: pw.TextStyle(
                        color: PdfColors.black,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  pw.Widget _contentFooter(pw.Context context) {
    double discountTotal = 0.00;
    double netAmount = 0.00;

    products!.forEach((element) {
      discountTotal += element.discountAmount;
      netAmount += element.netAmount;
    });

    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Row(mainAxisAlignment: pw.MainAxisAlignment.start, children: [
                pw.Text(
                  'AMOUNT IN WORDS:',
                  style: pw.TextStyle(
                    color: _VedColor,
                    fontWeight: pw.FontWeight.bold,
                    fontSize: 9,
                  ),
                ),
              ]),
              pw.Row(children: [
                pw.Text(
                  '${currencyInWords.format((transaction!.txnCashAmount! + transaction!.txnBalanceAmount!))}',
                  style: pw.TextStyle(
                    color: _darkColor,
                    fontStyle: pw.FontStyle.italic,
                    fontSize: 9,
                  ),
                ),
              ]),

              // pw.Text(
              //   paymentInfo,
              //   style: const pw.TextStyle(
              //     fontSize: 8,
              //     lineSpacing: 3,
              //     color: _darkColor,
              //   ),
              // ),
            ],
          ),
        ),
        pw.Expanded(
          flex: 1,
          child: pw.DefaultTextStyle(
            style: const pw.TextStyle(
              fontSize: 10,
              color: _darkColor,
            ),
            child: pw.Column(
              crossAxisAlignment: pw.CrossAxisAlignment.start,
              children: [
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text('Sub Total: '),
                    pw.Text(_formatCurrency(netAmount)),
                  ],
                ),
                pw.SizedBox(height: 5),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text('Discount:'),
                    pw.Text(_formatCurrency(
                        transaction!.txnDiscountAmount ?? 0.00)),
                  ],
                ),
                if (transaction!.txnTaxAmount! > 0.0)
                  pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text('${(transaction!.txnTaxPercent)}%\rTax:'),
                      pw.Text(_formatCurrency(transaction!.txnTaxAmount!)),
                    ],
                  ),
                pw.Divider(color: accentColor),
                pw.DefaultTextStyle(
                  style: pw.TextStyle(
                    color: _VedColor,
                    fontSize: 14,
                    fontWeight: pw.FontWeight.bold,
                  ),
                  child: pw.Row(
                    mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                    children: [
                      pw.Text('Total:'),
                      pw.Text(_formatCurrency((transaction!.txnCashAmount! +
                          transaction!.txnBalanceAmount!))),
                    ],
                  ),
                ),
                pw.SizedBox(height: 5),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text('Received:'),
                    pw.Text(_formatCurrency(transaction!.txnCashAmount!)),
                  ],
                ),
                pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
                  children: [
                    pw.Text('Due:'),
                    pw.Text(_formatCurrency(transaction!.txnBalanceAmount!)),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  pw.Widget _terms(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Container(
                padding: const pw.EdgeInsets.only(top: 63, bottom: 0),
                child: pw.Text(
                  'E.O. & E.',
                  style: pw.TextStyle(
                    fontSize: 10,
                    color: PdfColors.black,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),
        pw.Expanded(
          flex: 1,
          child: pw.DefaultTextStyle(
              style: pw.TextStyle(
                fontSize: 10,
                color: PdfColors.black,
              ),
              child: pw.Column(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.SizedBox(height: 50),
                    pw.Divider(color: accentColor, endIndent: 20, indent: 40),
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.center,
                        children: [
                          pw.Text('Received By'),
                        ])
                  ])),
        ),
      ],
    );
  }

  pw.Widget _contentTable(pw.Context context) {
    const tableHeaders = [
      'S.N.',
      'Item Description',
      'Quantity',
      'Price',
      'Amount',
      'Discount',
      'Net Amount'
    ];

    return pw.Table.fromTextArray(
      border: null,
      cellAlignment: pw.Alignment.centerLeft,
      headerDecoration: pw.BoxDecoration(
        borderRadius: const pw.BorderRadius.all(pw.Radius.circular(2)),
        color: PdfColors.blue100,
      ),
      headerHeight: 25,
      cellHeight: 40,
      cellAlignments: {
        0: pw.Alignment.centerLeft,
        1: pw.Alignment.centerLeft,
        2: pw.Alignment.center,
        3: pw.Alignment.center,
        4: pw.Alignment.centerRight,
        5: pw.Alignment.centerRight,
        6: pw.Alignment.centerRight,
      },
      columnWidths: {
        0: pw.FixedColumnWidth(40),
        1: pw.FlexColumnWidth(),
        2: pw.FixedColumnWidth(60),
        3: pw.FixedColumnWidth(60),
        4: pw.FixedColumnWidth(60),
        5: pw.FixedColumnWidth(60),
        6: pw.FixedColumnWidth(60)
      },
      headerStyle: pw.TextStyle(
        color: _baseTextColor,
        fontSize: 11,
        fontWeight: pw.FontWeight.bold,
      ),
      cellStyle: const pw.TextStyle(
        color: _darkColor,
        fontSize: 9,
      ),
      rowDecoration: pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: accentColor!,
            width: .5,
          ),
        ),
      ),
      headers: List<String>.generate(
        tableHeaders.length,
        (col) => tableHeaders[col],
      ),
      data: 0 == products!.length
          ? [
              ["", "", "", "", "", "", ""]
            ]
          : List<List<String>>.generate(
              products!.length,
              (row) => List<String>.generate(
                tableHeaders.length,
                (col) {
                  if (col == 5) {
                    return products![row].getIndex(col).replaceAll("(0.0%)",
                        "(${(products![row].discountAmount / products![row].total) * 100}%)");
                  } else {
                    return products![row].getIndex(col);
                  }
                },
              ),
            ),
    );
  }
}

String _formatCurrency(double amount) {
  return '${amount.toStringAsFixed(2)}';
}

String _formatDate(DateTime date) {
  final format = DateFormat.yMMMd('en_US');
  return format.format(date);
}

class Product {
  const Product(
      this.sku,
      this.productName,
      this.quantity,
      this.price,
      this.grossAmount,
      this.discountPercent,
      this.discountAmount,
      this.netAmount,
      {this.quantityName});

  final String? quantityName;
  final String sku;
  final String productName;
  final double quantity;
  final double price;
  final double grossAmount;
  final double discountPercent;
  final double discountAmount;
  final double netAmount;

  double get total => price * quantity;

  String getIndex(int index) {
    // disPercent =
    switch (index) {
      case 0:
        return sku;
      case 1:
        return productName;
      case 2:
        if (null != quantityName) {
          return quantity.toString() + "(${quantityName})";
        } else {
          return quantity.toString();
        }
        break;
      case 3:
        return _formatCurrency(price);
      case 4:
        return _formatCurrency(grossAmount);
      case 5:
        return _formatCurrency(discountAmount) + "\n(${discountPercent}%)";
      case 6:
        return _formatCurrency(netAmount);
    }
    return '';
  }
}
