PODS:
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - contacts_service (0.2.2):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_archive (0.0.1):
    - Flutter
    - ZIPFoundation (= 0.9.13)
  - flutter_image_compress (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_inappwebview (0.0.1):
    - Flutter
    - flutter_inappwebview/Core (= 0.0.1)
    - OrderedSet (~> 5.0)
  - flutter_inappwebview/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 5.0)
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_pdfview (1.0.2):
    - Flutter
  - flutter_share (0.0.1):
    - Flutter
  - FMDB (2.7.12):
    - FMDB/standard (= 2.7.12)
  - FMDB/Core (2.7.12)
  - FMDB/standard (2.7.12):
    - FMDB/Core
  - gallery_saver (0.0.1):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_review (2.0.0):
    - Flutter
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - open_file (0.0.1):
    - Flutter
  - OrderedSet (5.0.0)
  - package_info (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - printing (1.0.0):
    - Flutter
  - ReachabilitySwift (5.2.4)
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - share (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - SwiftyGif (5.4.5)
  - url_launcher_ios (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
  - ZIPFoundation (0.9.13)

DEPENDENCIES:
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - contacts_service (from `.symlinks/plugins/contacts_service/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_archive (from `.symlinks/plugins/flutter_archive/ios`)
  - flutter_image_compress (from `.symlinks/plugins/flutter_image_compress/ios`)
  - flutter_inappwebview (from `.symlinks/plugins/flutter_inappwebview/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - flutter_share (from `.symlinks/plugins/flutter_share/ios`)
  - gallery_saver (from `.symlinks/plugins/gallery_saver/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - open_file (from `.symlinks/plugins/open_file/ios`)
  - package_info (from `.symlinks/plugins/package_info/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/ios`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - printing (from `.symlinks/plugins/printing/ios`)
  - share (from `.symlinks/plugins/share/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - DKImagePickerController
    - DKPhotoGallery
    - FMDB
    - libwebp
    - Mantle
    - OrderedSet
    - ReachabilitySwift
    - SDWebImage
    - SDWebImageWebPCoder
    - SwiftyGif
    - ZIPFoundation

EXTERNAL SOURCES:
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  contacts_service:
    :path: ".symlinks/plugins/contacts_service/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_archive:
    :path: ".symlinks/plugins/flutter_archive/ios"
  flutter_image_compress:
    :path: ".symlinks/plugins/flutter_image_compress/ios"
  flutter_inappwebview:
    :path: ".symlinks/plugins/flutter_inappwebview/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  flutter_share:
    :path: ".symlinks/plugins/flutter_share/ios"
  gallery_saver:
    :path: ".symlinks/plugins/gallery_saver/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  open_file:
    :path: ".symlinks/plugins/open_file/ios"
  package_info:
    :path: ".symlinks/plugins/package_info/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/ios"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  printing:
    :path: ".symlinks/plugins/printing/ios"
  share:
    :path: ".symlinks/plugins/share/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  connectivity_plus: 413a8857dd5d9f1c399a39130850d02fe0feaf7e
  contacts_service: 849e1f84281804c8bfbec1b4c3eedcb23c5d3eca
  device_info_plus: e5c5da33f982a436e103237c0c85f9031142abed
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  file_picker: 817ab1d8cd2da9d2da412a417162deee3500fc95
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_archive: 1805fdd3a695fd284b43edb53dc35ca843fb761c
  flutter_image_compress: 5a5e9aee05b6553048b8df1c3bc456d0afaac433
  flutter_inappwebview: 3d32228f1304635e7c028b0d4252937730bbc6cf
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  flutter_local_notifications: 0c0b1ae97e741e1521e4c1629a459d04b9aec743
  flutter_pdfview: 2e4d13ffb774858562ffbdfdb61b40744b191adc
  flutter_share: 4be0208963c60b537e6255ed2ce1faae61cd9ac2
  FMDB: 728731dd336af3936ce00f91d9d8495f5718a0e6
  gallery_saver: 9fc173c9f4fcc48af53b2a9ebea1b643255be542
  image_picker_ios: 4a8aadfbb6dc30ad5141a2ce3832af9214a705b5
  in_app_review: a31b5257259646ea78e0e35fc914979b0031d011
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  open_file: 02eb5cb6b21264bd3a696876f5afbfb7ca4f4b7d
  OrderedSet: aaeb196f7fef5a9edf55d89760da9176ad40b93c
  package_info: 873975fc26034f0b863a300ad47e7f1ac6c7ec62
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  printing: eafa00acb682c0ca029d4d98d0798f55a1e27102
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  share: 0b2c3e82132f5888bccca3351c504d0003b3b410
  shared_preferences_foundation: 5b919d13b803cadd15ed2dc053125c68730e5126
  sqflite: 31f7eba61e3074736dff8807a9b41581e4f7f15a
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  url_launcher_ios: 68d46cc9766d0c41dbdc884310529557e3cd7a86
  webview_flutter_wkwebview: 2e2d318f21a5e036e2c3f26171342e95908bd60a
  ZIPFoundation: ae5b4b813d216d3bf0a148773267fff14bd51d37

PODFILE CHECKSUM: 153750f1c7e128abf89dae0a1b91ea8e1c1df888

COCOAPODS: 1.16.2
