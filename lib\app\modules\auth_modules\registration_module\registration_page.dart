// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/model/others/registration_model.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/otp_verification.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/registration_module/registration_controller.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:url_launcher/url_launcher.dart';

class NewRegistrationPage extends StatelessWidget {
  final formKey = GlobalKey<FormState>();

  final newRegistrationController = NewRegistrationController();

  NewRegistrationPage({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        toolbarHeight: 60,
        backgroundColor: colorPrimary,
        elevation: 4,
        centerTitle: true,
        title: RichText(
          // ignore: prefer_const_literals_to_create_immutables
          text: const TextSpan(children: [
            TextSpan(
              text: "mobile",
              style: TextStyle(
                  fontSize: 30, color: Colors.white, fontFamily: 'ArialBlack'),
            ),
            TextSpan(
              text: " खाता",
              style: TextStyle(
                fontSize: 35,
                color: Colors.white,
                fontWeight: FontWeight.w800,
                fontFamily: 'ArialBlack',
              ),
            ),
          ]),
        ),
      ),
      body: Container(
        padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 15),
        height: MediaQuery.of(context).size.height,
        child: Form(
          key: formKey,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 15,
                ),
                Text(
                  "New Registration",
                  style: TextStyle(
                      fontSize: 25,
                      color: colorPrimary,
                      fontWeight: FontWeight.bold,
                      decoration: TextDecoration.underline),
                ),
                const SizedBox(
                  height: 20,
                ),

                //====================================FullName
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      'व्यवसायीको नाम  (Proprietor’s Name) ',
                      style: RegformLabelStyle,
                    ),
                    const SizedBox(height: 10.0),
                    TextFormField(
                      autocorrect: false,
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.next,
                      style: formFieldTextStyle,
                      decoration: formFieldStyle.copyWith(
                        hintText: 'Proprietor Full Name',
                      ),
                      initialValue:
                          newRegistrationController.registration.fullName,
                      onChanged: (value) {
                        newRegistrationController.registration.fullName =
                            toBeginningOfSentenceCase(strTrim(value));
                      },
                      validator: (value) {
                        if (null == value || value.isEmpty) {
                          return 'व्यवसायीको नाम भर्नुहोस् (Fill Proprietor’s Name)';
                        }
                        return null;
                      },
                    )
                  ],
                ),

                //====================================Mobile No
                const SizedBox(height: 20.0),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      'मोबाईल नम्बर (Mobile No.)',
                      style: RegformLabelStyle,
                    ),
                    const SizedBox(height: 10.0),
                    TextFormField(
                      autocorrect: false,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      textInputAction: TextInputAction.next,
                      maxLength: 10,
                      style: formFieldTextStyle,
                      decoration: formFieldStyle.copyWith(
                          hintText: "Mobile No.", counterText: ''),
                      initialValue:
                          newRegistrationController.registration.mobileNo,
                      onChanged: (value) {
                        newRegistrationController.registration.mobileNo =
                            strTrim(value);
                      },
                      validator: (value) {
                        value = value?.trim();
                        if (value == null || value.isEmpty) {
                          return 'मोबाईल नम्बर भर्नुहोस् (Fill Mobile No.)';
                        }
                        if (10 != value.length) {
                          return 'अवैध मोबाइल नम्बर (Invalid Mobile No.)';
                        }
                        return null;
                      },
                    )
                  ],
                ),

                //====================================Company Name
                const SizedBox(height: 10.0),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      'पसल / व्यवसायको नाम\n(Shop / Company Name)',
                      style: RegformLabelStyle,
                    ),
                    const SizedBox(height: 10.0),
                    TextFormField(
                      autocorrect: false,
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.next,
                      style: formFieldTextStyle,
                      decoration: formFieldStyle.copyWith(
                        hintText: 'Shop / Company Name',
                      ),
                      initialValue:
                          newRegistrationController.registration.companyName,
                      onChanged: (value) {
                        newRegistrationController.registration.companyName =
                            toBeginningOfSentenceCase(strTrim(value));
                      },
                      validator: (value) {
                        value = value?.trim();
                        if (value == null || value.isEmpty) {
                          return 'पसल / व्यवसायको नाम भर्नुहोस् (Fill Shop/Company Name)';
                        }
                        return null;
                      },
                    )
                  ],
                ),

                //====================================PAN No
                const SizedBox(height: 20.0),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      'पान / मु. अ. कर नम्बर (Optional)\n(PAN / VAT No.)',
                      style: RegformLabelStyle,
                    ),
                    const SizedBox(height: 10.0),
                    TextFormField(
                      autocorrect: false,
                      keyboardType: TextInputType.number,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      textInputAction: TextInputAction.next,
                      style: formFieldTextStyle,
                      decoration:
                          formFieldStyle.copyWith(hintText: "PAN / VAT No."),
                      onChanged: (value) {
                        newRegistrationController.registration.panVatNo =
                            strTrim(value);
                      },
                    )
                  ],
                ),

                //===================================PAN No Type
                if (null != newRegistrationController.registration.panVatNo &&
                    newRegistrationController
                        .registration.panVatNo!.isNotEmpty) ...{
                  const SizedBox(height: 20.0),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'PAN हो / VAT हो ? छान्नुहोस्',
                        style: RegformLabelStyle,
                      ),
                      FormBuilderChoiceChip<String>(
                        name: "pan_vat_type",
                        selectedColor: colorPrimaryLight,
                        disabledColor: Colors.black12,
                        labelStyle: const TextStyle(color: Colors.white),
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                        ),
                        spacing: 20,
                        initialValue:
                            newRegistrationController.registration.panVatFlag,
                        onChanged: (value) {
                          newRegistrationController.registration.panVatFlag =
                              value!;
                        },
                        alignment: WrapAlignment.start,
                        options: [
                          FormBuilderChipOption(
                            value: "PAN",
                            child: SizedBox(
                              height: 30,
                              width: 80,
                              child: Row(
                                children: const [
                                  Icon(
                                    Icons.check_box,
                                    color: Colors.white,
                                  ),
                                  SizedBox(
                                    width: 5,
                                  ),
                                  Text("PAN"),
                                ],
                              ),
                            ),
                          ),
                          FormBuilderChipOption(
                            value: "VAT",
                            child: SizedBox(
                              height: 30,
                              width: 80,
                              child: Row(
                                children: const [
                                  Icon(
                                    Icons.check_box,
                                    color: Colors.white,
                                  ),
                                  SizedBox(
                                    width: 5,
                                  ),
                                  Text("VAT"),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                },

                //==================================Company Address
                const SizedBox(height: 20.0),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Text(
                      'पसल / व्यवसायको ठेगाना\n(Shop / Company Address)',
                      style: RegformLabelStyle,
                    ),
                    const SizedBox(height: 10.0),
                    TextFormField(
                      autocorrect: false,
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.done,
                      style: formFieldTextStyle,
                      decoration: formFieldStyle.copyWith(
                          hintText: "Shop / Company Address"),
                      initialValue:
                          newRegistrationController.registration.companyAddress,
                      onChanged: (value) {
                        newRegistrationController.registration.companyAddress =
                            toBeginningOfSentenceCase(strTrim(value));
                      },
                      validator: (value) {
                        value = value?.trim();
                        if (value == null || value.isEmpty) {
                          return 'पसल / व्यवसायको ठेगाना भर्नुहोस् (Fill Shop/Company Address)';
                        }
                        return null;
                      },
                    )
                  ],
                ),

                //==================================I accept
                const SizedBox(height: 15.0),
                Container(
                  alignment: Alignment.centerLeft,
                  child: Column(
                    children: <Widget>[
                      Row(
                        children: <Widget>[
                          Obx(() {
                            return Checkbox(
                              value: newRegistrationController
                                  .termsConditionChecked.value,
                              onChanged: (newValue) {
                                newRegistrationController
                                    .termsConditionChecked.value = newValue!;
                              },
                              activeColor: colorPrimary,
                              checkColor: Colors.white,
                            );
                          }),
                          Flexible(
                            child: GestureDetector(
                              onTap: () async {
                                Uri url = Uri.parse(
                                    "https://mobilekhaata.com/terms-and-conditions");
                                if (await canLaunchUrl(url)) {
                                  launchUrl(url);
                                }
                              },
                              child: Text(
                                "म सर्तहरू र गोपनीयता नीति स्वीकार गर्दछु । (I accept Terms & Conditions & Privacy Policy.)",
                                style: TextStyle(
                                    color: colorPrimary,
                                    fontSize: 15,
                                    decoration: TextDecoration.underline),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                //==================================Submit Button
                const SizedBox(height: 30.0),
                Center(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorPrimary,
                      foregroundColor: colorPrimaryLightest,
                    ),
                    child: Container(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 60, vertical: 10),
                        child: const Text(
                          "Save",
                          style: TextStyle(color: Colors.white, fontSize: 18),
                        )),
                    onPressed: () async {
                      FocusScope.of(context).unfocus();
                      if (formKey.currentState!.validate()) {
                        if (null !=
                                newRegistrationController
                                    .registration.panVatNo &&
                            newRegistrationController
                                .registration.panVatNo!.isNotEmpty) {
                          if (null ==
                                  newRegistrationController
                                      .registration.panVatFlag ||
                              newRegistrationController
                                  .registration.panVatFlag!.isEmpty) {
                            showAlertDialog(context,
                                alertType: AlertType.Error,
                                alertTitle: "Error",
                                message:
                                    'प्यान वा भ्याट छान्नुहोस्\n(Select PAN or VAT)');
                            return;
                          }
                        }

                        if (!newRegistrationController
                            .termsConditionChecked.value) {
                          showAlertDialog(context,
                              alertType: AlertType.Error,
                              alertTitle: "Error",
                              message: "Please accept the Terms & Conditions.");
                          return;
                        }

                        ProgressDialog progressDialog = ProgressDialog(context,
                            type: ProgressDialogType.normal,
                            isDismissible: false);
                        progressDialog.update(
                            message:
                                "Proceeding Registration. Please wait....");
                        await progressDialog.show();

                        ApiResponse apiResponse =
                            await newRegistrationController
                                .doRegistration(context);

                        await progressDialog.hide();

                        if (apiResponse.status) {
                          NewRegistrationApiResponseModel
                              newRegistrationResponse =
                              NewRegistrationApiResponseModel.fromJson(
                                  apiResponse.data);
                          Navigator.pushReplacementNamed(context, "/otp_verify",
                              arguments: OtpVerifyScreenArguments(
                                  newRegistrationResponse.token,
                                  newRegistrationResponse.registeredMobileNo));
                          showToastMessage(context,
                              message: apiResponse.msg ?? "", duration: 4);
                        } else {
                          showAlertDialog(context,
                              alertType: AlertType.Error,
                              alertTitle: "Error",
                              message: apiResponse.msg ?? "");
                        }
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ));
  }
}
