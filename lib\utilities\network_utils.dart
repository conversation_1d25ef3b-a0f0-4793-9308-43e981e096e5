import 'dart:async';
import 'dart:io';

import 'package:connectivity_plus/connectivity_plus.dart';

enum ConnectionType { wifi, mobile, offline }

class NetworkUtil {
  static final NetworkUtil _instance = NetworkUtil.internal();
  NetworkUtil.internal();
  factory NetworkUtil() => _instance;

  final Connectivity _connectivity = Connectivity();

  //This tracks the current connection status
  bool _hasConnection = false;

  ConnectionType? activeConnectionType;

  StreamController connectionChangeController = StreamController.broadcast();

  void initialize() {
    connectivity.onConnectivityChanged.listen(_connectionChange);
  }

  Stream get connectionChange => connectionChangeController.stream;

  Connectivity get connectivity {
    return _connectivity;
  }

  //flutter_connectivity's listener
  void _connectionChange(ConnectivityResult result) {
    checkInternetConnection();
  }

  //A clean up method to close our StreamController
  void dispose() {
    connectionChangeController.close();
  }

  Future<bool> checkConnectivity() async {
    bool connectivity = true;
    var connectivityResult = await _connectivity.checkConnectivity();

    if (connectivityResult == ConnectivityResult.mobile) {
      activeConnectionType = ConnectionType.mobile;
    } else if (connectivityResult == ConnectivityResult.wifi) {
      activeConnectionType = ConnectionType.wifi;
    } else {
      activeConnectionType = ConnectionType.offline;
      connectivity = false;
    }

    return connectivity;
  }

  Future<bool> checkInternetConnection() async {
    bool previousConnection = _hasConnection;

    bool connectivity = await checkConnectivity();
    if (!connectivity) {
      _hasConnection = false;
    } else {
      Socket? sock;
      try {
        sock = await Socket.connect('8.8.8.8', 53,
            timeout: const Duration(seconds: 20));
        _hasConnection = true;
        sock.destroy();
      } on SocketException catch (_) {
        _hasConnection = false;
      } on TimeoutException catch (_) {
        _hasConnection = false;
      }
    }

    //The connection status changed send out an update to all listeners
    if (previousConnection != _hasConnection) {
      connectionChangeController.add(_hasConnection);
    }

    return _hasConnection;
  }
}
