import 'package:mobile_khaata_v2/app/model/database/payment_type_model.dart';
import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/repository/bank_adjustment_repository.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:sqflite/sqflite.dart';
import 'package:tuple/tuple.dart';

class PaymentTypeRepository {
  final String tag = "PaymentTypeRepository";
  final String tableName = "mk_payment_types";
  DatabaseHelper databaseHelper = DatabaseHelper();
  QueryRepository queryRepository = QueryRepository();

  //========================================================================================= SYNCING ACTIONS
  Future<String> insert(PaymentTypeModel paymentTypeModel,
      {dynamic dbClient, String? batchID}) async {
    String id;
    dbClient ??= await databaseHelper.database;

    String primaryKeyPrefix = await getPrimaryKeyPrefix();
    paymentTypeModel.pmtTypeId = primaryKeyPrefix + uuidV4;

    paymentTypeModel.lastActivityAt = currentDateTime;
    paymentTypeModel.lastActivityBy = await getLastActivityBy();
    paymentTypeModel.lastActivityType = LastActivityType.New;

    await dbClient.insert(tableName, paymentTypeModel.toJson());

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.insert,
      data: paymentTypeModel.toJson(),
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    pushPendingQueries(
        source: "TRIGGER", dbClient: dbClient, singleBatchId: batchID);

    id = paymentTypeModel.pmtTypeId ?? "";
    return id;
  }

  Future<bool> update(PaymentTypeModel paymentTypeModel,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    paymentTypeModel.lastActivityAt = currentDateTime;
    paymentTypeModel.lastActivityBy = await getLastActivityBy();
    paymentTypeModel.lastActivityType = LastActivityType.Edit;

    String whereClause = "pmt_type_id = ?";
    List<dynamic> whereArgs = [paymentTypeModel.pmtTypeId];

    await dbClient.update(tableName, paymentTypeModel.toJson(),
        where: whereClause, whereArgs: whereArgs);

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.update,
      whereArgs: whereArgs,
      whereClause: whereClause,
      data: paymentTypeModel.toJson(),
    );
    await queryRepository.pushQuery(newQueryModel,
        dbClient: dbClient, batchID: batchID);

    pushPendingQueries(
        singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

    status = true;

    return status;
  }

  Future<Tuple2<bool, String>> delete(String id,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;
    String message = "";

    dbClient ??= await databaseHelper.database;
    String whereClause = "pmt_type_id = ?";
    List<dynamic> whereArgs = [id];
    Tuple2<bool, List<String>> hasRefRes =
        await DatabaseHelper.hasReferences(tablesWithColName: [
      {"table_name": "mk_transactions", "column_name": "txn_payment_type_id"},
      {
        "table_name": "mk_transactions",
        "column_name": "cheque_transferred_to_acc_id"
      },
      {"table_name": "mk_bank_adjustments", "column_name": "bank_adj_bank_id"},
      {
        "table_name": "mk_bank_adjustments",
        "column_name": "bank_adj_to_bank_id"
      }
    ], value: id);

    if (hasRefRes.item1) {
      //  there is  reference of this id in other table
      // so throw  error message
      message =
          "Cannot delete Bank. Please clear all transactions related to this bank before deleting";
    } else {
      // can soft delete for given id
      dbClient.update(
          tableName, {"last_activity_type": LastActivityType.Delete},
          where: whereClause, whereArgs: whereArgs);

      QueryModel newQueryModel = QueryModel(
          tableName: tableName,
          queryType: QueryType.update,
          whereArgs: whereArgs,
          whereClause: whereClause,
          data: {"last_activity_type": LastActivityType.Delete});

      await queryRepository.pushQuery(newQueryModel,
          batchID: batchID, dbClient: dbClient);

      pushPendingQueries(
          singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

      status = true;
      message = "Bank deleted successfully";
    }
    return Tuple2(status, message);
  }

  //=========================================================================================NON SYNCING ACTIONS
  Future<bool> checkUniqueShortName(String shortName, {String? bankId}) async {
    bool isUnique = await DatabaseHelper.isUnique(
      tableName: tableName,
      columnName: "pmt_type_short_name",
      checkValue: strTrim(shortName),
      keyColumn: "pmt_type_id",
      keyValue: bankId,
    );

    return isUnique;
  }

  Future<List<PaymentTypeModel>> getAll() async {
    List<PaymentTypeModel> modes = [];
    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> jsonCategoryList = (await dbClient!.query(
          tableName,
          where: 'last_activity_type!=${LastActivityType.Delete}'));
      modes =
          jsonCategoryList.map((e) => PaymentTypeModel.fromJson(e)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return modes;
  }

  Future<PaymentTypeModel?> getBankById(String bankId) async {
    PaymentTypeModel? mode;
    try {
      Database? dbClient = await databaseHelper.database;

      Map<String, dynamic> jsonCategoryList = (await dbClient!.query(
        tableName,
        where:
            "pmt_type_id='$bankId' AND last_activity_type!=${LastActivityType.Delete} AND pmt_type_type='BANK' ",
      ))
          .first;
      mode = PaymentTypeModel.fromJson(jsonCategoryList);
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return mode;
  }

  Future<List<PaymentTypeModel>> getAllBank() async {
    List<PaymentTypeModel> modes = [];
    try {
      Database? dbClient = await databaseHelper.database;

      String query = "SELECT "
          "mk_payment_types.*, "
          "(IFNULL(mk_payment_types.pmt_type_opening_balance, 0.00) + IFNULL(bal.txn_bank_balance, 0.00)) AS current_balance "
          "FROM mk_payment_types "
          "LEFT JOIN (SELECT tmp.bank_id, IFNULL(SUM(tmp.txn_amount),0.00) AS txn_bank_balance FROM (${BankAdjustmentRepository().bankBaseQuery()}) AS tmp GROUP BY tmp.bank_id) AS bal ON bal.bank_id=mk_payment_types.pmt_type_id "
          "WHERE mk_payment_types.pmt_type_type='BANK' AND mk_payment_types.last_activity_type<>3 "
          "ORDER BY mk_payment_types.pmt_type_bank_name ASC";

      List<Map<String, dynamic>> jsonList = await dbClient!.rawQuery(query);

      // Log.d("got banks ${jsonList}");

      modes = jsonList.map((e) => PaymentTypeModel.fromJson(e)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return modes;
  }
}
