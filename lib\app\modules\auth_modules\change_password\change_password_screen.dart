import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/change_password/change_password_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class ChangePasswordPage extends StatefulWidget {
  ChangePasswordPage({super.key});

  @override
  State<ChangePasswordPage> createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends State<ChangePasswordPage> {
  final String tag = "ChangePasswordPage";

  final changePasswordcontroller = ChangePasswordController();

  bool oldPassObs = true;

  bool newPassObs = true;

  bool verifyPassObs = true;

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        titleSpacing: -5,
        title: const Text("Change Password"),
      ),
      body: Form(
        key: changePasswordcontroller.formKey,
        child: Container(
          padding: const EdgeInsets.all(10),
          child: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'पुरानो पासवर्ड',
                      style: labelStyle2,
                    ),
                    const SizedBox(height: 5.0),
                    TextFormField(
                        // attribute: "old_password",
                        autocorrect: false,
                        keyboardType: TextInputType.text,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return "Old Password field is required";
                          }
                          return null;
                        },
                        obscureText: oldPassObs,
                        textInputAction: TextInputAction.next,
                        inputFormatters: [
                          FilteringTextInputFormatter.deny(
                              RegExp(r"\s\b|\b\s")),
                        ],
                        style: formFieldTextStyle,
                        decoration: formFieldStyle.copyWith(
                            suffixIcon: IconButton(
                              icon: Icon(
                                oldPassObs
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                color: Colors.grey,
                              ),
                              onPressed: () {
                                setState(() {
                                  oldPassObs = !oldPassObs;
                                });
                                changePasswordcontroller.update();
                              },
                            ),
                            labelText: "Old Password",
                            hintText: "Old Password"),
                        controller: changePasswordcontroller.oldPasswordCtrl)
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'नया पासवर्ड',
                      style: labelStyle2,
                    ),
                    const SizedBox(height: 5.0),
                    TextFormField(
                        // attribute: "new_password",
                        autocorrect: false,
                        keyboardType: TextInputType.text,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return "New Password field is required";
                          }
                          return null;
                        },
                        textInputAction: TextInputAction.next,
                        inputFormatters: [
                          FilteringTextInputFormatter.deny(
                              RegExp(r"\s\b|\b\s")),
                        ],
                        obscureText: newPassObs,
                        style: formFieldTextStyle,
                        decoration: formFieldStyle.copyWith(
                            labelText: "New Password",
                            suffixIcon: IconButton(
                              icon: Icon(
                                newPassObs
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                color: Colors.grey,
                              ),
                              onPressed: () {
                                setState(() {
                                  newPassObs = !newPassObs;
                                });
                                changePasswordcontroller.update();
                              },
                            ),
                            hintText: "New Password"),
                        controller: changePasswordcontroller.newPasswordCtrl
                        // controller: state.mobileCtrl,
                        )
                  ],
                ),
                const SizedBox(
                  height: 20,
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'पासवर्ड प्रमाणित गर्नुहोस्',
                      style: labelStyle2,
                    ),
                    const SizedBox(height: 5.0),
                    TextFormField(
                        //  attribute: "verify_password",
                        autocorrect: false,
                        keyboardType: TextInputType.text,
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return "Verify Password field is required";
                          }
                        },
                        obscureText: verifyPassObs,
                        textInputAction: TextInputAction.done,
                        inputFormatters: [
                          FilteringTextInputFormatter.deny(
                              RegExp(r"\s\b|\b\s")),
                        ],
                        style: formFieldTextStyle,
                        decoration: formFieldStyle.copyWith(
                            labelText: "Verify Password",
                            suffixIcon: IconButton(
                              icon: Icon(
                                verifyPassObs
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                color: Colors.grey,
                              ),
                              onPressed: () {
                                setState(() {
                                  verifyPassObs = !verifyPassObs;
                                });
                                changePasswordcontroller.update();
                              },
                            ),
                            hintText: "Verify Password"),
                        controller:
                            changePasswordcontroller.verifyNewPasswordCtrl
                        // controller: state.mobileCtrl,
                        )
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: BottomSaveCancelButton(
        shadow: false,
        enableFlag: !changePasswordcontroller.isSubmitting,
        onSaveBtnPressedFn: () {
          if (changePasswordcontroller.formKey.currentState!.validate()) {
            if (changePasswordcontroller.oldPasswordCtrl.text ==
                changePasswordcontroller.newPasswordCtrl.text) {
              showToastMessage(context,
                  message:
                      "पुरानो पासवर्ड र नयाँ पासवर्ड समान हुन सक्दैन | \nOld password and new password cannot be same.",
                  alertType: AlertType.Error);
              return;
            }

            if (changePasswordcontroller.newPasswordCtrl.text !=
                changePasswordcontroller.verifyNewPasswordCtrl.text) {
              showToastMessage(context,
                  message:
                      "नयाँ पासवर्ड र प्रमाणित पासवर्ड बराबर हुनुपर्दछ | \nNew password and verify password should be equal.",
                  alertType: AlertType.Error);
              return;
            }

            changePasswordcontroller.changePassword(context);
          }
        },
      ),
    ));
  }
}
