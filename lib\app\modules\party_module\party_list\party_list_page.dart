// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/components/pdf_card_view.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/party_list/party_list_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class PartyListPage extends StatefulWidget {
  const PartyListPage({super.key});

  @override
  _PartyListPageState createState() => _PartyListPageState();
}

class _PartyListPageState extends State<PartyListPage> {
  final String tag = "PartyListPage";

  final partyListController =
      Get.put(PartyListController(), tag: "PartyListController");

  bool showSearchBar = false;
  final FocusNode searchBoxFocus = FocusNode();
  String searchBoxPlaceholder = "पार्टी/ग्राहक खोज्नुहोस् (Party Search)";

  TabController? tabController;

  @override
  void initState() {
    partyListController.init();
    super.initState();
  }

  @override
  void dispose() {
    partyListController.onClose();
    super.dispose();
  }

  searchBoxOnChangeHandler(String searchString) {
    partyListController.searchLedger(searchString);
  }

  searchButtonOnPressedHandler() {
    showSearchBar = !showSearchBar;
    if (showSearchBar) searchBoxFocus.requestFocus();
    partyListController.searchLedger("");
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: false,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        toolbarHeight: 60,
        backgroundColor: colorPrimary,
        elevation: 0,
        leading: BackButton(
          onPressed: () => Navigator.pop(context, false),
        ),
        titleSpacing: -5.0,
        centerTitle: false,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            (showSearchBar)
                ? SizedBox(
                    width: MediaQuery.of(context).size.width * 0.56,
                    height: 50,
                    child: FormBuilderTextField(
                      name: "searchBox",
                      autocorrect: false,
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.done,
                      focusNode: searchBoxFocus,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                      ),
                      decoration: InputDecoration(
                          hintText: searchBoxPlaceholder,
                          contentPadding: EdgeInsets.zero,
                          prefixStyle: formFieldTextStyle,
                          hintStyle: const TextStyle(
                            color: Colors.white60,
                            fontSize: 18,
                          ),
                          border: InputBorder.none),
                      onChanged: (searchString) => searchBoxOnChangeHandler(
                        searchString!,
                      ),
                    ),
                  )
                : const Text(
                    "पार्टी/ग्राहकहरु (Party List)",
                    style: TextStyle(
                      fontSize: 18,
                      color: Colors.white,
                      fontFamily: 'HelveticaRegular',
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ],
        ),
        actions: <Widget>[
          IconButton(
            icon: (showSearchBar)
                ? const Icon(
                    Icons.cancel,
                    color: Colors.white,
                  )
                : const Icon(
                    Icons.search,
                    color: Colors.white,
                  ),
            onPressed: () => searchButtonOnPressedHandler(),
          )
        ],
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        decoration: BoxDecoration(
          color: backgroundColorShade,
        ),
        child: _PartyListView(partyListController),
      ),

      floatingActionButton: FloatingActionButton.extended(
        onPressed: () => partyListController.addButtonOnPressedHandler(context),
        // onPressed: ()async {
        //   print(await LedgerRepository().getAllLedger());
        // },
        backgroundColor: colorPrimary,
        elevation: 6,
        heroTag: 'addParty',
        icon: const Icon(Icons.add_circle),
        label: const Text('Add Party'),
      ),
    ));
  }
}

class _PartyListView extends StatelessWidget {
  final PartyListController partyListController;

  _PartyListView(this.partyListController);

  Widget _amountWidget(double balanceAmount) {
    bool isReceivable = (balanceAmount >= 0) ? true : false;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Transform.rotate(
          angle: (isReceivable) ? (3.14 / 1.3) : (-3.14 / 4),
          alignment: Alignment.center,
          child: CircleAvatar(
            radius: 8,
            backgroundColor: (isReceivable) ? colorGreenDark : colorRedLight,
            child: const Icon(
              Icons.arrow_forward,
              color: Colors.white,
              size: 12,
            ),
          ),
        ),
        const SizedBox(
          width: 5,
        ),
        RichText(
          text: TextSpan(
              text: (isReceivable) ? "लिनुपर्ने: " : "तिर्नुपर्ने: ",
              style: TextStyle(
                  color: (isReceivable) ? colorGreenDark : colorRedLight,
                  fontSize: 14),
              children: [
                TextSpan(
                  text: formatCurrencyAmount(balanceAmount.abs()),
                )
              ]),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        if (partyListController.partyLoading) {
          return Container(
              color: Colors.white,
              child: const Center(child: CircularProgressIndicator()));
        }
        if (partyListController.filteredLedgers.isEmpty) {
          return const SizedBox(
            width: double.infinity,
            child: Center(
              child: Text(
                "No Records",
                style: TextStyle(color: Colors.black54),
              ),
            ),
          );
        } else {
          return ListView.builder(
            itemCount: partyListController.filteredLedgers.length,
            shrinkWrap: true,
            itemBuilder: (context, int index) {
              LedgerDetailModel ledger =
                  partyListController.filteredLedgers[index];

              return Container(
                margin: EdgeInsets.only(
                    left: 5,
                    right: 5,
                    top: (0 == index) ? 10 : 0,
                    bottom: ((partyListController.filteredLedgers.length - 1) ==
                            index)
                        ? 20
                        : 5),
                child: PartyCardView(
                  ledger: ledger,
                ),
              );
            },
          );
        }
      },
    );
  }
}
