import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_dropdown.dart';
import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
import 'package:mobile_khaata_v2/app/model/database/payment_type_model.dart';
import 'package:mobile_khaata_v2/app/model/others/bank_transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/bank_statement_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/bank_statement/bank_statement_report_controller.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import 'package:nepali_date_picker/nepali_date_picker.dart';

// ignore: must_be_immutable
class BankStatementReport extends StatelessWidget {
  BankReportController _controller = BankReportController();
  String? startDate = currentDate;
  String? endDate = currentDate;
  String? bankID;
  List<LedgerDetailModel> ledgers = [];

  BankStatementReport() {
    _controller.loadBankList();
  }

  generate() async {
    if (null == bankID) {
      return;
    }
    _controller.generateBankStatementReport(
      startDate: startDate ?? "",
      endDate: endDate ?? "",
      bankId: bankID ?? "",
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        elevation: 0,
        titleSpacing: -5.0,
        backgroundColor: colorPrimary,
        title: Text(
          "बैंक स्टेट्मेन्ट (Bank Statement)",
          style: TextStyle(
              fontSize: 17,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
        actions: [
          Obx(() {
            return PrintButton(
              onPressed: (_controller.txnLoading ||
                      _controller.bankTransactions.isEmpty)
                  ? null
                  : () {
                      PaymentTypeModel l = new PaymentTypeModel();
                      if (null != bankID) {
                        l = _controller.banks.firstWhere(
                            (element) => element.pmtTypeId == bankID);
                      }
                      Navigator.pushNamed(context, '/printBankStatement',
                          arguments: BankStatementPrintPage(
                            transactions: _controller.bankTransactions,
                            bank: l,
                            startDate: startDate,
                            endDate: endDate,
                          ));
                    },
            );
          })
        ],
      ),
      body: GestureDetector(
        onTap: () => FocusScope.of(context).requestFocus(new FocusNode()),
        child: Container(
          color: Colors.black12,
          child: Column(
            // mainAxisSize: MainAxisSize.min,
            children: [
              //=============================transaction date filter
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: ReportCustomDatePickerTextField(
                        initialValue: toDateBS(DateTime.parse(startDate ?? "")),
                        hintText: "From Date",
                        onChange: (selectedDate) {
                          startDate =
                              toDateAD(NepaliDateTime.parse(selectedDate));
                          generate();
                        },
                      ),
                    ),
                    Expanded(
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 10),
                        child: Text(
                          "TO",
                          style: labelStyle2,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: ReportCustomDatePickerTextField(
                        initialValue: toDateBS(DateTime.parse(endDate ?? "")),
                        hintText: "To Date",
                        onChange: (selectedDate) {
                          endDate =
                              toDateAD(NepaliDateTime.parse(selectedDate));
                          generate();
                        },
                      ),
                    ),
                  ],
                ),
              ),

              Divider(
                height: 4,
                color: Colors.black54,
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10),
                child: Obx(() {
                  return CustomDropdown(
                      borderless: true,
                      style: formFieldTextStyle,
                      decoration: formFieldStyle,
                      value: bankID,
                      allowClear: false,
                      placeholder: "Select Bank",
                      options: [
                        ..._controller.banks.map((e) {
                          return {
                            'key': e.pmtTypeId,
                            'value': e.pmtTypeShortName
                          };
                        }).toList(),
                      ],
                      onChange: (value) {
                        // Log.d("on change $value");
                        bankID = value;
                        generate();
                      });
                }),
              ),
              Divider(
                height: 4,
                color: Colors.black54,
              ),

              Container(
                color: Colors.white,
                child: Column(
                  children: [
                    DefaultTextStyle(
                      style: TextStyle(
                          fontSize: 12,
                          color: textColor,
                          fontWeight: FontWeight.bold),
                      child: Container(
                        padding:
                            EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            //====================================1st Column
                            Expanded(
                              flex: 3,
                              child: Text(
                                "Date",
                              ),
                            ),

                            //====================================2nd Column
                            Expanded(
                              flex: 3,
                              child: Text(
                                "Description",
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                            ),

                            //====================================3rd Column
                            Expanded(
                              flex: 3,
                              child: Text(
                                "Withdrawal",
                                textAlign: TextAlign.right,
                              ),
                            ),

                            //====================================4th Column
                            Expanded(
                              flex: 3,
                              child: Text(
                                "Deposit",
                                textAlign: TextAlign.right,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Divider(
                      height: 4,
                      color: Colors.black54,
                    ),
                  ],
                ),
              ),

              Obx(() {
                if (_controller.txnLoading) {
                  return Container(
                      color: Colors.white,
                      child: Center(child: CircularProgressIndicator()));
                }

                if (_controller.bankTransactions.isEmpty) {
                  return Container(
                      color: Colors.white,
                      width: double.infinity,
                      child: Center(
                          child: Text(
                        "No Records",
                        style: TextStyle(color: Colors.black54),
                      )));
                } else {
                  return Expanded(
                      child: _TxnListView(_controller.bankTransactions));
                }
              }),
            ],
          ),
        ),
      ),
      // extendBody: true,
      bottomNavigationBar: Container(
        height: 45,
        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        color: colorPrimary,
        child: SingleChildScrollView(
          child: Obx(() {
            return DefaultTextStyle(
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              child: Text(
                "Closing Balance: ${formatCurrencyAmount(_controller.closingBalance.value, false)}",
                textAlign: TextAlign.right,
              ),
            );
          }),
        ),
      ),
    ));
  }
}

class _TxnListView extends StatelessWidget {
  final List<BankTransactionModel> _transactionList;

  _TxnListView(this._transactionList);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: _transactionList.length,
      // shrinkWrap: true,
      itemBuilder: (context, int index) {
        BankTransactionModel txn = _transactionList[index];

        return InkWell(
          // onTap: () => TransactionHelper.gotoTransactionEditPage(
          //     context, txn.txnId, txn.txnType),
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                DefaultTextStyle(
                  style: TextStyle(fontSize: 12, color: colorPrimary),
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        //====================================1st Column
                        Expanded(
                          flex: 3,
                          child: Text(
                            "${txn.txnDateBS}",
                          ),
                        ),

                        //====================================2nd Column
                        Expanded(
                          flex: 3,
                          child: Text(
                            "${txn.bankTransactionDesc ?? ''}",
                            overflow: TextOverflow.ellipsis,
                            maxLines: 3,
                          ),
                        ),

                        //====================================3rd Column
                        Expanded(
                          flex: 3,
                          child: Text(
                            txn.bankTransactionType ==
                                    TxnType.decreaseBankBalance
                                ? "${formatCurrencyAmount(txn.txnAmount ?? 0.00, false)}"
                                : "-",
                            textAlign: TextAlign.right,
                          ),
                        ),

                        //====================================4th Column
                        Expanded(
                          flex: 3,
                          child: Text(
                            txn.bankTransactionType ==
                                    TxnType.increaseBankBalance
                                ? "${formatCurrencyAmount(txn.txnAmount ?? 0.00, false)}"
                                : "-",
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                //Ad-d space if last element
                if (_transactionList.length - 1 == index) ...{
                  SizedBox(
                      // height: 100,
                      )
                },
              ],
            ),
          ),
        );
      },
    );
  }
}
