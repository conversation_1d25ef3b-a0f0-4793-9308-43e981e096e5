import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class BankAdjustmentModel {
  BankAdjustmentModel(
      {this.bankAdjId,
      this.bankAdjBankId,
      this.toBankAdjBankId,
      this.bankAdjDate,
      this.bankAdjDateBS,
      this.bankAdjType,
      this.bankAdjAmount,
      this.bankAdjDescription,
      this.lastActivityType,
      this.lastActivityAt,
      this.lastActivityBy});

  String? bankAdjId;
  String? bankAdjBankId;
  String? toBankAdjBankId;
  String? bankAdjDate;
  String? bankAdjDateBS;
  int? bankAdjType;
  double? bankAdjAmount;
  String? bankAdjDescription;
  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;

  factory BankAdjustmentModel.fromJson(Map<String, dynamic> json) {
    DateTime bankAdjDateTime = DateTime.parse(json["bank_adj_date"]);
    String bankAdjDate = DateFormat('y-MM-dd').format(bankAdjDateTime);
    String bankAdjDateBS = toDateBS(bankAdjDateTime);

    return BankAdjustmentModel(
        bankAdjId: json["bank_adj_id"],
        bankAdjBankId: json['bank_adj_bank_id'],
        toBankAdjBankId: json['bank_adj_to_bank_id'],
        bankAdjDate: bankAdjDate,
        bankAdjDateBS: bankAdjDateBS,
        bankAdjType: json["bank_adj_type"],
        bankAdjAmount: parseDouble(json["bank_adj_amount"]),
        bankAdjDescription: json["bank_adj_description"],
        lastActivityType: json["last_activity_type"],
        lastActivityAt: json["last_activity_at"],
        lastActivityBy: json['last_activity_by']);
  }

  Map<String, dynamic> toJson() => {
        "bank_adj_id": bankAdjId,
        "bank_adj_date": bankAdjDate,
        "bank_adj_to_bank_id": toBankAdjBankId,
        "bank_adj_type": bankAdjType,
        "bank_adj_amount": bankAdjAmount,
        "bank_adj_description": bankAdjDescription,
        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        "last_activity_by": lastActivityBy,
        "bank_adj_bank_id": bankAdjBankId
      };
}
