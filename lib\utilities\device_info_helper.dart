import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';

// import 'package:device_info_plus/device_info_plus.dart';

class DeviceInfoHelper {
  String? _deviceId;
  String? _platform;
  String? _model;
  String? _manufacturer;
  bool? _isPhysicalDevice;
  String? _name;

  String get deviceId {
    return _deviceId ?? "";
  }

  String get platform {
    return _platform ?? "";
  }

  String get model {
    return _model ?? "";
  }

  String get manufacturer {
    return _manufacturer ?? "";
  }

  String get name {
    return _name ?? "";
  }

  bool? get isPhysicalDevice {
    return _isPhysicalDevice;
  }

  Future<void> init() async {
    var deviceInfo = DeviceInfoPlugin();
    // final kera = await deviceInfo.androidInfo;
    // print("this is ${kera.id}");
    // print("this is ${kera.model}");
    // print("this is ${kera.manufacturer}");
    // print("this is ${kera.product}");
    // print("this is ${kera.isPhysicalDevice}");

    if (Platform.isIOS) {
      IosDeviceInfo iosDeviceInfo = await deviceInfo.iosInfo;
      _deviceId = iosDeviceInfo.identifierForVendor ?? "";
      _platform = "IOS";
      _model = iosDeviceInfo.model ?? "";
      _manufacturer = "Apple";
      _name = iosDeviceInfo.name ?? "";
      _isPhysicalDevice = iosDeviceInfo.isPhysicalDevice;
    } else {
      AndroidDeviceInfo androidDeviceInfo = await deviceInfo.androidInfo;
      _deviceId = androidDeviceInfo.id ?? "";
      _platform = "Android";
      _model = androidDeviceInfo.model ?? "";
      _manufacturer = androidDeviceInfo.manufacturer ?? "";
      _name = androidDeviceInfo.product ?? "";
      _isPhysicalDevice = androidDeviceInfo.isPhysicalDevice ?? true;
    }
  }
}
