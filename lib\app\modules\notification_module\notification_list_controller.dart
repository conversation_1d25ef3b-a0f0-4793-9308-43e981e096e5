import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/notification_model.dart';
import 'package:mobile_khaata_v2/app/repository/notification_repository.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class NotificationListController extends GetxController {
  final String tag = "NotificationListController";
  var _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  List<NotificationModel> _notifications = [];
  List<NotificationModel> get notifications => _notifications;

  getNotification() async {
    _isLoading(true);
    List<NotificationModel> nots =
        await NotificationRepository().getNotificationFor(date: currentDate);
    NotificationRepository().clearOldNotifications();
    _notifications.clear();
    _notifications.addAll(nots);
    _isLoading(false);
  }

  Future<bool> deletePrevious() async {
    bool status = await NotificationRepository().clearOldNotifications();
    return status;
  }
}
