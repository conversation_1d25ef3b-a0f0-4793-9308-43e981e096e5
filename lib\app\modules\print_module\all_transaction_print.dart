import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/registration_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/**
 * ALL TRANSACTION PDF REPORT GENERATOR - ENHANCED
 *
 * FIXED MAJOR ISSUES:
 * 1. Balance amount calculation logic completely wrong
 * 2. Running balance not properly calculated
 * 3. Cash in/out amounts incorrectly displayed
 * 4. Missing proper transaction type handling for balance calculations
 *
 * NEW LOGIC:
 * - Calculates proper running balance for each transaction
 * - Handles debit/credit transactions correctly
 * - Shows accurate cash flow amounts
 * - Maintains balance continuity throughout the report
 */
Future<Uint8List> generateCustomReport(
  PdfPageFormat pageFormat, {
  List<AllTransactionModel>? transactions,
  String partyText = "All Party",
  String txnTypeText = "All Transaction",
  String? startDate,
  String? endDate,
  double? totalSale,
  double? totalSaleReturn,
  double? totalPurchase,
  double? totalPurchaseReturn,
  double? totalExpense,
  double? totalPaymentIn,
  double? totalPaymentOut,
}) async {
  double totalAmount = 0.0;
  double calculatedTotalSale = totalSale ?? 0.0;
  double calculatedTotalSaleReturn = totalSaleReturn ?? 0.0;
  double calculatedTotalPurchase = totalPurchase ?? 0.0;
  double calculatedTotalPurchaseReturn = totalPurchaseReturn ?? 0.0;
  double calculatedTotalExpense = totalExpense ?? 0.0;
  double calculatedTotalPaymentIn = totalPaymentIn ?? 0.0;
  double calculatedTotalPaymentOut = totalPaymentOut ?? 0.0;

  // FIXED: Proper running balance calculation
  double runningBalance = 0.0;

  final products = <Product>[
    if (transactions != null)
      ...transactions.map((e) {
        totalAmount += e.txnTotalAmount ?? 0.00;

        // Only calculate totals if they weren't provided
        if (totalSale == null) {
          calculatedTotalSale +=
              ((TxnType.sales == e.txnType) ? e.txnTotalAmount : 0.00) ?? 0.00;
        }
        if (totalSaleReturn == null) {
          calculatedTotalSaleReturn +=
              ((TxnType.salesReturn == e.txnType) ? e.txnTotalAmount : 0.00) ??
                  0.00;
        }
        if (totalPurchase == null) {
          calculatedTotalPurchase +=
              ((TxnType.purchase == e.txnType) ? e.txnTotalAmount : 0.00) ??
                  0.00;
        }
        if (totalPurchaseReturn == null) {
          calculatedTotalPurchaseReturn +=
              ((TxnType.purchaseReturn == e.txnType)
                      ? e.txnTotalAmount
                      : 0.00) ??
                  0.00;
        }
        if (totalExpense == null) {
          calculatedTotalExpense +=
              ((TxnType.expense == e.txnType) ? e.txnTotalAmount : 0.00) ??
                  0.00;
        }
        if (totalPaymentIn == null) {
          calculatedTotalPaymentIn +=
              ((TxnType.paymentIn == e.txnType) ? e.txnTotalAmount : 0.00) ??
                  0.00;
        }
        if (totalPaymentOut == null) {
          calculatedTotalPaymentOut +=
              ((TxnType.paymentOut == e.txnType) ? e.txnTotalAmount : 0.00) ??
                  0.00;
        }

        // FIXED: Calculate proper cash amounts and running balance
        String cashInAmount = "";
        String cashOutAmount = "";
        double transactionAmount = e.txnTotalAmount ?? 0.0;

        // ENHANCED: Proper transaction impact on balance
        if (TxnType.cashInTransaction.contains(e.txnType)) {
          // Money coming in (Sales, Payment In, etc.)
          cashInAmount = transactionAmount.toStringAsFixed(2);
          runningBalance += transactionAmount;
        } else if (TxnType.cashOutTransaction.contains(e.txnType)) {
          // Money going out (Purchase, Payment Out, Expense, etc.)
          cashOutAmount = transactionAmount.toStringAsFixed(2);
          runningBalance -= transactionAmount;
        } else {
          // For other transaction types, use the transaction's balance impact
          runningBalance += (e.txnBalanceAmount ?? 0.0);
        }

        return Product(
          e.txnDateBS ?? "",
          e.txnRefNumberChar ?? "",
          e.ledgerTitle ?? "",
          e.txnTypeText ?? "",
          transactionAmount,
          cashInAmount,
          cashOutAmount,
          runningBalance, // FIXED: Use calculated running balance
        );
      }).toList()
  ];

  RegistrationDetailController registrationDetailController =
      Get.find<RegistrationDetailController>(
          tag: "RegistrationDetailController");
  ImageModel sellerImageModel = registrationDetailController.logo;

  RegistrationDetailModel myDetail =
      registrationDetailController.registrationDetail;

  final pdfReport = PDFReport(
    myDetail: myDetail,
    sellerImage: sellerImageModel,
    txnType: txnTypeText,
    partyName: partyText,
    startDate: startDate,
    endDate: endDate,
    products: products,
    baseColor: PdfColors.lightBlue,
    accentColor: PdfColors.blueGrey900,
    totalAmount: totalAmount,
    totalSale: calculatedTotalSale,
    totalSaleReturn: calculatedTotalSaleReturn,
    totalPurchase: calculatedTotalPurchase,
    totalPurchaseReturn: calculatedTotalPurchaseReturn,
    totalExpense: calculatedTotalExpense,
    totalPaymentIn: calculatedTotalPaymentIn,
    totalPaymentOut: calculatedTotalPaymentOut,
  );

  return await pdfReport.buildPdf(pageFormat);
}

class PDFReport {
  PDFReport({
    this.myDetail,
    this.sellerImage,
    this.products,
    this.baseColor,
    this.accentColor,
    this.startDate,
    this.endDate,
    this.partyName,
    this.txnType,
    this.totalAmount,
    this.totalSale,
    this.totalSaleReturn,
    this.totalPurchase,
    this.totalPurchaseReturn,
    this.totalExpense,
    this.totalPaymentIn,
    this.totalPaymentOut,
  });

  final RegistrationDetailModel? myDetail;
  final ImageModel? sellerImage;

  final List<Product>? products;
  final PdfColor? baseColor;
  final PdfColor? accentColor;
  final String? startDate;
  final String? endDate;
  final String? partyName;
  final String? txnType;

  final double? totalAmount;
  final double? totalSale;
  final double? totalSaleReturn;
  final double? totalPurchase;
  final double? totalPurchaseReturn;
  final double? totalExpense;
  final double? totalPaymentIn;
  final double? totalPaymentOut;

  static const _darkColor = PdfColors.blueGrey800;
  static const _lightColor = PdfColors.black;
  // ignore: constant_identifier_names
  static const _VedColor = PdfColor.fromInt(0xFF3560AF);
  PdfColor get _baseTextColor =>
      baseColor!.luminance < 0.5 ? _lightColor : _darkColor;

  var currencyInWords = NepaliNumberFormat(
    inWords: true,
    language: Language.english,
    isMonetory: true,
    decimalDigits: 2,
  );

  // Create a PDF document.
  Future<Uint8List> buildPdf(PdfPageFormat pageFormat) async {
    final doc = pw.Document();

    // Add page to the PDF
    doc.addPage(
      pw.MultiPage(
        header: _buildHeader,
        footer: _buildFooter,
        build: (context) => [
          _header(context),
          _newHeader(context),
          _contentTable(context),
          _tableTotal(context),
          pw.SizedBox(height: 20),
          // _contentFooter(context),
          pw.SizedBox(height: 20),
        ],
      ),
    );

    // Return the PDF file content
    return doc.save();
  }

  pw.Widget _tableTotal(pw.Context context) {
    return pw.Container(
        padding: const pw.EdgeInsets.only(top: 10),
        child: pw.Column(children: [
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Text("Total Amount: ${(totalAmount ?? 0.00).toStringAsFixed(2)}")
          ]),
          pw.SizedBox(height: 10),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Text(
                "Total Sales: Rs. ${(totalSale ?? 0.00).toStringAsFixed(2)}")
          ]),
          pw.SizedBox(height: 10),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Text(
                "Total Sales Return: Rs. ${(totalSaleReturn ?? 0.00).toStringAsFixed(2)}")
          ]),
          pw.SizedBox(height: 10),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Text(
                "Total Purchase: Rs. ${(totalPurchase ?? 0.00).toStringAsFixed(2)}")
          ]),
          pw.SizedBox(height: 10),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Text(
                "Total Purchase Return: Rs. ${(totalPurchaseReturn ?? 0.00).toStringAsFixed(2)}")
          ]),
          pw.SizedBox(height: 10),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Text(
                "Total Expenses: Rs. ${(totalExpense ?? 0.00).toStringAsFixed(2)}")
          ]),
          pw.SizedBox(height: 10),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Text(
                "Total Receipt: Rs. ${(totalPaymentIn ?? 0.00).toStringAsFixed(2)}")
          ]),
          pw.SizedBox(height: 10),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Text("Total Payment: Rs. ${totalPaymentOut!.toStringAsFixed(2)}")
          ]),
        ]));
  }

  pw.Widget _buildHeader(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Expanded(
              child: pw.Column(
                mainAxisSize: pw.MainAxisSize.min,
                children: [
                  if (null != sellerImage!.imageBitmap) ...{
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.center,
                        children: [
                          pw.Container(
                            margin: const pw.EdgeInsets.only(top: -20),
                            alignment: pw.Alignment.center,
                            height: 60,
                            width: 60,
                            child: sellerImage?.imageBitmap != null
                                ? pw.Image(pw.MemoryImage(Uint8List.fromList(
                                    sellerImage!.imageBitmap!)))
                                : pw.PdfLogo(),
                          ),
                        ])
                  },
                  // pw.Container(
                  //   color: baseColor,
                  //   padding: pw.EdgeInsets.only(top: 3),
                  // ),
                ],
              ),
            ),
          ],
        ),
        pw.Row(crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
          pw.Expanded(
              child: pw.Column(mainAxisSize: pw.MainAxisSize.min, children: [
            pw.Container(
              margin: const pw.EdgeInsets.only(top: 10),
              alignment: pw.Alignment.center,
              child: pw.Text(
                myDetail?.businessName ?? "",
                style: pw.TextStyle(
                  color: _VedColor,
                  fontWeight: pw.FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            pw.Container(
                margin: pw.EdgeInsets.only(),
                child: pw.Text(
                  myDetail?.businessAddress ?? "",
                  style: pw.TextStyle(
                    color: accentColor,
                    fontSize: 8,
                  ),
                )),
            if (myDetail != null &&
                null != myDetail?.tinNo &&
                "" != myDetail?.tinNo)
              pw.Container(
                  margin: const pw.EdgeInsets.only(),
                  child: pw.Text(
                    "${myDetail?.tinFlag ?? ''} No. :${myDetail?.tinNo ?? ''}",
                    style: pw.TextStyle(
                      color: accentColor,
                      fontSize: 8,
                    ),
                  )),
          ]))
        ]),
        if (context.pageNumber > 1) pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _header(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Expanded(
              child: pw.Column(
                children: [
                  pw.Container(
                    margin: const pw.EdgeInsets.only(bottom: 20, top: 10),
                    alignment: pw.Alignment.center,
                    child: pw.Text('All Transaction Report',
                        style: pw.TextStyle(
                            color: PdfColors.red,
                            fontSize: 15,
                            fontWeight: pw.FontWeight.bold)),
                  )
                ],
              ),
            ),
          ],
        ),
        pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _buildFooter(pw.Context context) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: [
        // pw.Container(
        //   height: 20,
        //   width: 100,
        //   child: pw.BarcodeWidget(
        //     barcode: pw.Barcode.pdf417(),
        //     data: 'Invoice# $invoiceNumber',
        //   ),
        // ),
        pw.Text(
          'Page ${context.pageNumber}/${context.pagesCount}',
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
        pw.Text(
          FOOTER_PRINT_TEXT,
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
      ],
    );
  }

  // pw.PageTheme _buildTheme(
  //     PdfPageFormat pageFormat, pw.Font base, pw.Font bold, pw.Font italic) {
  //   return pw.PageTheme(
  //     pageFormat: pageFormat,
  //     theme: pw.ThemeData.withFont(
  //       base: base,
  //       bold: bold,
  //       italic: italic,
  //     ),
  //     buildBackground: (context) => pw.FullPage(
  //       ignoreMargins: true,
  //     ),
  //   );
  // }

  pw.Widget _newHeader(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Container(
                height: 15,
                child: pw.Text(
                  'Party Name:\r $partyName',
                  style: pw.TextStyle(
                    color: PdfColors.black,
                    lineSpacing: 10,
                    fontSize: 10,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.Container(
                height: 15,
                child: pw.Text(
                  'Transaction type:\r $txnType',
                  style: pw.TextStyle(
                    color: PdfColors.black,
                    lineSpacing: 5,
                    fontSize: 10,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              ),
              pw.Container(
                height: 20,
                child: pw.Text(
                  'Duration:\rFrom\r$startDate\rTo\r$endDate',
                  style: pw.TextStyle(
                    color: PdfColors.black,
                    lineSpacing: 5,
                    fontSize: 10,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  pw.Widget _contentTable(pw.Context context) {
    const tableHeaders = [
      'Date',
      'Bill No.',
      'Name',
      'Type',
      'Total Amount',
      'Received Amount',
      'Paid Amount',
      'Balance Amount',
    ];

    return pw.Table.fromTextArray(
      border: null,
      cellAlignment: pw.Alignment.centerLeft,
      headerDecoration: pw.BoxDecoration(
        color: PdfColors.blue100,
      ),
      headerHeight: 35,
      cellHeight: 40,
      cellAlignments: {
        0: pw.Alignment.centerLeft,
        1: pw.Alignment.centerLeft,
        2: pw.Alignment.centerLeft,
        3: pw.Alignment.center,
        4: pw.Alignment.center,
        5: pw.Alignment.center,
        6: pw.Alignment.center,
        7: pw.Alignment.center
      },
      headerStyle: pw.TextStyle(
        color: _baseTextColor,
        fontSize: 9.5,
        fontWeight: pw.FontWeight.bold,
      ),
      cellStyle: pw.TextStyle(
        color: accentColor,
        fontSize: 8,
      ),
      rowDecoration: pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: accentColor!,
            width: .5,
          ),
        ),
      ),
      headers: List<String>.generate(
        tableHeaders.length,
        (col) => tableHeaders[col],
      ),
      data: List<List<String>>.generate(
        products!.length,
        (row) => List<String>.generate(
          tableHeaders.length,
          (col) => products![row].getIndex(col),
        ),
      ),
    );
  }
}

class Product {
  const Product(
    this.date,
    this.billNo,
    this.productName,
    this.txnType,
    this.totalAmount,
    this.receivedAmount,
    this.paidAmount,
    this.balanceAmount,
  );

  final String date;
  final String billNo;
  final String productName;
  final String txnType;
  final double totalAmount;
  final String receivedAmount;
  final String paidAmount;
  final double balanceAmount;

  String getIndex(int index) {
    switch (index) {
      case 0:
        return date;
      case 1:
        return billNo;
      case 2:
        return productName;
      case 3:
        return txnType;
      case 4:
        return totalAmount.toString();
      case 5:
        return receivedAmount.toString();
      case 6:
        return paidAmount.toString();
      case 7:
        return balanceAmount.toString();
    }
    return '';
  }
}
