// import 'package:flutter/material.dart';
// import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
// import 'package:mobile_khaata_v2/app/modules/payment_module/add_edit_payment_screen.dart';
// import 'package:mobile_khaata_v2/app/modules/receipt_module/add_edit_receipt_page.dart';
// import 'package:mobile_khaata_v2/utilities/styles.dart';

// // ignore: must_be_immutable
// class PartyCardView extends StatelessWidget {
//   final LedgerDetailModel ledger;
//   final bool? showBothPayReceiveBtn;
//   bool? _isReceivable;

//   PartyCardView(
//       {super.key, required this.ledger, this.showBothPayReceiveBtn = true}) {
//     _isReceivable = (ledger.balanceAmount! >= 0) ? true : false;
//   }

//   Widget _amountWidget(double balanceAmount) {
//     bool isReceivable = (balanceAmount >= 0) ? true : false;

//     return Row(
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         Transform.rotate(
//           angle: (isReceivable) ? (3.14 / 1.3) : (-3.14 / 4),
//           alignment: Alignment.center,
//           child: CircleAvatar(
//             radius: 8,
//             backgroundColor: (isReceivable) ? colorGreenDark : colorRedLight,
//             child: const Icon(
//               Icons.arrow_forward,
//               color: Colors.white,
//               size: 12,
//             ),
//           ),
//         ),
//         const SizedBox(
//           width: 5,
//         ),
//         RichText(
//           text: TextSpan(
//               text: (isReceivable) ? "लिनुपर्ने: " : "तिर्नुपर्ने: ",
//               style: TextStyle(
//                   color: (isReceivable) ? colorGreenDark : colorRedLight,
//                   fontSize: 14),
//               children: [
//                 TextSpan(
//                   text: formatCurrencyAmount(balanceAmount.abs()),
//                 )
//               ]),
//         ),
//       ],
//     );
//   }

//   Widget _receiveBtn(BuildContext context) {
//     return ElevatedButton(
//       // elevation: 4,
//       // textColor: Colors.black54,
//       // color: colorGreen,
//       // padding: EdgeInsets.zero,
//       // splashColor: colorPrimaryLighter,
//       // shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
//       onPressed: () => Navigator.pushNamed(context, '/creditReceive',
//           arguments: AddEditReceiptPage(
//             ledgerId: ledger.ledgerId,
//           )),
//       child: Container(
//         padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
//         child: const Text(
//           "लिनुहोस् (Take)\n[Cr.]",
//           textAlign: TextAlign.center,
//           style: TextStyle(color: Colors.white),
//         ),
//       ),
//     );
//   }

//   Widget _payBtn(BuildContext context) {
//     return ElevatedButton(
//       // elevation: 4,
//       // textColor: Colors.black54,
//       // color: colorRedLight,
//       // padding: EdgeInsets.zero,
//       // splashColor: colorPrimaryLighter,
//       // shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
//       onPressed: () => Navigator.pushNamed(context, '/creditPay',
//           arguments: AddEditPaymentPage(
//             ledgerId: ledger.ledgerId,
//           )),
//       child: Container(
//         padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 4),
//         child: const Text(
//           "दिनुहोस् (Give)\n[Dr.]",
//           textAlign: TextAlign.center,
//           style: TextStyle(color: Colors.white),
//         ),
//       ),
//     );
//   }

//   Widget _showBothPayReceiveBtn(BuildContext context) {
//     return Row(
//       children: [
//         _receiveBtn(context),
//         const SizedBox(
//           width: 20,
//         ),
//         _payBtn(context),
//       ],
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Card(
//       child: Container(
//         padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
//         child: Column(
//           children: [
//             Row(
//               children: [
//                 Expanded(
//                   child: InkWell(
//                     onTap: () =>
//                         Navigator.pushNamed(context, '/partyLedgerDetail',
//                             arguments: PartyDetailPage(
//                               ledgerId: ledger.ledgerId,
//                             )),
//                     child: Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         Text(
//                           "${ledger.ledgerTitle}",
//                           style: TextStyle(
//                               fontWeight: FontWeight.bold,
//                               fontSize: 14,
//                               color: textColor),
//                           overflow: TextOverflow.ellipsis,
//                           maxLines: 2,
//                         ),
//                         const SizedBox(
//                           height: 10,
//                         ),
//                         _amountWidget(ledger.balanceAmount),
//                         const SizedBox(
//                           height: 10,
//                         ),
//                       ],
//                     ),
//                   ),
//                 ),
//                 const SizedBox(
//                   width: 20,
//                 ),
//                 Column(
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   children: [
//                     if (null != ledger.mobileNo)
//                       Text(
//                         "${ledger.mobileNo}",
//                         textAlign: TextAlign.center,
//                       ),

//                     LedgerSmsCallReminderGroup(
//                       ledger: ledger,
//                     ),

//                     // Row(
//                     //   children: [
//                     //     Container(
//                     //       width: 35,
//                     //       child: IconButton(
//                     //         icon: Icon(Icons.phone, size: 20, color: colorPrimary,),
//                     //         onPressed: () {
//                     //           (null==ledger.mobileNo) ?
//                     //           showAlertDialog(context, alertType: AlertType.Error, alertTitle: "Error", message: 'सम्पर्क नम्बर थपिएको छैन |\n(Contact No. not added)')
//                     //           : dialNumber(ledger.mobileNo);
//                     //         },
//                     //       ),
//                     //     ),
//                     //     Container(
//                     //       width: 35,
//                     //       child: IconButton(
//                     //         icon: Icon(Icons.message, size: 20, color: colorPrimary,),
//                     //         onPressed: () {
//                     //           if(null==ledger.mobileNo){
//                     //             showAlertDialog(context, alertType: AlertType.Error, alertTitle: "Error", message: 'सम्पर्क नम्बर थपिएको छैन |\n(Contact No. not added)');
//                     //           }
//                     //           else{
//                     //             String message;
//                     //             if(_isReceivable){
//                     //               message = (DUE_RECEIVE_MSG.replaceFirst("{Amount}",  ledger.balanceAmount.abs().toStringAsFixed(2))).replaceFirst("{shopName}", "Shyam Dai ko Pasal");
//                     //             }else{
//                     //               message = (DUE_PAY_MSG.replaceFirst("{Amount}",  ledger.balanceAmount.abs().toStringAsFixed(2))).replaceFirst("{shopName}", "Shyam Dai ko Pasal");
//                     //             }
//                     //
//                     //             sendSMS(ledger.mobileNo, message);
//                     //           }
//                     //         },
//                     //       ),
//                     //     ),
//                     //     Container(
//                     //       width: 35,
//                     //       child: IconButton(
//                     //         icon: Icon(Icons.calendar_today_sharp, size: 20, color: colorPrimary,),
//                     //         onPressed: () {
//                     //           String message = "Give payment to ${ledger.ledgerTitle} of Rs. ${ledger.balanceAmount.abs()}";
//                     //           int reminderType = ReminderType.payment;
//                     //           if(_isReceivable){
//                     //             message = "Collect payment from ${ledger.ledgerTitle} of Rs. ${ledger.balanceAmount.abs()}";
//                     //           }
//                     //           Navigator.pushNamed(context, '/addEditReminder', arguments: AddEditReminderPage(reminderDesc: message, reminderType: reminderType));
//                     //         },
//                     //       ),
//                     //     ),
//                     //   ],
//                     // )
//                   ],
//                 ),
//               ],
//             ),

//             // Divider(height: 5,),

//             Row(
//               mainAxisAlignment: MainAxisAlignment.end,
//               children: <Widget>[
//                 if (showBothPayReceiveBtn)
//                   _showBothPayReceiveBtn(context)
//                 else if (_isReceivable)
//                   _receiveBtn(context)
//                 else
//                   _payBtn(context)
//               ],
//             ),
//           ],
//         ),
//       ),
//     );
//   }
// }
