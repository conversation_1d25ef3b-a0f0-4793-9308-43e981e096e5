// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/model/database/item_modal.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/item_adjustment/item_adjustment_controller.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/repository/item_adjustment_repository.dart';
import 'package:mobile_khaata_v2/database/item_adj_type.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:tuple/tuple.dart';

class ItemAdjustmentPage extends StatefulWidget {
  final String tag = "ItemAdjustmentPage";

  final String? itemId;
  final String? adjustmentId;

  const ItemAdjustmentPage({super.key, this.itemId, this.adjustmentId});

  @override
  State<ItemAdjustmentPage> createState() => _ItemAdjustmentPageState();
}

class _ItemAdjustmentPageState extends State<ItemAdjustmentPage> {
  final itemAdjustmentController = ItemAdjustmentController();

  @override
  void initState() {
    super.initState();
    _initializeController();
  }

  // TODO @Alok: Edit not implemented i.e. field not initilized
  Future<void> _initializeController() async {
    print(" ${widget.itemId} -> ${widget.adjustmentId}");

    // Initialize the controller first
    await itemAdjustmentController.init(widget.itemId ?? "");

    // Then set the values after initialization
    if (itemAdjustmentController.itemAdj != null) {
      itemAdjustmentController.itemAdj!.itemAdjItemId = widget.itemId;
      itemAdjustmentController.itemAdj!.itemAdjId = widget.adjustmentId;
    }
  }

  @override
  void dispose() {
    itemAdjustmentController
        .dispose(); // If the controller has a dispose method
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (itemAdjustmentController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
        // resizeToAvoidBottomPadding: true,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          toolbarHeight: 60,
          backgroundColor: colorPrimary,
          elevation: 4,
          leading: BackButton(
            onPressed: () => Navigator.pop(context, false),
          ),
          centerTitle: false,
          titleSpacing: -5.0,
          title: Text(
            (!itemAdjustmentController.editFlag)
                ? "नयाँ समायोजन\n(New Adjustment)"
                : "समायोजन\n(Edit Adjustment)",
            style: const TextStyle(
                fontSize: 16,
                color: Colors.white,
                fontFamily: 'HelveticaRegular',
                fontWeight: FontWeight.bold),
          ),
          actions: [
            if (itemAdjustmentController.editFlag) ...{
              InkWell(
                  onTap: () => itemAdjustmentController.readOnlyFlag =
                      !itemAdjustmentController.readOnlyFlag,
                  splashColor: colorPrimaryLighter,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 15),
                    child: (itemAdjustmentController.readOnlyFlag)
                        ? Column(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(
                                Icons.mode_edit,
                                color: Colors.white,
                              ),
                              Text(
                                "Edit",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          )
                        : Column(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(
                                Icons.close,
                                color: Colors.white,
                              ),
                              Text(
                                "Cancel",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          ),
                  )),
            }
          ],
        ),
        body: GestureDetector(
          onTap: () => FocusScope.of(context).unfocus(),
          child: Container(
            decoration: const BoxDecoration(color: Colors.white),
            padding: const EdgeInsets.only(
              left: 15,
              right: 15,
            ),
            child: Form(
              key: itemAdjustmentController.formKey,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    const SizedBox(
                      height: 15,
                    ),

                    //===================================Adjustment Date Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "मिति",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        CustomDatePickerTextField(
                          readOnly: itemAdjustmentController.readOnlyFlag,
                          initialValue:
                              itemAdjustmentController.itemAdj?.itemAdjDateBS,
                          maxBSDate: NepaliDateTime.now(),
                          onChange: (selectedDate) {
                            itemAdjustmentController.itemAdj?.itemAdjDateBS =
                                selectedDate;
                          },
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),

                    //===============================================Item
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "सामान",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        TypeAheadField<ItemModel>(
                          textFieldConfiguration: TextFieldConfiguration(
                            enabled: !itemAdjustmentController.readOnlyFlag,
                            controller:
                                itemAdjustmentController.itemNameController,
                            decoration: formFieldStyle.copyWith(
                                labelText: "Item",
                                suffixIcon: IconButton(
                                  icon: const Icon(
                                    Icons.close,
                                    size: 20,
                                  ),
                                  onPressed: () => itemAdjustmentController
                                      .itemOnClearHandler(),
                                ),
                                suffixIconConstraints:
                                    const BoxConstraints(maxWidth: 35)),
                            onChanged: (value) => itemAdjustmentController
                                .itemAdj?.itemAdjItemId = value,
                          ),
                          suggestionsCallback: (pattern) {
                            return itemAdjustmentController.items
                                .where((itemModel) {
                              return itemModel.itemName!
                                  .toLowerCase()
                                  .startsWith(pattern.toLowerCase());
                            });
                          },
                          transitionBuilder:
                              (context, suggestionsBox, controller) {
                            return suggestionsBox;
                          },
                          itemBuilder: (context, itemModel) {
                            return Column(
                              children: [
                                ListTile(
                                  contentPadding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 0),
                                  title: Text(
                                    "${itemModel.itemName}",
                                    style: const TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  trailing: (null != itemModel.itemCode)
                                      ? Text(
                                          "(${itemModel.itemCode})",
                                          style: const TextStyle(
                                              fontSize: 12,
                                              color: Colors.black54),
                                        )
                                      : null,
                                ),
                                const Divider(
                                  height: 0,
                                  thickness: 1,
                                ),
                              ],
                            );
                          },
                          onSuggestionSelected: (item) {
                            itemAdjustmentController.itemOnSelectHandler(item);
                          },
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),

                    //===============================================Adjustment Type Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "समायोजनको किसिम",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderDropdown(
                          name: 'adjustment_type',
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              labelText: "Adjustment Type"),
                          items: ItemAdjType.itemAdjTypeList.map((row) {
                            return DropdownMenuItem(
                                value: row["value"], child: Text(row["text"]));
                          }).toList(),
                          // readOnly: itemAdjustmentController.readOnlyFlag,
                          initialValue:
                              itemAdjustmentController.itemAdj?.itemAdjType,
                          onChanged: (value) {
                            itemAdjustmentController.itemAdj?.itemAdjType =
                                value as int;
                          },
                          validator: (value) {
                            if (parseDouble(value)! <= 0) {
                              return "समायोजनको किसिम छनौट गर्नुहोस् (Please select Adjustment Type)";
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),

                    //===============================================Quantity Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "परिमाण",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "quantity",
                          autocorrect: false,
                          textInputAction: TextInputAction.next,
                          keyboardType: const TextInputType.numberWithOptions(
                              decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^(\d+)?\.?\d{0,2}'))
                          ],
                          style: formFieldTextStyle,
                          maxLength: 10,
                          decoration: formFieldStyle.copyWith(
                              labelText: "Quantity", counterText: ''),
                          readOnly: itemAdjustmentController.readOnlyFlag,
                          initialValue: itemAdjustmentController
                              .itemAdj?.itemAdjQuantity
                              ?.toString(),
                          onChanged: (value) {
                            itemAdjustmentController.itemAdj?.itemAdjQuantity =
                                parseDouble(value);
                          },
                          validator: (value) {
                            if (parseDouble(value)! <= 0) {
                              return 'परिमाण खाली वा शून्य राख्नमिल्दैन (Quantity cannot be empty or zero)';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),

                    //===================================Adjustment Price Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "मुल्य",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "adjustment_price",
                          autocorrect: false,
                          textInputAction: TextInputAction.next,
                          keyboardType: const TextInputType.numberWithOptions(
                              decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^(\d+)?\.?\d{0,2}'))
                          ],
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              labelText: "At Price/Unit"),
                          readOnly: itemAdjustmentController.readOnlyFlag,
                          initialValue: itemAdjustmentController
                              .itemAdj?.itemAdjAtprice
                              ?.toString(),
                          onChanged: (value) {
                            itemAdjustmentController.itemAdj?.itemAdjAtprice =
                                parseDouble(value);
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 20.0),

                    //========================================Item Description Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "विवरण",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "adjustment_desc",
                          autocorrect: false,
                          textInputAction: TextInputAction.done,
                          keyboardType: TextInputType.text,
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              labelText: "Adjustment Description"),
                          readOnly: itemAdjustmentController.readOnlyFlag,
                          initialValue: itemAdjustmentController
                              .itemAdj?.itemAdjDescription,
                          onChanged: (value) {
                            itemAdjustmentController
                                .itemAdj?.itemAdjDescription = strTrim(value!);
                          },
                          // validators: [],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        //=================================================Save button
        bottomNavigationBar: BottomSaveCancelButton(
          shadow: false,
          hasDelete: (itemAdjustmentController.editFlag &&
                  !itemAdjustmentController.readOnlyFlag)
              ? true
              : false,
          onDeleteBtnPressedFn: () async {
            showAlertDialog(context,
                okText: "YES",
                alertType: AlertType.Error,
                alertTitle: "Confirm Delete", onCloseButtonPressed: () async {
              // Navigator.of(_).pop();
              ProgressDialog progressDialog = ProgressDialog(context,
                  type: ProgressDialogType.normal, isDismissible: false);
              progressDialog.update(
                  message: "Checking Permission. Please wait....");
              await progressDialog.show();
              Tuple2<bool, String> checkResp =
                  await PermissionWrapperController().requestForPermissionCheck(
                      forPermission: PermissionManager.itemAdjDelete);
              if (checkResp.item1) {
                //has  permission
                progressDialog.update(
                    message: "Deleting Data. Please wait....");
                Tuple2<bool, String> deleteResp =
                    await ItemAdjustmentRepository().delete(
                  widget.adjustmentId ?? "",
                );
                await progressDialog.hide();
                if (deleteResp.item1) {
                  //  data deleted
                  TransactionHelper.refreshPreviousPages();
                  showAlertDialog(context,
                      barrierDismissible: false,
                      alertType: AlertType.Success,
                      alertTitle: "", onCloseButtonPressed: () {
                    // Navigator.of(_).pop();
                    Navigator.of(context).pop();
                  }, message: deleteResp.item2);
                } else {
                  //cannot  delete  data
                  showAlertDialog(context,
                      alertType: AlertType.Error,
                      alertTitle: "",
                      message: deleteResp.item2);
                }
              } else {
                await progressDialog.hide();
                showAlertDialog(context,
                    alertType: AlertType.Error,
                    alertTitle: "",
                    message: checkResp.item2);
              }
            },
                message:
                    "Are you sure you  want to  delete this adjustment record?");
          },
          enableFlag: !itemAdjustmentController.readOnlyFlag,
          onSaveBtnPressedFn: (itemAdjustmentController.readOnlyFlag)
              ? null
              : () async {
                  FocusScope.of(context).unfocus();

                  if (itemAdjustmentController.formKey.currentState!
                      .validate()) {
                    if (null ==
                        itemAdjustmentController.itemAdj?.itemAdjItemId) {
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "Error", onCloseButtonPressed: () {
                        Navigator.of(context).pop();
                      }, message: "सामान छनौट गर्नुहोस्\n(Please select Item)");
                      return;
                    }

                    if (null ==
                        itemAdjustmentController.itemAdj?.itemAdjDateBS) {
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "Error", onCloseButtonPressed: () {
                        Navigator.of(context).pop();
                      }, message: "मिति भर्नुहोस्\n(Fill Date)");
                      return;
                    }

                    ProgressDialog progressDialog = ProgressDialog(context,
                        type: ProgressDialogType.normal, isDismissible: false);
                    progressDialog.update(
                        message: "Saving data. Please wait....");
                    await progressDialog.show();

                    bool status = false;
                    try {
                      if (!itemAdjustmentController.editFlag) {
                        status =
                            await itemAdjustmentController.createAdjustment();
                      } else {
                        status =
                            await itemAdjustmentController.updateAdjustment();
                      }
                    } catch (e, trace) {
                      // Log.e(tag, e.toString() + trace.toString());
                    }
                    await progressDialog.hide();

                    if (status) {
                      Navigator.pop(context, true);

                      TransactionHelper.refreshPreviousPages();

                      String message = (itemAdjustmentController.editFlag)
                          ? "Adjustment Updated Successfully."
                          : "Adjustment Created Successfully.";
                      showToastMessage(context, message: message, duration: 2);
                    } else {
                      showToastMessage(context,
                          alertType: AlertType.Error,
                          message: "Failed to process operation",
                          duration: 2);
                    }
                  }
                },
        ),
      ));
    });
  }
}
