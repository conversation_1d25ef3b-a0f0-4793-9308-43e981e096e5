// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/model/others/synced_batched_model.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/app/repository/synced_batched_repository.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/login_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

Future<void> logoutFromDevice(BuildContext context,
    {bool isSwitch = false}) async {
  try {
    int pendingCount = await QueryRepository().getRemainingPendingQueries();
    List<SyncedBatchModel> syncedBatchModels = [];
    syncedBatchModels = await SyncedBatchRepository().getSyncedBatchIDs();

    if (pendingCount > 0 || syncedBatchModels.isNotEmpty) {
      if (!context.mounted) return;
      showToastMessage(context,
          message:
              "You cannot logout at this moment. Please wait till all data are synced to server.",
          alertType: AlertType.Error);
      pushPendingQueries(showNotificationAfterResult: false, all: true);
      return;
    }

    if (!context.mounted) return;

    ProgressDialog progressDialog = ProgressDialog(context,
        type: ProgressDialogType.normal, isDismissible: false);
    progressDialog.update(message: "Logging out. Please wait....");
    await progressDialog.show();

    try {
      ApiBaseHelper apiBaseHelper = ApiBaseHelper();
      ApiResponse apiResponse =
          await apiBaseHelper.get(apiBaseHelper.ACTION_AUTH_LOGOUT);

      await progressDialog.hide();

      if (!context.mounted) return;

      if (apiResponse.status) {
        // Close any open dialogs/screens
        Navigator.of(context).pop();

        // Clear all awaiting notifications
        await flutterLocalNotificationsPlugin.cancelAll();

        // Perform local logout
        LoginHelper loginHelper = LoginHelper();
        bool status = await loginHelper.logout();

        if (status) {
          if (!context.mounted) return;

          if (!isSwitch) {
            await Navigator.pushNamedAndRemoveUntil(
              context,
              "/login",
              (route) => false,
            );
          }

          if (context.mounted) {
            showToastMessage(
              context,
              message: apiResponse.msg ?? "Logged out successfully",
            );
          }
        } else {
          // Local logout failed
          if (context.mounted) {
            showToastMessage(context,
                message: "Failed to clear local session. Please try again.",
                alertType: AlertType.Error);
          }
        }
      } else {
        // API logout failed
        if (context.mounted) {
          showToastMessage(context,
              message: apiResponse.msg ?? "Failed to logout from server",
              alertType: AlertType.Error);
        }
      }
    } catch (e) {
      // Ensure progress dialog is hidden on error
      await progressDialog.hide();

      if (context.mounted) {
        showToastMessage(context,
            message:
                "Network error. Please check your connection and try again.",
            alertType: AlertType.Error);
      }
      print("Logout error: $e");
    }
  } catch (e) {
    print("Logout initialization error: $e");
    if (context.mounted) {
      showToastMessage(context,
          message: "An error occurred during logout. Please try again.",
          alertType: AlertType.Error);
    }
  }
}
