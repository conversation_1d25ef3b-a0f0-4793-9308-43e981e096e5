// ignore_for_file: non_constant_identifier_names, unused_catch_clause

import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/http/api_exception.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:mobile_khaata_v2/utilities/login_helper.dart';
import 'package:mobile_khaata_v2/utilities/network_utils.dart';
import 'package:http/http.dart' as http;
import 'package:mobile_khaata_v2/utilities/shared_pref_helper1.dart';
import 'package:sqflite/sqflite.dart';

import '../database/database_helpler.dart';

class ApiResponse {
  String? msg;
  dynamic data;
  dynamic file;
  dynamic syncQuery;
  bool status = true;
}

class ApiBaseHelper {
  final String tag = "ApiBaseHelper";

  final Duration getTimeOut = const Duration(seconds: 20);
  final Duration postTimeOut = const Duration(minutes: 10);
  final String _baseUrl = ApiBaseUrl;

  final String _apiKeyHeader = ApiKeyHeader;
  final String _apiKeySecret = ApiKeySecret;

  // final String ACTION_DOWNLOAD_DB = "download";

  final String ACTION_RESENDOTP = "resend-otp";

  final String ACTION_REG_REGISTRATION = "registration";
  final String ACTION_REG_ACTIVATE = "registration/activate-account";

  final String ACTION_AUTH_LOGIN = "auth/login";
  final String CHANGE_PASSWORD = 'auth/change-password';
  final String ACTION_AUTH_FORGOT_PASS = "auth/forgot-password";
  final String ACTION_AUTH_RESET_PASS = "auth/reset-password";
  final String ACTION_AUTH_LOGOUT = "auth/logout";

  final String PUSH_PENDING_QUERY = "sync/push-query";
  final String PULL_PENDING_QUERY = "sync/pull-query";
  final String YOUTUBE_TUTORIALS = "tutorial-links";

  final String ADD_USER = "sub-user/new";
  final String LIST_USER = "sub-user/";
  final String CHILD_COMPANY_LIST = "web/get-child-company";

  // url will be sub-user/update-user-state/${id}
  final String TOGGLE_USER_STATUS = "sub-user/update-user-state/";

  final String TOGGLE_MULTI_USER = "auth/update-multiuser-status";
  final String DISABLE_WEB = "web/disable-web";

  // url will be sub-user/edit/${id}
  final String EDIT_USER = "sub-user/edit/";

  final String REGISTER_CHILD_COMPANY = "web/register-child-company";
  final String UPDATE_SYNC_TASK = "sync/update-sync-task";

  final String UPDATE_USER_PERMISSION = 'permission/assign-permission';

  //url will be sub-user/reset-password/{id}
  final String RESET_SUB_USER_PASSWORD = "sub-user/reset-password/";

  final String RECHECK_ACTIVE = "sub-user/user-active-status";

  // final String RESET_PASSWORD = "auth/reset-password";

  final String CHECK_USER_NUMBER = "auth/forgot-password";

  final String RECHECK_EXPIRY = "account/expiry-status";

  //url will be permission/check-permission/{slug}'
  final String CHECK_FOR_PERMISSION = 'permission/check-permission/';

  final String AGENTT_UPDATE = 'account/update-agent';

  final String VERIFY_AGENT = "account/verify-agent";

  final String SUBSCRIPTION_PACKAGES = "account/subscription-package";

  //url will be account/mobile-esewa-confirmation/{refid}
  final String VERIFY_ESEWA_PAYMENT = "account/mobile-esewa-confirmation/";

  final String INITIATE_ESEWA_PAYMENT = "web/initiate-esewa-transaction";

  final String GET_STARTED_INFO = "get-started";

  static final ApiBaseHelper _instance = ApiBaseHelper._internal();

  ApiBaseHelper._internal();

  factory ApiBaseHelper() => _instance;

  final NetworkUtil _netUtil = NetworkUtil();

  bool _checkResponse(http.Response response) {
    // Log.d("for responnse ${response.statusCode}");
    switch (response.statusCode) {
      case 200:
        return true;
      case 400:
        throw BadRequestException(response.body, response.statusCode);
      case 401:
        // SharedPrefHelper1().setUnauthorized(true);
        // LoginHelper().logout();editeed
        // Log.d("unauthorized");
        throw UnauthorizedException(
            response.body.toString(), response.statusCode);
      case 403:
        throw ForbiddenException(response.body.toString(), response.statusCode);
      case 404:
        throw NotFoundException(response.body.toString(), response.statusCode);
      case 405:
        throw MethodNotAllowedException(
            response.body.toString(), response.statusCode);
      case 422:
        throw InvalidInputException(
            response.body.toString(), response.statusCode);
      case 500:
        throw InternalServeErrorException(
            response.body.toString(), response.statusCode);
      default:
        throw FetchDataException(
            "Error occured while Communication with Server with StatusCode : ${response.statusCode}",
            response.statusCode);
    }
  }

  dynamic _convertJson(String jsonString) {
    try {
      var responseJson = json.decode(jsonString) as Map<String, dynamic>;
      return responseJson;
    } catch (e) {
      print("invalid format hiihaaaaa");
      print(jsonString);
      print(e);
      throw FormatException(
          '{"message": "Invalid data format received."}', 500);
    }
  }

  //get Method
  Future<ApiResponse> get(String url, {bool accessToken = true}) async {
    url = Uri.encodeFull(_baseUrl + url);

    ApiResponse apiResponse = ApiResponse();
    Map<String, String> headers = {};

    try {
      bool connectionStatus = await _netUtil.checkInternetConnection();
      if (!connectionStatus) {
        throw NoInternetException(
            "Please turn on your internet connection to continue.");
      }

      headers["Accept"] = 'application/json';
      headers["Content-Type"] = 'application/json; charset=UTF-8';
      headers[_apiKeyHeader] = _apiKeySecret;

      if (accessToken) {
        LoginHelper loginHelper = LoginHelper();
        String? token = await loginHelper.accessToken;
        headers['Authorization'] = 'bearer $token';
      }

      // final response = await http.get(url, headers: headers);
      final response = await http
          .get(Uri.parse(url), headers: headers)
          .timeout(getTimeOut, onTimeout: () {
        throw FetchDataException("Network Timeout");
      });

      Log.d("server");
      Log.d(response.body.toString());

      _checkResponse(response);

      var responseJson = _convertJson(response.body.toString());
      apiResponse.msg = responseJson["message"];
      apiResponse.data = responseJson["data"] ?? "";
      apiResponse.syncQuery = responseJson["sync_queries"];
    } on SocketException {
      throw FetchDataException("Server not reachable.");
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());

      String errorMessage = "";
      bool isJson = false;
      final data = e.toString();
      print(data);
      Map<String, dynamic> m = {};
      try {
        m = json.decode(data) as Map<String, dynamic>;
        isJson == true;
      } on FormatException catch (e) {
        isJson = false;
      }

      if (isJson == true) {
        if (m.containsKey("message")) {
          errorMessage = m['message'];
        } else {
          errorMessage = m.toString();
        }
      }

      apiResponse.msg = errorMessage;
      apiResponse.status = false;
      // Log.d(trace.toString(), tag: tag);
    }
    print("==========API RESPONSE GET METHOD===============");
    print("|  URL:  ${url}");
    print("|  REQUEST HEADER:  ${headers}");
    // print("|  REQUEST BODY:  ${body}");
    print("|  STATUS:  ${apiResponse.status}");
    print("|  RESPONSE DATA:  ${apiResponse.data}");
    print("|  RESPONSE MESSAGE:  ${apiResponse.msg}");
    print("|  RESPONSE Queries:  ${apiResponse.syncQuery}");
    print("|  RESPONSE FILE:  ${apiResponse.file}");
    print("================================================");

    if (apiResponse.syncQuery != null &&
        apiResponse.syncQuery is List &&
        apiResponse.syncQuery.isNotEmpty) {
      print("Sync Queries: ${apiResponse.syncQuery.length}");

      DatabaseHelper databaseHelper = DatabaseHelper();
      SharedPrefHelper1 sharedPrefHelper1 = SharedPrefHelper1();
      try {
        print("1");
        for (var query in apiResponse.syncQuery) {
          if (!await sharedPrefHelper1.containsBatchIds(query['batch_id'])) {
            bool result = await databaseHelper.handleDynamicQuery(
              data: query['data'],
              queryType: query['query_type'],
              tableName: query['table_name'],
              whereClause: query['where_clause'],
              whereArgs: query['where_args'].toString(),
            );

            if (result) {
              print(
                  "Successfully executed query: ${query['query_type']} on ${query['table_name']}");
              await sharedPrefHelper1.addBatchIDs(query['batch_id']);
            }
          }
        }

        List<String> batchIds = await sharedPrefHelper1.getBatchIds();
        if (await batchIds.length > 0) {
          ApiBaseHelper apiBaseHelper = ApiBaseHelper();

          Map<String, dynamic> body = {
            'batch_ids': batchIds,
          };

          print("BODY: $body");

          ApiResponse apiResponse = await apiBaseHelper
              .post(apiBaseHelper.UPDATE_SYNC_TASK, body, accessToken: true);

          if (apiResponse.syncQuery.isEmpty ||
              apiResponse.syncQuery == null ||
              apiResponse.syncQuery.length == 0) {
            sharedPrefHelper1.clearBatchIds();
          }
        }
      } catch (e) {
        print("Error inserting sync queries: $e");
      }
      // databaseHelper.insertSyncQueries(apiResponse.syncQuery);

      for (var query in apiResponse.syncQuery) {
        print("===========");
        print(query);
        print("==========");
      }
    } else {
      print("Empty Sync Queries");
    }

    return apiResponse;
  }

  //post method
  Future<ApiResponse> post(String url, Map body,
      {bool accessToken = true}) async {
    url = Uri.encodeFull(_baseUrl + url);

    ApiResponse apiResponse = ApiResponse();
    bool connectionStatus = await _netUtil.checkInternetConnection();
    Map<String, String> headers = {};

    if (!connectionStatus) {
      apiResponse.status = false;
      apiResponse.msg = "Please turn on your internet connection to continue.";
      return apiResponse;
    } else {
      try {
        headers["Accept"] = 'application/json';
        headers["Content-Type"] = 'application/json; charset=UTF-8';
        headers[_apiKeyHeader] = _apiKeySecret;

        if (accessToken) {
          refreshGlobalVariables();
          LoginHelper loginHelper = LoginHelper();
          String? token = await loginHelper.accessToken;
          headers['Authorization'] = 'bearer $token';
        }

        final response = await http
            .post(
          Uri.parse(url),
          headers: headers,
          body: jsonEncode(body),
        )
            .timeout(postTimeOut, onTimeout: () {
          throw FetchDataException("Network Timeout");
        });
        // print("===========Response================");
        // print(response.body.toString());
        // print(response.body.toString());
        _checkResponse(response);

        var responseJson = _convertJson(response.body.toString());

        apiResponse.msg = responseJson["message"];
        apiResponse.data = responseJson["data"];
        apiResponse.syncQuery = responseJson["sync_queries"];
      } on SocketException catch (e) {
        print("===========Socket Input================");
        print(e.toString());
        throw FetchDataException("Server not reachable.");
      } on InvalidInputException catch (e) {
        print("===========Invalid Input================");

        var error = e.getJson();
        String errorMessage = "";

        error["errors"]
            .forEach((key, value) => errorMessage += "${value[0]}\n\n");

        apiResponse.msg = errorMessage;
        apiResponse.status = false;
      } catch (e) {
        print("===========error================");
        print(e);
        apiResponse.msg = e.toString();
        apiResponse.status = false;
      }
    }

    print("==========API RESPONSE POST METHOD===============");
    print("|  URL:  ${url}");
    print("|  REQUEST HEADER:  ${headers}");
    print("|  REQUEST BODY:  ${jsonEncode(body)}");
    print(
        "|  REQUEST Synced Batch id:  ${jsonEncode(body['synced_batch_id'])}");
    print("|  STATUS:  ${apiResponse.status}");
    print("|  RESPONSE DATA:  ${jsonEncode(apiResponse.data)}");
    print("|  RESPONSE MESSAGE:  ${apiResponse.msg}");
    print("|  RESPONSE Queries:  ${apiResponse.syncQuery}");
    print("|  RESPONSE FILE:  ${apiResponse.file}");
    print("================================================");

    if (apiResponse.syncQuery != null &&
        apiResponse.syncQuery is List &&
        apiResponse.syncQuery.isNotEmpty) {
      print("Sync Queries: ${apiResponse.syncQuery.length}");

      DatabaseHelper databaseHelper = DatabaseHelper();
      SharedPrefHelper1 sharedPrefHelper1 = SharedPrefHelper1();
      try {
        print("1");
        for (var query in apiResponse.syncQuery) {
          if (!await sharedPrefHelper1.containsBatchIds(query['batch_id'])) {
            bool result = await databaseHelper.handleDynamicQuery(
              data: query['data'],
              queryType: query['query_type'],
              tableName: query['table_name'],
              whereClause: query['where_clause'],
              whereArgs: query['where_args'].toString(),
            );

            if (result) {
              print(
                  "Successfully executed query: ${query['query_type']} on ${query['table_name']}");
              await sharedPrefHelper1.addBatchIDs(query['batch_id']);
            }
          }
        }

        List<String> batchIds = await sharedPrefHelper1.getBatchIds();
        if (await batchIds.length > 0) {
          ApiBaseHelper apiBaseHelper = ApiBaseHelper();
          Map<String, dynamic> body = {
            'batch_ids': batchIds,
          };
          print("BODY: $body");
          ApiResponse apiResponse = await apiBaseHelper
              .post(apiBaseHelper.UPDATE_SYNC_TASK, body, accessToken: true);

          if (apiResponse.syncQuery.isEmpty ||
              apiResponse.syncQuery == null ||
              apiResponse.syncQuery.length == 0) {
            sharedPrefHelper1.clearBatchIds();
          }
        }
      } catch (e) {
        print("Error inserting sync queries: $e");
      }
      // databaseHelper.insertSyncQueries(apiResponse.syncQuery);

      for (var query in apiResponse.syncQuery) {
        print("===========");
        print(query);
        print("==========");
      }
    } else {
      print("Empty Sync Queries");
    }

    if (url ==
        "https://mkapi.mobilekhaata.com/api/permission/assign-permission") {
      print('PERMISSINS:  ${body['permissions'].length}');
      print('PERMISSINS:  ${body.length}');
      for (String perission in body['permissions']) {
        print(perission);
      }
    }

    return apiResponse;
  }
}
