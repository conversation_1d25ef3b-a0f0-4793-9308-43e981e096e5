// ignore_for_file: library_private_types_in_public_api, invalid_use_of_protected_member, use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_image_picker/form_builder_image_picker.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/components/phone_contact_text_input.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/add_party/add_edit_party_controller.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:tuple/tuple.dart';

class AddEditPartyPage extends StatefulWidget {
  final String? partyID;
  final bool? reaOnlyFlag;

  const AddEditPartyPage({super.key, this.partyID, this.reaOnlyFlag});

  @override
  _AddEditPartyPageState createState() => _AddEditPartyPageState();
}

class _AddEditPartyPageState extends State<AddEditPartyPage>
    with SingleTickerProviderStateMixin {
  final String tag = "Party Add/Edit Page";

  TabController? tabController;
  final addEditPartyController = AddEditPartyController();

  @override
  void initState() {
    tabController = TabController(length: 2, vsync: this);
    onInit();
    super.initState();
  }

  onInit() async {
    addEditPartyController.onInit();
    if (null != widget.partyID) {
      await addEditPartyController.initEdit(
          widget.partyID, widget.reaOnlyFlag ?? true);
    }

    // await addEditPartyController.initEdit(
    //     '12-29-19304144-fdd9-4933-97aa-f1b2102899ef',
    //     widget.reaOnlyFlag ?? true);
  }

  @override
  void dispose() {
    tabController!.dispose();
    addEditPartyController.onClose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (addEditPartyController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      } else {
        return SafeArea(
          child: Scaffold(
            // resizeToAvoidBottomPadding: true,
            resizeToAvoidBottomInset: true,
            appBar: AppBar(
              backgroundColor: colorPrimary,
              toolbarHeight: 60,
              elevation: 4,
              leading: BackButton(
                onPressed: () => Navigator.pop(context, false),
              ),
              centerTitle: false,
              titleSpacing: -5.0,
              title: Text(
                (!addEditPartyController.editFlag)
                    ? "नयाँ पार्टी/ग्राहक (New Party)"
                    : "पार्टी/ग्राहक (Edit Party)",
                style: const TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontFamily: 'HelveticaRegular',
                    fontWeight: FontWeight.bold),
              ),
              actions: [
                if (addEditPartyController.editFlag) ...{
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    child: InkWell(
                        onTap: () {
                          if (!addEditPartyController.readOnlyFlag) {
                            // Cancel button tapped - remove selected file
                            addEditPartyController.clearSelectedImage();
                          }
                          addEditPartyController.readOnlyFlag =
                              !addEditPartyController.readOnlyFlag;
                          addEditPartyController.refresh();
                        },
                        child: (addEditPartyController.readOnlyFlag)
                            ? Column(
                                children: const [
                                  Icon(
                                    Icons.mode_edit,
                                    color: Colors.white,
                                  ),
                                  Text(
                                    "Click here to Edit",
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 10),
                                  ),
                                ],
                              )
                            : Column(
                                children: const [
                                  Icon(
                                    Icons.close,
                                    color: Colors.white,
                                  ),
                                  Text(
                                    "Cancel",
                                    style: TextStyle(
                                        color: Colors.white, fontSize: 10),
                                  ),
                                ],
                              )),
                  ),
                }
              ],
            ),

            //===========================================================================Body Part
            body: GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(),
              child: Container(
                decoration: BoxDecoration(color: backgroundColorShade),
                child: Form(
                  key: addEditPartyController.formKey,
                  child: SingleChildScrollView(
                    child: Container(
                      decoration: const BoxDecoration(color: Colors.white),
                      child: Column(
                        children: [
                          //==========================================================Top Panel
                          Container(
                            margin: const EdgeInsets.only(top: 10),
                            padding: const EdgeInsets.only(
                              left: 15,
                              right: 15,
                            ),
                            child: Column(
                              children: [
                                const SizedBox(height: 5.0),

                                //===========================FullName
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'व्यक्ति/फर्मको नाम',
                                      style: labelStyle2,
                                    ),
                                    const SizedBox(height: 5.0),
                                    PhoneContactTextInput(
                                      enableFlag:
                                          !addEditPartyController.readOnlyFlag,
                                      labelText: "Person/Firm's Name",
                                      controller: addEditPartyController
                                          .titleController,
                                      onChangedFn: (v) {
                                        addEditPartyController
                                            .party.ledgerTitle = v;
                                      },
                                      onSuggestionSelectedFn:
                                          (Contact contact) {
                                        String phoneString = "";
                                        if (contact.phones.isNotEmpty) {
                                          phoneString = contact.phones
                                              .elementAt(0)
                                              .number
                                              .replaceAll("(", "")
                                              .replaceAll(")", "")
                                              .replaceAll("-", "")
                                              .replaceAll(" ", "");
                                        }

                                        addEditPartyController.party
                                            .ledgerTitle = contact.displayName;
                                        addEditPartyController.party.mobileNo =
                                            phoneString.length > 10
                                                ? phoneString.substring(
                                                    phoneString.length - 10)
                                                : phoneString;
                                        addEditPartyController.partyMain
                                            .refresh();

                                        addEditPartyController.titleController
                                            .text = contact.displayName;
                                        addEditPartyController
                                                .mobileController.text =
                                            phoneString.length > 10
                                                ? phoneString.substring(
                                                    phoneString.length - 10)
                                                : phoneString;
                                      },
                                    )
                                  ],
                                ),

                                const SizedBox(
                                  height: 15,
                                ),

                                //===================================Mobile
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'फोन नम्बर',
                                      style: labelStyle2,
                                    ),
                                    const SizedBox(height: 5.0),
                                    FormBuilderTextField(
                                      name: "mobile",
                                      readOnly:
                                          addEditPartyController.readOnlyFlag,
                                      autocorrect: false,
                                      keyboardType: TextInputType.number,
                                      textInputAction: TextInputAction.next,
                                      maxLength: 10,
                                      inputFormatters: [
                                        FilteringTextInputFormatter.digitsOnly
                                      ],
                                      style: formFieldTextStyle,
                                      decoration: formFieldStyle.copyWith(
                                          labelText: "Contact No.",
                                          counterText: ''),
                                      controller: addEditPartyController
                                          .mobileController,
                                      onChanged: (v) {
                                        addEditPartyController.party.mobileNo =
                                            v;
                                      },
                                      validator: (value) {
                                        if (value == null && value!.isEmpty) {
                                          return 'Mobile No. should be 10 digits long!';
                                        }
                                        return null;
                                      },
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 15.0),

                                //===================================Address
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'ठेगाना',
                                      style: labelStyle2,
                                    ),
                                    const SizedBox(height: 5.0),
                                    FormBuilderTextField(
                                      name: "address",
                                      readOnly:
                                          addEditPartyController.readOnlyFlag,
                                      autocorrect: false,
                                      keyboardType: TextInputType.text,
                                      textInputAction: TextInputAction.next,
                                      style: formFieldTextStyle,
                                      decoration: formFieldStyle.copyWith(
                                          labelText: "Address"),
                                      initialValue:
                                          addEditPartyController.party.address,
                                      onChanged: (value) {
                                        addEditPartyController.party.address =
                                            strTrim(value ?? "");
                                      },
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 15.0),
                              ],
                            ),
                          ),

                          //===================================================TAB Panel
                          Column(
                            children: [
                              Container(
                                height: 55,
                                decoration: BoxDecoration(
                                    color: Colors.black12,
                                    border:
                                        Border.all(color: colorPrimaryLight)),
                                child: TabBar(
                                  controller: tabController,
                                  indicatorColor: Colors.white,
                                  labelStyle: const TextStyle(fontSize: 15),
                                  unselectedLabelColor: Colors.black,
                                  indicator:
                                      BoxDecoration(color: colorPrimaryLight),
                                  tabs: const [
                                    Tab(
                                      child: Text(
                                        "थप् जानकारी\n(Extra Information)",
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                    Tab(
                                      child: Text(
                                        "सुरु मौज्दात\n(Opening Balance)",
                                        textAlign: TextAlign.center,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              Container(
                                height: MediaQuery.of(context).size.height,
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 15),
                                child: TabBarView(
                                  controller: tabController,
                                  children: [
                                    //===================================================Extra Information Tab
                                    Column(
                                      children: [
                                        //==========================================Image
                                        SizedBox(
                                          width: 130,
                                          child: Obx(() =>
                                              addEditPartyController
                                                      .fileUploadPermission
                                                  ? FormBuilderImagePicker(
                                                      enabled:
                                                          !addEditPartyController
                                                              .readOnlyFlag,
                                                      name: "image_picker",
                                                      decoration:
                                                          const InputDecoration(
                                                        border:
                                                            InputBorder.none,
                                                      ),
                                                      maxImages: 1,
                                                      iconColor:
                                                          colorPrimaryLight,
                                                      initialValue:
                                                          addEditPartyController
                                                              .imagePickerValues,
                                                      onChanged: (value) =>
                                                          addEditPartyController
                                                              .imagePickerOnChangeHandler(
                                                                  value),
                                                    )
                                                  : Container(
                                                      height: 130,
                                                      width: 130,
                                                      margin:
                                                          const EdgeInsets.all(
                                                              10.0),
                                                      decoration: BoxDecoration(
                                                        color: Colors.black12,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(10.0),
                                                      ),
                                                      child: Column(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .center,
                                                        children: [
                                                          const Icon(
                                                            Icons
                                                                .image_not_supported_outlined,
                                                            size: 50.0,
                                                            color:
                                                                Colors.black26,
                                                          ),
                                                          const SizedBox(
                                                              height: 10.0),
                                                          Text(
                                                            "File Upload \nNot Allowed",
                                                            style: TextStyle(
                                                              color: Colors
                                                                  .black54,
                                                              fontSize: 12.0,
                                                              fontFamily:
                                                                  'HelveticaRegular',
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    )),
                                        ),

                                        //===============================Contact Person Field
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'संपर्क व्यक्ति',
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            FormBuilderTextField(
                                              name: "contact_person",
                                              readOnly: addEditPartyController
                                                  .readOnlyFlag,
                                              autocorrect: false,
                                              keyboardType: TextInputType.text,
                                              textInputAction:
                                                  TextInputAction.next,
                                              style: formFieldTextStyle,
                                              decoration:
                                                  formFieldStyle.copyWith(
                                                      labelText:
                                                          "Contact Person"),
                                              initialValue:
                                                  addEditPartyController
                                                      .party.contactPersonName,
                                              onChanged: (value) {
                                                addEditPartyController.party
                                                        .contactPersonName =
                                                    strTrim(value ?? "");
                                              },
                                            ),
                                          ],
                                        ),

                                        const SizedBox(height: 15.0),

                                        //=================================Email Field
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "ईमेल",
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            FormBuilderTextField(
                                              name: "email",
                                              readOnly: addEditPartyController
                                                  .readOnlyFlag,
                                              autocorrect: false,
                                              keyboardType: TextInputType.text,
                                              textInputAction:
                                                  TextInputAction.next,
                                              style: formFieldTextStyle,
                                              decoration: formFieldStyle
                                                  .copyWith(labelText: "Email"),
                                              initialValue:
                                                  addEditPartyController
                                                      .party.email,
                                              onChanged: (value) {
                                                addEditPartyController
                                                        .party.email =
                                                    strTrim(value ?? "");
                                              },
                                            ),
                                          ],
                                        ),

                                        const SizedBox(height: 15.0),

                                        //=====================================PAN No Field
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "पान / मु. अ. कर नम्बर",
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            FormBuilderTextField(
                                              name: "pan_no",
                                              readOnly: addEditPartyController
                                                  .readOnlyFlag,
                                              autocorrect: false,
                                              keyboardType:
                                                  TextInputType.number,
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .digitsOnly
                                              ],
                                              textInputAction:
                                                  TextInputAction.next,
                                              style: formFieldTextStyle,
                                              decoration:
                                                  formFieldStyle.copyWith(
                                                      labelText: "PAN/VAT No."),
                                              initialValue:
                                                  addEditPartyController
                                                      .party.tinNo,
                                              onChanged: (value) {
                                                addEditPartyController
                                                        .party.tinNo =
                                                    strTrim(value ?? "");
                                              },
                                            ),
                                          ],
                                        ),

                                        const SizedBox(height: 15.0),

                                        //=====================================PAN No Type Field
                                        Container(
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                'PAN हो / VAT हो ? छान्नुहोस',
                                                style: labelStyle2,
                                                // style: TextStyle(fontSize: 14, color: Colors.black54),
                                              ),
                                              Container(
                                                child: FormBuilderChoiceChip(
                                                  enabled:
                                                      !addEditPartyController
                                                          .readOnlyFlag,
                                                  name: "pan_vat_type",
                                                  selectedColor:
                                                      colorPrimaryLight,
                                                  disabledColor: Colors.black12,
                                                  labelStyle: const TextStyle(
                                                      color: Colors.white),
                                                  decoration:
                                                      const InputDecoration(
                                                    border: InputBorder.none,
                                                  ),
                                                  spacing: 20,
                                                  initialValue:
                                                      addEditPartyController
                                                          .party.tinFlag,
                                                  onChanged: (value) {
                                                    addEditPartyController
                                                        .party.tinFlag = value;
                                                    addEditPartyController
                                                        .partyMain
                                                        .refresh();
                                                    // addEditPartyController._party.
                                                  },
                                                  alignment: WrapAlignment
                                                      .spaceBetween,
                                                  options: [
                                                    FormBuilderChipOption(
                                                      value: "PAN",
                                                      child: Row(
                                                        children: const [
                                                          Icon(
                                                            Icons.check_box,
                                                            color: Colors.white,
                                                          ),
                                                          SizedBox(
                                                            width: 5,
                                                          ),
                                                          Text("PAN"),
                                                        ],
                                                      ),
                                                    ),
                                                    FormBuilderChipOption(
                                                      value: "VAT",
                                                      child: Row(
                                                        children: const [
                                                          Icon(
                                                            Icons.check_box,
                                                            color: Colors.white,
                                                          ),
                                                          SizedBox(
                                                            width: 5,
                                                          ),
                                                          Text("VAT"),
                                                        ],
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),

                                    //=====================================Opening Balance Tab
                                    Column(
                                      children: [
                                        const SizedBox(height: 20.0),

                                        //=====================================Opening Amount Field
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "सुरु मौज्दात",
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            FormBuilderTextField(
                                                name: "opening_bal_amt",
                                                readOnly: addEditPartyController
                                                    .readOnlyFlag,
                                                autocorrect: false,
                                                keyboardType:
                                                    const TextInputType
                                                            .numberWithOptions(
                                                        decimal: true),
                                                inputFormatters: [
                                                  FilteringTextInputFormatter
                                                      .allow(RegExp(
                                                          r'^(\d+)?\.?\d{0,2}'))
                                                ],
                                                textInputAction: TextInputAction
                                                    .next,
                                                style: formFieldTextStyle,
                                                maxLength: 10,
                                                decoration: formFieldStyle
                                                    .copyWith(
                                                        labelText:
                                                            "Opening Balance",
                                                        counterText: ''),
                                                initialValue:
                                                    addEditPartyController
                                                        .party.openingBalance
                                                        ?.toString(),
                                                onChanged: (value) {
                                                  addEditPartyController.party
                                                          .openingBalance =
                                                      parseDouble(value);
                                                  addEditPartyController
                                                      .partyMain
                                                      .refresh();
                                                }),
                                          ],
                                        ),

                                        const SizedBox(height: 20.0),

                                        //=================================Opening Balance Date Field
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "सुरु मौज्दात मिति",
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            CustomDatePickerTextField(
                                              readOnly: addEditPartyController
                                                  .readOnlyFlag,
                                              maxBSDate: NepaliDateTime.now(),
                                              initialValue:
                                                  addEditPartyController
                                                      .party.openingDateBS,
                                              onChange: (selectedDate) {
                                                addEditPartyController
                                                        .party.openingDateBS =
                                                    selectedDate;
                                                addEditPartyController.partyMain
                                                    .refresh();
                                              },
                                            ),
                                          ],
                                        ),

                                        const SizedBox(height: 15.0),

                                        //=================================Opening Balance Type Field
                                        FormBuilderChoiceChip(
                                          enabled: !addEditPartyController
                                              .readOnlyFlag,
                                          name: "opening_bal_type",
                                          selectedColor: (TxnType.openingPay ==
                                                  addEditPartyController
                                                      .party.openingType)
                                              ? colorRedLight
                                              : colorGreenDark,
                                          disabledColor: Colors.black12,
                                          labelStyle: const TextStyle(
                                              color: Colors.white),
                                          backgroundColor: Colors.black45,
                                          decoration: const InputDecoration(
                                            border: InputBorder.none,
                                          ),
                                          spacing: 30,
                                          initialValue: addEditPartyController
                                              .party.openingType,
                                          onChanged: (value) {
                                            addEditPartyController
                                                .party.openingType = value;
                                            addEditPartyController.partyMain
                                                .refresh();
                                          },
                                          alignment: WrapAlignment.center,
                                          options: [
                                            FormBuilderChipOption(
                                              value: TxnType.openingReceive,
                                              child: SizedBox(
                                                height: 40,
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.3,
                                                child: Row(
                                                  children: const [
                                                    SizedBox(
                                                      width: 10,
                                                    ),
                                                    Icon(
                                                      Icons.check_circle,
                                                      color: Colors.white,
                                                    ),
                                                    SizedBox(
                                                      width: 20,
                                                    ),
                                                    Text(
                                                      "लिनुपर्ने",
                                                      style: TextStyle(
                                                          fontSize: 18),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ),
                                            FormBuilderChipOption(
                                              value: TxnType.openingPay,
                                              child: SizedBox(
                                                height: 40,
                                                width: MediaQuery.of(context)
                                                        .size
                                                        .width *
                                                    0.3,
                                                child: Row(
                                                  children: const [
                                                    SizedBox(
                                                      width: 10,
                                                    ),
                                                    Icon(
                                                      Icons.check_circle,
                                                      color: Colors.white,
                                                    ),
                                                    SizedBox(
                                                      width: 20,
                                                    ),
                                                    Text("तिर्नुपर्ने",
                                                        style: TextStyle(
                                                            fontSize: 18)),
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),

            bottomNavigationBar: SafeArea(
              child: BottomSaveCancelButton(
                shadow: false,
                hasDelete: (addEditPartyController.editFlag &&
                        !addEditPartyController.readOnlyFlag)
                    ? true
                    : false,
                onDeleteBtnPressedFn: () async {
                  showAlertDialog(context,
                      okText: "YES",
                      hasCancel: true,
                      cancelText: "NO",
                      alertType: AlertType.Error,
                      alertTitle: "Confirm Delete",
                      onCloseButtonPressed: () async {
                    // Navigator.of(context).pop();
                    //     int counter = 0;
                    //     Navigator.popUntil(context, (route) => counter++ >= 2);

                    ProgressDialog progressDialog = ProgressDialog(context,
                        type: ProgressDialogType.normal, isDismissible: false);
                    progressDialog.update(
                        message: "Checking Permission. Please wait....");
                    await progressDialog.show();
                    Tuple2<bool, String> checkResp =
                        await PermissionWrapperController()
                            .requestForPermissionCheck(
                                forPermission: PermissionManager.partyDelete);
                    if (checkResp.item1) {
                      //has  permission
                      progressDialog.update(
                          message: "Deleting Data. Please wait....");
                      Tuple2<bool, String> deleteResp =
                          await LedgerRepository().delete(widget.partyID ?? "");
                      await progressDialog.hide();
                      if (deleteResp.item1) {
                        print("=========deleteResp.item1========");
                        print(deleteResp.item1);
                        print(deleteResp.item2);
                        await TransactionHelper.refreshPreviousPages();
                        // Navigator.of(context).pop();
                        showAlertDialog(context,
                            barrierDismissible: false,
                            alertType: AlertType.Success,
                            alertTitle: "", onCloseButtonPressed: () {
                          print("Close Button pressen harre");
                          int counter = 0;
                          Navigator.popUntil(
                              context, (route) => counter++ >= 2);
                        }, message: deleteResp.item2);
                      } else {
                        //cannot  delete  data
                        showAlertDialog(context,
                            alertType: AlertType.Error,
                            alertTitle: "",
                            message: deleteResp.item2);
                      }
                    } else {
                      await progressDialog.hide();
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "",
                          message: checkResp.item2);
                    }
                  }, message: "Are you sure you  want to  delete this party?");
                },
                enableFlag: !addEditPartyController.readOnlyFlag,
                onSaveBtnPressedFn: (addEditPartyController.readOnlyFlag)
                    ? null
                    : () async {
                        FocusScope.of(context).unfocus();
                        if (addEditPartyController.formKey.currentState!
                            .validate()) {
                          if (!await addEditPartyController.checkPhone(
                              addEditPartyController.editFlag
                                  ? addEditPartyController.party.ledgerId!
                                  : "")) {
                            showAlertDialog(context,
                                alertType: AlertType.Error,
                                alertTitle: "Error",
                                message: "Party with same Mobile No. Exists");
                            return;
                          }

                          if (addEditPartyController.party.ledgerTitle ==
                                  null ||
                              addEditPartyController.party.ledgerTitle == "") {
                            showAlertDialog(context,
                                alertType: AlertType.Error,
                                alertTitle: "Error",
                                message: "Name cannot be empty");
                            return;
                          }
                          if (null != addEditPartyController.party.tinNo &&
                              addEditPartyController.party.tinNo!.isNotEmpty) {
                            if (9 !=
                                addEditPartyController.party.tinNo!.length) {
                              showAlertDialog(context,
                                  alertType: AlertType.Error,
                                  alertTitle: "Error",
                                  message:
                                      'प्यान वा भ्याट नम्बर मिलेन\n(Invalid PAN or VAT)');
                              return;
                            }
                          }
                          if (addEditPartyController.party.tinFlag != null &&
                              addEditPartyController
                                  .party.tinFlag!.isNotEmpty) {
                            if (addEditPartyController.party.tinNo == null ||
                                addEditPartyController.party.tinNo!.length !=
                                    9) {
                              showAlertDialog(context,
                                  alertType: AlertType.Error,
                                  alertTitle: "Error",
                                  message:
                                      "Enter Pan/Vat No. of 9 digits only");
                              return;
                            }
                          }

                          if (addEditPartyController.party.tinNo != null &&
                              addEditPartyController.party.tinNo!.isNotEmpty) {
                            print("Tin NO chai xa hai xaa");
                            if (addEditPartyController.party.tinFlag == null ||
                                addEditPartyController.party.tinFlag!.isEmpty) {
                              print("Tin flag chai xaina hai xaa");
                              showAlertDialog(context,
                                  alertType: AlertType.Error,
                                  alertTitle: "Error",
                                  message: "Select PAN or VAT Type!");
                              return;
                            }
                          }

                          if (null != addEditPartyController.party.tinNo &&
                              addEditPartyController.party.tinNo!.isNotEmpty) {
                            if (null == addEditPartyController.party.tinFlag ||
                                addEditPartyController.party.tinFlag!.isEmpty) {
                              showAlertDialog(context,
                                  alertType: AlertType.Error,
                                  alertTitle: "Error",
                                  message:
                                      'प्यान वा भ्याट छान्नुहोस\n(Select PAN or VAT)');
                              return;
                            }
                          }

                          if (null != addEditPartyController.party.email &&
                              addEditPartyController.party.email!.isNotEmpty) {
                            final emailRegex = RegExp(
                              r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
                            );

                            if (!emailRegex.hasMatch(
                                addEditPartyController.party.email!)) {
                              showAlertDialog(
                                context,
                                alertType: AlertType.Error,
                                alertTitle: "Error",
                                message:
                                    'अवैध ईमेल ढाँचा\n(Invalid email format)', // Nepali and English message
                              );
                              return; // Stop further processing if email is invalid
                            }
                          }

                          if (parseDouble(addEditPartyController
                                  .party.openingBalance)! >
                              0) {
                            if (null ==
                                addEditPartyController.party.openingType) {
                              showAlertDialog(context,
                                  alertType: AlertType.Error,
                                  alertTitle: "Error",
                                  message:
                                      'सुरु मौज्दात लिनुपर्ने/तिर्नुपर्ने ? छान्नुहोस\n(Select Opening Balance Payable/Receivable)');
                              return;
                            }

                            if (null ==
                                addEditPartyController.party.openingDateBS) {
                              showAlertDialog(context,
                                  alertType: AlertType.Error,
                                  alertTitle: "Error",
                                  message:
                                      " सुरु मौज्दात मिति भर्नुहोस्\n(Fill Opening Balance Date)");
                              return;
                            }
                          }

                          if (addEditPartyController.party.ledgerTitle!
                                  .toLowerCase()
                                  .contains("cash sale") ||
                              addEditPartyController.party.ledgerTitle!
                                  .toLowerCase()
                                  .contains("cash purchase")) {
                            showAlertDialog(context,
                                alertType: AlertType.Error,
                                alertTitle: "Error",
                                message:
                                    "Cash Sale/Cash Purchase already exists.");
                            return;
                          }

                          if (addEditPartyController
                                  .mobileController.text.length <
                              10) {
                            showAlertDialog(context,
                                alertType: AlertType.Error,
                                alertTitle: "Error",
                                message: "Phone Number must be 10 digits");
                            return;
                          }

                          ProgressDialog progressDialog = ProgressDialog(
                              context,
                              type: ProgressDialogType.normal,
                              isDismissible: false);
                          progressDialog.update(
                              message: "Saving data. Please wait....");
                          await progressDialog.show();
                          bool status = false;
                          try {
                            if (!addEditPartyController.editFlag) {
                              status = await addEditPartyController.saveParty();
                            } else {
                              status =
                                  await addEditPartyController.updateParty();
                            }
                          } catch (e) {
                            // Log.e(tag, e.toString() + trace.toString());
                          }
                          await progressDialog.hide();

                          if (status) {
                            Navigator.pop(context, true);

                            TransactionHelper.refreshPreviousPages();

                            String message = (addEditPartyController.editFlag)
                                ? "Party Updated Successfully."
                                : "Party Created Successfully.";
                            showToastMessage(context,
                                message: message, duration: 2);
                          } else {
                            showAlertDialog(context,
                                alertType: AlertType.Error,
                                alertTitle: "Error",
                                message: "Failed To Save data");
                          }
                        }
                      },
              ),
            ),
          ),
        );
      }
    });
  }
}
