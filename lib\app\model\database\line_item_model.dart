class LineItemModel {
  LineItemModel({
    this.txnId,
    this.sno,
    this.itemId,
    this.quantity,
    this.pricePerUnit,
    this.totalAmount,
    this.lineItemUnitId,
    this.lineItemUnitConversionFactor,
    this.discountPercent,
    this.discountAmount,
    this.taxPercent,
    this.taxAmount,
  });

  String? txnId;
  int? sno;
  String? itemId;
  double? quantity;
  double? pricePerUnit;
  double? totalAmount;
  String? lineItemUnitId;
  double? lineItemUnitConversionFactor;

  double? discountPercent;
  double? discountAmount;
  double? taxPercent;
  double? taxAmount;

  factory LineItemModel.fromJson(Map<String, dynamic> json) => LineItemModel(
        txnId: json["txn_id"],
        sno: json["sno"],
        itemId: json["item_id"],
        quantity: (json["quantity"] ?? 0).toDouble(),
        pricePerUnit: (json["price_per_unit"] ?? 0).toDouble(),
        totalAmount: (json["total_amount"] ?? 0).toDouble(),
        lineItemUnitId: json["line_item_unit_id"],
        lineItemUnitConversionFactor:
            (json["line_item_unit_conversion_factor"] ?? 0).toDouble(),
        discountPercent: (json['line_item_discount_percent'] ?? 0).toDouble(),
        discountAmount: (json['line_item_discount_amount'] ?? 0).toDouble(),
        taxPercent: (json['line_item_tax_percent'] ?? 0).toDouble(),
        taxAmount: (json['line_item_tax_amount'] ?? 0).toDouble(),
      );

  Map<String, dynamic> toJson() => {
        "txn_id": txnId,
        "sno": sno,
        "item_id": itemId,
        "quantity": quantity,
        "price_per_unit": pricePerUnit,
        "total_amount": totalAmount,
        "line_item_unit_id": lineItemUnitId,
        "line_item_unit_conversion_factor": lineItemUnitConversionFactor,
        "line_item_discount_percent": discountPercent,
        "line_item_discount_amount": discountAmount,
        "line_item_tax_percent": taxPercent,
        "line_item_tax_amount": taxAmount,
      };
}
