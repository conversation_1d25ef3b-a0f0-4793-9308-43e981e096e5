class ReminderType {
  static final int payment = 1;
  static final int task = 2;

  static final Map<int, String> reminderTypeText = ({
    ReminderType.payment: "भुक्तानी रिमाइन्डर (Payment Reminder)",
    ReminderType.task: "कार्य रिमाइन्डर (Task Reminder)",
  });

  static final List reminderTypeList = [
    {
      "value": ReminderType.payment,
      "text": ReminderType.reminderTypeText[ReminderType.payment]
    },
    {
      "value": ReminderType.task,
      "text": ReminderType.reminderTypeText[ReminderType.task]
    },
  ];
}
