import 'package:get/get.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/shared_pref_helper1.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:tuple/tuple.dart';

// TODO: SYNC ISSUE - This controller only implements PUSH sync (unidirectional)
// TODO: CRITICAL PROBLEM: Missing pull sync causes web-mobile sync failures
// TODO: IMPACT: Backup/sync fails after web transactions because mobile is out of sync

class BackupController extends GetxController {
  final _lastSyncedAt = "".obs;

  String get lastSyncedAt => _lastSyncedAt.value;

  final _isLoading = true.obs;

  bool get isLoading => _isLoading.value;

  getBackupInfo() async {
    Map<String, dynamic> backupInfo = await SharedPrefHelper1().getBackupInfo();
    // Log.d("backup data $backupInfo");
    if (null != backupInfo['last_synced_at']) {
      DateTime syncDateTime = DateTime.parse(backupInfo["last_synced_at"]);
      _lastSyncedAt.value = toDateTimeBS(syncDateTime);

      // _lastSyncedAt.value = backupInfo['last_synced_at'];
    }
    _isLoading(false);
  }

  Future<bool> syncAllData() async {
    // sync and update last back up at

    // TODO: CRITICAL - This method only does PUSH sync, missing PULL sync
    // TODO: Should be: 1. Pull from server first, 2. Then push local changes
    // TODO: Current implementation causes sync failures after web transactions
    bool status = true;
    // status = await pullPendingQueries(pullCount: -1); // TODO: Re-enable this line
    status = status && await pushPendingQueries(all: true);
    await getBackupInfo();
    return status;
  }

  Future<Tuple2<bool, String>> syncAllDataWithMessage() async {
    // sync and update last back up at

    // TODO: CRITICAL - Missing bidirectional sync implementation
    // TODO: This is why backup fails after web transactions
    // TODO: Need to implement proper pull-then-push sync flow
    bool status = false;
    String message = "";
    // Tuple2<bool, String> pullResp =
    //     await pullPendingQueriesWithMessage(pullCount: -1); // TODO: Re-enable this

    // TODO: ISSUE - Only pushing changes, not pulling web changes first
    // TODO: This causes conflicts when web has newer data
    Tuple2<bool, String> pushResp =
        await pushPendingQueriesWithMessage(all: true);

    // Log.d("pull resp ${pullResp.item2}");
    // status = (pullResp.item1 && pushResp.item1); // TODO: Should check both pull and push
    status = (pushResp.item1); // TODO: Currently only checking push success

    String rawMessage = pushResp.item2 ?? "";

// Step 1: Remove the leading error code (like "524:")
    String withoutCode = rawMessage.contains(":")
        ? rawMessage.substring(rawMessage.indexOf(":") + 1).trim()
        : rawMessage.trim();

// Step 2: Remove any trailing "with statuscode : <code>" using regex
    message = withoutCode
        .replaceAll(
            RegExp(r'with statuscode\s*:\s*\d+', caseSensitive: false), '')
        .trim();

    // message = "${pushResp.item2}";

    //
    // if (pullResp.item2 == pushResp.item2) {
    //   message = "${pullResp.item2}";
    // }else{
    //   message = "${pullResp.item2}\n\n${pushResp.item2}";
    //
    // }

    await getBackupInfo();
    return Tuple2(status, message);
  }
}
