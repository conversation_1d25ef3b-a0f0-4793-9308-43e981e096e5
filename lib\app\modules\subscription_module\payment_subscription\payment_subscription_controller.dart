import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/package_model.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:tuple/tuple.dart';

class PaymentSubscriptionController extends GetxController {
  final String tag = "PaymentSubscriptionController";
  var _isLoading = true.obs;

  bool get isLoading => _isLoading.value;

  var _isError = false.obs;

  bool get isError => _isError.value;

  dynamic _subPackages = [].obs;

  dynamic get subPackages => _subPackages;

  String errorMessage = "";

  init() {
    getPackages();
  }

  Future<Tuple2<bool, String>> verifyEsewaPayment(String refCode) async {
    bool status = false;
    String message = "";
    // Log.d("sending data  $refCode");
    ApiBaseHelper apiBaseHelper = ApiBaseHelper();
    ApiResponse apiResponse = await apiBaseHelper
        .get(apiBaseHelper.VERIFY_ESEWA_PAYMENT + refCode, accessToken: true);

    if (apiResponse.status) {
      status = true;
      message = apiResponse.msg ?? "";
    } else {
      message = apiResponse.msg ?? "";
    }
    return Tuple2(status, message);
  }

  getPackages() async {
    _isError(false);
    _isLoading(true);
    errorMessage = "";

    ApiBaseHelper apiBaseHelper = ApiBaseHelper();
    ApiResponse apiResponse = await apiBaseHelper
        .get(apiBaseHelper.SUBSCRIPTION_PACKAGES, accessToken: true);

    if (apiResponse.status) {
      print("success in getting packages");
      print(apiResponse);
      try {
        List<dynamic> pckgs = (apiResponse.data['packages'] ?? []);
        // Log.d("pckgs  $pckgs");
        _subPackages.clear();
        _subPackages = pckgs.map((e) => PackageModel.fromJson(e)).toList();

        // Log.d(pckgs.map((e) => PackageModel.fromJson(e)).toList());
        // Log.d("data");
        _isLoading(false);
      } catch (e) {
        print("error in getting packages e walla");
        print(e.toString());
        _isError(true);
        errorMessage =
            "Cannot load packages at this moment. Please try again later.";
        _isLoading(false);
      }
    } else {
      //error in gettting links
      print("error in getting packages");
      print(apiResponse.msg);
      _isError(true);
      errorMessage = apiResponse.msg ?? "";
      _isLoading(false);
    }
  }

  String generateTransactionId() {
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomNumber = (DateTime.now().microsecondsSinceEpoch % 1000);
    return 'MK_${timestamp}_${randomNumber}';
  }

  initiatEsewaPayment(String env, String packageCode, String totalAmount,
      String uuid, String tax_amnt) async {
    ApiBaseHelper apiBaseHelper = ApiBaseHelper();
    ApiResponse apiResponse = await apiBaseHelper.post(
      apiBaseHelper.INITIATE_ESEWA_PAYMENT,
      {
        "env": env,
        "package_code": packageCode,
        "total_amount": totalAmount,
        "transaction_uuid": uuid,
        "txn_amount": tax_amnt,
      },
      accessToken: true,
    );
    print(apiResponse);
  }
}
