// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/model/database/unit_modal.dart';
import 'package:mobile_khaata_v2/app/model/others/item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/add_edit_item/add_edit_item_page.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/add_edit_unit/add_edit_unit_dialog.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/item_adjustment/item_adjustment_page.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/item_detail/item_detail_page.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';
import 'package:mobile_khaata_v2/app/repository/unit_repository.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:tuple/tuple.dart';

class ItemUnitListController extends GetxController {
  var _itemLoading = true.obs;
  bool get itemLoading => _itemLoading.value;

  var _unitLoading = true.obs;
  bool get unitLoading => _unitLoading.value;

  List<ItemDetailModel> _items = [];
  List<ItemDetailModel> _filteredItems = [];
  List<ItemDetailModel> get filteredItems => _filteredItems;

  List<UnitModel> _units = [];
  List<UnitModel> _filteredUnits = [];
  List<UnitModel> get filteredUnits => _filteredUnits;

  final ItemRepository _itemRepository = ItemRepository();
  final UnitRepository _unitRepository = UnitRepository();

  initItem() async {
    _itemLoading(true);

    _items = await _itemRepository.getAllActiveItemsDetail();

    _filteredItems.clear();
    _filteredItems.addAll(_items);

    _itemLoading(false);
  }

  initUnit() async {
    _unitLoading.value = true;

    _units = await _unitRepository.getAllUnits();

    _filteredUnits.clear();
    _filteredUnits.addAll(_units);

    _unitLoading.value = false;
  }

  searchItem(String searchString) {
    _itemLoading(true);
    _filteredItems.clear();

    for (var item in _items) {
      _filteredItems.addIf(
          item.itemName!
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          item);
    }

    _itemLoading(false);
  }

  searchUnit(String searchString) {
    _unitLoading.value = true;
    _filteredUnits.clear();

    for (var unit in _units) {
      _filteredUnits.addIf(
          unit.unitName!
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          unit);
    }

    _unitLoading.value = false;
  }

  Future<bool> addItemButtonOnPressedHandler(BuildContext context) async {
    final res = await Navigator.pushNamed(context, "/addItem");
    if (res != null) {
      initUnit();
      return true;
    }
    return false;
  }

  editItemButtonOnPressedHandler(BuildContext context, String itemId) async {
    print("this is main id $itemId ");
    final res = await Navigator.pushNamed(context, "/addItem",
        arguments: AddEditItemPage(
          itemId: itemId,
        ));
    if (res != null) {
      initItem();
    }
  }

  adjustmentButtonOnPressedHandler(BuildContext context, String itemId) async {
    final res = await Navigator.pushNamed(context, "/itemAdjustment",
        arguments: ItemAdjustmentPage(
          itemId: itemId,
        ));
    if (res != null) {
      initItem();
    }
  }

  Future<bool> showUnitAddEditForm(BuildContext context, String unitId) async {
    UnitModel? unit = await displayAddEditUnitDialog(context, unitId: unitId);
    if (unit == null) return false;
    UnitRepository unitRepo = UnitRepository();

    await unitRepo.insert(unit);
    initUnit();
    return true;
    // print("this is unit ${unit!.unitName}");
    // if (null != unit) initUnit();
  }

  Future<bool> addUnitButtonOnPressedHandler(BuildContext context) async {
    Tuple2<bool, String> checkResponse = await checkPermission(
        context: context, forPermission: PermissionManager.itemAdd);
    if (checkResponse.item1) {
      final dbool = await showUnitAddEditForm(context, "");
      initUnit();
      return dbool;
    } else {
      showAlertDialog(
        context,
        alertType: AlertType.Error,
        alertTitle: "",
        message: checkResponse.item2,
        onCloseButtonPressed: () {
          Navigator.of(context).pop();
        },
      );
      return false;
    }
  }

  editUnitButtonOnPressedHandler(BuildContext context, String unitId) async {
    Tuple2<bool, String> checkResponse = await checkPermission(
        context: context, forPermission: PermissionManager.itemAdd);
    if (checkResponse.item1) {
      showUnitAddEditForm(context, unitId);
    } else {
      showAlertDialog(
        context,
        alertType: AlertType.Error,
        alertTitle: "",
        onCloseButtonPressed: () {
          Navigator.of(context).pop();
        },
        message: checkResponse.item2,
      );
    }
  }

  deleteUnitButtonOnPressedHandler(BuildContext context, String unitId) async {
    showAlertDialog(
      context,
      okText: "YES",
      alertType: AlertType.Error,
      alertTitle: "Confirm Delete",
      message: "Are you sure you  want to  delete this item?",
      onCloseButtonPressed: () async {
        // Navigator.of(context).pop();
        ProgressDialog progressDialog = ProgressDialog(context,
            type: ProgressDialogType.normal, isDismissible: false);
        progressDialog.update(message: "Checking Permission. Please wait....");
        await progressDialog.show();
        Tuple2<bool, String> checkResp = await PermissionWrapperController()
            .requestForPermissionCheck(
                forPermission: PermissionManager.itemDelete);
        if (checkResp.item1) {
          //has  permission
          progressDialog.update(message: "Deleting Data. Please wait....");
          Tuple2<bool, String> deleteResp =
              await UnitRepository().delete(unitId);
          await progressDialog.hide();
          if (deleteResp.item1) {
            //  data deleted
            TransactionHelper.refreshPreviousPages();
            initUnit();
            showAlertDialog(
              context,
              // barrierDismissible: false,
              alertType: AlertType.Success,
              alertTitle: "",
              message: deleteResp.item2,
              onCloseButtonPressed: () {
                Navigator.of(context).pop();
              },
            );
          } else {
            //cannot  delete  data
            showAlertDialog(
              context,
              alertType: AlertType.Error,
              alertTitle: "",
              message: deleteResp.item2,
              onCloseButtonPressed: () {
                Navigator.of(context).pop();
              },
            );
          }
        } else {
          await progressDialog.hide();
          showAlertDialog(
            context,
            alertType: AlertType.Error,
            alertTitle: "",
            message: checkResp.item2,
            onCloseButtonPressed: () {
              Navigator.of(context).pop();
            },
          );
        }
      },
    );
  }

  itemListOnTapHandler(BuildContext context, String itemId) async {
    final res = await Navigator.pushNamed(context, "/itemDetail",
        arguments: ItemDetailPage(
          itemId: itemId,
        ));
    if (res != null) {
      initItem();
    }
  }
}
