import 'package:get/get.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/shared_pref_helper1.dart';

import 'package:tuple/tuple.dart';

class SubscriptionPageController extends GetxController {
  final String tag = "SubscriptionPageController";
  var _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  var _isExpired = false.obs;
  bool get isExpired => _isExpired.value;

  var _isChecking = false.obs;
  bool get isChecking => _isChecking.value;

  var _subDetail = {}.obs;
  dynamic get subDetail => _subDetail;

  @override
  void onInit() {
    super.onInit();
    checkExpiry();
    checkSub();
    recheckExpiryFromNetwork();
  }

  checkSub() async {
    dynamic d = await SharedPrefHelper1().getSubsDetail ?? {};
    // Log.d("gettig  from shaared pref $d");
    _subDetail(d);
  }

  Future<bool> checkExpiry() async {
    bool status = await SharedPrefHelper1().isExpired;
    // Log.d("is Expired checking $status");
    _isExpired(status);
    return status;
  }

  Future<Tuple2<bool, String>> recheckExpiryFromNetwork() async {
    bool expired = true;
    String message = "";
    _isChecking(true);
    try {
      ApiBaseHelper apiBaseHelper = ApiBaseHelper();
      ApiResponse apiResponse = await apiBaseHelper
          .get(apiBaseHelper.RECHECK_EXPIRY, accessToken: true);

      if (apiResponse.status) {
        // _subDetail(apiResponse.data);
        await SharedPrefHelper1().setSubsDetail(apiResponse.data);
        expired = apiResponse.data['is_account_expired'];
        message = apiResponse.msg ?? "";

        if (!expired) {
          await setExpiredUser(flag: false);
        } else {
          await setExpiredUser(flag: true);
        }

        checkSub();
        checkExpiry();
      } else {
        message = apiResponse.msg ?? "";
      }
    } catch (e) {
      print(e);
      message = "Cannot check payment at this moment. Please try again later";
    }
    _isChecking(false);

    return Tuple2(expired, message);
  }

  makeEsewaPayment() {}

  makeKhaltiPayment() {}
}
