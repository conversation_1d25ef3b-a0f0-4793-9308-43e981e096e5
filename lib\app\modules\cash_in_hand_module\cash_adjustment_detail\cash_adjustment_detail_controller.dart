import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/cash_txn_model.dart';
import 'package:mobile_khaata_v2/app/repository/cash_adjustment_repository.dart';
import 'package:tuple/tuple.dart';

class CashAdjustmentDetailController extends GetxController {
  final String tag = "CashAdjustmentDetailController";

  var _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  var _isSearching = true.obs;
  bool get isSearching => _isSearching.value;

  List<CashTxnModel> _cashTxnList = [];
  List<CashTxnModel> _filteredCashTxnList = [];
  List<CashTxnModel> get filteredCashTxnList => _filteredCashTxnList;

  double _totalCashInHand = 0.00;
  double get totalCashInHand => _totalCashInHand;

  CashAdjustmentRepository _cashAdjustmentRepository =
      CashAdjustmentRepository();

  @override
  onClose() {
    clearList();
    super.onClose();
  }

  clearList() {
    _cashTxnList.clear();
    _filteredCashTxnList.clear();
  }

  init() async {
    _isLoading.value = true;
    clearList();

    Tuple2<double, List<CashTxnModel>> data =
        await _cashAdjustmentRepository.getAllCashInHandTxnWithTotal();

    _totalCashInHand = data.item1;
    _cashTxnList = data.item2;

    print("total cash in hand ko samadhan: $_totalCashInHand");
    print("total cash in hand ko samadhan2: $data");

    _filteredCashTxnList.addAll(_cashTxnList);
    _isLoading.value = false;
  }

  searchTransaction(String searchString) {
    _isSearching(true);

    _filteredCashTxnList.clear();
    for (var item in _cashTxnList) {
      _filteredCashTxnList.addIf(
          item
              .toString()
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          item);
    }

    _isSearching(false);
  }
}
