import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class ItemDetailModel {
  ItemDetailModel({
    this.itemId,
    this.itemCode,
    this.itemName,
    this.categoryId,
    this.itemLocation,
    this.itemType,
    this.openingStock,
    this.openingDate,
    this.openingDateBS,
    this.itemSaleUnitPrice,
    this.itemPurchaseUnitPrice,
    this.itemMinStockQuantity,
    this.baseUnitId,
    this.unitName,
    this.unitShortName,
    this.alternateUnitId,
    this.altUnitName,
    this.altUnitShortName,
    this.unitConversionFactor,
    this.balanceQuantity,
    this.inQuantity,
    this.outQuantity,
    this.openingQuantity,
    this.totalPurchaseAmount,
    this.totalPurchaseQuantity,
  });

  String? itemId;
  String? itemCode;
  String? itemName;
  String? categoryId;
  String? itemLocation;
  num? itemType;
  num? openingStock;
  String? openingDate;
  String? openingDateBS;
  num? itemSaleUnitPrice;
  num? itemPurchaseUnitPrice;
  num? itemMinStockQuantity;
  String? baseUnitId;
  String? unitName;
  String? unitShortName;
  String? alternateUnitId;
  String? altUnitName;
  String? altUnitShortName;
  num? unitConversionFactor;

  num? balanceQuantity;
  num? inQuantity;
  num? outQuantity;
  num? openingQuantity;
  num? totalPurchaseAmount;
  num? totalPurchaseQuantity;
  num? avgPurchaseRate;

  factory ItemDetailModel.fromJson(Map<String, dynamic> json) {
    DateTime? openingDateTimeObj =
        DateTime.tryParse(json["opening_date"] ?? "");

    return ItemDetailModel(
      itemId: json["item_id"],
      itemCode: json["item_code"],
      itemName: json["item_name"],
      categoryId: json["category_id"],
      itemLocation: json["item_location"],
      itemType: json["item_type"],
      openingStock: json["opening_stock"],
      openingDate: (null != openingDateTimeObj)
          ? DateFormat('y-MM-dd').format(openingDateTimeObj)
          : '',
      openingDateBS:
          (null != openingDateTimeObj) ? toDateBS(openingDateTimeObj) : '',
      itemSaleUnitPrice: json["item_sale_unit_price"],
      itemPurchaseUnitPrice: json["item_purchase_unit_price"],
      itemMinStockQuantity: json["item_min_stock_quantity"],
      baseUnitId: json["base_unit_id"],
      unitName: json["unit_name"],
      unitShortName: json["unit_short_name"],
      alternateUnitId: json["alternate_unit_id"],
      altUnitName: json["alt_unit_name"],
      altUnitShortName: json["alt_unit_short_name"],
      unitConversionFactor: json["unit_conversion_factor"] != null
          ? num.tryParse(json["unit_conversion_factor"].toString())
          : null,
      balanceQuantity: json["balance_quantity"],
      inQuantity: json["in_quantity"],
      outQuantity: json["out_quantity"],
      openingQuantity: json["opening_quantity"],
      totalPurchaseAmount: json["total_purchase_amount"],
      totalPurchaseQuantity: json["total_purchase_quantity"],
    );
  }

  Map<String, dynamic> toJson() => {
        "item_id": itemId,
        "item_code": itemCode,
        "item_name": itemName,
        "category_id": categoryId,
        "item_location": itemLocation,
        "item_type": itemType,
        "opening_stock": openingStock,
        "opening_date": openingDate,
        "opening_date_bs": openingDateBS,
        "item_sale_unit_price": itemSaleUnitPrice,
        "item_purchase_unit_price": itemPurchaseUnitPrice,
        "item_min_stock_quantity": itemMinStockQuantity,
        "base_unit_id": baseUnitId,
        "unit_name": unitName,
        "unit_short_name": unitShortName,
        "alternate_unit_id": alternateUnitId,
        "alt_unit_name": altUnitName,
        "alt_unit_short_name": altUnitShortName,
        "unit_conversion_factor": unitConversionFactor,
        "balance_quantity": balanceQuantity,
        "in_quantity": inQuantity,
        "out_quantity": outQuantity,
        "opening_quantity": openingQuantity,
      };
}
