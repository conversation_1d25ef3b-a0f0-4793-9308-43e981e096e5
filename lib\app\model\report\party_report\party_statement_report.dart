// // ignore_for_file: library_private_types_in_public_api
//
// import 'package:flutter/material.dart';
// import 'package:get/get.dart';
// import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
// import 'package:mobile_khaata_v2/app/components/custom_dropdown.dart';
// import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
// import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
// import 'package:mobile_khaata_v2/app/model/report/party_report/party_statement_report_controller.dart';
// import 'package:mobile_khaata_v2/app/model/report/party_satement_report_model.dart';
// import 'package:mobile_khaata_v2/app/modules/party_module/party_list/party_list_controller.dart';
// import 'package:mobile_khaata_v2/app/modules/print_module/party_statement_print_page.dart';
// import 'package:mobile_khaata_v2/utilities/common_helper.dart';
// import 'package:mobile_khaata_v2/utilities/styles.dart';
// import 'package:nepali_date_picker/nepali_date_picker.dart';
//
// // ignore: must_be_immutable
// class PartyStatementReport extends StatefulWidget {
//   const PartyStatementReport({super.key});
//
//   @override
//   _PartyStatementReportState createState() => _PartyStatementReportState();
// }
//
// class _PartyStatementReportState extends State<PartyStatementReport> {
//   final PartyStatementReportController _controller =
//       PartyStatementReportController();
//   var partyListController = PartyListController();
//
//   List<int> types = [];
//
//   String startDate = currentDate;
//   String endDate = currentDate;
//   String? ledgerID;
//   List<LedgerDetailModel> ledgers = [];
//
//   @override
//   void initState() {
//     super.initState();
//     getPartyList();
//   }
//
//   getPartyList() async {
//     await partyListController.init();
//     setState(() {
//       ledgers = partyListController.filteredLedgers;
//     });
//   }
//
//   generate() async {
//     if (null == ledgerID) {
//       return;
//     }
//     _controller.generatePartyReport(
//         startDate: startDate,
//         endDate: endDate,
//         types: types,
//         ledgerID: ledgerID);
//   }
//
//   @override
//   void dispose() {
//     super.dispose();
//     partyListController.dispose();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return SafeArea(
//         child: Scaffold(
//       // resizeToAvoidBottomPadding: true,
//       resizeToAvoidBottomInset: true,
//       appBar: AppBar(
//         elevation: 0,
//         titleSpacing: -5.0,
//         backgroundColor: colorPrimary,
//         title: const Text(
//           "पार्टी खाता विवरण (Party Statement)",
//           style: TextStyle(
//               fontSize: 17,
//               color: Colors.white,
//               fontFamily: 'HelveticaRegular',
//               fontWeight: FontWeight.bold),
//         ),
//         actions: [
//           PrintButton(
//             onPressed: null == ledgerID
//                 ? null
//                 : () {
//                     var partyText = "All Party";
//                     if (null != ledgerID) {
//                       LedgerDetailModel l = ledgers.firstWhere(
//                           (element) => element.ledgerId == ledgerID);
//                       partyText = l.ledgerTitle ?? "";
//                     }
//                     Navigator.pushNamed(context, '/printSinglePartyStatement',
//                         arguments: PartyStatementPrintPage(
//                           transactions: _controller.transactions,
//                           partyText: partyText,
//                           startDate: startDate,
//                           endDate: endDate,
//                         ));
//                   },
//           ),
//         ],
//       ),
//       body: GestureDetector(
//         onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
//         child: Container(
//           color: Colors.black12,
//           child: Column(
//             // mainAxisSize: MainAxisSize.min,
//             children: [
//               //=============================transaction date filter
//               Container(
//                 padding: const EdgeInsets.symmetric(horizontal: 10),
//                 child: Row(
//                   children: [
//                     Expanded(
//                       flex: 2,
//                       child: ReportCustomDatePickerTextField(
//                         initialValue: toDateBS(DateTime.parse(startDate)),
//                         hintText: "From Date",
//                         onChange: (selectedDate) {
//                           startDate =
//                               toDateAD(NepaliDateTime.parse(selectedDate));
//                           setState(() {});
//                           generate();
//                         },
//                       ),
//                     ),
//                     Expanded(
//                       child: Container(
//                         padding: const EdgeInsets.symmetric(horizontal: 10),
//                         child: Text(
//                           "TO",
//                           style: labelStyle2,
//                           textAlign: TextAlign.center,
//                         ),
//                       ),
//                     ),
//                     Expanded(
//                       flex: 2,
//                       child: ReportCustomDatePickerTextField(
//                         initialValue: toDateBS(DateTime.parse(endDate)),
//                         hintText: "To Date",
//                         onChange: (selectedDate) {
//                           endDate =
//                               toDateAD(NepaliDateTime.parse(selectedDate));
//                           setState(() {});
//                           generate();
//                         },
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//
//               const Divider(
//                 height: 4,
//                 color: Colors.black54,
//               ),
//               Container(
//                 padding: const EdgeInsets.symmetric(horizontal: 10),
//                 child: Obx(() {
//                   if (partyListController.partyLoading) {
//                     return Container(
//                       height: 48,
//                       decoration: BoxDecoration(
//                         border: Border.all(color: Colors.grey),
//                         borderRadius: BorderRadius.circular(4),
//                       ),
//                       child: const Center(
//                         child: CircularProgressIndicator(),
//                       ),
//                     );
//                   }
//                   return CustomDropdown(
//                       borderless: true,
//                       style: formFieldTextStyle,
//                       decoration: formFieldStyle,
//                       value: ledgerID,
//                       allowClear: false,
//                       placeholder: "Select Party",
//                       options: [
//                         ...partyListController.filteredLedgers.map((e) {
//                           return {'key': e.ledgerId, 'value': e.ledgerTitle};
//                         }).toList(),
//                       ],
//                       onChange: (value) {
//                         // Log.d("on change $value");
//                         ledgerID = value;
//                         setState(() {});
//                         generate();
//                       });
//                 }),
//               ),
//               const Divider(
//                 height: 4,
//                 color: Colors.black54,
//               ),
//
//               Obx(() {
//                 if (_controller.txnLoading) {
//                   return Container(
//                       color: Colors.white,
//                       child: const Center(child: CircularProgressIndicator()));
//                 }
//
//                 if (_controller.transactions.isEmpty) {
//                   return Container(
//                       color: Colors.white,
//                       width: double.infinity,
//                       child: const Center(
//                           child: Text(
//                         "No Records",
//                         style: TextStyle(color: Colors.black54),
//                       )));
//                 } else {
//                   return Expanded(
//                       child: ListView.builder(
//                     itemCount: _controller.transactions.length + 1,
//                     // shrinkWrap: true,
//                     itemBuilder: (context, int index) {
//                       if (0 == index) {
//                         return Container(
//                           color: Colors.white,
//                           child: Column(
//                             children: [
//                               DefaultTextStyle(
//                                 style: TextStyle(
//                                     fontSize: 14,
//                                     color: textColor,
//                                     fontWeight: FontWeight.bold),
//                                 child: Container(
//                                   padding: const EdgeInsets.symmetric(
//                                       vertical: 10, horizontal: 5),
//                                   child: Row(
//                                     mainAxisAlignment:
//                                         MainAxisAlignment.spaceBetween,
//                                     children: const [
//                                       //====================================1st Column
//                                       Expanded(
//                                         flex: 3,
//                                         child: Text(
//                                           "Particulars",
//                                           textAlign: TextAlign.left,
//                                         ),
//                                       ),
//
//                                       //====================================2nd Column
//                                       Expanded(
//                                         flex: 3,
//                                         child: Text(
//                                           "Dr",
//                                           textAlign: TextAlign.right,
//                                         ),
//                                       ),
//
//                                       //====================================3rd Column
//                                       Expanded(
//                                         flex: 3,
//                                         child: Text(
//                                           "Cr",
//                                           textAlign: TextAlign.right,
//                                         ),
//                                       ),
//                                       Expanded(
//                                         flex: 4,
//                                         child: Text(
//                                           "Balance",
//                                           textAlign: TextAlign.right,
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                               ),
//                               const Divider(
//                                 height: 4,
//                                 color: Colors.black54,
//                               ),
//                             ],
//                           ),
//                         );
//                       }
//
//                       PartyStatementReportModel txn =
//                           _controller.transactions[index - 1];
//
//                       // CALCULATE RUNNING BALANCE:
//                       // For the first transaction (opening balance), use its own balance
//                       // For subsequent transactions, calculate cumulative balance
//                       double runningBalance = 0.0;
//
//                       if (index == 1) {
//                         // First transaction after header (opening balance)
//                         runningBalance =
//                             (txn.drAmount ?? 0.0) - (txn.crAmount ?? 0.0);
//                       } else {
//                         // Calculate cumulative balance up to current transaction
//                         double cumulativeBalance = 0.0;
//                         for (int i = 0; i < index; i++) {
//                           PartyStatementReportModel prevTxn =
//                               _controller.transactions[i];
//                           cumulativeBalance += (prevTxn.drAmount ?? 0.0) -
//                               (prevTxn.crAmount ?? 0.0);
//                         }
//                         runningBalance = cumulativeBalance;
//                       }
//
//                       return InkWell(
//                         child: Container(
//                           color: Colors.white,
//                           child: Column(
//                             children: [
//                               DefaultTextStyle(
//                                 style: TextStyle(
//                                     fontSize: 14, color: colorPrimary),
//                                 child: Container(
//                                   padding: const EdgeInsets.symmetric(
//                                       vertical: 10, horizontal: 5),
//                                   child: Row(
//                                     mainAxisAlignment:
//                                         MainAxisAlignment.spaceBetween,
//                                     children: [
//                                       //====================================1st Column
//                                       Expanded(
//                                         flex: 3,
//                                         child: Column(
//                                           crossAxisAlignment:
//                                               CrossAxisAlignment.start,
//                                           children: [
//                                             Text(
//                                               "${txn.description}",
//                                               textAlign: TextAlign.left,
//                                             ),
//                                             const SizedBox(height: 3),
//                                             Text(
//                                               "${txn.txnDateBS}",
//                                               style: const TextStyle(
//                                                   fontSize: 12,
//                                                   color: Colors.black54),
//                                             ),
//                                             const SizedBox(height: 5),
//                                             Text(
//                                               "Ref No:  ${(null != txn.txnRefNumberChar) ? "#${txn.txnRefNumberChar}" : ""}",
//                                               style: const TextStyle(
//                                                   fontSize: 12,
//                                                   color: Colors.black54),
//                                             ),
//                                           ],
//                                         ),
//                                       ),
//
//                                       //====================================2nd Column - DEBIT
//                                       Expanded(
//                                         flex: 3,
//                                         child: Text(
//                                           formatCurrencyAmount(
//                                               txn.drAmount ?? 0.00, false),
//                                           textAlign: TextAlign.right,
//                                         ),
//                                       ),
//
//                                       //====================================3rd Column - CREDIT
//                                       Expanded(
//                                         flex: 3,
//                                         child: Text(
//                                           formatCurrencyAmount(
//                                               txn.crAmount ?? 0.00, false),
//                                           textAlign: TextAlign.right,
//                                         ),
//                                       ),
//
//                                       //====================================4th Column - RUNNING BALANCE
//                                       // FIXED: Shows cumulative running balance, not just Dr - Cr
//                                       Expanded(
//                                         flex: 4,
//                                         child: Text(
//                                           formatCurrencyAmount(
//                                             runningBalance,
//                                             false,
//                                           ),
//                                           textAlign: TextAlign.right,
//                                           style: TextStyle(
//                                             fontWeight: FontWeight.w600,
//                                             // Color coding for balance type
//                                             color: runningBalance >= 0
//                                                 ? Colors.green[
//                                                     700] // Positive = Receivable
//                                                 : Colors.red[
//                                                     700], // Negative = Payable
//                                           ),
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                 ),
//                               ),
//                               const Divider(
//                                 height: 4,
//                                 color: Colors.black54,
//                               ),
//
//                               //Add space if last element
//                               if (_controller.transactions.length - 1 ==
//                                   index) ...{const SizedBox()},
//                             ],
//                           ),
//                         ),
//                       );
//                     },
//                   ));
//                 }
//               }),
//             ],
//           ),
//         ),
//       ),
//       // extendBody: true,
//       bottomNavigationBar: Container(
//         height: 45,
//         padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
//         color: colorPrimary,
//         child: SingleChildScrollView(
//           child: Obx(() {
//             return DefaultTextStyle(
//               style: const TextStyle(
//                 color: Colors.white,
//                 fontSize: 16,
//               ),
//               child: Row(
//                 children: [
//                   Expanded(
//                     flex: 1,
//                     child: Text(_controller.totalClosingBalance.value >= 0
//                         ? "Closing (Receivable)"
//                         : "Closing (Payable)"),
//                   ),
//                   Expanded(
//                       // flex: 1,
//                       child: Text(
//                     formatCurrencyAmount(
//                         _controller.totalClosingBalance.value.abs(), false),
//                     textAlign: TextAlign.right,
//                   )),
//                 ],
//               ),
//             );
//           }),
//         ),
//       ),
//     ));
//   }
// }
