import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_custom_clippers/flutter_custom_clippers.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

import 'package:localstorage/localstorage.dart';
import 'package:mobile_khaata_v2/app/modules/intro_page_module/get_started_controller.dart';

import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class GetStartedIntro extends StatefulWidget {
  @override
  _GetStartedIntroState createState() {
    return new _GetStartedIntroState();
  }
}

class _GetStartedIntroState extends State<GetStartedIntro> {
  final controller = GetStartedController();
  @override
  void initState() {
    super.initState();
    controller.init();
  }

  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;

    return Container(
      decoration: const BoxDecoration(color: Color(0xFFf5f8ff)),
      child: Stack(
        fit: StackFit.expand,
        children: [
          Positioned(
            top: screenHeight * 0,
            left: 0,
            right: 0,
            bottom: screenHeight * 0.21,
            child: ClipPath(
              clipper: DiagonalPathClipperTwo(),
              child: Container(
                width: double.infinity,
                margin: const EdgeInsets.symmetric(horizontal: 10),
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                decoration:
                    BoxDecoration(color: Colors.white, boxShadow: downShadow),
                child: SingleChildScrollView(
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Column(
                      children: [
                        const SizedBox(
                          height: 20,
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Obx(() {
                          if (controller.isLoading) {
                            return Container(
                                height: screenWidth * 0.4,
                                color: colorPrimary,
                                child: const Center(
                                    child: CircularProgressIndicator()));
                          }
                          if (controller.isError) {
                            return Column(
                              children: [
                                Icon(
                                  FontAwesomeIcons.exclamationCircle,
                                  size: 30,
                                  color: colorRedDark,
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Text(controller.errorMessage),
                                const SizedBox(
                                  height: 10,
                                ),
                                ElevatedButton(
                                    onPressed: () {
                                      controller.init();
                                    },
                                    child: Text(
                                      "Retry",
                                      style: labelStyle2,
                                    ))
                              ],
                            );
                          }
                          return Obx(
                            () => Column(
                              children: [
                                Container(
                                    height: screenWidth * 0.4,
                                    child: "video" == controller.urlType
                                        ? YoutubePlayer(
                                            controller: YoutubePlayerController(
                                              initialVideoId:
                                                  YoutubePlayer.convertUrlToId(
                                                      controller.url!)!,
                                              flags: const YoutubePlayerFlags(
                                                hideControls: false,
                                                controlsVisibleAtStart: true,
                                                autoPlay: false,
                                                mute: false,
                                              ),
                                            ),
                                            showVideoProgressIndicator: true,
                                            progressIndicatorColor: Colors.red,
                                          )
                                        : "image" == controller.urlType
                                            ? Container(
                                                height: screenWidth * 0.4,
                                                color: Colors.transparent,
                                                child: Image.network(
                                                  "${controller.url}",
                                                  fit: BoxFit.fitHeight,
                                                ),
                                              )
                                            : const SizedBox(
                                                height: 0,
                                                width: 0,
                                              )),
                                const SizedBox(
                                  height: 10,
                                ),
                                Text(
                                  "${controller.title}",
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(
                                      fontSize: 20,
                                      fontWeight: FontWeight.w800,
                                      fontFamily: "HelveticaRegular"),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Text(
                                  "${controller.subTitle}",
                                  textAlign: TextAlign.center,
                                  style: const TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.normal,
                                  ),
                                ),
                                const SizedBox(
                                  height: 10,
                                ),
                                Text(
                                  "${controller.subTitle}",
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          );
                        }),
                        const SizedBox(
                          height: 10,
                        ),
                        ElevatedButton(
                          child: const Text("Get Started"),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: colorPrimary,
                          ),
                          // buttonText: "Get Started",
                          onPressed: () async {
                            final storage = new LocalStorage(MobileSettings);
                            await storage.ready;
                            storage.setItem(FreshInstallStatus, false);
                            Navigator.pushReplacementNamed(context, '/login');
                          },
                        )
                      ],
                    )),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build11(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;

    return Container(
        decoration: const BoxDecoration(color: Color(0XFFF5F8FF)),
        child: Stack(
          fit: StackFit.expand,
          children: [
            Positioned(
              top: screenHeight * 0.05,
              left: 0,
              right: 0,
              bottom: screenHeight * 0.21,
              child: ClipPath(
                clipper: DiagonalPathClipperTwo(),
                child: Container(
                  width: double.infinity,
                  margin: const EdgeInsets.symmetric(horizontal: 10),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                  decoration:
                      BoxDecoration(color: Colors.white, boxShadow: downShadow),
                  child: SingleChildScrollView(
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: Column(
                        children: [
                          const SizedBox(
                            height: 20,
                          ),
                          Container(
                            width: 220,
                            height: 220,
                            decoration: BoxDecoration(
                                color: colorOrangeDark, boxShadow: downShadow),
                          ),
                          const SizedBox(
                            height: 40,
                          ),
                          const Text(
                            "Learn more about mobile खाता",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                fontSize: 20,
                                fontWeight: FontWeight.w800,
                                fontFamily: "HelveticaRegular"),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          const Text(
                            "Watch the video to learn how to use mobile खाता.",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.normal,
                            ),
                          ),
                          const SizedBox(
                            height: 20,
                          ),
                          // GradientButtonBlue(
                          //   buttonText: "Get Started",
                          //   onClick: () async {
                          //     final storage = new LocalStorage(MobileSettings);
                          //     await storage.ready;
                          //     storage.setItem(FreshInstallStatus, false);
                          //     Navigator.pushReplacementNamed(context, '/login');
                          //   },
                          // )
                        ],
                      )),
                ),
              ),
            ),
          ],
        ));
  }
}
