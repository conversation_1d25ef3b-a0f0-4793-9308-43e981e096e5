import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/reminder_model.dart';
import 'package:mobile_khaata_v2/app/repository/reminder_repository.dart';
import 'package:mobile_khaata_v2/database/reminder_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:nepali_utils/nepali_utils.dart';

class AddEditReminderController extends GetxController {
  final String tag = "AddEditReminderController";

  final _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  final _editFlag = false.obs;
  bool get editFlag => _editFlag.value;

  final _readOnlyFlag = false.obs;
  bool get readOnlyFlag => _readOnlyFlag.value;
  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  var _reminder = ReminderModel(
      reminderType: ReminderType.task, startDateBS: tomorrowDateBS);
  ReminderModel get reminder => _reminder;

  final ReminderRepository _reminderRepository = ReminderRepository();

  final formKey = GlobalKey<FormState>();

  @override
  void onClose() {
    super.onClose();
  }

  initEdit(reminderId, readOnlyFlag) async {
    _isLoading(true);
    _reminder = await _reminderRepository.getReminderById(reminderId);

    _editFlag.value = true;
    _readOnlyFlag.value = readOnlyFlag;

    _isLoading(false);
  }

  Future<bool> createReminder() async {
    bool status = false;
    try {
      // reminder.startDatetime = toDateAD(NepaliDateTime.parse(strTrim(reminder.startDateBS))) + " " + DateFormat("HH:mm:ss").format(DateFormat.jm().parse(strTrim(reminder.startTime)));

      reminder.startDatetime =
          toDateAD(NepaliDateTime.parse(strTrim(reminder.startDateBS ?? "")));
      await _reminderRepository.insert(reminder) ?? "";
      status = true;
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<bool> updateReminder() async {
    bool status = false;
    try {
      // reminder.startDatetime = toDateAD(NepaliDateTime.parse(strTrim(reminder.startDateBS))) + " " + DateFormat("HH:mm:ss").format(DateFormat.jm().parse(strTrim(reminder.startTime)));
      reminder.startDatetime =
          toDateAD(NepaliDateTime.parse(strTrim(reminder.startDateBS ?? "")));

      status = await _reminderRepository.update(reminder);
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }
}
