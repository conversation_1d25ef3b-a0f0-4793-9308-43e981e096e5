import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/expense_print.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/general_transaction_print_controller.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/paid_print.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/purchase_print.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/receipt_print.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/sale_print.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import 'package:printing/printing.dart';

class GeneralTransactionPrintPage extends StatelessWidget {
  final String? txnID;
  final int? txnType;
  final generalTransactionPrintCotroller = GeneralTransactionPrintCotroller();

  GeneralTransactionPrintPage({this.txnID, this.txnType}) {
    generalTransactionPrintCotroller.getTransactionDetails(txnID!, txnType!);
  }

  String titleForTxnType() {
    return TxnType.txnTypeText[txnType]! + " Preview";
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (generalTransactionPrintCotroller.isLoading) {
        return Container(
            color: Colors.white,
            child: Center(child: CircularProgressIndicator()));
      } else {
        return SafeArea(
            child: Scaffold(
                // resizeToAvoidBottomPadding: true,
                resizeToAvoidBottomInset: true,
                appBar: AppBar(
                  toolbarHeight: 60,
                  elevation: 4,
                  backgroundColor: colorPrimary,
                  leading: BackButton(
                    onPressed: () => Navigator.pop(context, false),
                  ),
                  centerTitle: false,
                  titleSpacing: -5.0,
                  title: Text(
                    titleForTxnType(),
                    style: TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontFamily: 'HelveticaRegular',
                        fontWeight: FontWeight.bold),
                  ),
                  actions: [
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 12),
                      child: ElevatedButton(
                          // textColor: Colors.black54,
                          // color: Colors.transparent,
                          // padding: EdgeInsets.zero,
                          // splashColor: colorPrimaryLight,
                          style: ElevatedButton.styleFrom(
                            elevation: 0,
                            backgroundColor: colorPrimary,
                            foregroundColor: colorPrimaryLightest,
                            shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(10)),
                          ),
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: Column(
                            children: [
                              Icon(
                                Icons.close,
                                color: Colors.white,
                              ),
                              Text(
                                "Cancel",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          )),
                    ),
                  ],
                ),
                body: PdfPreview(
                  useActions: true,
                  canChangePageFormat: false,
                  // allowPrinting: false,
                  // allowSharing: false,
                  initialPageFormat: defaultPdfPageFormat,
                  onPrinted: (_) {
                    // Log.d("print clicked");
                  },
                  pdfFileName:
                      "${TxnType.txnTypeText[txnType]} ${generalTransactionPrintCotroller.mainTransaction.txnDisplayName} ${generalTransactionPrintCotroller.mainTransaction.txnDateBS} .pdf",
                  // initialPageFormat: PdfPageFormat.a4
                  //     .copyWith()
                  //     .applyMargin(left: 200, right: 200, top: 200, bottom: 200),
                  maxPageWidth: 700,
                  // actions: actions,
                  build: (format) {
                    if (TxnType.sales == txnType)
                      return generateSaleBill(defaultPdfPageFormat,
                          lineItems: generalTransactionPrintCotroller.items,
                          ledgerDetailModel:
                              generalTransactionPrintCotroller.party,
                          transactionModel:
                              generalTransactionPrintCotroller.mainTransaction);
                    else if (TxnType.salesReturn == txnType)
                      return generateSaleBill(defaultPdfPageFormat,
                          billTitle: "Sales Return",
                          lineItems: generalTransactionPrintCotroller.items,
                          ledgerDetailModel:
                              generalTransactionPrintCotroller.party,
                          transactionModel:
                              generalTransactionPrintCotroller.mainTransaction);
                    else if (TxnType.purchase == txnType)
                      return generatePurchaseBill(defaultPdfPageFormat,
                          lineItems: generalTransactionPrintCotroller.items,
                          ledgerDetailModel:
                              generalTransactionPrintCotroller.party,
                          transactionModel:
                              generalTransactionPrintCotroller.mainTransaction);
                    else if (TxnType.purchaseReturn == txnType)
                      return generatePurchaseBill(defaultPdfPageFormat,
                          billTitle: "Purchase Return",
                          lineItems: generalTransactionPrintCotroller.items,
                          ledgerDetailModel:
                              generalTransactionPrintCotroller.party,
                          transactionModel:
                              generalTransactionPrintCotroller.mainTransaction);
                    else if (TxnType.paymentIn == txnType)
                      return generateReceiptVoucher(defaultPdfPageFormat,
                          ledgerDetailModel:
                              generalTransactionPrintCotroller.party,
                          transactionModel:
                              generalTransactionPrintCotroller.mainTransaction);
                    else if (TxnType.paymentOut == txnType)
                      return generatepPaymentVoucher(defaultPdfPageFormat,
                          ledgerDetailModel:
                              generalTransactionPrintCotroller.party,
                          transactionModel:
                              generalTransactionPrintCotroller.mainTransaction);
                    else if (TxnType.expense == txnType)
                      return generateExpensePrint(defaultPdfPageFormat,
                          ledgerDetailModel:
                              generalTransactionPrintCotroller.party,
                          transactionModel:
                              generalTransactionPrintCotroller.mainTransaction);

                    return generateSaleBill(defaultPdfPageFormat,
                        lineItems: generalTransactionPrintCotroller.items,
                        ledgerDetailModel:
                            generalTransactionPrintCotroller.party,
                        transactionModel:
                            generalTransactionPrintCotroller.mainTransaction);
                  },
                )));
      }
    });
  }
}
