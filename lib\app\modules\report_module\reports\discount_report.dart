import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
import 'package:mobile_khaata_v2/app/model/report/discount_summary_model.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/discount_report_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/report_controllers/report_transaction_controller.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import 'package:nepali_date_picker/nepali_date_picker.dart';

// ignore: must_be_immutable
class DiscountReport extends StatelessWidget {
  final ReportTransactionController _controller = ReportTransactionController();
  List<int> types = [TxnType.sales, TxnType.purchase];
  final String? receivedStart;
  final String? receivedEnd;
  String? startDate;
  String? endDate;

  DiscountReport({super.key, this.receivedStart, this.receivedEnd}) {
    startDate = receivedStart ?? currentDate;
    endDate = receivedEnd ?? currentDate;
    generate();
  }

  generate() {
    _controller.generateDiscountReport(
        startDate: startDate!, endDate: endDate!, types: types);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        elevation: 0,
        titleSpacing: -5.0,
        backgroundColor: colorPrimary,
        title: const Text(
          "छुट रिपोर्ट (Discount Report)",
          style: TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
        actions: [
          PrintButton(
            onPressed: () {
              Navigator.pushNamed(context, '/printDiscountReport',
                  arguments: DiscountReportPrintPage(
                    transactions: _controller.discountSummaryList,
                    startDate: startDate,
                    endDate: endDate,
                    pageTitle: "Discount Report",
                  ));
              //   TransactionHelper.gotoSingleTransactionPrintPage(context,
              //       txnTypeText: TxnType.txnTypeText[types[0]],
              //       pageTitle: "Discount Report",
              //       transactions: _controller.transactions,
              //       startDate: startDate,
              //       endDate: endDate);
            },
          )
        ],
      ),
      body: Container(
        color: Colors.black12,
        child: Column(
          children: [
            //=============================transaction date filter
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Row(
                children: [
                  Expanded(
                    flex: 2,
                    child: ReportCustomDatePickerTextField(
                      initialValue: toDateBS(DateTime.parse(startDate!)),
                      hintText: "From Date",
                      onChange: (selectedDate) {
                        startDate =
                            toDateAD(NepaliDateTime.parse(selectedDate));
                        generate();
                      },
                    ),
                  ),
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: Text(
                        "TO",
                        style: labelStyle2,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: ReportCustomDatePickerTextField(
                      initialValue: toDateBS(DateTime.parse(endDate!)),
                      hintText: "To Date",
                      onChange: (selectedDate) {
                        endDate = toDateAD(NepaliDateTime.parse(selectedDate));
                        generate();
                      },
                    ),
                  ),
                ],
              ),
            ),

            const Divider(
              height: 0,
              color: Colors.black54,
            ),

            DefaultTextStyle(
              style: TextStyle(
                  fontSize: 12, color: textColor, fontWeight: FontWeight.bold),
              child: Container(
                color: Colors.white,
                padding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                child: Row(
                  // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    //====================================1st Column
                    Expanded(
                      flex: 1,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: const [
                          Text(
                            "Party Name",
                            overflow: TextOverflow.ellipsis,
                            maxLines: 2,
                          ),
                        ],
                      ),
                    ),

                    //====================================2nd Column
                    const Expanded(
                      flex: 2,
                      child: Text(
                        "Sale Discount",
                        textAlign: TextAlign.right,
                      ),
                    ),

                    //====================================3rd Column
                    const Expanded(
                      flex: 2,
                      child: Text(
                        "Purchase Discount",
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const Divider(
              height: 0,
              color: Colors.black54,
            ),

            Obx(() {
              if (_controller.txnLoading) {
                return Container(
                    color: Colors.white,
                    child: const Center(child: CircularProgressIndicator()));
              }

              if (_controller.discountSummaryList.isEmpty) {
                return Container(
                    color: Colors.white,
                    width: double.infinity,
                    child: const Center(
                        child: Text(
                      "No Records",
                      style: TextStyle(color: Colors.black54),
                    )));
              } else {
                return Expanded(
                    child: _TxnListView(_controller.discountSummaryList));
              }
            }),
          ],
        ),
      ),
      // extendBody: true,
      bottomNavigationBar: Container(
        height: 45,
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        color: colorPrimary,
        child: SingleChildScrollView(
          child: Obx(() {
            return DefaultTextStyle(
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              child: Row(
                children: [
                  const Expanded(
                      flex: 1,
                      child: Text(
                        "Total Discount: ",
                        textAlign: TextAlign.right,
                      )),
                  Expanded(
                      flex: 1,
                      child: Text(
                        formatCurrencyAmount(
                            _controller.totalSaleDisAmount.value, false),
                        textAlign: TextAlign.right,
                      )),
                  Expanded(
                      flex: 1,
                      child: Text(
                        formatCurrencyAmount(
                            _controller.totalPurchaseDisAmount.value, false),
                        textAlign: TextAlign.right,
                      )),
                ],
              ),
            );
          }),
        ),
      ),
    ));
  }
}

class _TxnListView extends StatelessWidget {
  final List<DiscountSummaryReportModel> _transactionList;

  const _TxnListView(this._transactionList);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: _transactionList.length,
      // shrinkWrap: true,
      itemBuilder: (context, int index) {
        DiscountSummaryReportModel rowData = _transactionList[index];

        return InkWell(
          // onTap: () => TransactionHelper.gotoTransactionEditPage(
          //     context, txn.txnId, txn.txnType),
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                DefaultTextStyle(
                  style: TextStyle(fontSize: 12, color: colorPrimary),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                    child: Row(
                      // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        //====================================1st Column
                        Expanded(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                rowData.ledgerTitle ?? '',
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                                style: const TextStyle(
                                    fontSize: 12, fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          width: 20,
                        ),

                        //====================================2nd Column
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                formatCurrencyAmount(
                                    rowData.saleDiscountAmount!, false),
                                textAlign: TextAlign.right,
                              ),
                            ],
                          ),
                        ),

                        //====================================3rd Column
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                formatCurrencyAmount(
                                    rowData.purchaseDiscountAmount!, false),
                                textAlign: TextAlign.right,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                //Add space if last element
                if (_transactionList.length - 1 == index) ...{
                  const SizedBox(
                      // height: 100,
                      )
                },
              ],
            ),
          ),
        );
      },
    );
  }
}
