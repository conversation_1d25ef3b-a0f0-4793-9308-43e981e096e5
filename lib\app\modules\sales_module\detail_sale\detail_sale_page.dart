// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/components/image_grid_gallery.dart';
import 'package:mobile_khaata_v2/app/components/ledger_autocomplete%20_%20textfield_with_add.dart';
import 'package:mobile_khaata_v2/app/components/payment_mode_selector.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/modules/sales_module/add_sell_bill_item/add_edit_sell_bill_view_page.dart';
import 'package:mobile_khaata_v2/app/modules/sales_module/detail_sale/detail_sale_controller.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

import '../../../../database/txn_type.dart';

extension ExtendedIterable<E> on Iterable<E> {
  /// Like Iterable<T>.map but callback have index as second argument
  Iterable<T> mapIndex<T>(T Function(E e, int i) f) {
    var i = 0;
    return map((e) => f(e, i++));
  }

  void forEachIndex(void Function(E e, int i) f) {
    var i = 0;
    for (var e in this) {
      f(e, i++);
    }
  }
}

class DetailSalePage extends StatelessWidget {
  final String tag = "Sales Detail Page";

  final String? saleID;
  final bool? reaOnlyFlag;

  final saleController = SaleDetailController();

  DetailSalePage({super.key, this.saleID, this.reaOnlyFlag}) {
    saleController.onInit();

    // saleController.initEdit(
    //     '7-3-4606d314-9fb3-45d3-bbfb-b1eaf2d96843', this.reaOnlyFlag ?? true);

    if (null != saleID) {
      saleController.initEdit(saleID, reaOnlyFlag ?? true);
    } else {
      saleController.initialize();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      bool isCashSale =
          saleController.transaction.value.ledgerId == CASH_SALES_LEDGER_ID;
      bool hasSubTotal =
          (saleController.transaction.value.txnSubTotalAmount != null &&
              saleController.transaction.value.txnSubTotalAmount! > 0.0);

      if (saleController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      } else {
        return SafeArea(
            child: Scaffold(
          // resizeToAvoidBottomPadding: true,
          resizeToAvoidBottomInset: true,
          appBar: AppBar(
            toolbarHeight: 60,
            elevation: 4,
            leading: BackButton(
              onPressed: () => Navigator.pop(context, false),
            ),
            centerTitle: false,
            backgroundColor: colorPrimary,
            titleSpacing: -5.0,
            title: const Text(
              "बिक्री (Sale Detail)",
              style: TextStyle(
                  fontSize: 18,
                  color: Colors.white,
                  fontFamily: 'HelveticaRegular',
                  fontWeight: FontWeight.bold),
            ),
            actions: [
              if (saleController.editFlag) ...{
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  child: InkWell(
                      onTap: () {
                        TransactionHelper.gotoTransactionEditPage(
                            context, saleID ?? "", TxnType.sales,
                            forEdit: true);
                      },
                      child: (saleController.readOnlyFlag)
                          ? Column(
                              children: const [
                                Icon(
                                  Icons.mode_edit,
                                  color: Colors.white,
                                ),
                                Text(
                                  "Click here to Edit",
                                  style: TextStyle(
                                      color: Colors.white, fontSize: 10),
                                ),
                              ],
                            )
                          : Column(
                              children: const [
                                Icon(
                                  Icons.close,
                                  color: Colors.white,
                                ),
                                Text(
                                  "Cancel",
                                  style: TextStyle(
                                      color: Colors.white, fontSize: 10),
                                ),
                              ],
                            )),
                ),
              }
            ],
          ),

          //===========================================================================Body Part
          body: Center(
            child: Container(
              color: backgroundColorShade,
              child: GestureDetector(
                onTap: () => FocusScope.of(context).unfocus(),
                child: Form(
                  key: saleController.formKey,
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Card(
                          elevation: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 15),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                //===========================Bill No.
                                Expanded(
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "बिल न. ",
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      FormBuilderTextField(
                                        name: "bill_no",
                                        readOnly: saleController.readOnlyFlag,
                                        autocorrect: false,
                                        keyboardType: TextInputType.text,
                                        textInputAction: TextInputAction.done,
                                        textAlign: TextAlign.right,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Bill No."),
                                        controller: saleController.billNoCtrl,
                                        onChanged: (value) {
                                          saleController.transaction.value
                                              .txnRefNumberChar = value;
                                          saleController.transaction.refresh();
                                        },
                                      ),
                                    ],
                                  ),
                                ),

                                const SizedBox(
                                  width: 20,
                                ),

                                //===========================Transaction Date
                                Expanded(
                                  flex: 1,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "मिति",
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      CustomDatePickerTextField(
                                        readOnly: saleController.readOnlyFlag,
                                        maxBSDate: NepaliDateTime.now(),
                                        initialValue: saleController
                                            .transaction.value.txnDateBS,
                                        onChange: (selectedDate) {
                                          saleController.transaction.value
                                              .txnDateBS = selectedDate;
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),

                        Card(
                          elevation: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 10),
                            child: Column(
                              children: [
                                //===============================================Party Balance
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    Row(
                                      children: [
                                        SizedBox(
                                          width: 20,
                                          height: 20,
                                          child: Checkbox(
                                            activeColor: colorPrimary,
                                            checkColor: Colors.white,
                                            value: saleController
                                                .isCashSaleSelected,
                                            onChanged:
                                                saleController.readOnlyFlag
                                                    ? null
                                                    : (value) {
                                                        saleController
                                                                .setIsCashSaleSelected =
                                                            value!;
                                                        if (value) {
                                                          saleController.onChangeParty(
                                                              LedgerDetailModel(
                                                                  ledgerId:
                                                                      CASH_SALES_LEDGER_ID,
                                                                  ledgerTitle:
                                                                      CASH_SALES_LEDGER_NAME));
                                                        } else {
                                                          saleController
                                                              .onChangeParty(
                                                                  LedgerDetailModel(
                                                                      ledgerId:
                                                                          null));
                                                        }
                                                      },
                                          ),
                                        ),
                                        Text("  खुद्रा बिक्री\n  (Cash Sale)",
                                            style: labelStyle2)
                                      ],
                                    ),
                                    RichText(
                                      textAlign: TextAlign.right,
                                      text: TextSpan(
                                          text: "पुरानो बाँकी: ",
                                          style: TextStyle(color: textColor),
                                          children: [
                                            if (null !=
                                                saleController.transaction.value
                                                    .ledgerId) ...{
                                              TextSpan(
                                                text:
                                                    "${saleController.selectedLedger.value.balanceAmount ?? 0}",
                                                style: TextStyle(
                                                    color: ((saleController
                                                                    .selectedLedger
                                                                    .value
                                                                    .balanceAmount ??
                                                                0) >=
                                                            0.0)
                                                        ? colorGreenDark
                                                        : colorRedLight),
                                              )
                                            }
                                          ]),
                                    )
                                  ],
                                ),
                                Container(
                                  height: 10,
                                ),
                                //===============================================Party Field
                                if (!saleController.isCashSaleSelected)
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'ग्राहकको नाम',
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      LedgerAutoCompleteTextFieldWithAdd(
                                          excludedIDS: const [
                                            CASH_PURCHASE_LEDGER_ID
                                          ],
                                          enableFlag:
                                              !saleController.readOnlyFlag,
                                          labelText: "Customer Name",
                                          controller:
                                              saleController.partyNameCtrl,
                                          onChangedFn: (value) {
                                            // Log.d("called i text change");
                                          },
                                          ledgetID: saleController
                                              .transaction.value.ledgerId,
                                          onSuggestionSelectedFn:
                                              (LedgerDetailModel ledger) {
                                            saleController
                                                .onChangeParty(ledger);
                                          })
                                    ],
                                  ),

                                ...isCashSale
                                    ? [
                                        const SizedBox(
                                          height: 10,
                                        ),
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "खुद्रा ग्राहकको नाम",
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            FormBuilderTextField(
                                              name: "display_text",
                                              // readOnly:
                                              //     (null == state.selectedLedger.ledgerId)
                                              //         ? false
                                              //         : true,
                                              readOnly:
                                                  saleController.readOnlyFlag,
                                              autocorrect: false,
                                              textInputAction:
                                                  TextInputAction.done,
                                              style: formFieldTextStyle,
                                              decoration:
                                                  formFieldStyle.copyWith(
                                                      labelText:
                                                          "Billing Name"),
                                              controller: saleController
                                                  .displayTextCtrl,
                                              onChanged: (value) {
                                                saleController.transaction.value
                                                    .txnDisplayName = value;
                                                saleController.transaction
                                                    .refresh();
                                              },
                                            ),
                                          ],
                                        )
                                      ]
                                    : [],

                                const SizedBox(
                                  height: 10,
                                ),
                                if (!isCashSale)
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      //===================================Mobile
                                      SizedBox(
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.3,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'फोन नम्बर',
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            FormBuilderTextField(
                                                name: "mobile",
                                                readOnly: true,
                                                autocorrect: false,
                                                keyboardType:
                                                    TextInputType.number,
                                                textInputAction:
                                                    TextInputAction.done,
                                                inputFormatters: [
                                                  FilteringTextInputFormatter
                                                      .digitsOnly
                                                ],
                                                style: formFieldTextStyle,
                                                decoration:
                                                    formFieldStyle.copyWith(
                                                        labelText:
                                                            "Contact no.",
                                                        hintText: "Contact no"),
                                                controller:
                                                    saleController.mobileCtrl
                                                // controller: state.mobileCtrl,
                                                ),
                                          ],
                                        ),
                                      ),

                                      //===================================Address
                                      SizedBox(
                                        width:
                                            MediaQuery.of(context).size.width *
                                                0.5,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'ठेगाना',
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            FormBuilderTextField(
                                              name: "address",
                                              readOnly: true,
                                              autocorrect: false,
                                              keyboardType: TextInputType.text,
                                              textInputAction:
                                                  TextInputAction.done,
                                              style: formFieldTextStyle,
                                              decoration:
                                                  formFieldStyle.copyWith(
                                                      labelText: "Address"),
                                              controller:
                                                  saleController.addressCtrl,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                if (!isCashSale) const SizedBox(height: 10.0),

                                //=====================================PAN No Field
                                if (!isCashSale)
                                  Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "पान / मु. अ. कर नम्बर",
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      FormBuilderTextField(
                                          name: "pan_no",
                                          readOnly: true,
                                          autocorrect: false,
                                          keyboardType: TextInputType.number,
                                          inputFormatters: [
                                            FilteringTextInputFormatter
                                                .digitsOnly
                                          ],
                                          textInputAction: TextInputAction.done,
                                          style: formFieldTextStyle,
                                          decoration: formFieldStyle.copyWith(
                                              labelText: "PAN/VAT No."),
                                          controller: saleController.panNoCtrl),
                                    ],
                                  ),
                              ],
                            ),
                          ),
                        ),

                        //================================================Item Container
                        Card(
                          child: Container(
                              width: double.infinity,
                              padding: const EdgeInsets.all(0.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 15, vertical: 8),
                                    decoration: BoxDecoration(
                                        color: colorPrimaryLight,
                                        borderRadius: const BorderRadius.only(
                                            topLeft: Radius.circular(4),
                                            topRight: Radius.circular(4))),
                                    child: DefaultTextStyle(
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontSize: 15,
                                      ),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          const Text(
                                            "बिक्री सामानहरु (Sale Items)",
                                          ),
                                          Text(
                                            "Total Item: ${saleController.items.length}",
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  Container(
                                    constraints:
                                        const BoxConstraints(maxHeight: 300),
                                    child: getItemListView(context,
                                        saleController.items, saleController),
                                  ),
                                  // Container(
                                  //   margin:
                                  //       EdgeInsets.symmetric(vertical: 10),
                                  //   child: Center(
                                  //     child: RaisedButton(
                                  //       color: colorPrimary,
                                  //       elevation: 10,
                                  //       shape: RoundedRectangleBorder(
                                  //         borderRadius:
                                  //             BorderRadius.circular(20.0),
                                  //       ),
                                  //       splashColor: colorPrimaryLightest,
                                  //       child: Padding(
                                  //         padding:
                                  //             const EdgeInsets.symmetric(
                                  //                 vertical: 10,
                                  //                 horizontal: 10),
                                  //         child: Text(
                                  //           "बिक्री सामान थप्नुहोस्  (Add Sales Item)",
                                  //           style: TextStyle(
                                  //             color: Colors.white,
                                  //             fontSize: 15,
                                  //           ),
                                  //         ),
                                  //       ),
                                  //       onPressed: saleController
                                  //               .readOnlyFlag
                                  //           ? null
                                  //           : () async {
                                  //               var returnedData =
                                  //                   await showDialog(
                                  //                       context: context,
                                  //                       useRootNavigator:
                                  //                           true,
                                  //                       barrierDismissible:
                                  //                           false,
                                  //                       builder: (d_c) {
                                  //                         return AlertDialog(
                                  //                             insetPadding: EdgeInsets.symmetric(
                                  //                                 horizontal:
                                  //                                     10,
                                  //                                 vertical:
                                  //                                     10),
                                  //                             contentPadding:
                                  //                                 EdgeInsets
                                  //                                     .zero,
                                  //                             clipBehavior: Clip
                                  //                                 .hardEdge,
                                  //                             content:
                                  //                                 Container(
                                  //                               width: MediaQuery.of(context)
                                  //                                       .size
                                  //                                       .width -
                                  //                                   20,
                                  //                               child:
                                  //                                   AddSaleBilledItemScreenView(),
                                  //                             ));
                                  //                       });
                                  //               if (null != returnedData) {
                                  //                 // saleController.formKey.currentState.
                                  //                 if (null !=
                                  //                     returnedData
                                  //                         .billedItem) {
                                  //                   saleController.items
                                  //                       .add(returnedData
                                  //                           .billedItem);
                                  //                   saleController.items
                                  //                       .refresh();

                                  //                   saleController
                                  //                       .recalculateForItems();
                                  //                   Log.d(
                                  //                       "got  billed item ${returnedData.billedItem.toJson()}");
                                  //                 }
                                  //               }
                                  //             },
                                  //     ),
                                  //   ),
                                  // ),
                                ],
                              )),
                        ),

                        //===============================================Total Amount
                        Card(
                          elevation: 2,
                          child: Column(
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 4, horizontal: 8),
                                decoration: BoxDecoration(
                                    color: colorPrimaryLight,
                                    borderRadius: const BorderRadius.only(
                                        topLeft: Radius.circular(4),
                                        topRight: Radius.circular(4))),
                                child: const Center(
                                    child: Text(
                                  "Bill Totals (जम्मा बिल)",
                                  style: TextStyle(
                                      color: Colors.white, fontSize: 16),
                                )),
                              ),
                              const SizedBox(
                                height: 10,
                              ),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 10,
                                ),
                                child: Column(
                                  children: [
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "उप कुल",
                                          style: labelStyle2,
                                        ),
                                        const SizedBox(height: 5.0),
                                        FormBuilderTextField(
                                          name: "txn_subtotal",
                                          readOnly: saleController.readOnlyFlag
                                              ? true
                                              : (saleController
                                                      .items.isNotEmpty)
                                                  ? true
                                                  : false,
                                          autocorrect: false,
                                          keyboardType: const TextInputType
                                              .numberWithOptions(decimal: true),
                                          textInputAction: TextInputAction.done,
                                          style: formFieldTextStyle,
                                          inputFormatters: [
                                            FilteringTextInputFormatter.allow(
                                                RegExp(r'^(\d+)?\.?\d{0,2}'))
                                          ],
                                          maxLength: 10,
                                          decoration: formFieldStyle.copyWith(
                                              hintText: "Sub Total",
                                              counterText: ''),
                                          textAlign: TextAlign.end,
                                          controller:
                                              saleController.subTotalAmountCtrl,
                                          onChanged: (value) {
                                            saleController
                                                .onSubTotalIndividualChange(
                                                    value ?? "",
                                                    editorTag: 'txn_subtotal');
                                            saleController.transaction
                                                .refresh();
                                          },
                                        ),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 20,
                                    ),

                                    // =============================================Discount
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "छुट (Discount)",
                                          style: labelStyle2,
                                        ),
                                        const SizedBox(height: 5.0),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            SizedBox(
                                              width: 80,
                                              child: FormBuilderTextField(
                                                name: "txn_discount_percent",
                                                readOnly: saleController
                                                        .readOnlyFlag ||
                                                    (!hasSubTotal),
                                                autocorrect: false,
                                                keyboardType:
                                                    const TextInputType
                                                        .numberWithOptions(
                                                        decimal: true),
                                                textInputAction:
                                                    TextInputAction.done,
                                                style: formFieldTextStyle,
                                                decoration:
                                                    formFieldStyle.copyWith(
                                                        suffix: const Text("%"),
                                                        labelText: "%"),
                                                textAlign: TextAlign.end,
                                                controller: saleController
                                                    .discountPercentageCtrl,
                                                onChanged: (value) {
                                                  saleController
                                                      .updateDiscountPercentage(
                                                          value ?? "",
                                                          editorTag:
                                                              'txn_discount_percent');
                                                },
                                              ),
                                            ),
                                            const SizedBox(
                                              width: 20,
                                            ),
                                            Expanded(
                                              child: FormBuilderTextField(
                                                  name: "txn_discount_amount",
                                                  readOnly: saleController
                                                          .readOnlyFlag ||
                                                      (!hasSubTotal),
                                                  autocorrect: false,
                                                  keyboardType:
                                                      const TextInputType
                                                          .numberWithOptions(
                                                          decimal: true),
                                                  textInputAction:
                                                      TextInputAction.done,
                                                  style: formFieldTextStyle,
                                                  decoration:
                                                      formFieldStyle.copyWith(
                                                          labelText:
                                                              "छुट रकम (Dis. Amount)"),
                                                  textAlign: TextAlign.end,
                                                  controller: saleController
                                                      .discountAmountCtrl,
                                                  onChanged: (value) {
                                                    saleController
                                                        .updateDiscountAmount(
                                                            value ?? "",
                                                            editorTag:
                                                                'txn_discount_amount');
                                                  }),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 25,
                                    ),

                                    // =============================================VAT
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Row(
                                          children: [
                                            SizedBox(
                                              width: 20,
                                              height: 20,
                                              child: Checkbox(
                                                activeColor: colorPrimary,
                                                checkColor: Colors.white,
                                                value:
                                                    saleController.isVatEnabled,
                                                onChanged: (saleController
                                                            .readOnlyFlag ||
                                                        (!(hasSubTotal)))
                                                    ? null
                                                    : (value) {
                                                        saleController
                                                            .onToggleVat(
                                                                value!);
                                                      },
                                              ),
                                            ),
                                            Text("  मु.अ. कर (VAT)",
                                                style: labelStyle2)
                                          ],
                                        ),
                                        const SizedBox(height: 10.0),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            SizedBox(
                                              width: 80,
                                              child: FormBuilderTextField(
                                                name: "txn_tax_percent",
                                                readOnly: true,
                                                autocorrect: false,
                                                keyboardType:
                                                    const TextInputType
                                                        .numberWithOptions(
                                                        decimal: true),
                                                textInputAction:
                                                    TextInputAction.done,
                                                style: formFieldTextStyle,
                                                decoration:
                                                    formFieldStyle.copyWith(
                                                        suffix: const Text("%"),
                                                        labelText: "%"),
                                                textAlign: TextAlign.end,
                                                controller: saleController
                                                    .vatPercentCtrl,
                                                onChanged: (value) {
                                                  saleController
                                                      .onvatPercentChange(
                                                          value ?? "",
                                                          editorTag:
                                                              'txn_tax_percent');
                                                },
                                              ),
                                            ),
                                            const SizedBox(
                                              width: 20,
                                            ),
                                            Expanded(
                                              child: FormBuilderTextField(
                                                name: "txn_tax_amount",
                                                readOnly: true,
                                                autocorrect: false,
                                                keyboardType:
                                                    const TextInputType
                                                        .numberWithOptions(
                                                        decimal: true),
                                                textInputAction:
                                                    TextInputAction.done,
                                                style: formFieldTextStyle,
                                                decoration: formFieldStyle.copyWith(
                                                    labelText:
                                                        "मु.अ. कर रकम (VAT Amount) "),
                                                textAlign: TextAlign.end,
                                                controller: saleController
                                                    .vatAmountCtrl,
                                                onChanged: (value) {
                                                  saleController
                                                      .onvatAmountChange(
                                                          value ?? "",
                                                          editorTag:
                                                              'txn_tax_amount');
                                                },
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 5,
                                    ),

                                    const Divider(
                                      height: 5,
                                    ),
                                    const Divider(
                                      height: 0,
                                    ),
                                    const SizedBox(
                                      height: 15,
                                    ),

                                    // =============================================Total Amount
                                    Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "कुल रकम",
                                          style: labelStyle2,
                                        ),
                                        const SizedBox(
                                          height: 5,
                                        ),
                                        FormBuilderTextField(
                                            name: "txn_total",
                                            readOnly: true,
                                            autocorrect: false,
                                            keyboardType: const TextInputType
                                                .numberWithOptions(
                                                decimal: true),
                                            textInputAction:
                                                TextInputAction.done,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                labelText: "Total Amount"),
                                            textAlign: TextAlign.end,
                                            controller:
                                                saleController.totalAmountCtrl),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 25,
                                    ),

                                    // =============================================Paid Amount

                                    Row(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.end,
                                        children: [
                                          Expanded(
                                            child: Column(
                                              // crossAxisAlignment:
                                              //     CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  children: [
                                                    SizedBox(
                                                      width: 20,
                                                      height: 20,
                                                      child: Checkbox(
                                                        activeColor:
                                                            colorPrimary,
                                                        checkColor:
                                                            Colors.white,
                                                        value: isCashSale
                                                            ? true
                                                            : (saleController
                                                                .isReceived),
                                                        onChanged: (saleController
                                                                    .readOnlyFlag ||
                                                                isCashSale ||
                                                                (!hasSubTotal))
                                                            ? null
                                                            : (value) {
                                                                saleController
                                                                        .setIsReceived =
                                                                    value!;
                                                                if (value) {
                                                                  saleController
                                                                      .transaction
                                                                      .value
                                                                      .txnCashAmount = saleController
                                                                          .transaction
                                                                          .value
                                                                          .txnTotalAmount ??
                                                                      0.0;
                                                                  saleController
                                                                      .transaction
                                                                      .value
                                                                      .txnBalanceAmount = 0.00;
                                                                } else {
                                                                  saleController
                                                                      .transaction
                                                                      .value
                                                                      .txnCashAmount = 0.0;

                                                                  saleController
                                                                      .transaction
                                                                      .value
                                                                      .txnBalanceAmount = saleController
                                                                          .transaction
                                                                          .value
                                                                          .txnTotalAmount ??
                                                                      0.0;
                                                                }
                                                                saleController
                                                                    .assignTransactionToTextFields();
                                                              },
                                                      ),
                                                    ),
                                                    Text("  प्राप्त रकम",
                                                        style: labelStyle2)
                                                  ],
                                                ),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                PaymentModeSelector(
                                                  onChangedFn: (v) {
                                                    saleController
                                                        .transaction
                                                        .value
                                                        .txnPaymentTypeId = v;

                                                    saleController
                                                            .transaction
                                                            .value
                                                            .txnPaymentReference =
                                                        null;
                                                    saleController
                                                            .transaction
                                                            .value
                                                            .chequeIssueDateBS =
                                                        null;
                                                    saleController.transaction
                                                        .refresh();
                                                  },
                                                  paymentModeID: saleController
                                                      .transaction
                                                      .value
                                                      .txnPaymentTypeId,
                                                  enableFlag: saleController
                                                      .readOnlyFlag,
                                                )
                                              ],
                                            ),
                                          ),
                                          const SizedBox(
                                            width: 10,
                                          ),
                                          Expanded(
                                            child: FormBuilderTextField(
                                              name: "txn_cash_amount",
                                              readOnly: (saleController
                                                      .readOnlyFlag ||
                                                  !hasSubTotal ||
                                                  isCashSale),
                                              autocorrect: false,
                                              keyboardType: const TextInputType
                                                  .numberWithOptions(
                                                  decimal: true),
                                              textInputAction:
                                                  TextInputAction.done,
                                              inputFormatters: [
                                                FilteringTextInputFormatter
                                                    .digitsOnly
                                              ],
                                              style: formFieldTextStyle,
                                              decoration:
                                                  formFieldStyle.copyWith(
                                                      labelText:
                                                          "Received Amount"),
                                              textAlign: TextAlign.end,
                                              controller: saleController
                                                  .receivedAmountCtrl,
                                              onChanged: (value) {
                                                saleController
                                                    .changeReceivedAmount(
                                                        value ?? "",
                                                        editorTag:
                                                            'txn_cash_amount');
                                              },
                                            ),
                                          )
                                        ]),

                                    ...(saleController.transaction.value
                                                .txnPaymentTypeId !=
                                            PAYMENT_MODE_CASH_ID)
                                        ? [
                                            const SizedBox(
                                              height: 25,
                                            ),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text("चेक/भौचर न.",
                                                    style: labelStyle2),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                TextField(
                                                    autocorrect: false,
                                                    readOnly: saleController
                                                        .readOnlyFlag,
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            labelText:
                                                                "Cheque/Voucher No."),
                                                    controller: saleController
                                                        .paymentRefCtrl,
                                                    onChanged: (v) {
                                                      saleController
                                                          .transaction
                                                          .value
                                                          .txnPaymentReference = v;
                                                      saleController.transaction
                                                          .refresh();
                                                    }),
                                              ],
                                            ),
                                          ]
                                        : [],
                                    if (saleController.transaction.value
                                            .txnPaymentTypeId ==
                                        PAYMENT_MODE_CHEQUE_ID) ...[
                                      const SizedBox(
                                        height: 25,
                                      ),
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text("चेक मिति", style: labelStyle2),
                                          const SizedBox(
                                            height: 10,
                                          ),
                                          CustomDatePickerTextField(
                                            labelText: "Cheque Date",
                                            readOnly:
                                                saleController.readOnlyFlag,
                                            // maxBSDate: NepaliDateTime.now(),
                                            initialValue: saleController
                                                .transaction
                                                .value
                                                .chequeIssueDateBS,
                                            onChange: (selectedDate) {
                                              saleController.transaction.value
                                                      .chequeIssueDateBS =
                                                  selectedDate;
                                            },
                                          ),
                                        ],
                                      )
                                    ],

                                    const SizedBox(
                                      height: 25,
                                    ),

                                    // =============================================Balance Amount
                                    ...isCashSale
                                        ? []
                                        : [
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text("बाँकी रहेको रकम",
                                                    style: labelStyle2),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                FormBuilderTextField(
                                                    name: "txn_balance_amount",
                                                    readOnly: true,
                                                    autocorrect: false,
                                                    keyboardType:
                                                        const TextInputType
                                                            .numberWithOptions(
                                                            decimal: true),
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            labelText:
                                                                "Balance Due"),
                                                    textAlign: TextAlign.end,
                                                    controller: saleController
                                                        .dueAmountCtrl),
                                              ],
                                            ),
                                            const SizedBox(
                                              height: 20,
                                            ),
                                          ],
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        //===============================================Image
                        // Container(
                        //   width: double.infinity,
                        //   child: Card(
                        //     elevation: 2,
                        //     child: Container(
                        //       // color: Colors.red,
                        //         height: 140,
                        //         width: 100,
                        //         // width: ,
                        //         // child: (null==state.selectImage)?
                        //         child: FormBuilderImagePicker(
                        //             attribute: "image_picker",
                        //             bottomSheetPadding: EdgeInsets.all(0),
                        //             imageHeight: 100,
                        //             imageWidth: 100,
                        //
                        //             // maxHeight: 100,
                        //             // maxWidth: 100,
                        //             imageMargin: EdgeInsets.symmetric(
                        //                 horizontal: 10),
                        //             readOnly: saleController.readOnlyFlag,
                        //             decoration: InputDecoration(
                        //               border: InputBorder.none,
                        //             ),
                        //             maxImages: 2,
                        //             iconColor: colorPrimaryLight,
                        //             validators: [],
                        //             initialValue:
                        //             saleController.files.value,
                        //             onChanged: (_fls) async {
                        //               // return;
                        //               saleController.files.value =
                        //                   _fls.cast<File>();
                        //               saleController.files.refresh();
                        //               bool isLargeFile =
                        //               await saleController
                        //                   .checkLargeImage(_fls);
                        //               if (isLargeFile) {
                        //                 showToastMessage(context,
                        //                     message:
                        //                     MAX_IMAGE_SIZE_MESSAGE,
                        //                     alertType: AlertType.Error);
                        //                 return;
                        //               }
                        //             })),
                        //   ),
                        // ),

                        //===============================================Image Preview Grid
                        SizedBox(
                          width: double.infinity,
                          child: Card(
                            elevation: 2,
                            child: SizedBox(
                                height: 140,
                                width: 100,
                                child: ImageGalleryGrid(
                                    images: saleController.files)),
                          ),
                        ),

                        //===============================================Description
                        Card(
                          elevation: 2,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 10, vertical: 10),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "कैफियत",
                                  style: labelStyle2,
                                ),
                                const SizedBox(height: 5.0),
                                FormBuilderTextField(
                                  name: "description",
                                  readOnly: saleController.readOnlyFlag,
                                  autocorrect: false,
                                  textAlign: TextAlign.start,
                                  textInputAction: TextInputAction.newline,
                                  style: formFieldTextStyle,
                                  decoration: formFieldStyle.copyWith(
                                      labelText: "Remarks"),
                                  minLines: 4,
                                  maxLines: 4,
                                  controller: saleController.descCtrl,
                                  onChanged: (value) {
                                    saleController.transaction.value
                                        .txnDescription = value;
                                    saleController.transaction.refresh();
                                  },
                                  // validators: [],
                                ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),

          //=================================================Save button
          bottomNavigationBar: BottomSaveCancelButton(
            shadow: false,
            hasDelete: true,
            onDeleteBtnPressedFn: () async {
              showAlertDialog(context,
                  okText: "YES",
                  hasCancel: true,
                  cancelText: "NO",
                  alertType: AlertType.Error,
                  alertTitle: "Confirm Delete", onCloseButtonPressed: () async {
                // Navigator.of(_).pop();
                ProgressDialog progressDialog = ProgressDialog(context,
                    type: ProgressDialogType.normal, isDismissible: false);
                progressDialog.update(
                    message: "Checking Permission. Please wait....");
                await progressDialog.show();
                Tuple2<bool, String> checkResp =
                    await PermissionWrapperController()
                        .requestForPermissionCheck(
                            forPermission: PermissionManager.salesDelete);
                if (checkResp.item1) {
                  //has  permission
                  progressDialog.update(
                      message: "Deleting Data. Please wait....");
                  Tuple2<bool, String> deleteResp =
                      await TransactionRepository().delete(saleID ?? "");

                  await progressDialog.hide();
                  if (deleteResp.item1) {
                    //  data deleted
                    TransactionHelper.refreshPreviousPages();
                    showAlertDialog(context,
                        barrierDismissible: false,
                        alertType: AlertType.Success,
                        alertTitle: "", onCloseButtonPressed: () {
                      // Navigator.of(_).pop();
                      Navigator.of(context).pop();
                    }, message: deleteResp.item2);
                  } else {
                    //cannot  delete  data
                    showAlertDialog(context,
                        alertType: AlertType.Error,
                        alertTitle: "",
                        message: deleteResp.item2);
                  }
                } else {
                  await progressDialog.hide();
                  showAlertDialog(context,
                      alertType: AlertType.Error,
                      alertTitle: "",
                      message: checkResp.item2);
                }
              },
                  message:
                      "Are you sure you  want to  delete this sales record?");
            },
            enableFlag: true,
            saveText: "Share",
            onSaveBtnPressedFn: () async {
              TransactionHelper.goToPrintPage(
                  context, saleID ?? "", TxnType.sales);
            },
          ),
        ));
      }
    });
  }
}

Widget getItemListView(
  BuildContext context,
  List<LineItemDetailModel> _items,
  SaleDetailController saleController,
) {
  // return Container(height: 40, color: Colors.red);
  ScrollController scrollController = ScrollController();
  var listView = Scrollbar(
    controller: scrollController,
    thumbVisibility: true,
    child: ListView.builder(
        controller: scrollController,
        itemCount: _items.length,
        shrinkWrap: true,
        itemBuilder: (context, int index) {
          LineItemDetailModel item = _items[index];
          return Row(children: [
            Expanded(
                child: GestureDetector(
              onTap: saleController.readOnlyFlag
                  ? null
                  : () async {
                      var returnedData = await showDialog(
                          context: context,
                          useRootNavigator: true,
                          barrierDismissible: false,
                          builder: (dC) {
                            return AlertDialog(
                                insetPadding: const EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 10),
                                contentPadding: EdgeInsets.zero,
                                clipBehavior: Clip.hardEdge,
                                content: SizedBox(
                                  width: MediaQuery.of(context).size.width - 20,
                                  child: AddSaleBilledItemScreenView(
                                    lineItemModel: item,
                                  ),
                                ));
                          });
                      if (null != returnedData) {
                        if (returnedData.deleteFlag) {
                          saleController.items.removeAt(index);
                        } else if (null != returnedData.billedItem) {
                          saleController.items.replaceRange(
                              index, 1, [returnedData.billedItem]);
                          saleController.recalculateForItems();
                        }
                        saleController.recalculateForItems();
                      }
                    },
              child: Card(
                elevation: 2,
                margin: EdgeInsets.only(
                    left: 5,
                    right: 5,
                    top: (0 == index) ? 15 : 8,
                    bottom: ((_items.length - 1) == index) ? 20 : 8),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                  decoration: const BoxDecoration(color: Colors.black12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // =============================================item name
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              item.itemName ?? "",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: colorPrimaryDark),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 5,
                      ),

                      // =============================================gross amount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const SizedBox(
                            width: 75,
                            child: Text(
                              "Amount",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  color: Colors.black54),
                            ),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              "${item.quantity} ${item.lineItemUnitName ?? ""} X ${formatCurrencyAmount(item.pricePerUnit!)} = ${formatCurrencyAmount(item.grossAmount!, false)}",
                              textAlign: TextAlign.right,
                              style: TextStyle(fontSize: 12, color: textColor),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 5,
                      ),

                      // =============================================Discount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            width: 100,
                            child: Text(
                              "Discount(%): ${item.discountPercent}",
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  color: Colors.black54),
                            ),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              " = ${formatCurrencyAmount(item.discountAmount!, false)}",
                              textAlign: TextAlign.right,
                              style: TextStyle(fontSize: 12, color: textColor),
                            ),
                          ),
                        ],
                      ),
                      const Divider(
                        height: 5,
                      ),
                      const Divider(
                        height: 0,
                      ),

                      // =============================================netAmount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              formatCurrencyAmount(item.totalAmount!, false),
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: colorPrimary),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            )),
          ]);
        }),
  );

  return listView;
}
