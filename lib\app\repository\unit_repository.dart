// ignore_for_file: unnecessary_brace_in_string_interps

import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/model/database/unit_modal.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:tuple/tuple.dart';

class UnitRepository {
  final String tag = "UnitRepository";
  final String tableName = 'mk_item_units';
  DatabaseHelper databaseHelper = new DatabaseHelper();
  QueryRepository queryRepository = new QueryRepository();

  //========================================================================================= SYNCING ACTIONS
  Future<String> insert(UnitModel unit,
      {dynamic dbClient, String? batchID}) async {
    String unitId;

    if (null == dbClient) {
      dbClient = await databaseHelper.database;
    }

    String primaryKeyPrefix = await getPrimaryKeyPrefix();
    unit.unitId = primaryKeyPrefix + uuidV4;

    unit.unitName = toBeginningOfSentenceCase(unit.unitName);

    unit.lastActivityType = LastActivityType.New;
    unit.lastActivityAt = currentDateTime;
    unit.lastActivityBy = await getLastActivityBy();

    await dbClient.insert(tableName, unit.toJson());

    QueryModel newQueryModel = QueryModel(
        tableName: tableName, queryType: QueryType.insert, data: unit.toJson());
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    pushPendingQueries(source: "TRIGGER", dbClient: dbClient);

    unitId = unit.unitId ?? "";

    return unitId;
  }

  Future<bool> update(UnitModel unit,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    unit.unitName = toBeginningOfSentenceCase(unit.unitName);
    unit.lastActivityType = LastActivityType.Edit;
    unit.lastActivityAt = currentDateTime;
    unit.lastActivityBy = await getLastActivityBy();

    String whereClause = 'unit_id = ?';
    List<dynamic> whereArgs = [unit.unitId];

    await dbClient.update(tableName, unit.toJson(),
        where: whereClause, whereArgs: whereArgs);

    QueryModel newQueryModel = QueryModel(
        tableName: tableName,
        queryType: QueryType.update,
        data: unit.toJson(),
        whereArgs: whereArgs,
        whereClause: whereClause);
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);
    pushPendingQueries(source: "TRIGGER", dbClient: dbClient);

    status = true;

    return status;
  }

  Future<Tuple2<bool, String>> delete(String unitId,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;
    String message = "";

    dbClient ??= await databaseHelper.database;
    String whereClause = "unit_id = ?";
    List<dynamic> whereArgs = [unitId];
    Tuple2<bool, List<String>> hasRefRes =
        await DatabaseHelper.hasReferences(tablesWithColName: [
      {"table_name": "mk_items", "column_name": "base_unit_id"},
      {"table_name": "mk_items", "column_name": "alternate_unit_id"},
      {"table_name": "mk_line_items", "column_name": "line_item_unit_id"},
    ], value: unitId);

    if (hasRefRes.item1) {
      //  there is  reference of this id in other table
      // so throw  error message
      message =
          "Cannot delete Unit. Please clear all items related to unit before deleting";
    } else {
      // can soft delete for given id
      dbClient.update(
          tableName, {"last_activity_type": LastActivityType.Delete},
          where: whereClause, whereArgs: whereArgs);

      QueryModel newQueryModel = QueryModel(
          tableName: tableName,
          queryType: QueryType.update,
          whereArgs: whereArgs,
          whereClause: whereClause,
          data: {"last_activity_type": LastActivityType.Delete});

      await queryRepository.pushQuery(newQueryModel,
          batchID: batchID, dbClient: dbClient);
      pushPendingQueries(source: "TRIGGER", dbClient: dbClient);

      status = true;
      message = "Unit deleted successfully";
    }

    return Tuple2(status, message);
  }

  //=========================================================================================NON SYNCING ACTIONS
  Future<List<UnitModel>> getAllUnits({dynamic dbClient}) async {
    List<UnitModel> units = [];
    try {
      dbClient ??= await databaseHelper.database;
      List<Map<String, dynamic>> jsonUnits = await dbClient.rawQuery(
          "SELECT * FROM mk_item_units WHERE last_activity_type!=? ORDER BY unit_name ASC",
          [LastActivityType.Delete]);

      units = jsonUnits.map((unit) => UnitModel.fromJson(unit)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return units;
  }

  Future<UnitModel> getUnitById(String unitId, {dynamic dbClient}) async {
    UnitModel unit = UnitModel();
    try {
      dbClient ??= await databaseHelper.database;

      Map<String, dynamic> jsonItem = (await dbClient.rawQuery(
              "SELECT * FROM mk_item_units WHERE unit_id=? AND last_activity_type!=? ORDER BY unit_name ASC",
              [unitId, LastActivityType.Delete]))
          .first;
      unit = UnitModel.fromJson(jsonItem);
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return unit;
  }

  Future<List<UnitModel>> getUnitsByTitle(String title,
      {dynamic dbClient}) async {
    List<UnitModel> units = [];
    try {
      dbClient ??= await databaseHelper.database;

      List<Map<String, dynamic>> jsonList = [];
      jsonList = await dbClient.rawQuery(
          "SELECT * FROM mk_item_units WHERE unit_name LIKE ? OR unit_short_name LIKE ? AND last_activity_type!=? ORDER BY unit_name ASC Limit 10",
          ["${title}%", "${title}%", LastActivityType.Delete]);
      units = jsonList.map((unit) => UnitModel.fromJson(unit)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return units;
  }
}
