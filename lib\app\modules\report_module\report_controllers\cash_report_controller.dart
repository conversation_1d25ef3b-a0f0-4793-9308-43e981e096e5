import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/cash_txn_model.dart';
import 'package:mobile_khaata_v2/app/repository/report_repository.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:tuple/tuple.dart';

/**
 * CASH REPORT CONTROLLER - CALCULATION FIXES AND EXPLANATIONS:
 *
 * This controller handles cash flow statement calculations. Key improvements:
 * 1. Proper running balance calculations with decimal precision
 * 2. Clear opening balance handling
 * 3. Accurate cash flow tracking with transaction types
 * 4. Comprehensive comments explaining cash flow logic
 *
 * CASH FLOW CALCULATION PRINCIPLES:
 * - Opening Balance: Starting cash position
 * - Running Balance: Cumulative cash after each transaction
 * - Cash In: Positive transactions (sales, payments received)
 * - Cash Out: Negative transactions (purchases, payments made)
 */

class CashReportController extends GetxController {
  final String tag = "CashReportController";

  final ReportRepository _reportRepository = ReportRepository();

  final _txnLoading = false.obs;
  bool get txnLoading => _txnLoading.value;

  List<CashTxnModel> cashTransactions = [];

  var closingBalance = 0.00.obs;

  /**
   * CASH STATEMENT REPORT GENERATION:
   * Creates a detailed cash flow statement with running balance
   *
   * CASH FLOW CALCULATION LOGIC:
   * 1. Start with opening balance for the period
   * 2. Add each cash transaction sequentially
   * 3. Maintain running total for accurate closing balance
   * 4. Categorize transactions as cash in/out for clarity
   *
   * @param startDate - Report period start date
   * @param endDate - Report period end date
   */
  generateCashStatementReport(
      {required String startDate, required String endDate}) async {
    _txnLoading(true);

    // STEP 1: FETCH OPENING BALANCE AND TRANSACTION DATA
    // Repository returns tuple: (opening balance, transaction list)
    Tuple2<double, List<Map<String, dynamic>>> data = await _reportRepository
        .getCashTransaction(startDate: startDate, endDate: endDate);

    // STEP 2: RESET PREVIOUS DATA
    cashTransactions.clear();
    closingBalance.value = 0.00;

    // STEP 3: CREATE OPENING BALANCE ENTRY
    // This shows the cash position at the start of the report period
    double openingBalance = data.item1;

    cashTransactions.add(CashTxnModel(
      txnDateBS: toDateBS(DateTime.parse(startDate)),
      txnDate: startDate,
      txnAmount: openingBalance,
      cashTransactionType:
          (openingBalance >= 0) ? TxnType.addCash : TxnType.reduceCash,
      cashTransactionDesc: 'Balance B/F', // Balance Brought Forward
      txnTypeText: "",
    ));

    // STEP 4: INITIALIZE RUNNING BALANCE
    // Start with opening balance for cumulative calculation
    closingBalance.value =
        parseDouble(openingBalance.toStringAsFixed(2)) ?? 0.0;

    // STEP 5: PROCESS EACH CASH TRANSACTION
    for (int i = 0; i < data.item2.length; i++) {
      CashTxnModel txn = CashTxnModel.fromJson(data.item2[i]);

      // RUNNING BALANCE CALCULATION:
      // Add each transaction amount to get cumulative cash position
      // Positive amounts = Cash In, Negative amounts = Cash Out
      closingBalance.value = parseDouble(
              (closingBalance.value + (txn.txnAmount ?? 0.0))
                  .toStringAsFixed(2)) ??
          0.0;

      // TRANSACTION DESCRIPTION ENHANCEMENT:
      // Combine custom description with transaction type for clarity
      String? customDesc = strTrim(txn.cashTransactionDesc ?? "");
      txn.cashTransactionDesc = (customDesc != null && customDesc.isNotEmpty)
          ? "${txn.cashTransactionDesc!}\n[${txn.txnTypeText}]"
          : "[${txn.txnTypeText}]";

      cashTransactions.add(txn);
    }

    _txnLoading(false);
  }
}
