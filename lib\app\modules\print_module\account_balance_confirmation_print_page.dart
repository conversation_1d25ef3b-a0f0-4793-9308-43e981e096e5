// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/controllers/registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/registration_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/cash_txn_model.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

class LOCModal {
  bool? showHeader;
  String? partyName;
  String? partyAddress;
  String? tinNo;
  String? tinType;
  String? fiscalYear;
  double? openingBalance;
  double? closingBalance;
  double? salesTaxableTotal;
  double? salesTaxTotal;
  double? purchaseTaxableTotal;
  double? purchaseTaxTotal;
  double? salesReturnTaxableTotal;
  double? salesReturnTaxTotal;
  double? purchaseReturnTaxableTotal;
  double? purchaseReturnTaxTotal;

  LOCModal({
    this.showHeader,
    this.partyName,
    this.partyAddress,
    this.tinNo,
    this.tinType,
    this.fiscalYear,
    this.openingBalance,
    this.closingBalance,
    this.salesTaxableTotal,
    this.salesTaxTotal,
    this.purchaseTaxableTotal,
    this.purchaseTaxTotal,
    this.salesReturnTaxableTotal,
    this.salesReturnTaxTotal,
    this.purchaseReturnTaxableTotal,
    this.purchaseReturnTaxTotal,
  });
}

class AccountBalanceConfirmationPrintPage extends StatelessWidget {
  final String? startDate;
  final String? endDate;
  LOCModal? locData;

  AccountBalanceConfirmationPrintPage({
    super.key,
    this.startDate,
    this.endDate,
    this.locData,
  });

  @override
  Widget build(BuildContext context) {
    String startDate =
        NepaliDateTime.parse(toDateBS(DateTime.parse(this.startDate ?? "")))
            .format("y-MM-dd");
    String endDate =
        NepaliDateTime.parse(toDateBS(DateTime.parse(this.endDate ?? "")))
            .format("y-MM-dd");
    return SafeArea(
        child: Scaffold(
            // resizeToAvoidBottomPadding: true,
            resizeToAvoidBottomInset: true,
            appBar: AppBar(
              toolbarHeight: 60,
              elevation: 4,
              leading: BackButton(
                onPressed: () => Navigator.pop(context, false),
              ),
              centerTitle: false,
              backgroundColor: colorPrimary,
              titleSpacing: -5.0,
              title: const Text(
                "Confirmation Letter",
                style: TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontFamily: 'HelveticaRegular',
                    fontWeight: FontWeight.bold),
              ),
              actions: [
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10)),
                        backgroundColor: colorPrimary,
                        foregroundColor: colorPrimaryLightest,
                      ),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Column(
                        children: const [
                          Icon(
                            Icons.close,
                            color: Colors.white,
                          ),
                          Text(
                            "Cancel",
                            style: TextStyle(color: Colors.white, fontSize: 10),
                          ),
                        ],
                      )),
                ),
              ],
            ),
            body: PdfPreview(
                useActions: true,
                canChangePageFormat: false,
                initialPageFormat: defaultPdfPageFormat,
                // allowPrinting: false,
                // allowSharing: false,
                pdfFileName: "Confirmation Letter $startDate to  $endDate .pdf",
                maxPageWidth: 700,
                // actions: actions,
                build: (format) {
                  return generateReport(defaultPdfPageFormat,
                      transactions: [],
                      locData: locData!,
                      startDate: this.startDate!,
                      endDate: this.endDate!);
                })));
  }
}

/**
 * ACCOUNT BALANCE CONFIRMATION PDF GENERATOR - ENHANCED
 *
 * FIXED CRITICAL ISSUES:
 * 1. Wrong amount columns in table - using taxable amount for total column
 * 2. Sales Return and Purchase Return showing wrong values
 * 3. Missing proper calculation for net amounts
 * 4. Inconsistent column mapping in Product objects
 *
 * CORRECT LOGIC:
 * - Taxable Amount: The base amount before VAT
 * - VAT Amount: The tax amount calculated
 * - Total Amount: Taxable + VAT (for most transactions)
 * - Opening/Closing Balance: Should show only in total column
 */
Future<Uint8List> generateReport(PdfPageFormat pageFormat,
    {List<CashTxnModel>? transactions,
    String billTitle = "Cash in Hand Statement",
    String? startDate,
    LOCModal? locData,
    String? endDate}) async {
  double total = 0.0;

  double totalWithdrawl = 0.0;
  double totalDeposit = 0.0;

  final products = <Product>[];

  // FIXED: Proper amount calculations for each transaction type

  // Opening Balance - only in total column
  products.add(
      Product("Opening Balance", 0.0, 0.0, locData!.openingBalance ?? 0.0));

  // Sales - FIXED: Total = Taxable + VAT
  final salesTotalAmount =
      (locData.salesTaxableTotal ?? 0.0) + (locData.salesTaxTotal ?? 0.0);
  products.add(Product(
      "Sales",
      locData.salesTaxableTotal ?? 0.0,
      locData.salesTaxTotal ?? 0.0,
      salesTotalAmount)); // FIXED: Use calculated total

  // Sales Return - FIXED: Negative impact on totals
  final salesReturnTotalAmount = (locData.salesReturnTaxableTotal ?? 0.0) +
      (locData.salesReturnTaxTotal ?? 0.0);
  products.add(Product(
      "Sales Return",
      locData.salesReturnTaxableTotal ?? 0.0,
      locData.salesReturnTaxTotal ?? 0.0,
      -salesReturnTotalAmount)); // FIXED: Negative for return

  // Purchase - FIXED: Total = Taxable + VAT
  final purchaseTotalAmount =
      (locData.purchaseTaxableTotal ?? 0.0) + (locData.purchaseTaxTotal ?? 0.0);
  products.add(Product(
      "Purchase",
      locData.purchaseTaxableTotal ?? 0.0,
      locData.purchaseTaxTotal ?? 0.0,
      purchaseTotalAmount)); // FIXED: Use calculated total

  // Purchase Return - FIXED: Negative impact on totals
  final purchaseReturnTotalAmount =
      (locData.purchaseReturnTaxableTotal ?? 0.0) +
          (locData.purchaseReturnTaxTotal ?? 0.0);
  products.add(Product(
      "Purchase Return",
      locData.purchaseReturnTaxableTotal ?? 0.0,
      locData.purchaseReturnTaxTotal ?? 0.0,
      -purchaseReturnTotalAmount)); // FIXED: Negative for return

  // Closing Balance - only in total column
  products
      .add(Product("Closing Balance", 0.0, 0.0, locData.closingBalance ?? 0.0));

  RegistrationDetailController registrationDetailController =
      Get.find<RegistrationDetailController>(
          tag: "RegistrationDetailController");
  ImageModel sellerImageModel = registrationDetailController.logo;

  RegistrationDetailModel myDetail =
      registrationDetailController.registrationDetail;

  final invoice = Invoice(
    myDetail: myDetail,
    sellerImage: sellerImageModel,
    startDate: NepaliDateTime.parse(toDateBS(DateTime.parse(startDate ?? "")))
        .format("y/MM/dd"),
    endDate: NepaliDateTime.parse(toDateBS(DateTime.parse(endDate ?? "")))
        .format("y/MM/dd"),
    products: products,
    totalWithdrawl: totalWithdrawl,
    totalDeposit: totalDeposit,
    totalAmount: total,
    locData: locData,
    reportTitle: billTitle,
    baseColor: PdfColors.lightBlue,
    accentColor: PdfColors.blueGrey900,
  );

  return await invoice.buildPdf(pageFormat);
}

class Invoice {
  Invoice(
      {this.myDetail,
      this.sellerImage,
      this.products,
      this.baseColor,
      this.accentColor,
      this.startDate,
      this.endDate,
      this.totalAmount,
      this.partyName,
      this.txnType,
      this.totalWithdrawl,
      this.locData,
      this.totalDeposit,
      this.reportTitle});

  final RegistrationDetailModel? myDetail;
  final ImageModel? sellerImage;
  final LOCModal? locData;

  final List<Product>? products;
  final PdfColor? baseColor;
  final PdfColor? accentColor;
  final String? startDate;
  final String? endDate;
  final String? partyName;
  final String? txnType;
  final String? reportTitle;
  final double? totalWithdrawl;
  final double? totalDeposit;

  final double? totalAmount;

  static const _darkColor = PdfColors.blueGrey800;
  static const _lightColor = PdfColors.black;
  // ignore: constant_identifier_names
  static const _VedColor = PdfColor.fromInt(0xFF3560AF);
  PdfColor get _baseTextColor =>
      baseColor!.luminance < 0.5 ? _lightColor : _darkColor;

  var currencyInWords = NepaliNumberFormat(
    inWords: true,
    language: Language.english,
    isMonetory: true,
    decimalDigits: 2,
  );

  Future<Uint8List> buildPdf(PdfPageFormat pageFormat) async {
    // Create a PDF document.
    final doc = pw.Document();

    // Add page to the PDF
    doc.addPage(
      pw.MultiPage(
        header: _buildHeader,
        // footer: _buildFooter,
        build: (context) => [
          // _header(context),
          pw.SizedBox(height: 20),
          _renderTopPart(context),
          pw.SizedBox(height: 20),
          _renderSubject(context),
          pw.SizedBox(height: 20),
          _renderBodyTopPart(context),
          pw.SizedBox(height: 20),
          _contentTable(context),

          pw.SizedBox(height: 20),
          _renderBodyBottomPart(context),
          pw.SizedBox(height: 30),
          _renderBottomPart(context),
          // _contentFooter(context),
          pw.SizedBox(height: 20),
        ],
      ),
    );

    // Return the PDF file content
    return doc.save();
  }

  pw.Widget _renderBottomPart(pw.Context context) {
    return pw.Row(crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
      pw.Expanded(
          child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text("Thank you",
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
          pw.SizedBox(height: 6),
          pw.Text("For: ${myDetail!.businessName}",
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
        ],
      )),
      pw.SizedBox(width: 20),
      pw.Expanded(
          child: pw.Column(
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        children: [
          pw.Text("For: ${locData!.partyName}",
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
          pw.SizedBox(height: 50),
          pw.Text(".............................",
              style: pw.TextStyle(fontWeight: pw.FontWeight.bold)),
          pw.Text("Signature & Stamp"),
          pw.SizedBox(height: 10),
          pw.Text("Name: "),
          pw.SizedBox(height: 10),
          pw.Text("Designation: "),
          pw.SizedBox(height: 10),
          pw.Text("Date: "),
        ],
      )),
    ]);
  }

  pw.Widget _renderBodyTopPart(pw.Context context) {
    return pw.Container(
        child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
          pw.Text("Dear Sir/Madam,"),
          pw.Text(
              "With reference to above subject, the details of transaction fiscal year ${locData!.fiscalYear ?? ""} is as follows:"),
          // pw.Text("PAN/VAT: 304600345")
        ]));
  }

  pw.Widget _renderBodyBottomPart(pw.Context context) {
    return pw.Container(
        child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
          pw.Text(
              "Kindly send one copy of this letter duly signed and stamped within 7 days otherwise we shall remain this balance as your acceptance."),
        ]));
  }

  pw.Widget _renderSubject(pw.Context context) {
    return pw.Container(
        width: double.infinity,
        child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.center,
            children: [
              pw.Text(
                "Subject: Confirmation of Business Transaction for Fiscal Year ${locData!.fiscalYear ?? ""}",
                style: pw.TextStyle(
                  fontWeight: pw.FontWeight.bold,
                  decoration: pw.TextDecoration.underline,
                ),
              )
              // pw.Text("M/s: Moonlight International Education"),
              // pw.Text("PAN/VAT: 304600345")
            ]));
  }

  pw.Widget _renderTopPart(pw.Context context) {
    return pw.Container(
        child: pw
            .Column(crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
      pw.Text("Date: ${DateTime.now().toNepaliDateTime().format("y-MM-dd")}"),
      pw.Text("M/s: ${locData!.partyName ?? ""}"),
      pw.Text("Address: ${locData!.partyAddress ?? "N/A"}"),
      if (!["", null].contains(locData!.tinNo))
        pw.Text("${locData!.tinType}: ${locData!.tinNo ?? ""}"),
    ]));
  }

  pw.Widget _buildHeader(pw.Context context) {
    return pw.Column(
      children: [
        ...(locData!.showHeader!)
            ? [
                pw.Row(
                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                  children: [
                    pw.Expanded(
                      child: pw.Column(
                        mainAxisSize: pw.MainAxisSize.min,
                        children: [
                          if (null != sellerImage!.imageBitmap) ...{
                            pw.Row(
                                mainAxisAlignment: pw.MainAxisAlignment.center,
                                children: [
                                  pw.Container(
                                    margin: const pw.EdgeInsets.only(top: -20),
                                    alignment: pw.Alignment.center,
                                    height: 60,
                                    width: 60,
                                    child: sellerImage!.imageBitmap != null
                                        ? pw.Image(pw.MemoryImage(
                                            Uint8List.fromList(
                                                sellerImage!.imageBitmap!)))
                                        : pw.PdfLogo(),
                                  ),
                                ])
                          },
                          // pw.Container(
                          //   color: baseColor,
                          //   padding: pw.EdgeInsets.only(top: 3),
                          // ),
                        ],
                      ),
                    ),
                  ],
                ),
                pw.Row(
                    crossAxisAlignment: pw.CrossAxisAlignment.start,
                    children: [
                      pw.Expanded(
                          child: pw.Column(
                              mainAxisSize: pw.MainAxisSize.min,
                              children: [
                            pw.Container(
                              margin: const pw.EdgeInsets.only(top: 10),
                              alignment: pw.Alignment.center,
                              child: pw.Text(
                                myDetail!.businessName!,
                                style: pw.TextStyle(
                                  color: _VedColor,
                                  fontWeight: pw.FontWeight.bold,
                                  fontSize: 16,
                                ),
                              ),
                            ),
                            pw.Container(
                                margin: const pw.EdgeInsets.only(),
                                child: pw.Text(
                                  myDetail!.businessAddress ?? "",
                                  style: pw.TextStyle(
                                    color: accentColor,
                                    fontSize: 8,
                                  ),
                                )),
                            if (myDetail != null &&
                                null != myDetail!.tinNo &&
                                "" != myDetail!.tinNo)
                              pw.Container(
                                  margin: const pw.EdgeInsets.only(),
                                  child: pw.Text(
                                    "${myDetail?.tinFlag ?? ''} No. :${myDetail?.tinNo ?? ''}",
                                    style: pw.TextStyle(
                                      color: accentColor,
                                      fontSize: 8,
                                    ),
                                  )),
                          ]))
                    ]),
              ]
            : [
                pw.Container(
                  height: 100,
                ),
              ],
        if (context.pageNumber > 1) pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _header(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Expanded(
              child: pw.Column(
                children: [
                  pw.Container(
                    margin: const pw.EdgeInsets.only(bottom: 20, top: 10),
                    alignment: pw.Alignment.center,
                    child: pw.Text(reportTitle ?? "",
                        style: pw.TextStyle(
                            color: PdfColors.red,
                            fontSize: 15,
                            fontWeight: pw.FontWeight.bold)),
                  )
                ],
              ),
            ),
          ],
        ),
        if (context.pageNumber > 1) pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _buildFooter(pw.Context context) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: [
        // pw.Container(
        //   height: 20,
        //   width: 100,
        //   child: pw.BarcodeWidget(
        //     barcode: pw.Barcode.pdf417(),
        //     data: 'Invoice# $invoiceNumber',
        //   ),
        // ),
        pw.Text(
          'Page ${context.pageNumber}/${context.pagesCount}',
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
        pw.Text(
          FOOTER_PRINT_TEXT,
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
      ],
    );
  }

  // pw.PageTheme _buildTheme(
  //     PdfPageFormat pageFormat, pw.Font base, pw.Font bold, pw.Font italic) {
  //   return pw.PageTheme(
  //     pageFormat: pageFormat,
  //     theme: pw.ThemeData.withFont(
  //       base: base,
  //       bold: bold,
  //       italic: italic,
  //     ),
  //     buildBackground: (context) => pw.FullPage(
  //       ignoreMargins: true,
  //     ),
  //   );
  // }

  pw.Widget _contentTable(pw.Context context) {
    const tableHeaders = [
      'Particulars',
      'Taxable Amt.',
      'VAT Amt.',
      'Total Amt.',
    ];

    return pw.Table.fromTextArray(
      // border: null,
      cellAlignment: pw.Alignment.centerLeft,
      headerDecoration: const pw.BoxDecoration(
          // color: PdfColors.blue100,
          ),
      headerHeight: 24,
      cellHeight: 24,
      cellAlignments: {
        0: pw.Alignment.centerLeft,
        1: pw.Alignment.centerRight,
        2: pw.Alignment.centerRight,
        3: pw.Alignment.centerRight,
        4: pw.Alignment.centerRight
      },
      headerStyle: pw.TextStyle(
        color: _baseTextColor,
        fontSize: 9.5,
        fontWeight: pw.FontWeight.bold,
      ),
      cellStyle: pw.TextStyle(
        color: accentColor,
        fontSize: 8,
      ),
      rowDecoration: pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: accentColor!,
            width: .5,
          ),
        ),
      ),
      headers: List<String>.generate(
        tableHeaders.length,
        (col) => tableHeaders[col],
      ),
      data: List<List<String>>.generate(
        products!.length,
        (row) => List<String>.generate(
          tableHeaders.length,
          (col) => products![row].getIndex(col),
        ),
      ),
    );
  }
}

String _formatDate(DateTime date) {
  final format = DateFormat.yMMMd('en_US');
  return format.format(date);
}

class Product {
  const Product(
      this.particular, this.taxableAmount, this.taxAmount, this.total);

  final String particular;
  final double taxableAmount;
  final double taxAmount;
  final double total;

  String getNumber(double d) {
    if (d == 0.0) {
      return "-";
    }
    if (d < 0) {
      return "( " + d.abs().toStringAsFixed(2) + " )";
    }
    return d.toStringAsFixed(2);
  }

  String getIndex(int index) {
    switch (index) {
      case 0:
        return particular;
      case 1:
        return getNumber(taxableAmount);

      case 2:
        return getNumber(taxAmount);
      case 3:
        return getNumber(total);
      // case 5:
      //   return receivedAmount.toString();
      // case 6:
      //   return paidAmount.toString();
      // case 7:
      //   return balanceAmount.toString();
    }
    return '';
  }
}
