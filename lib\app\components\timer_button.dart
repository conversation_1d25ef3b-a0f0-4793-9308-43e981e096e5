// ignore_for_file: constant_identifier_names, library_private_types_in_public_api

import 'dart:async';

import 'package:flutter/material.dart';

enum ButtonType { RaisedButton, FlatButton, OutlineButton }

const int aSec = 1;

const String secPostFix = 's';
const String labelSplitter = " |  ";

class DTTimerButton extends StatefulWidget {
  /// Create a TimerButton button.
  ///
  /// The [label], [onPressed], and [timeOutInSeconds]
  /// arguments must not be null.

  ///label
  final String label;

  ///[timeOutInSeconds] after which the button is enabled
  final int timeOutInSeconds;

  ///[onPressed] Called when the button is tapped or otherwise activated.
  final VoidCallback onPressed;

  /// Defines the button's base colors
  final Color color;

  /// The color to use for this button's text when the button is disabled.
  final Color disabledColor;

  /// activeTextStyle
  final TextStyle? activeTextStyle;

  ///disabledTextStyle
  final TextStyle? disabledTextStyle;

  ///buttonType
  final ButtonType buttonType;

  const DTTimerButton({
    Key? key,
    required this.label,
    required this.onPressed,
    required this.timeOutInSeconds,
    this.color = Colors.blue,
    this.disabledColor = Colors.black45,
    this.buttonType = ButtonType.FlatButton,
    this.activeTextStyle = const TextStyle(color: Colors.white),
    this.disabledTextStyle = const TextStyle(color: Colors.black45),
  })  : assert(activeTextStyle != null),
        assert(disabledTextStyle != null),
        super(key: key);

  @override
  _DTTimerButtonState createState() => _DTTimerButtonState();
}

class _DTTimerButtonState extends State<DTTimerButton> {
  bool timeUpFlag = false;
  int timeCounter = 0;

  String get _timerText => '$timeCounter$secPostFix';

  @override
  void initState() {
    super.initState();
    timeCounter = widget.timeOutInSeconds;
    _timerUpdate();
  }

  @override
  dispose() {
    kera!.cancel();
    super.dispose();
  }

  Timer? kera;

  _timerUpdate() {
    kera = Timer(const Duration(seconds: aSec), () async {
      setState(() {
        timeCounter--;
      });
      if (timeCounter != 0) {
        _timerUpdate();
      } else {
        timeUpFlag = true;
      }
    });
  }

  Widget _buildChild() {
    return Container(
      // color: Colors.red,
      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
      child: timeUpFlag
          ? Text(
              widget.label,
              style: widget.activeTextStyle!.copyWith(color: widget.color),
            )
          : Text(
              widget.label + labelSplitter + _timerText,
              style: widget.disabledTextStyle,
            ),
    );
  }

  _onPressed() {
    if (timeUpFlag) {
      widget.onPressed();
    }
  }

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: _onPressed,
      child: _buildChild(),
    );
  }
}
