import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/model/database/expanse_category_model.dart';
import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';

import 'package:sqflite/sqflite.dart';
import 'package:tuple/tuple.dart';

class ExpensesCategoryRepository {
  final String tag = "ExpensesCategoryRepository";
  final String tableName = "mk_expense_category";
  DatabaseHelper databaseHelper = DatabaseHelper();
  QueryRepository queryRepository = QueryRepository();

  //========================================================================================= SYNCING ACTIONS
  Future<String> insert(ExpenseCategoryModel expenseCategoryModel,
      {dynamic dbClient, String? batchID}) async {
    String? expenseCategoryId;

    dbClient ??= await databaseHelper.database;

    String primaryKeyPrefix = await getPrimaryKeyPrefix();
    expenseCategoryModel.expenseCategoryId = primaryKeyPrefix + uuidV4;

    expenseCategoryModel.expenseTitle =
        toBeginningOfSentenceCase(expenseCategoryModel.expenseTitle);
    expenseCategoryModel.lastActivityAt = currentDateTime;
    expenseCategoryModel.lastActivityBy = await getLastActivityBy();
    expenseCategoryModel.lastActivityType = LastActivityType.New;

    await dbClient.insert(tableName, expenseCategoryModel.toJson());

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.insert,
      data: expenseCategoryModel.toJson(),
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    pushPendingQueries(source: "TRIGGER", dbClient: dbClient);

    expenseCategoryId = expenseCategoryModel.expenseCategoryId ?? "";
    return expenseCategoryId;
  }

  Future<bool> update(ExpenseCategoryModel expenseCategoryModel,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    expenseCategoryModel.expenseTitle =
        toBeginningOfSentenceCase(expenseCategoryModel.expenseTitle);
    expenseCategoryModel.lastActivityAt = currentDateTime;
    expenseCategoryModel.lastActivityBy = await getLastActivityBy();
    expenseCategoryModel.lastActivityType = LastActivityType.Edit;

    String whereClause = "expense_category_id = ?";
    List<dynamic> whereArgs = [expenseCategoryModel.expenseCategoryId];

    await dbClient.update(tableName, expenseCategoryModel.toJson(),
        where: whereClause, whereArgs: whereArgs);

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.update,
      whereArgs: whereArgs,
      whereClause: whereClause,
      data: expenseCategoryModel.toJson(),
    );
    await queryRepository.pushQuery(newQueryModel,
        dbClient: dbClient, batchID: batchID);

    pushPendingQueries(
        singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

    status = true;

    return status;
  }

  Future<Tuple2<bool, String>> delete(String categoryID,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;
    String message = "";

    dbClient ??= await databaseHelper.database;
    String whereClause = "expense_category_id = ?";
    List<dynamic> whereArgs = [categoryID];
    Tuple2<bool, List<String>> hasRefRes =
        await DatabaseHelper.hasReferences(tablesWithColName: [
      {"table_name": "mk_transactions", "column_name": "expense_category_id"}
    ], value: categoryID);

    if (hasRefRes.item1) {
      //  there is  reference of this id in other table
      // so throw  error message
      message =
          "Cannot delete Expense Category. Please clear all transaction related to this category before deleting";
    } else {
      // can soft delete for given id
      dbClient.update(
          tableName, {"last_activity_type": LastActivityType.Delete},
          where: whereClause, whereArgs: whereArgs);

      QueryModel newQueryModel = QueryModel(
          tableName: tableName,
          queryType: QueryType.update,
          whereArgs: whereArgs,
          whereClause: whereClause,
          data: {"last_activity_type": LastActivityType.Delete});

      await queryRepository.pushQuery(newQueryModel,
          batchID: batchID, dbClient: dbClient);

      pushPendingQueries(
          singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

      status = true;
      message = "Expense Category deleted successfully";
    }

    return Tuple2(status, message);
  }

  //=========================================================================================NON SYNCING ACTIONS

  Future<bool> checkUniqueCategoryName(String categoryName,
      {String? categoryID}) async {
    bool isUnique = await DatabaseHelper.isUnique(
      tableName: tableName,
      columnName: "expense_title",
      checkValue: strTrim(categoryName),
      keyColumn: "expense_category_id",
      keyValue: categoryID,
    );

    return isUnique;
  }

  Future<String> createIfNotExistByCategoryName(String categoryName,
      {dynamic dbClient, String? batchID}) async {
    String categoryID;

    dbClient ??= await databaseHelper.database;

    categoryName = strTrim(categoryName);

    List<Map<String, dynamic>> itemJsonList = (await dbClient.rawQuery(
        "SELECT expense_category_id FROM $tableName WHERE last_activity_type!=3 AND expense_title = ?",
        [categoryName]));
    if (itemJsonList.isNotEmpty) {
      categoryID = itemJsonList[0]['item_id'];
      // item already exist, so return if of that item
    } else {
      categoryID = await insert(
          ExpenseCategoryModel(expenseTitle: strTrim(categoryName)),
          dbClient: dbClient,
          batchID: batchID);
    }

    return categoryID;
  }

  Future<List<ExpenseCategoryModel>> getAllExpenseCategories() async {
    List<ExpenseCategoryModel> categories = [];
    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> jsonCategoryList =
          (await dbClient!.query(tableName, where: 'last_activity_type!=3'));
      categories = jsonCategoryList
          .map((e) => ExpenseCategoryModel.fromJson(e))
          .toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return categories;
  }

  Future<ExpenseCategoryModel?> getExpenseCategoriesByID(
      String categoryID) async {
    ExpenseCategoryModel? category;
    try {
      Database? dbClient = await databaseHelper.database;
      Map<String, dynamic> jsonCategoryItem = (await dbClient!.query(tableName,
              where: 'expense_category_id = ?', whereArgs: [categoryID]))
          .first;
      category = ExpenseCategoryModel.fromJson(jsonCategoryItem);
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return category;
  }
}
