import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/change_password/change_password_screen.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/change_password/securitiy_menu_page.dart';
// import 'package:flutter/widgets.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/login_screen.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/otp_verification.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/registration_module/registration_page.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/reset_password/reset_password_screen.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/update_registration_detail/update_registration_detail_page.dart';
import 'package:mobile_khaata_v2/app/modules/backup_download_module/backup_download_page.dart';
import 'package:mobile_khaata_v2/app/modules/backup_module/backup_screen.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/add_edit_bank_account/add_edit_bank_account_page.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_account_detail/bank_account_detail_page.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_account_list/bank_account_list_page.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_adjustment/add_edit_bank_adjustment_page.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_deposit/add_edit_bank_desposit_page.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_to_bank_transfer/add_edit_bank_to_bank_transfer_page.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_withdrawl/add_edit_bank_withdrawl_page.dart';
import 'package:mobile_khaata_v2/app/modules/cash_in_hand_module/add_edit_cash_adjustment/add_edit_cash_adjustment_page.dart';
import 'package:mobile_khaata_v2/app/modules/cash_in_hand_module/cash_adjustment_detail/cash_adjustment_detail_page.dart';
import 'package:mobile_khaata_v2/app/modules/cheque_module/cheque_deposit/cheque_deposit_page.dart';
import 'package:mobile_khaata_v2/app/modules/cheque_module/cheque_list/cheque_list_page.dart';
import 'package:mobile_khaata_v2/app/modules/credit_list_module/credit_list_page.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_category_module/expense_category_list/expense_category_list_page.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_module/add_edit_expense/add_edit_expense_page.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_module/detail_expense/detail_express_page.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_module/expense_list/expense_list_page.dart';
import 'package:mobile_khaata_v2/app/modules/home_module/landing_screen.dart';
import 'package:mobile_khaata_v2/app/modules/home_module/splash_screen.dart';
import 'package:mobile_khaata_v2/app/modules/import_modules/import_item_page.dart';
import 'package:mobile_khaata_v2/app/modules/import_modules/import_party_page.dart';
import 'package:mobile_khaata_v2/app/modules/intro_page_module/intro_screen.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/add_edit_item/add_edit_item_page.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/item_adjustment/item_adjustment_page.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/item_detail/item_detail_page.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/item_list/item_unit_list_page.dart';
import 'package:mobile_khaata_v2/app/modules/notification_module/notification_list_page.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/add_party/add_edit_party_page.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/party_detail/party_detail_page.dart';
import 'package:mobile_khaata_v2/app/modules/party_module/party_list/party_list_page.dart';
import 'package:mobile_khaata_v2/app/modules/payment_module/add_edit_payment_screen.dart';
import 'package:mobile_khaata_v2/app/modules/payment_module/detail_payment/detail_payment_screen.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_view.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/account_balance_confirmation_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/all_party_statement_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/all_transaction_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/annex_report_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/bank_statement_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/cash_in_hand_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/daybook_report_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/discount_report_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/expense_category_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/expense_transaction_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/general_transaction_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/party_statement_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/preview.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/sales_purchase_vat_report_print.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/single_transaction_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/stock_detail_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/stock_summary_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_module.dart/add_edit_purchase/add_edit_purchase_page.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_module.dart/detail_purchase/detail_purchase_page.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_return_module/add_purchase_return/add_edit_purchase_return_view.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_return_module/detail_purchase_return/detail_purchase_return_page.dart';
import 'package:mobile_khaata_v2/app/modules/receipt_module/add_edit_receipt_page.dart';
import 'package:mobile_khaata_v2/app/modules/receipt_module/detailed_receipt/detailed_receipt_screen.dart';
import 'package:mobile_khaata_v2/app/modules/reminder_module/add_edit_reminder/add_edit_reminder_page.dart';
import 'package:mobile_khaata_v2/app/modules/reminder_module/reminder_list/reminder_list_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/report_list_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/account_balaance_confirmation.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/all_transaction_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/annex_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/bank_statement/bank_statement_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/cash_statement_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/day_book/day_book_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/discount_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/expense_category_summary_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/expense_transaction_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/party_report/all_party_report_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/party_report/party_statement_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/purchase_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/purchase_return_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/sales_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/sales_return_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/stock_report/low_stock_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/stock_report/stock_detail_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/stock_report/stock_summary_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/var_purchase_register_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/vat_expense_register_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/vat_sales_register_report.dart';
import 'package:mobile_khaata_v2/app/modules/sales_module/add_sale/add_edit_sale_view.dart';
import 'package:mobile_khaata_v2/app/modules/sales_module/detail_sale/detail_sale_page.dart';
import 'package:mobile_khaata_v2/app/modules/sales_return_module/add_sale_return/add_edit_sale_return_view.dart';
import 'package:mobile_khaata_v2/app/modules/sales_return_module/detail_sale_return/detail_sale_return_page.dart';
import 'package:mobile_khaata_v2/app/modules/subscription_module/subscription_page/subscription_page.dart';
import 'package:mobile_khaata_v2/app/modules/subscription_module/subscription_payment_detail/subscriptiobn_payment_detail_page.dart';
import 'package:mobile_khaata_v2/app/modules/transaction_list_module/all_transaction_list_page.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/add_edit_user/add_edit_user_screen.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/inactive_module/inactive_screen.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/user_list/user_list_screen.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/user_permisson/user_permission_screen.dart';
import 'package:mobile_khaata_v2/app/modules/web_view_modal/general_web_view.dart';
import 'package:mobile_khaata_v2/app/modules/youtube_module/youtube_tutorial_list_page.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';

class RouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final args = settings.arguments;

    if (!isUserLoggedIn) {
      switch (settings.name) {
        case '/':
          return MaterialPageRoute(builder: (_) => SplashScreen());
        case '/login':
          return MaterialPageRoute(builder: (_) => LoginScreen());
        case '/introScreen':
          return MaterialPageRoute(builder: (_) => const IntroScreen());
        case '/resetPassword':
          return MaterialPageRoute(builder: (_) {
            ResetPasswordScreen? optArgs;
            if (args != null) {
              optArgs = args as ResetPasswordScreen;
            }
            return ResetPasswordScreen(
              username: optArgs?.username,
              token: optArgs?.token,
              mobileNo: optArgs?.mobileNo,
            );
          });
        case '/otp_verify':
          return MaterialPageRoute(builder: (_) {
            OtpVerifyScreenArguments? optArgs;
            if (args != null) {
              optArgs = args as OtpVerifyScreenArguments;
            }
            return OtpVerificationScreen(
                optArgs?.id ?? "", optArgs?.mobileNo ?? "");
          });
        case '/registration':
          return MaterialPageRoute(builder: (_) => NewRegistrationPage());
        default:
          return MaterialPageRoute(builder: (_) => LoginScreen());
      }
    }

    // routes available for inactive users
    if (isActiveUser == 0) {
      switch (settings.name) {
        case '/':
          return MaterialPageRoute(builder: (_) => SplashScreen());
        case '/login':
          return MaterialPageRoute(builder: (_) => LoginScreen());
        case '/registration':
          return MaterialPageRoute(builder: (_) => NewRegistrationPage());
        default:
          return MaterialPageRoute(builder: (_) => InactivePage());
      }
    }

    // routes available for expired users
    if (isExpiredUser) {
      switch (settings.name) {
        case '/':
          return MaterialPageRoute(builder: (_) => SplashScreen());
        case '/home':
          return MaterialPageRoute(builder: (_) => LandingScreen());
        case '/login':
          return MaterialPageRoute(builder: (_) => LoginScreen());
        case '/registration':
          return MaterialPageRoute(builder: (_) => NewRegistrationPage());
        case '/subscriptionPaymentDetail':
          return MaterialPageRoute(builder: (_) {
            SubscriptionPaymentDetailPage? optArgs;
            if (args != null) {
              optArgs = args as SubscriptionPaymentDetailPage;
            }
            return SubscriptionPaymentDetailPage(package: optArgs?.package);
          });
        default:
          return MaterialPageRoute(builder: (_) => const SubscriptionPage());
      }
    }

    switch (settings.name) {
      case '/':
        return MaterialPageRoute(builder: (_) => SplashScreen());
      case '/introScreen':
        return MaterialPageRoute(builder: (_) => IntroScreen());
      case '/login':
        return MaterialPageRoute(builder: (_) => LoginScreen());
      case '/registration':
        return MaterialPageRoute(builder: (_) => NewRegistrationPage());
      case '/resetPassword':
        return MaterialPageRoute(builder: (_) {
          ResetPasswordScreen? optArgs;
          if (args != null) {
            optArgs = args as ResetPasswordScreen;
          }
          return ResetPasswordScreen(
            username: optArgs?.username,
            token: optArgs?.token,
            mobileNo: optArgs?.mobileNo,
          );
        });

      case '/otp_verify':
        return MaterialPageRoute(builder: (_) {
          OtpVerifyScreenArguments? optArgs;
          if (args != null) {
            optArgs = args as OtpVerifyScreenArguments;
          }
          return OtpVerificationScreen(
              optArgs?.id ?? "", optArgs?.mobileNo ?? "");
        });

      //======================================================================== Home Module
      case '/home':
        return MaterialPageRoute(builder: (_) => LandingScreen());
      case '/userBasicInfo':
        return MaterialPageRoute(
            builder: (_) => UpdateRegistrationDetailPage());

      case '/itemList':
        return MaterialPageRoute(
            builder: (context) => const ItemUnitListPage());
      case '/addItem':
        return MaterialPageRoute(builder: (_) {
          AddEditItemPage? optArgs;
          if (args != null) {
            optArgs = args as AddEditItemPage;
          }
          return PermissionWrapper(
            requiredPermission: (null != optArgs?.itemId)
                ? PermissionManager.itemEdit
                : PermissionManager.itemAdd,
            child: AddEditItemPage(
              itemId: optArgs?.itemId ?? "",
            ),
          );
        });

      case '/itemDetail':
        return MaterialPageRoute(builder: (context) {
          ItemDetailPage? optArgs;
          if (args != null) {
            optArgs = args as ItemDetailPage;
          }
          return PermissionWrapper(
            requiredPermission: PermissionManager.itemAdd,
            child: ItemDetailPage(
              itemId: optArgs?.itemId ?? "",
            ),
          );
        });

      case '/itemAdjustment':
        return MaterialPageRoute(builder: (context) {
          ItemAdjustmentPage? optArgs;
          if (args != null) {
            optArgs = args as ItemAdjustmentPage;
          }

          return PermissionWrapper(
            requiredPermission: (null != optArgs?.adjustmentId)
                ? PermissionManager.itemAdjEdit
                : PermissionManager.itemAdjAdd,
            child: ItemAdjustmentPage(
              itemId: optArgs?.itemId,
              adjustmentId: optArgs?.adjustmentId,
            ),
          );
        });

      case '/importItem':
        return MaterialPageRoute(builder: (context) {
          return ImportItemPage();
        });

      //==================================================================================== Cash in hand Module
      case '/cashInHandDetail':
        return MaterialPageRoute(builder: (context) {
          return PermissionWrapper(
              requiredPermission: PermissionManager.cashInHandView,
              child: CashAdjustmentDetailPage());
        });
      case '/cashInHandAdjustment':
        return MaterialPageRoute(builder: (context) {
          AddEditCashAdjustmentPage? optArgs;
          if (args != null) {
            optArgs = args as AddEditCashAdjustmentPage;
          }
          return PermissionWrapper(
            requiredPermission: (null != optArgs?.adjustmentId)
                ? PermissionManager.cashAdjEdit
                : PermissionManager.cashAdjAdd,
            child: AddEditCashAdjustmentPage(
              adjustmentId: optArgs?.adjustmentId,
            ),
          );
        });

      //==================================================================================== Party Module
      case '/partyLedgerList':
        return MaterialPageRoute(builder: (context) => const PartyListPage());
      case '/addPartyLedger':
        return MaterialPageRoute(builder: (context) {
          AddEditPartyPage? optArgs;
          if (args != null) {
            optArgs = args as AddEditPartyPage;
          }
          return PermissionWrapper(
            requiredPermission: (null != optArgs?.partyID)
                ? PermissionManager.partyEdit
                : PermissionManager.partyAdd,
            child: AddEditPartyPage(
              partyID: optArgs?.partyID,
            ),
          );
        });

      case '/partyLedgerDetail':
        return MaterialPageRoute(builder: (context) {
          PartyDetailPage? optArgs;
          if (args != null) {
            optArgs = args as PartyDetailPage;
          }
          return PermissionWrapper(
            requiredPermission: PermissionManager.partyAdd,
            child: PartyDetailPage(
              ledgerId: optArgs?.ledgerId ?? "",
            ),
          );
        });

      case '/importParty':
        return MaterialPageRoute(builder: (context) {
          return ImportPartyPage();
        });

      //====================================================================================  Purchase Module
      case '/addPurchase':
        return MaterialPageRoute(builder: (context) {
          AddEditPurchasePage? optArgs;
          if (args != null) {
            optArgs = args as AddEditPurchasePage;
          }
          return PermissionWrapper(
              requiredPermission: (null != optArgs?.purchaseID)
                  ? PermissionManager.purchaseEdit
                  : PermissionManager.purchaseAdd,
              child: AddEditPurchasePage(
                purchaseID: optArgs?.purchaseID,
                reaOnlyFlag: optArgs?.reaOnlyFlag,
              ));
        });
      case '/detailPurchase':
        return MaterialPageRoute(builder: (context) {
          DetailPurchasePage? optArgs;
          if (args != null) {
            optArgs = args as DetailPurchasePage;
          }

          return PermissionWrapper(
              requiredPermission: PermissionManager.purchaseAdd,
              child: DetailPurchasePage(
                purchaseID: optArgs?.purchaseID,
                reaOnlyFlag: optArgs?.reaOnlyFlag,
              ));
        });

      //==================================================================================== Sale Module
      case '/addSale':
        return MaterialPageRoute(builder: (_) {
          AddEditSalePage? optArgs;
          if (args != null) {
            optArgs = args as AddEditSalePage;
          }
          return PermissionWrapper(
            requiredPermission: (null != optArgs?.saleID)
                ? PermissionManager.salesEdit
                : PermissionManager.salesAdd,
            child: AddEditSalePage(
              saleID: optArgs?.saleID,
              reaOnlyFlag: optArgs?.reaOnlyFlag,
            ),
          );
        });
      case '/detailSale':
        return MaterialPageRoute(builder: (_) {
          DetailSalePage? optArgs;
          if (args != null) {
            optArgs = args as DetailSalePage;
          }
          return PermissionWrapper(
            requiredPermission: PermissionManager.salesAdd,
            child: DetailSalePage(
              saleID: optArgs?.saleID,
              reaOnlyFlag: optArgs?.reaOnlyFlag,
            ),
          );
        });

      //====================================================================================  Sale Return Module
      case '/addSaleReturn':
        return MaterialPageRoute(builder: (_) {
          AddEditSaleReturnPage? optArgs;
          if (args != null) {
            optArgs = args as AddEditSaleReturnPage;
          }
          return PermissionWrapper(
              requiredPermission: (null != optArgs?.salesReturnID)
                  ? PermissionManager.salesReturnEdit
                  : PermissionManager.salesReturnAdd,
              child: AddEditSaleReturnPage(
                salesReturnID: optArgs?.salesReturnID,
                reaOnlyFlag: optArgs?.reaOnlyFlag,
              ));
        });
      case '/detailSaleReturn':
        return MaterialPageRoute(builder: (_) {
          DetailSaleReturnPage? optArgs;
          if (args != null) {
            optArgs = args as DetailSaleReturnPage;
          }
          return PermissionWrapper(
              requiredPermission: PermissionManager.salesReturnAdd,
              child: DetailSaleReturnPage(
                salesReturnID: optArgs?.salesReturnID,
                reaOnlyFlag: optArgs?.reaOnlyFlag,
              ));
        });

      //====================================================================================  Purchase Return Module
      case '/addPurchaseReturn':
        return MaterialPageRoute(builder: (_) {
          AddEditPurchaseReturnPage? optArgs;
          if (args != null) {
            optArgs = args as AddEditPurchaseReturnPage;
          }
          return PermissionWrapper(
              requiredPermission: (null != optArgs?.purchaseReturnID)
                  ? PermissionManager.purchaseReturnEdit
                  : PermissionManager.purchaseReturnAdd,
              child: AddEditPurchaseReturnPage(
                purchaseReturnID: optArgs?.purchaseReturnID,
                reaOnlyFlag: optArgs?.reaOnlyFlag,
              ));
        });
      case '/detailPurchaseReturn':
        return MaterialPageRoute(builder: (_) {
          DetailPurchaseReturnPage? optArgs;
          if (args != null) {
            optArgs = args as DetailPurchaseReturnPage;
          }
          return PermissionWrapper(
              requiredPermission: PermissionManager.purchaseReturnAdd,
              child: DetailPurchaseReturnPage(
                purchaseReturnID: optArgs?.purchaseReturnID,
                reaOnlyFlag: optArgs?.reaOnlyFlag,
              ));
        });

      //====================================================================================  Expenses Category Module
      case '/listExpensesCategory':
        return MaterialPageRoute(builder: (_) {
          return const ExpenseCategoryListPage();
        });
      case '/addExpensesCategory':
        return MaterialPageRoute(builder: (_) {
          AddEditExpensesPage? optArgs;
          if (args != null) {
            optArgs = args as AddEditExpensesPage;
          }
          return PermissionWrapper(
              requiredPermission: (null != optArgs?.expensID)
                  ? PermissionManager.expenseCategoryEdit
                  : PermissionManager.expenseCategoryDelete,
              child: AddEditExpensesPage(
                expensID: optArgs?.expensID,
                reaOnlyFlag: optArgs?.reaOnlyFlag,
              ));
        });

      //====================================================================================  Expenses Module
      case '/listExpenses':
        return MaterialPageRoute(builder: (_) {
          return PermissionWrapper(
              requiredPermission: PermissionManager.expenseAdd,
              child: const ExpensesListPage());
        });
      case '/addExpenses':
        return MaterialPageRoute(builder: (_) {
          AddEditExpensesPage? optArgs;
          if (args != null) {
            optArgs = args as AddEditExpensesPage;
          }
          return PermissionWrapper(
              requiredPermission: (null != optArgs?.expensID)
                  ? PermissionManager.expenseEdit
                  : PermissionManager.expenseAdd,
              child: AddEditExpensesPage(
                expensID: optArgs?.expensID,
                reaOnlyFlag: optArgs?.reaOnlyFlag,
              ));
        });
      case '/detailExpenses':
        return MaterialPageRoute(builder: (_) {
          DetailExpensesPage? optArgs;
          if (args != null) {
            optArgs = args as DetailExpensesPage;
          }
          return PermissionWrapper(
              requiredPermission: PermissionManager.expenseAdd,
              child: DetailExpensesPage(
                expensID: optArgs?.expensID,
                reaOnlyFlag: optArgs?.reaOnlyFlag,
              ));
        });

      //====================================================================================  Credit Module
      case '/creditList':
        CreditListPage? optArgs;
        if (args != null) {
          optArgs = args as CreditListPage;
        }
        return MaterialPageRoute(
            builder: (_) => CreditListPage(
                  initialPage: optArgs?.initialPage,
                ));
      case '/creditReceive':
        return MaterialPageRoute(builder: (context) {
          AddEditReceiptPage? optArgs;
          if (args != null) {
            optArgs = args as AddEditReceiptPage;
          }
          if (args != null) {
            optArgs = args as AddEditReceiptPage;
          }
          return PermissionWrapper(
            requiredPermission: (null != optArgs?.txnId)
                ? PermissionManager.receiptEdit
                : PermissionManager.receiptAdd,
            child: AddEditReceiptPage(
              ledgerId: optArgs?.ledgerId,
              txnId: optArgs?.txnId,
            ),
          );
        });
      case '/detailcreditReceive':
        return MaterialPageRoute(builder: (context) {
          DetailReceiptPage? optArgs;
          if (args != null) {
            optArgs = args as DetailReceiptPage;
          }
          return PermissionWrapper(
            requiredPermission: PermissionManager.receiptAdd,
            child: DetailReceiptPage(
              ledgerId: optArgs?.ledgerId,
              txnId: optArgs?.txnId,
            ),
          );
        });
      case '/creditPay':
        return MaterialPageRoute(builder: (context) {
          AddEditPaymentPage? optArgs;
          if (args != null) {
            optArgs = args as AddEditPaymentPage;
          }

          if (args != null) {
            optArgs = args as AddEditPaymentPage;
          }
          return PermissionWrapper(
            requiredPermission: (null != optArgs?.txnId)
                ? PermissionManager.paymentEdit
                : PermissionManager.paymentAdd,
            child: AddEditPaymentPage(
              ledgerId: optArgs?.ledgerId,
              txnId: optArgs?.txnId,
            ),
          );
        });
      case '/detailcreditPay':
        return MaterialPageRoute(builder: (context) {
          DetailPaymentPage? optArgs;
          if (args != null) {
            optArgs = args as DetailPaymentPage;
          }
          return PermissionWrapper(
            requiredPermission: PermissionManager.paymentAdd,
            child: DetailPaymentPage(
              ledgerId: optArgs?.ledgerId,
              txnId: optArgs?.txnId,
            ),
          );
        });
      //====================================================================================  All Transaction Module
      case '/allTransaction':
        return MaterialPageRoute(builder: (_) {
          return AllTransactionListPage();
        });
      //====================================================================================  User Module
      case '/allUsers':
        return MaterialPageRoute(builder: (_) {
          return UserListPage();
        });
      case '/addEditUser':
        return MaterialPageRoute(builder: (_) {
          AddEditUser? optArgs;
          if (args != null) {
            optArgs = args as AddEditUser;
          }
          return AddEditUser(
            userData: optArgs?.userData,
            userId: optArgs?.userId,
          );
        });
      case '/userPermission':
        return MaterialPageRoute(builder: (_) {
          UserPermissionScreen? optArgs;
          if (args != null) {
            optArgs = args as UserPermissionScreen;
          }
          return UserPermissionScreen(
            user: optArgs?.user,
          );
        });

      //====================================================================================  Tutorial Module
      case '/tutorials':
        return MaterialPageRoute(builder: (_) => const YoutubeTutorialPage());

      //====================================================================================  Security Module
      case '/securitySetting':
        return MaterialPageRoute(builder: (_) => const SecurityPage());
      case '/changePassword':
        return MaterialPageRoute(builder: (_) => ChangePasswordPage());

      //====================================================================================  Subscription Module
      case '/subscription':
        return MaterialPageRoute(builder: (_) => const SubscriptionPage());
      case '/subscriptionPaymentDetail':
        return MaterialPageRoute(builder: (_) {
          SubscriptionPaymentDetailPage? optArgs;
          if (args != null) {
            optArgs = args as SubscriptionPaymentDetailPage;
          }
          return SubscriptionPaymentDetailPage(package: optArgs?.package);
        });

      case '/backup':
        return MaterialPageRoute(builder: (_) => BackupPage());

      case '/print':
        return MaterialPageRoute(builder: (_) {
          GeneralTransactionPrintPage? optArgs;
          if (args != null) {
            optArgs = args as GeneralTransactionPrintPage;
          }
          return GeneralTransactionPrintPage(
              txnID: optArgs?.txnID, txnType: optArgs?.txnType);
        });

      case '/printSingleTransactions':
        return MaterialPageRoute(builder: (_) {
          SingleTransactionPrintPage? optArgs;
          if (args != null) {
            optArgs = args as SingleTransactionPrintPage;
          }
          return SingleTransactionPrintPage(
            pageTitle: optArgs?.pageTitle,
            partyText: optArgs?.partyText,
            transactions: optArgs?.transactions,
            txnTypeText: optArgs?.txnTypeText,
            startDate: optArgs?.startDate,
            endDate: optArgs?.endDate,
          );
        });

      case '/printDayBook':
        return MaterialPageRoute(builder: (_) {
          DaybookReportPrintPage? optArgs;
          if (args != null) {
            optArgs = args as DaybookReportPrintPage;
          }
          return DaybookReportPrintPage(
            transactions: optArgs?.transactions,
            dateFor: optArgs?.dateFor,
          );
        });

      case '/printTransactions':
        return MaterialPageRoute(builder: (_) {
          AllTransactionPrintPage? optArgs;
          if (args != null) {
            optArgs = args as AllTransactionPrintPage;
          }
          return AllTransactionPrintPage(
            pageTitle: optArgs?.pageTitle,
            partyText: optArgs?.partyText,
            transactions: optArgs?.transactions,
            txnTypeText: optArgs?.txnTypeText,
            startDate: optArgs?.startDate,
            endDate: optArgs?.endDate,
          );
        });
      case '/printPartyStatement':
        return MaterialPageRoute(builder: (_) {
          AllPartyStatementPrintPage? optArgs;
          if (args != null) {
            optArgs = args as AllPartyStatementPrintPage;
          }
          return AllPartyStatementPrintPage(
            ledgers: optArgs?.ledgers,
          );
        });

      case '/printSinglePartyStatement':
        return MaterialPageRoute(builder: (_) {
          PartyStatementPrintPage? optArgs;
          if (args != null) {
            optArgs = args as PartyStatementPrintPage;
          }
          return PartyStatementPrintPage(
            transactions: optArgs?.transactions,
            startDate: optArgs?.startDate,
            endDate: optArgs?.endDate,
            partyText: optArgs?.partyText,
          );
        });

      case '/printExpenseTransactionReport':
        return MaterialPageRoute(builder: (_) {
          ExpenseTransactionReportPrintPage? optArgs;
          if (args != null) {
            optArgs = args as ExpenseTransactionReportPrintPage;
          }
          return ExpenseTransactionReportPrintPage(
              transactions: optArgs?.transactions,
              startDate: optArgs?.startDate,
              endDate: optArgs?.endDate,
              categoryText: optArgs?.categoryText);
        });

      case '/printExpenseCategoryReport':
        return MaterialPageRoute(builder: (_) {
          ExpenseCategoryReportPrintPage? optArgs;
          if (args != null) {
            optArgs = args as ExpenseCategoryReportPrintPage;
          }
          return ExpenseCategoryReportPrintPage(
            transactions: optArgs?.transactions,
            startDate: optArgs?.startDate,
            endDate: optArgs?.endDate,
          );
        });

      case '/printSalesPurchaseVatReport':
        return MaterialPageRoute(builder: (_) {
          SalesPurchaseVatPrintPage? optArgs;
          if (args != null) {
            optArgs = args as SalesPurchaseVatPrintPage;
          }
          return SalesPurchaseVatPrintPage(
            pageTitle: optArgs?.pageTitle ?? "",
            transactions: optArgs?.transactions,
            txnTypeText: optArgs?.txnTypeText ?? "",
            startDate: optArgs?.startDate ?? "",
            endDate: optArgs?.endDate,
          );
        });

      case '/printStockSummaryReport':
        return MaterialPageRoute(builder: (_) {
          StockSummaryPrintPage? optArgs;
          if (args != null) {
            optArgs = args as StockSummaryPrintPage;
          }
          return StockSummaryPrintPage(
            pageTitle: optArgs?.pageTitle ?? "",
            transactions: optArgs?.transactions,
            dateFor: optArgs?.dateFor,
          );
        });
      case '/printStockDetailReport':
        return MaterialPageRoute(builder: (_) {
          StockDetailPrintPage? optArgs;
          if (args != null) {
            optArgs = args as StockDetailPrintPage;
          }
          return StockDetailPrintPage(
            pageTitle: optArgs?.pageTitle ?? "",
            transactions: optArgs?.transactions,
            startDate: optArgs?.startDate,
            endDate: optArgs?.endDate,
          );
        });

      case '/printDiscountReport':
        return MaterialPageRoute(builder: (_) {
          DiscountReportPrintPage? optArgs;
          if (args != null) {
            optArgs = args as DiscountReportPrintPage;
          }
          return DiscountReportPrintPage(
            pageTitle: optArgs?.pageTitle,
            transactions: optArgs?.transactions,
            startDate: optArgs?.startDate,
            endDate: optArgs?.endDate,
          );
        });

      case '/printBankStatement':
        return MaterialPageRoute(builder: (_) {
          BankStatementPrintPage? optArgs;
          if (args != null) {
            optArgs = args as BankStatementPrintPage;
          }
          return BankStatementPrintPage(
            transactions: optArgs?.transactions,
            startDate: optArgs?.startDate,
            endDate: optArgs?.endDate,
            bank: optArgs?.bank,
          );
        });
      case '/printCashStatement':
        return MaterialPageRoute(builder: (_) {
          CashInHandPrintPage? optArgs;
          if (args != null) {
            optArgs = args as CashInHandPrintPage;
          }
          return CashInHandPrintPage(
            transactions: optArgs?.transactions,
            startDate: optArgs?.startDate,
            endDate: optArgs?.endDate,
          );
        });

      case '/printLOC':
        return MaterialPageRoute(builder: (_) {
          AccountBalanceConfirmationPrintPage? optArgs;
          if (args != null) {
            optArgs = args as AccountBalanceConfirmationPrintPage;
          }
          return AccountBalanceConfirmationPrintPage(
            // transactions: optArgs?.transactions,
            locData: optArgs?.locData,
            startDate: optArgs?.startDate,
            endDate: optArgs?.endDate,
          );
        });

      case '/printAnnex13':
        return MaterialPageRoute(builder: (_) {
          AnnexReportPrintPage? optArgs;
          if (args != null) {
            optArgs = args as AnnexReportPrintPage;
          }
          return AnnexReportPrintPage(
            transactions: optArgs?.transactions,
            startDate: optArgs?.startDate,
            endDate: optArgs?.endDate,
          );
        });

      case '/testPrint':
        return MaterialPageRoute(builder: (_) => Preview());

      //====================================================================================  Report Module
      case '/reportList':
        return MaterialPageRoute(builder: (_) => ReportListPage());
      case '/salesReport':
        SalesReport? optArgs;
        if (args != null) {
          optArgs = args as SalesReport;
        }

        return MaterialPageRoute(
          builder: (_) => PermissionWrapper(
              requiredPermission: PermissionManager.reportView,
              child: SalesReport(
                  receivedEnd: optArgs?.receivedEnd,
                  receivedStart: optArgs?.receivedStart)),
        );
      case '/salesReturnReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: SalesReturnReport()));
      case '/purchaseReport':
        PurchaseReport? optArgs;
        if (args != null) {
          optArgs = args as PurchaseReport;
        }
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: PurchaseReport(
                    receivedEnd: optArgs?.receivedEnd,
                    receivedStart: optArgs?.receivedStart)));
      case '/purchaseReturnReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: PurchaseReturnReport()));
      case '/allTransactionReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: const AllTransactionReport()));
      case '/dayBookReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: DayBookReport()));
      case '/stockSummaryReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: StockSummaryReport()));
      case '/stockDetailReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: StockDetailReport()));
      case '/lowStockReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: LowStockReport()));
      case '/accountBalanceConfirmation':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: AccountBalanceConfirmation()));
      case '/expenseTransactionReport':
        ExpenseTransactionReport? optArgs;
        if (args != null) {
          optArgs = args as ExpenseTransactionReport;
        }
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: ExpenseTransactionReport(
                    receivedEnd: optArgs?.receivedEnd,
                    receivedStart: optArgs?.receivedStart)));

      case '/expenseCategorySummaryReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: ExpenseCategorySummaryReport()));

      case '/vatSalesRegisterReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: VATSalesRegisterReport()));
      case '/vatPurchaseRegisterReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: VATPurchaseRegisterReport()));

      case '/vatExpenseRegisterReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: VATExpenseRegisterReport()));

      case '/partyStatementReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: const PartyStatementReport()));
      case '/allPartyReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: const AllPartyReportPage()));

      case '/discountReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: DiscountReport()));
      case '/bankStatementReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: BankStatementReport()));
      case '/cashStatementReport':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: CashStatementReport()));

      case '/annex13Report':
        return MaterialPageRoute(
            builder: (_) => PermissionWrapper(
                requiredPermission: PermissionManager.reportView,
                child: const AnnexReport()));

      //====================================================================================  Cheque Module
      case '/cheque':
        return MaterialPageRoute(builder: (_) {
          return PermissionWrapper(
              requiredPermission: PermissionManager.chequeListView,
              child: ChequeListPage());
        });
      case '/chequeDeposit':
        ChequeDepositPage? optArgs;
        if (args != null) {
          optArgs = args as ChequeDepositPage;
        }
        return MaterialPageRoute(builder: (_) {
          return PermissionWrapper(
              requiredPermission: PermissionManager.chequeDepositWithdraw,
              child: ChequeDepositPage(
                chequeTxnId: optArgs?.chequeTxnId ?? "",
              ));
        });

      //====================================================================================  Bank Module
      case '/banks':
        return MaterialPageRoute(builder: (_) {
          return PermissionWrapper(
              requiredPermission: PermissionManager.bankListView,
              child: BankAccountListPage());
        });
      case '/addEditBank':
        AddEditBankAccountPage? optArgs;
        if (args != null) {
          optArgs = args as AddEditBankAccountPage;
        }
        return MaterialPageRoute(builder: (_) {
          return PermissionWrapper(
              requiredPermission: (null != optArgs?.bankId)
                  ? PermissionManager.bankEdit
                  : PermissionManager.bankAdd,
              child: AddEditBankAccountPage(
                bankId: optArgs?.bankId,
              ));
        });
      case '/bankAccountDetail':
        BankAccountDetailPage? optArgs;
        if (args != null) {
          optArgs = args as BankAccountDetailPage;
        }
        return MaterialPageRoute(builder: (_) {
          return PermissionWrapper(
              requiredPermission: PermissionManager.bankAdd,
              child: BankAccountDetailPage(
                bankId: optArgs?.bankId,
              ));
        });
      case '/bankDeposit':
        return MaterialPageRoute(builder: (context) {
          AddEditBankDepositPage? optArgs;
          if (args != null) {
            optArgs = args as AddEditBankDepositPage;
          }
          return PermissionWrapper(
            requiredPermission: (null != optArgs?.adjustmentId)
                ? PermissionManager.bankDepositEdit
                : PermissionManager.bankDepositAdd,
            child: AddEditBankDepositPage(
              adjustmentId: optArgs?.adjustmentId,
              bankID: optArgs?.bankID,
            ),
          );
        });
      case '/bankWithdrawal':
        return MaterialPageRoute(builder: (context) {
          AddEditBankWithdrawalPage? optArgs;
          if (args != null) {
            optArgs = args as AddEditBankWithdrawalPage;
          }
          return PermissionWrapper(
            requiredPermission: (null != optArgs?.adjustmentId)
                ? PermissionManager.bankWithdrawEdit
                : PermissionManager.bankWithdrawAdd,
            child: AddEditBankWithdrawalPage(
              adjustmentId: optArgs?.adjustmentId,
              bankID: optArgs?.bankID,
            ),
          );
        });
      case '/bankToBankTransfer':
        return MaterialPageRoute(builder: (context) {
          AddEditBankToBankTransferPage? optArgs;
          if (args != null) {
            optArgs = args as AddEditBankToBankTransferPage;
          }
          return PermissionWrapper(
            requiredPermission: (null != optArgs?.adjustmentId)
                ? PermissionManager.bankToBankTransferEdit
                : PermissionManager.bankToBankTransferAdd,
            child: AddEditBankToBankTransferPage(
              adjustmentId: optArgs?.adjustmentId,
              bankID: optArgs?.bankID,
            ),
          );
        });
      case '/bankAdjustment':
        return MaterialPageRoute(builder: (context) {
          AddEditBankAdjustmentPage? optArgs;
          if (args != null) {
            optArgs = args as AddEditBankAdjustmentPage;
          }
          return PermissionWrapper(
            requiredPermission: (null != optArgs?.adjustmentId)
                ? PermissionManager.bankAdjustmentEdit
                : PermissionManager.bankAdjustmentAdd,
            child: AddEditBankAdjustmentPage(
              adjustmentId: optArgs?.adjustmentId,
              bankID: optArgs?.bankID,
            ),
          );
        });

      //====================================================================================  Reminders Module
      case '/addEditReminder':
        return MaterialPageRoute(builder: (_) {
          AddEditReminderPage? optArgs;
          if (args != null) {
            optArgs = args as AddEditReminderPage;
          }
          return PermissionWrapper(
              requiredPermission: (null != optArgs?.reminderId)
                  ? PermissionManager.reminderEdit
                  : PermissionManager.reminderAdd,
              child: AddEditReminderPage(
                reminderId: optArgs?.reminderId,
                reaOnlyFlag: optArgs?.reaOnlyFlag,
                reminderType: optArgs?.reminderType,
                reminderDesc: optArgs?.reminderDesc,
                ledgerId: optArgs?.ledgerId,
              ));
        });
      case '/reminderList':
        return MaterialPageRoute(builder: (_) => const ReminderListPage());

      case '/notifications':
        return MaterialPageRoute(builder: (_) => NotificationPage());

      case '/aboutUs':
        return MaterialPageRoute(
            builder: (_) => const GeneralWebViewPage(
                  pageTitle: "About Us",
                  pageURL: ABOUT_US_LINK,
                ));

      //Download Backup
      case '/backupDownload':
        return MaterialPageRoute(builder: (context) {
          return const BackupDownloadPage();
        });

      default:
        // If there is no such named route in the switch statement, e.g. /third
        return _errorRoute();
    }
  }

  static Route<dynamic> _errorRoute() {
    return MaterialPageRoute(builder: (_) {
      return SafeArea(
          child: Scaffold(
        appBar: AppBar(
          title: const Text('Error'),
        ),
        body: const Center(
          child: Text('Page Not Found.'),
        ),
      ));
    });
  }
}
