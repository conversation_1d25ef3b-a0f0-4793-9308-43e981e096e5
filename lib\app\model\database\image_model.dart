import 'dart:convert';

class ImageModel {
  ImageModel({
    this.imageId,
    this.imageBitmap,
    this.imageExt,
    this.lastActivityType,
    this.lastActivityAt,
    this.lastActivityBy,
  });

  String? imageId;
  List<int>? imageBitmap;
  String? imageExt;

  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;

  factory ImageModel.fromJson(Map<String, dynamic> json) {
    return ImageModel(
      imageId: json["image_id"],
      imageBitmap: json['image_bitmap'] != null
          ? List<int>.from(json['image_bitmap'])
          : null,
      imageExt: json["image_ext"],
      lastActivityType: json["last_activity_type"],
      lastActivityAt: json["last_activity_at"],
      lastActivityBy: json['last_activity_by'],
    );
  }

  Map<String, dynamic> toJson() => {
        "image_id": imageId,
        "image_bitmap": jsonEncode(imageBitmap),
        "image_ext": imageExt,
        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        "last_activity_by": lastActivityBy
      };
}
