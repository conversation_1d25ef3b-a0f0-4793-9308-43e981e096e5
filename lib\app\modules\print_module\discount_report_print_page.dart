import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/controllers/registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/registration_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/report/discount_summary_model.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:printing/printing.dart';

class DiscountReportPrintPage extends StatelessWidget {
  final String? pageTitle;
  final List<DiscountSummaryReportModel>? transactions;
  final String? endDate;
  final String? startDate;

  const DiscountReportPrintPage(
      {super.key,
      this.pageTitle = "Print Preview",
      this.transactions,
      this.startDate,
      this.endDate});

  @override
  Widget build(BuildContext context) {
    String startDate =
        NepaliDateTime.parse(toDateBS(DateTime.parse(this.startDate ?? "")))
            .format("y-MM-dd");
    String endDate =
        NepaliDateTime.parse(toDateBS(DateTime.parse(this.endDate ?? "")))
            .format("y-MM-dd");

    return SafeArea(
        child: Scaffold(
            // resizeToAvoidBottomPadding: true,
            resizeToAvoidBottomInset: true,
            appBar: AppBar(
              toolbarHeight: 60,
              elevation: 4,
              leading: BackButton(
                onPressed: () => Navigator.pop(context, false),
              ),
              centerTitle: false,
              backgroundColor: colorPrimary,
              titleSpacing: -5.0,
              title: Text(
                pageTitle ?? "",
                style: const TextStyle(
                    fontSize: 18,
                    color: Colors.white,
                    fontFamily: 'HelveticaRegular',
                    fontWeight: FontWeight.bold),
              ),
              actions: [
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(10)),
                        backgroundColor: colorPrimary,
                        foregroundColor: colorPrimaryLightest,
                      ),
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      child: Column(
                        children: const [
                          Icon(
                            Icons.close,
                            color: Colors.white,
                          ),
                          Text(
                            "Cancel",
                            style: TextStyle(color: Colors.white, fontSize: 10),
                          ),
                        ],
                      )),
                ),
              ],
            ),
            body: PdfPreview(
                useActions: true,
                canChangePageFormat: false,
                // allowPrinting: false,
                // allowSharing: false,
                initialPageFormat: defaultPdfPageFormat,
                maxPageWidth: 700,
                // actions: actions,
                pdfFileName: "$pageTitle From $startDate to $endDate .pdf",
                build: (format) {
                  return generateReport(defaultPdfPageFormat,
                      transactions: transactions!,
                      billTitle: pageTitle ?? "",
                      startDate: this.startDate ?? "",
                      endDate: this.endDate ?? "");
                })));
  }
}

Future<Uint8List> generateReport(PdfPageFormat pageFormat,
    {List<DiscountSummaryReportModel>? transactions,
    String billTitle = "",
    String? startDate,
    String? endDate}) async {
  double total = 0.0;
  double totalSalesDiscount = 0.0;
  double totalPurchaseDiscount = 0.0;

  final products = <Product>[
    ...transactions!.map((txn) {
      totalSalesDiscount += txn.saleDiscountAmount!;
      totalPurchaseDiscount += txn.purchaseDiscountAmount!;

      return Product(txn.ledgerTitle ?? "", txn.saleDiscountAmount ?? 0.0,
          txn.purchaseDiscountAmount ?? 0.0);
    }).toList()
  ];

  RegistrationDetailController registrationDetailController =
      Get.find<RegistrationDetailController>();
  ImageModel sellerImageModel = registrationDetailController.logo;

  RegistrationDetailModel myDetail =
      registrationDetailController.registrationDetail;

  final invoice = Invoice(
    myDetail: myDetail,
    sellerImage: sellerImageModel,
    startDate: NepaliDateTime.parse(toDateBS(DateTime.parse(startDate ?? "")))
        .format("y/MM/dd"),
    endDate: NepaliDateTime.parse(toDateBS(DateTime.parse(endDate ?? "")))
        .format("y/MM/dd"),
    products: products,
    totalAmount: total,
    totalPurchaseDiscount: totalPurchaseDiscount,
    totalSalesDiscountAmount: totalSalesDiscount,
    reportTitle: billTitle,
    baseColor: PdfColors.lightBlue,
    accentColor: PdfColors.blueGrey900,
  );

  return await invoice.buildPdf(pageFormat);
}

class Invoice {
  Invoice(
      {this.myDetail,
      this.sellerImage,
      this.products,
      this.baseColor,
      this.accentColor,
      this.startDate,
      this.totalAmount,
      this.totalPurchaseDiscount,
      this.totalSalesDiscountAmount,
      this.endDate,
      this.partyName,
      this.txnType,
      this.reportTitle});

  final RegistrationDetailModel? myDetail;
  final ImageModel? sellerImage;

  final List<Product>? products;
  final PdfColor? baseColor;
  final PdfColor? accentColor;
  final String? startDate;
  final String? endDate;
  final String? partyName;
  final String? txnType;
  final String? reportTitle;

  final double? totalAmount;
  final double? totalPurchaseDiscount;
  final double? totalSalesDiscountAmount;

  static const _darkColor = PdfColors.blueGrey800;
  static const _lightColor = PdfColors.black;
  // ignore: constant_identifier_names
  static const _VedColor = PdfColor.fromInt(0xFF3560AF);
  PdfColor get _baseTextColor =>
      baseColor!.luminance < 0.5 ? _lightColor : _darkColor;

  var currencyInWords = NepaliNumberFormat(
    inWords: true,
    language: Language.english,
    isMonetory: true,
    decimalDigits: 2,
  );

  String? _logo;

  Future<Uint8List> buildPdf(PdfPageFormat pageFormat) async {
    // Create a PDF document.
    final doc = pw.Document();

    final font1 = await rootBundle.load('assets/roboto1.ttf');
    final font2 = await rootBundle.load('assets/roboto1.ttf');
    final font3 = await rootBundle.load('assets/roboto3.ttf');

    _logo = await rootBundle.loadString('assets/mobilekhata.svg');

    // Add page to the PDF
    doc.addPage(
      pw.MultiPage(
        // pageTheme: _buildTheme(
        //   pageFormat,
        //   pw.Font.ttf(font1),
        //   pw.Font.ttf(font2),
        //   pw.Font.ttf(font3),
        // ),
        header: _buildHeader,
        footer: _buildFooter,
        build: (context) => [
          _header(context),
          _newHeader(context),
          _contentTable(context),
          _tableTotal(context),
          pw.SizedBox(height: 20),
          // _contentFooter(context),
          pw.SizedBox(height: 20),
        ],
      ),
    );

    // Return the PDF file content
    return doc.save();
  }

  pw.Widget _tableTotal(pw.Context context) {
    return pw.Container(
      padding: const pw.EdgeInsets.only(top: 10),
      child: pw.Column(
        children: [
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.end,
            children: [
              pw.Text("Total Sales Discount: Rs. $totalSalesDiscountAmount")
            ],
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.end,
            children: [
              pw.Text("Total Purchase Discount : Rs. $totalPurchaseDiscount")
            ],
          ),
          // pw.SizedBox(height: 10),
          // pw.Row(
          //     mainAxisAlignment: pw.MainAxisAlignment.end,
          //     children: [pw.Text("Total $txnType : Rs. ${totalAmount}")]),
        ],
      ),
    );
  }

  pw.Widget _buildHeader(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Expanded(
              child: pw.Column(
                mainAxisSize: pw.MainAxisSize.min,
                children: [
                  if (null != sellerImage?.imageBitmap) ...{
                    pw.Row(
                      mainAxisAlignment: pw.MainAxisAlignment.center,
                      children: [
                        pw.Container(
                          margin: const pw.EdgeInsets.only(top: -20),
                          alignment: pw.Alignment.center,
                          height: 60,
                          width: 60,
                          child: sellerImage?.imageBitmap != null
                              ? pw.Image(
                                  pw.MemoryImage(
                                    Uint8List.fromList(
                                      sellerImage!.imageBitmap!,
                                    ),
                                  ),
                                )
                              : pw.PdfLogo(),
                        ),
                      ],
                    )
                  },
                  // pw.Container(
                  //   color: baseColor,
                  //   padding: pw.EdgeInsets.only(top: 3),
                  // ),
                ],
              ),
            ),
          ],
        ),
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Expanded(
              child: pw.Column(
                mainAxisSize: pw.MainAxisSize.min,
                children: [
                  pw.Container(
                    margin: const pw.EdgeInsets.only(top: 10),
                    alignment: pw.Alignment.center,
                    child: pw.Text(
                      myDetail?.businessName ?? "",
                      style: pw.TextStyle(
                        color: _VedColor,
                        fontWeight: pw.FontWeight.bold,
                        fontSize: 16,
                      ),
                    ),
                  ),
                  pw.Container(
                    margin: const pw.EdgeInsets.only(),
                    child: pw.Text(
                      myDetail?.businessAddress ?? "",
                      style: pw.TextStyle(
                        color: accentColor,
                        fontSize: 8,
                      ),
                    ),
                  ),
                  if (myDetail != null &&
                      null != myDetail!.tinNo &&
                      "" != myDetail!.tinNo)
                    pw.Container(
                      margin: const pw.EdgeInsets.only(),
                      child: pw.Text(
                        "${myDetail?.tinFlag} No. :${myDetail?.tinNo}",
                        style: pw.TextStyle(
                          color: accentColor,
                          fontSize: 8,
                        ),
                      ),
                    ),
                ],
              ),
            )
          ],
        ),
        if (context.pageNumber > 1) pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _header(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Expanded(
              child: pw.Column(
                children: [
                  pw.Container(
                    margin: const pw.EdgeInsets.only(bottom: 20, top: 10),
                    alignment: pw.Alignment.center,
                    child: pw.Text(
                      reportTitle ?? "",
                      style: pw.TextStyle(
                        color: PdfColors.red,
                        fontSize: 15,
                        fontWeight: pw.FontWeight.bold,
                      ),
                    ),
                  )
                ],
              ),
            ),
          ],
        ),
        pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _buildFooter(pw.Context context) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: [
        // pw.Container(
        //   height: 20,
        //   width: 100,
        //   child: pw.BarcodeWidget(
        //     barcode: pw.Barcode.pdf417(),
        //     data: 'Invoice# $invoiceNumber',
        //   ),
        // ),
        pw.Text(
          'Page ${context.pageNumber}/${context.pagesCount}',
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
        pw.Text(
          FOOTER_PRINT_TEXT,
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
      ],
    );
  }

  // pw.PageTheme _buildTheme(
  //     PdfPageFormat pageFormat, pw.Font base, pw.Font bold, pw.Font italic) {
  //   return pw.PageTheme(
  //     pageFormat: pageFormat,
  //     theme: pw.ThemeData.withFont(
  //       base: base,
  //       bold: bold,
  //       italic: italic,
  //     ),
  //     buildBackground: (context) => pw.FullPage(
  //       ignoreMargins: true,
  //     ),
  //   );
  // }

  pw.Widget _newHeader(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // pw.Container(
              //   height: 15,
              //   child: pw.Text(
              //     'Party Name:\r $partyName',
              //     style: pw.TextStyle(
              //       color: PdfColors.black,
              //       lineSpacing: 10,
              //       fontSize: 10,
              //       fontWeight: pw.FontWeight.bold,
              //     ),
              //   ),
              // ),
              // pw.Container(
              //   height: 15,
              //   child: pw.Text(
              //     'Transaction type:\r $txnType',
              //     style: pw.TextStyle(
              //       color: PdfColors.black,
              //       lineSpacing: 5,
              //       fontSize: 10,
              //       fontWeight: pw.FontWeight.bold,
              //     ),
              //   ),
              // ),

              pw.Container(
                height: 20,
                child: pw.Text(
                  'Duration:\rFrom\r$startDate\rTo\r$endDate',
                  style: pw.TextStyle(
                    color: PdfColors.black,
                    lineSpacing: 5,
                    fontSize: 10,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  pw.Widget _contentTable(pw.Context context) {
    const tableHeaders = [
      'Party Name',
      'Sale Discount',
      'Purchase Discount',
    ];

    return pw.Table.fromTextArray(
      border: null,
      cellAlignment: pw.Alignment.centerLeft,
      headerDecoration: const pw.BoxDecoration(
        color: PdfColors.blue100,
      ),
      headerHeight: 24,
      cellHeight: 24,
      cellAlignments: {
        0: pw.Alignment.centerLeft,
        1: pw.Alignment.centerRight,
        2: pw.Alignment.centerRight,
      },
      headerStyle: pw.TextStyle(
        color: _baseTextColor,
        fontSize: 9.5,
        fontWeight: pw.FontWeight.bold,
      ),
      cellStyle: pw.TextStyle(
        color: accentColor,
        fontSize: 8,
      ),
      rowDecoration: pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: accentColor!,
            width: .5,
          ),
        ),
      ),
      headers: List<String>.generate(
        tableHeaders.length,
        (col) => tableHeaders[col],
      ),
      data: List<List<String>>.generate(
        products!.length,
        (row) => List<String>.generate(
          tableHeaders.length,
          (col) => products![row].getIndex(col),
        ),
      ),
    );
  }
}

String _formatDate(DateTime date) {
  final format = DateFormat.yMMMd('en_US');
  return format.format(date);
}

class Product {
  const Product(
      this.ledgerTitle, this.saleDiscountAmount, this.purchaseDiscountAmount);
  final String ledgerTitle;
  final double saleDiscountAmount;
  final double purchaseDiscountAmount;

  String getIndex(int index) {
    switch (index) {
      case 0:
        return ledgerTitle;
      case 1:
        return saleDiscountAmount.toString();
      case 2:
        return purchaseDiscountAmount.toString();
      // case 7:
      //   return balanceAmount.toString();
    }
    return '';
  }
}
