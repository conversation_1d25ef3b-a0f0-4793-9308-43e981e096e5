import 'package:flutter/material.dart';
import 'package:get/get_rx/src/rx_typedefs/rx_typedefs.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

// ignore: must_be_immutable
class BottomSaveCancelButton extends StatelessWidget {
  bool enableFlag;
  Callback? onSaveBtnPressedFn;
  Callback? onSaveAndNewBtnPressedFn;

  Callback? onDeleteBtnPressedFn;

  bool shadow;
  bool hasDelete;
  bool enableDelete;
  String saveText;
  bool hasNew;
  String saveAndNewText;

  BottomSaveCancelButton(
      {super.key,
      this.enableFlag = true,
      this.onSaveBtnPressedFn,
      this.onDeleteBtnPressedFn,
      this.onSaveAndNewBtnPressedFn,
      this.enableDelete = true,
      this.shadow = true,
      this.hasNew = false,
      this.saveAndNewText = "Save and New",
      this.saveText = "Save",
      this.hasDelete = false});

  @override
  Widget build(BuildContext context) {
    return Container(
        decoration: BoxDecoration(
          color: Colors.green,
          boxShadow: (!shadow)
              ? null
              : [
                  BoxShadow(
                      color: Colors.black87.withOpacity(0.5),
                      spreadRadius: -2,
                      offset: const Offset(0, -4),
                      blurRadius: 5),
                ],
        ),
        width: double.infinity,
        child: Row(
          mainAxisSize: MainAxisSize.max,
          children: [
            hasNew
                ? Expanded(
                    flex: 1,
                    child: Container(
                      height: 45,
                      color: (enableFlag) ? colorPrimaryDark : Colors.black54,
                      child: ElevatedButton(
                        style: ElevatedButton.styleFrom(
                            backgroundColor: colorPrimaryDark),
                        onPressed: enableFlag ? onSaveAndNewBtnPressedFn : null,
                        child: Text(saveAndNewText,
                            style: TextStyle(
                              color:
                                  (enableFlag) ? Colors.white : Colors.white24,
                              fontSize: 18,
                            )),
                      ),
                    ),
                  )
                : hasDelete
                    ? Expanded(
                        flex: 1,
                        child: Container(
                          height: 45,
                          color: Color.fromRGBO(0, 0, 0, 0.122),
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: colorRedLight,
                            ),
                            onPressed:
                                (enableDelete) ? onDeleteBtnPressedFn : null,
                            child: const Text(
                              "Delete",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                              ),
                            ),
                          ),
                        ),
                      )
                    : Expanded(
                        flex: 1,
                        child: Container(
                          height: 45,
                          color: Colors.black12,
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.white,
                            ),
                            child: Text(
                              "Cancel",
                              style: TextStyle(
                                color: colorPrimary,
                                fontSize: 18,
                              ),
                            ),
                            onPressed: () {
                              Navigator.pop(context, null);
                            },
                          ),
                        ),
                      ),
            Expanded(
              flex: 1,
              child: Container(
                height: 45,
                color: (enableFlag) ? colorPrimary : Colors.black54,
                child: ElevatedButton(
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.transparent,
                  ),
                  onPressed: (enableFlag) ? onSaveBtnPressedFn : null,
                  child: Text(
                    saveText,
                    style: TextStyle(
                      color: (enableFlag) ? Colors.white : Colors.white24,
                      fontSize: 18,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ));
  }
}
