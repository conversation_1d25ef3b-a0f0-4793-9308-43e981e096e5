import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/model/database/registration_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/image_repository.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:sqflite/sqflite.dart';
import 'package:tuple/tuple.dart';

class RegistrationDetailRepository {
  final String tag = "RegistrationDetailRepository";
  final String tableName = "mk_registration_detail";

  DatabaseHelper databaseHelper = DatabaseHelper();
  QueryRepository queryRepository = QueryRepository();

//========================================================================================= SYNCING ACTIONS
  Future<bool> update(RegistrationDetailModel registrationDetail,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    registrationDetail.lastActivityAt = currentDateTime;
    registrationDetail.lastActivityType = LastActivityType.Edit;
    registrationDetail.lastActivityBy = await getLastActivityBy();

    String whereClause = "user_id = ?";
    List<dynamic> whereArgs = [registrationDetail.userId];

    await dbClient.update(tableName, registrationDetail.toJson(),
        where: whereClause, whereArgs: whereArgs);

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.update,
      data: registrationDetail.toJson(),
      whereArgs: whereArgs,
      whereClause: whereClause,
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    status = true;
    pushPendingQueries(
        source: "TRIGGER", dbClient: dbClient, singleBatchId: batchID);
    return status;
  }

  //=========================================================================================NON SYNCING ACTIONS
  Future<RegistrationDetailModel> getRegistrationDetail() async {
    RegistrationDetailModel registrationDetail = RegistrationDetailModel();
    try {
      Database? dbClient = await databaseHelper.database;

      Map<String, dynamic> firmData = (await dbClient!
              .rawQuery('SELECT rd.* FROM mk_registration_detail rd limit 1'))
          .first;
      registrationDetail = RegistrationDetailModel.fromJson(firmData);
    } catch (e) {
      // Log.e(tag, e.toString());
    }

    return registrationDetail;
  }

  Future<Tuple2<RegistrationDetailModel, ImageModel>>
      getRegistrationDetailWithImage() async {
    RegistrationDetailModel registrationDetail = RegistrationDetailModel();
    ImageModel imageModel = ImageModel();
    try {
      registrationDetail = await getRegistrationDetail();

      // Log.d(registrationDetail.toJson());

      if (null != registrationDetail.logoId) {
        ImageRepository imageRepository = ImageRepository();
        imageModel =
            await imageRepository.getImageById(registrationDetail.logoId!);
      }
    } catch (e) {
      // Log.e(tag, e.toString());
    }

    return Tuple2(registrationDetail, imageModel);
  }
}
