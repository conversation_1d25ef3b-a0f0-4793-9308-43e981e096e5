PODS:
  - connectivity_plus (0.0.1):
    - FlutterMacOS
    - ReachabilitySwift
  - device_info_plus_macos (0.0.1):
    - FlutterMacOS
  - flutter_archive (0.0.1):
    - FlutterMacOS
    - ZIPFoundation (= 0.9.13)
  - flutter_local_notifications (0.0.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - in_app_review (0.2.0):
    - FlutterMacOS
  - package_info (0.0.1):
    - FlutterMacOS
  - path_provider_macos (0.0.1):
    - FlutterMacOS
  - printing (1.0.0):
    - FlutterMacOS
  - ReachabilitySwift (5.0.0)
  - shared_preferences_macos (0.0.1):
    - FlutterMacOS
  - sqflite (0.0.2):
    - FlutterMacOS
    - FMDB (>= 2.7.5)
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - ZIPFoundation (0.9.13)

DEPENDENCIES:
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - device_info_plus_macos (from `Flutter/ephemeral/.symlinks/plugins/device_info_plus_macos/macos`)
  - flutter_archive (from `Flutter/ephemeral/.symlinks/plugins/flutter_archive/macos`)
  - flutter_local_notifications (from `Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - in_app_review (from `Flutter/ephemeral/.symlinks/plugins/in_app_review/macos`)
  - package_info (from `Flutter/ephemeral/.symlinks/plugins/package_info/macos`)
  - path_provider_macos (from `Flutter/ephemeral/.symlinks/plugins/path_provider_macos/macos`)
  - printing (from `Flutter/ephemeral/.symlinks/plugins/printing/macos`)
  - shared_preferences_macos (from `Flutter/ephemeral/.symlinks/plugins/shared_preferences_macos/macos`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/macos`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)

SPEC REPOS:
  trunk:
    - FMDB
    - ReachabilitySwift
    - ZIPFoundation

EXTERNAL SOURCES:
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  device_info_plus_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/device_info_plus_macos/macos
  flutter_archive:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_archive/macos
  flutter_local_notifications:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_local_notifications/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  in_app_review:
    :path: Flutter/ephemeral/.symlinks/plugins/in_app_review/macos
  package_info:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info/macos
  path_provider_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_macos/macos
  printing:
    :path: Flutter/ephemeral/.symlinks/plugins/printing/macos
  shared_preferences_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/shared_preferences_macos/macos
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/macos
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos

SPEC CHECKSUMS:
  connectivity_plus: 18d3c32514c886e046de60e9c13895109866c747
  device_info_plus_macos: 1ad388a1ef433505c4038e7dd9605aadd1e2e9c7
  flutter_archive: 1a28d55ba9013cf5f4513ae1bb6af118f787df96
  flutter_local_notifications: 3805ca215b2fb7f397d78b66db91f6a747af52e4
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  in_app_review: a850789fad746e89bce03d4aeee8078b45a53fd0
  package_info: 6eba2fd8d3371dda2d85c8db6fe97488f24b74b2
  path_provider_macos: 3c0c3b4b0d4a76d2bf989a913c2de869c5641a19
  printing: e4b7e232ff3d5d50a70b20d959aa71035a5c31f9
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  shared_preferences_macos: 8b221d457159a85f478c0b9d2f19aeae9feff475
  sqflite: a5789cceda41d54d23f31d6de539d65bb14100ea
  url_launcher_macos: 597e05b8e514239626bcf4a850fcf9ef5c856ec3
  ZIPFoundation: ae5b4b813d216d3bf0a148773267fff14bd51d37

PODFILE CHECKSUM: 353c8bcc5d5b0994e508d035b5431cfe18c1dea7

COCOAPODS: 1.11.3
