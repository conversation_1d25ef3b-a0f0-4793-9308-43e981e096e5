// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/modules/backup_module/backup_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

class BackupPage extends StatelessWidget {
  final controller = BackupController();

  BackupPage({super.key}) {
    controller.getBackupInfo();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      } else {
        return SafeArea(
            child: Scaffold(
          // resizeToAvoidBottomPadding: true,
          resizeToAvoidBottomInset: true,
          appBar: AppBar(
            titleSpacing: -5.0,
            centerTitle: true,
            elevation: 0,
            title: const Text(
              "ब्याकअप जानकारी\nBackup Information",
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 18,
                  color: Colors.white,
                  fontFamily: 'HelveticaRegular',
                  fontWeight: FontWeight.bold),
            ),
          ),
          body: Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  FontAwesomeIcons.solidHardDrive,
                  color: colorPrimary,
                  size: 80,
                ),
                const SizedBox(
                  height: 20,
                ),
                Text(
                  "तपाईंको डाटा स्वत: ब्याक अप हुन्छ जब फोन इन्टरनेटमा जडित हुन्छ। यदि तपाईंले यस फोनलाई फर्म्याट गर्नुभयो वा गल्तीले एप्लिकेसन मेटाउनुभयो भने मात्र एप्लिकेसन डाउनलोड गर्नुहोस् र लगइन आईडी प्रयोग गरेर लगइन गर्नुहोस्। तपाईंको डाटा स्वचालित रूपमा डाउनलोड हुनेछ। ",
                  style: TextStyle(color: textColor, fontSize: 14),
                  textAlign: TextAlign.justify,
                ),
                Text(
                  "\n(Your data is automatically backed-up whenever the phone is connected to the internet."
                  " In case you format this phone or mistakenly deleted the app,"
                  " just the download the app again and login using your login Id."
                  " Your data will be automatically restored.)",
                  style: TextStyle(color: textColor, fontSize: 14),
                  textAlign: TextAlign.justify,
                ),

                // Divider(thickness: 0.5, color: colorPrimary, height: 20,),
                const SizedBox(
                  height: 40,
                ),
                Text(
                  "Last Backup at : ${"" != controller.lastSyncedAt ? controller.lastSyncedAt : "N/A"}",
                  style: labelStyle2.copyWith(
                      color: textColor, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(
                  height: 30,
                ),
                Center(
                  child: ElevatedButton(
                      // color: colorPrimary,
                      // elevation: 10,
                      // shape: RoundedRectangleBorder(
                      //   borderRadius: BorderRadius.circular(10.0),
                      // ),
                      // splashColor: colorPrimaryLightest,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(
                            vertical: 10, horizontal: 10),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          mainAxisSize: MainAxisSize.min,
                          children: const [
                            Icon(
                              Icons.cloud_upload_sharp,
                              color: Colors.white,
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Text(
                              "ब्याकअप (Backup)",
                              style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 15,
                                  fontWeight: FontWeight.bold),
                            ),
                          ],
                        ),
                      ),
                      onPressed: () async {
                        ProgressDialog progressDialog = ProgressDialog(context,
                            type: ProgressDialogType.normal,
                            isDismissible: false);
                        progressDialog.update(
                            message: "Syncing data. Please wait....");
                        await progressDialog.show();

                        Tuple2<bool, String> combinedResp =
                            await controller.syncAllDataWithMessage();

                        // bool status = await controller.syncAllData();
                        await progressDialog.hide();

                        showAlertDialog(context,
                            alertType: combinedResp.item1
                                ? AlertType.Success
                                : AlertType.Error,
                            alertTitle:
                                "ब्याकअप जानकारी \n(Backup Information)",
                            message: combinedResp.item2);
                      }),
                ),
              ],
            ),
          ),
        ));
      }
    });
  }
}
