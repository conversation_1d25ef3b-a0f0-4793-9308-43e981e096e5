// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';

import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/model/database/unit_modal.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/add_edit_unit/add_edit_unit_controller.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

Future<UnitModel?> displayAddEditUnitDialog(BuildContext context,
    {required String unitId}) async {
  UnitModel? returnedData = await showDialog(
      context: context,
      useRootNavigator: true,
      barrierDismissible: false,
      builder: (_) {
        return AlertDialog(
          insetPadding:
              const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          contentPadding: EdgeInsets.zero,
          clipBehavior: Clip.hardEdge,
          content: SizedBox(
            width: MediaQuery.of(context).size.width - 20,
            child: AddEditUnitDialog(
              unitId: unitId,
            ),
          ),
        );
      });
  return returnedData;
}

class AddEditUnitDialog extends StatelessWidget {
  final String tag = "AddEditUnitDialog";
  final String? unitId;

  final addEditUnitController = AddEditUnitController();

  AddEditUnitDialog({super.key, this.unitId}) {
    if (null != unitId) {
      addEditUnitController.initEdit(unitId ?? "");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
              padding: const EdgeInsets.only(left: 15, right: 15, top: 15),
              child: Container(
                padding: const EdgeInsets.only(bottom: 10),
                child: Text(
                  (addEditUnitController.editFlag)
                      ? "एकाइ (Edit Unit)"
                      : "नयाँ एकाइ (New Unit)",
                  style: TextStyle(
                      color: textColor,
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                ),
              )),
          if (addEditUnitController.isLoading) ...{
            Container(
                color: Colors.white,
                child: const Center(child: CircularProgressIndicator()))
          } else ...{
            GestureDetector(
              onTap: () {
                FocusScope.of(context).unfocus();
              },
              child: Form(
                key: addEditUnitController.formKey,
                child: SingleChildScrollView(
                  child: Container(
                    margin: const EdgeInsets.only(top: 10),
                    padding: const EdgeInsets.only(
                      left: 15,
                      right: 15,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const SizedBox(height: 5.0),

                        //===============================================Item Name
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "एकाइको नाम",
                              style: labelStyle2,
                            ),
                            const SizedBox(height: 5.0),
                            FormBuilderTextField(
                              name: "unit_name",
                              autocorrect: false,
                              keyboardType: TextInputType.text,
                              textInputAction: TextInputAction.next,
                              style: formFieldTextStyle,
                              decoration: formFieldStyle.copyWith(
                                  labelText: "Unit Name"),
                              initialValue: addEditUnitController.unit.unitName,
                              onChanged: (value) {
                                addEditUnitController.unit.unitName =
                                    strTrim(value ?? "");
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return "एकाइको नाम राख्नुहोस् (Fill Unit Name)";
                                }
                                return null;
                              },
                            ),
                          ],
                        ),

                        const SizedBox(
                          height: 20,
                        ),

                        //===============================================Item Name
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "एकाइको कोड",
                              style: labelStyle2,
                            ),
                            const SizedBox(height: 5.0),
                            FormBuilderTextField(
                              name: "unit_short_name",
                              autocorrect: false,
                              keyboardType: TextInputType.text,
                              textInputAction: TextInputAction.done,
                              style: formFieldTextStyle,
                              decoration: formFieldStyle.copyWith(
                                  labelText: "Unit Short code"),
                              initialValue:
                                  addEditUnitController.unit.unitShortName,
                              onChanged: (value) {
                                addEditUnitController.unit.unitShortName =
                                    strTrim(value ?? "");
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return "एकाइको कोड राख्नुहोस् (Fill Unit Short Code)";
                                }
                                return null;
                              },
                            ),
                          ],
                        ),

                        const SizedBox(height: 25.0),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          },
          BottomSaveCancelButton(
            shadow: false,
            onSaveBtnPressedFn: () async {
              FocusScope.of(context).unfocus();
              if (addEditUnitController.formKey.currentState!.validate()) {
                bool isUnique = await DatabaseHelper.isUnique(
                  tableName: "mk_item_units",
                  columnName: "unit_name",
                  checkValue:
                      strTrim(addEditUnitController.unit.unitName ?? ""),
                  keyColumn: "unit_id",
                  keyValue: addEditUnitController.unit.unitId,
                );

                if (!isUnique) {
                  showAlertDialog(context, alertType: AlertType.Error,
                      onCloseButtonPressed: () {
                    Navigator.of(context).pop();
                  },
                      alertTitle: "Error",
                      message:
                          "एकाइको नाम पहिल्यै प्रयोगमा छ\n(Unit Name already in use)");
                  return;
                }

                isUnique = await DatabaseHelper.isUnique(
                  tableName: "mk_item_units",
                  columnName: "unit_short_name",
                  checkValue:
                      strTrim(addEditUnitController.unit.unitShortName ?? ""),
                  keyColumn: "unit_id",
                  keyValue: addEditUnitController.unit.unitId,
                );

                if (!isUnique) {
                  showAlertDialog(
                    context,
                    alertType: AlertType.Error,
                    onCloseButtonPressed: () {
                      Navigator.of(context).pop();
                    },
                    alertTitle: "Error",
                    message:
                        "एकाइको कोड पहिल्यै प्रयोगमा छ\n(Unit Short Code already in use)",
                  );
                  return;
                }

                ProgressDialog progressDialog = ProgressDialog(context,
                    type: ProgressDialogType.normal, isDismissible: false);
                progressDialog.update(message: "Saving data. Please wait....");
                await progressDialog.show();

                bool status = false;
                try {
                  if (!addEditUnitController.editFlag) {
                    status = await addEditUnitController.createUnit();
                  } else {
                    status = await addEditUnitController.updateUnit();
                  }
                } catch (e, trace) {
                  // Log.e(tag, e.toString() + trace.toString());
                }
                await progressDialog.hide();

                if (status) {
                  Navigator.pop(context, addEditUnitController.unit);
                  String message = (addEditUnitController.editFlag)
                      ? "Unit Updated Successfully."
                      : "Unit Created Successfully.";
                  showToastMessage(context, message: message, duration: 2);
                } else {
                  showToastMessage(
                    context,
                    alertType: AlertType.Error,
                    message: "Failed to process operation",
                    duration: 2,
                  );
                }
              }
            },
          ),
        ],
      );
    });
  }
}
