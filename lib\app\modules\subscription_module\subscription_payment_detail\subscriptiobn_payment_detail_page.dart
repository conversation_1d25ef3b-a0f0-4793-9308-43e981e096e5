// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/model/others/package_model.dart';
import 'package:mobile_khaata_v2/app/modules/subscription_module/payment_subscription/payment_subscription_view.dart';
import 'package:mobile_khaata_v2/app/modules/subscription_module/subscription_page/subscription_page_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

class SubscriptionPaymentDetailPage extends StatelessWidget {
  final PackageModel? package;

  const SubscriptionPaymentDetailPage({super.key, required this.package});

  onPayBtnClick(BuildContext context, PackageModel package) async {
    var esewaRes = await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (_) => ConstrainedBox(
        constraints: BoxConstraints(
          maxHeight: MediaQuery.of(context).size.height * 0.6,
        ),
        child: PaymentSubscriptionView(
          packageModel: package,
        ),
      ),
    );

    if (esewaRes == true) {
      ProgressDialog progressDialog = ProgressDialog(context,
          type: ProgressDialogType.normal, isDismissible: false);
      progressDialog.update(message: "Checking Expiry. Please wait....");
      await progressDialog.show();
      Tuple2<bool, String> checkResponse =
          await SubscriptionPageController().recheckExpiryFromNetwork();
      await progressDialog.hide();
      if (!checkResponse.item1) {
        showToastMessage(context, message: checkResponse.item2, duration: 2);

        Navigator.of(context).pop();
        Navigator.of(context).pop();
        if (Navigator.of(context).canPop()) {
          Navigator.of(context).pop();
        }
        Navigator.pushNamed(context, '/subscription');
      } else {
        showToastMessage(context,
            alertType: AlertType.Error,
            message: checkResponse.item2,
            duration: 2);
      }
    } else {
      // Log.d("failed");
      // Navigator.of(context).pop();
      // Navigator.of(context).pop(true);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Log.d("${package.toJson()}");

    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: true,
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        centerTitle: false,
        elevation: 0,
        title: const Text(
          "भुक्तानी विवरण (Payment Details)",
          textAlign: TextAlign.left,
          style: TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
      ),
      body: Column(
        children: [
          Container(
            width: double.infinity,
            padding:
                const EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 15),
            color: colorPrimary,
            child: DefaultTextStyle(
              style: const TextStyle(color: Colors.white, fontSize: 16),
              child: Column(
                children: [
                  const Text("Total Amount to be Paid"),
                  const SizedBox(
                    height: 8,
                  ),
                  Text(
                    formatCurrencyAmount(package!.packageTotalAmount!),
                    style: const TextStyle(color: Colors.white, fontSize: 24),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(
            height: 20,
          ),
          Expanded(
            child: DefaultTextStyle(
              textAlign: TextAlign.center,
              style: TextStyle(color: textColor, fontSize: 14),
              child: ListView(
                children: [
                  const Text("Package Name:"),
                  const SizedBox(
                    height: 5,
                  ),
                  Text(
                    package!.title ?? "",
                    style: TextStyle(color: colorPrimary, fontSize: 18),
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                  const Text("Package Price:"),
                  const SizedBox(
                    height: 5,
                  ),
                  Text(
                    formatCurrencyAmount(package!.packagePrice!),
                    style: TextStyle(color: colorPrimary, fontSize: 18),
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                  Text("VAT @ ${package!.vatPercentage}%:"),
                  const SizedBox(
                    height: 5,
                  ),
                  Text(
                    formatCurrencyAmount(package!.vatAmount!),
                    style: TextStyle(color: colorPrimary, fontSize: 18),
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                  const Text("Total:"),
                  const SizedBox(
                    height: 5,
                  ),
                  Text(
                    formatCurrencyAmount(package!.packageTotalAmount!),
                    style: TextStyle(color: colorPrimary, fontSize: 18),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      bottomNavigationBar: Container(
        margin: const EdgeInsets.symmetric(horizontal: 15, vertical: 15),
        width: double.infinity,
        child: ElevatedButton(
          child: const Text(
            "Pay",
            style: TextStyle(color: Colors.white, fontSize: 20),
          ),
          onPressed: () {
            onPayBtnClick(context, package!);
          },
        ),
      ),
    ));
  }
}
