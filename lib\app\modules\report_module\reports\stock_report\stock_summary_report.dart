import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
import 'package:mobile_khaata_v2/app/model/others/item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/stock_summary_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/stock_report/report_stock_transaction_controller.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';

/**
 * STOCK SUMMARY REPORT PAGE
 *
 * This page displays a comprehensive summary of current stock levels and values.
 *
 * FEATURES:
 * - Date-based stock calculation (as of selected date)
 * - Item-wise stock quantities and values
 * - Average purchase rate calculations
 * - Total stock quantity and value summaries
 * - Print functionality for reports
 *
 * CALCULATIONS:
 * - Stock Quantity = In Quantity - Out Quantity + Opening Stock
 * - Stock Value = Average Purchase Rate × Current Stock Quantity
 * - Average Purchase Rate = Weighted average based on purchase history
 */
// ignore: must_be_immutable
class StockSummaryReport extends StatelessWidget {
  final ReportStockTransactionController _controller =
      ReportStockTransactionController();
  String endDate = currentDate;

  StockSummaryReport({super.key}) {
    generate();
  }

  /**
   * GENERATE REPORT DATA
   *
   * Triggers stock summary calculation for the selected date
   */
  generate() {
    _controller.generateStockSummaryReport(endDate: endDate);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        elevation: 0,
        titleSpacing: -5.0,
        backgroundColor: colorPrimary,
        title: const Text(
          "स्टक विवरण रिपोर्ट\n(Stock Summary Report)",
          style: TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
        actions: [
          PrintButton(
            onPressed: () {
              Navigator.of(context).pushNamed('/printStockSummaryReport',
                  arguments: StockSummaryPrintPage(
                    transactions: _controller.itemDetailList,
                    pageTitle: "Stock Summary Report",
                    dateFor: endDate,
                  ));
            },
          )
        ],
      ),
      body: Container(
        color: Colors.black12,
        child: Column(
          children: [
            // DATE FILTER SECTION
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    "As of Date:",
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(width: 10),
                  SizedBox(
                    width: 140,
                    child: ReportCustomDatePickerTextField(
                      initialValue: toDateBS(DateTime.parse(endDate)),
                      hintText: "Select Date",
                      onChange: (selectedDate) {
                        endDate = toDateAD(NepaliDateTime.parse(selectedDate));
                        generate();
                      },
                    ),
                  ),
                ],
              ),
            ),

            const Divider(
              height: 0,
              color: Colors.black54,
            ),

            // COLUMN HEADERS
            DefaultTextStyle(
              style: TextStyle(
                  fontSize: 12, color: textColor, fontWeight: FontWeight.bold),
              child: Container(
                color: Colors.white,
                padding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: const [
                    // Item Name Column
                    Expanded(
                      flex: 2,
                      child: Text(
                        "Item Name",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),

                    // Stock Quantity Column
                    Expanded(
                      flex: 1,
                      child: Text(
                        "Stock Qty",
                        textAlign: TextAlign.center,
                      ),
                    ),

                    // Average Stock Value Column
                    Expanded(
                      flex: 1,
                      child: Text(
                        "Stock Value",
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const Divider(
              height: 0,
              color: Colors.black54,
            ),

            // MAIN CONTENT SECTION
            Obx(() {
              if (_controller.txnLoading) {
                return Container(
                    color: Colors.white,
                    child: const Center(child: CircularProgressIndicator()));
              }

              if (_controller.itemDetailList.isEmpty) {
                return Container(
                    color: Colors.white,
                    width: double.infinity,
                    child: const Center(
                        child: Text(
                      "No Stock Records Found",
                      style: TextStyle(color: Colors.black54),
                    )));
              } else {
                return Expanded(
                    child: _StockListView(_controller.itemDetailList));
              }
            }),
          ],
        ),
      ),

      // IMPROVED BOTTOM SUMMARY BAR
      bottomNavigationBar: Container(
        height: 70,
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        color: colorPrimary,
        child: SingleChildScrollView(
          child: Obx(() {
            return DefaultTextStyle(
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Total Quantity Summary
                  Text(
                    "Total Quantity: ${formatCurrencyAmount(_controller.totalQty.value, false)}",
                    textAlign: TextAlign.left,
                  ),
                  const SizedBox(height: 8),

                  // Total Stock Value Summary
                  Text(
                    "Total Stock Value: ${formatCurrencyAmount(_controller.totalAvgValue.value, true)}",
                    textAlign: TextAlign.left,
                  ),
                ],
              ),
            );
          }),
        ),
      ),
    ));
  }
}

/**
 * STOCK LIST VIEW WIDGET
 *
 * Displays the list of items with their stock information
 */
class _StockListView extends StatelessWidget {
  final List<ItemDetailModel> _itemDetailList;

  const _StockListView(this._itemDetailList);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: _itemDetailList.length,
      itemBuilder: (context, int index) {
        ItemDetailModel item = _itemDetailList[index];

        return InkWell(
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                DefaultTextStyle(
                  style: TextStyle(fontSize: 12, color: colorPrimary),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // ITEM NAME SECTION
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${item.itemName ?? 'Unknown Item'}",
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                                style: const TextStyle(
                                    fontWeight: FontWeight.bold),
                              ),
                            ],
                          ),
                        ),

                        // STOCK QUANTITY SECTION - FIXED formatting
                        Expanded(
                          flex: 1,
                          child: Text(
                            // IMPROVED: Better quantity formatting with proper type casting
                            _formatQuantity(
                                (item.balanceQuantity ?? 0.0).toDouble()),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              // Color coding for stock levels
                              color: (item.balanceQuantity ?? 0.0) <= 0
                                  ? Colors.red
                                  : colorPrimary,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),

                        // STOCK VALUE SECTION - FIXED calculation
                        Expanded(
                          flex: 1,
                          child: Text(
                            // FIXED: Proper null safety and calculation
                            formatCurrencyAmount(
                              _calculateStockValue(item),
                              false,
                            ),
                            textAlign: TextAlign.right,
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                // Add spacing for last element
                if (_itemDetailList.length - 1 == index) ...{
                  const SizedBox(height: 10),
                },
              ],
            ),
          ),
        );
      },
    );
  }

  /**
   * FORMAT QUANTITY FOR DISPLAY
   *
   * Formats quantity numbers for better readability
   * Removes unnecessary decimal places for whole numbers
   */
  String _formatQuantity(double quantity) {
    if (quantity == quantity.roundToDouble()) {
      // Whole number - show without decimals
      return quantity.round().toString();
    } else {
      // Decimal number - show with 2 decimal places
      return quantity.toStringAsFixed(2);
    }
  }

  /**
   * CALCULATE STOCK VALUE SAFELY
   *
   * Calculates item stock value with proper null safety
   * Stock Value = Average Purchase Rate × Balance Quantity
   */
  double _calculateStockValue(ItemDetailModel item) {
    final avgRate = (item.avgPurchaseRate ?? 0.0).toDouble();
    final quantity = (item.balanceQuantity ?? 0.0).toDouble();
    return avgRate * quantity;
  }
}
