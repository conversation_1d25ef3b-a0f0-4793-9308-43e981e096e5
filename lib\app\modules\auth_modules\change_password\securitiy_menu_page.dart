// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_typedefs/rx_typedefs.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/change_password/security_menu_page_controller.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/reset_password/reset_password_controller.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/reset_password/reset_password_screen.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/login_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

class SecurityPage extends StatefulWidget {
  const SecurityPage({super.key});

  @override
  // ignore: library_private_types_in_public_api
  _SecurityPageState createState() => _SecurityPageState();
}

class _SecurityPageState extends State<SecurityPage> {
  final String tag = "SecurityPage";
  String? mobileNumber;

  _customListTile(
      {String iconType = "image",
      String? leadingImageIcon,
      Widget? trailing,
      Icon? leadingIcon,
      required String title,
      String subTitle = "",
      Callback? onClick}) {
    return Container(
      child: ListTile(
        dense: true,
        title: Text(
          title,
          textAlign: TextAlign.left,
          style: TextStyle(color: colorPrimary),
        ),
        subtitle: (subTitle.isNotEmpty)
            ? Text(
                subTitle,
                textAlign: TextAlign.left,
                style: TextStyle(color: textColor, fontSize: 12),
              )
            : null,
        trailing: trailing ??
            Icon(
              Icons.arrow_forward_ios,
              color: colorPrimary,
            ),
        onTap: onClick,
      ),
    );
  }

  final securityMenuPageController = SecurityMenuPageController();

  @override
  void initState() {
    super.initState();
    getNumber();
  }

  getNumber() async {
    mobileNumber = await LoginHelper().getUser();
  }

  resetPasswordHandler() async {
    // Log.d("mob nno" + mobileNumber.toString());
    var resetPasswordController = PasswordResetController();
    resetPasswordController.mobileNoCtrl.text = mobileNumber ?? "";
    Tuple2<bool, String> checkResponse =
        await resetPasswordController.checkUserExistForReset();
    if (!checkResponse.item1) {
      showAlertDialog(context,
          alertType: AlertType.Error,
          alertTitle: "",
          message: checkResponse.item2);
    } else {
      Navigator.of(context).pushNamed('/resetPassword',
          arguments: ResetPasswordScreen(
            username: mobileNumber,
            token: resetPasswordController.token,
            mobileNo: resetPasswordController.mobileNo,
          ));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (securityMenuPageController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }
      return SafeArea(
          child: Scaffold(
              // resizeToAvoidBottomPadding: true,
              resizeToAvoidBottomInset: true,
              appBar: AppBar(
                titleSpacing: -5,
                backgroundColor: colorPrimary,
                title: const Text(
                  "सुरक्षा (Security Setting)",
                  style: TextStyle(
                      fontSize: 18,
                      color: Colors.white,
                      fontFamily: 'HelveticaRegular',
                      fontWeight: FontWeight.bold),
                ),
              ),
              body: ListView(
                children: [
                  _customListTile(
                      // leadingImageIcon: 'images/lock-blue.png',
                      title: "पासवर्ड परिवर्तन गर्नुहोस्",
                      subTitle: "Change Password",
                      iconType: 'ss',
                      onClick: () {
                        // Navigator.of(context).pop();
                        Navigator.pushNamed(context, "/changePassword");
                      }),
                  const Divider(),
                  if (isAdmin) ...[
                    _customListTile(
                      // leadingImageIcon: 'images/lock-blue.png',
                      title: "बहु प्रयोगकर्ता",
                      subTitle: "Multi User",
                      iconType: 'ss',
                      trailing: Switch(
                          activeTrackColor: colorGreenLight,
                          activeColor: colorGreen,
                          value: securityMenuPageController.multiUserValue == 1
                              ? true
                              : false,
                          onChanged: (flag) async {
                            // Tuple2<bool, String> updateResp =
                            //     await securityMenuPageController
                            //         .toggleMultiUser(flag ? 1 : 0);

                            // resetPasswordHandler();
                            int newValMultiUser = isMultiUser == 0 ? 1 : 0;
                            ProgressDialog progressDialog = ProgressDialog(
                                context,
                                type: ProgressDialogType.normal,
                                isDismissible: false);
                            progressDialog.update(
                                message: "Updating data. Please wait....");
                            await progressDialog.show();
                            Tuple2<bool, String> updateResp =
                                await securityMenuPageController
                                    .toggleMultiUser(newValMultiUser);
                            await progressDialog.hide();
                            if (updateResp.item1) {
                              showToastMessage(context,
                                  message: updateResp.item2, duration: 2);
                              securityMenuPageController
                                  .setmultiUserValue(newValMultiUser);
                              await setMultiUser(status: newValMultiUser);
                            } else {
                              showToastMessage(context,
                                  alertType: AlertType.Error,
                                  message: updateResp.item2,
                                  duration: 2);
                            }
                            // Navigator.of(context).pop();
                            // Navigator.pushNamed(context, "/changePassword");
                          }),
                      onClick: () => null,
                    ),
                    const Divider(),
                  ],
                  if (isAdmin) ...[
                    _customListTile(
                      // leadingImageIcon: 'images/lock-blue.png',
                      title: "वेब लगइन",
                      subTitle: "Web Login",
                      iconType: 'ss',
                      trailing: Switch(
                          activeTrackColor: colorGreenLight,
                          activeColor: colorGreen,
                          value: !securityMenuPageController.isWebEnabledValue,
                          onChanged: (flag) async {
                            bool newValWebEnabled = !isWebEnabled;
                            ProgressDialog progressDialog = ProgressDialog(
                                context,
                                type: ProgressDialogType.normal,
                                isDismissible: false);
                            progressDialog.update(
                                message: "Updating data. Please wait....");
                            await progressDialog.show();
                            Tuple2<bool, String> updateResp =
                                await securityMenuPageController
                                    .toggleEnableWeb();
                            await progressDialog.hide();
                            if (updateResp.item1) {
                              showToastMessage(context,
                                  message: updateResp.item2, duration: 2);
                              securityMenuPageController
                                  .setWebEnabledValue(newValWebEnabled);
                              await setWebEnabledValue(
                                  status: newValWebEnabled);
                            } else {
                              showToastMessage(context,
                                  alertType: AlertType.Error,
                                  message: updateResp.item2,
                                  duration: 2);
                            }
                            // Navigator.of(context).pop();
                            // Navigator.pushNamed(context, "/changePassword");
                          }),
                      onClick: () => null,
                    ),
                    const Divider(),
                  ],

                  // _customListTile(
                  //     leadingImageIcon: 'images/lock-blue.png',
                  //     title: "सुरक्षा",
                  //     subTitle: "Security",
                  //     onClick: () {
                  //       Navigator.of(context).pop();
                  //       Navigator.pushNamed(context, "/securitySetting");
                  //     }),
                ],
              )));
    });
  }
}
