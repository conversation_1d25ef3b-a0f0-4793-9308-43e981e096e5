import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:localstorage/localstorage.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/login_helper.dart';
import 'package:mobile_khaata_v2/utilities/shared_pref_helper1.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import '../../../main.dart';

class SplashScreen extends StatefulWidget {
  @override
  SplashScreenState createState() => new SplashScreenState();
}

class SplashScreenState extends State<SplashScreen> {
  final logo = Image.asset(
    'images/logo-splash.png',
    fit: BoxFit.cover,
  );
  final map = Image.asset(
    'images/nepal_map_outline.png',
    fit: BoxFit.fill,
  );
  final flag = Image.asset(
    'images/nepal_flag.gif',
    fit: BoxFit.cover,
  );

  startTime() async {
    var _duration = new Duration(seconds: 4);
    return new Timer(_duration, navigate);
  }

  void navigate() async {
    final storage = new LocalStorage(MobileSettings);
    await storage.ready;
    bool introStatus = storage.getItem(FreshInstallStatus) ?? true;

    LoginHelper _loginHelper = new LoginHelper();
    bool loginStatus = await _loginHelper.isLoggedIn;

    isUserLoggedIn = loginStatus;

    bool isExpired = await SharedPrefHelper1().isExpired;

    if (loginStatus) {
      Navigator.pushReplacementNamed(context, "/home");
    } else if (introStatus) {
      Navigator.pushReplacementNamed(context, "/introScreen");
    } else {
      Navigator.pushReplacementNamed(context, "/login");
    }
  }

  @override
  void initState() {
    super.initState();
    startTime();
  }

  @override
  void didChangeDependencies() {
    precacheImage(logo.image, context);
    precacheImage(map.image, context);
    precacheImage(flag.image, context);
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;

    return new Scaffold(
      // resizeToAvoidBottomPadding: false,
      resizeToAvoidBottomInset: false,
      body: Container(
        padding: EdgeInsets.symmetric(horizontal: 30, vertical: 20),
        color: colorPrimary,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            SizedBox(
              height: 30,
            ),
            Expanded(
              child: Column(
                children: [
                  Text(
                    "मोबाईल हातमा\nखाता साथमा",
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontSize: screenHeight * 0.06,
                        fontWeight: FontWeight.w800,
                        color: Colors.white,
                        fontFamily: "ArialBlack"),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Container(
                    height: screenHeight * 0.3,
                    child: logo,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Text(
                        "mobile",
                        style: TextStyle(
                            fontSize: screenWidth * 0.12,
                            color: Colors.white,
                            fontFamily: 'ArialBlack'),
                      ),
                      Text(
                        " खाता",
                        style: TextStyle(
                          fontSize: screenWidth * 0.12,
                          color: Colors.white,
                          fontWeight: FontWeight.w800,
                          fontFamily: 'ArialBlack',
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.only(
                  top: screenWidth * 0.06, left: screenWidth * 0.12),
              height: screenHeight * 0.18,
              decoration:
                  BoxDecoration(image: new DecorationImage(image: map.image)),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    height: screenHeight * 0.08,
                    child: flag,
                  ),
                  Transform.rotate(
                    angle: math.pi / 95,
                    child: Container(
                        margin:
                            EdgeInsets.only(left: 0, top: screenHeight * 0.074),
                        child: Row(
                          children: [
                            Text(
                              "अब बन्दैछ नेपाल डिजिटल",
                              style: TextStyle(
                                fontSize: screenWidth * 0.045,
                                color: colorPrimary,
                                fontWeight: FontWeight.w800,
                              ),
                            ),
                          ],
                        )),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: screenWidth * 0.025,
            ),
            Text(
              "Vedanta Technologies",
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: screenWidth * 0.04,
                color: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
