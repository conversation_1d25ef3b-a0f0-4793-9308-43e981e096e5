// ignore_for_file: unnecessary_null_comparison

import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

/**
 * PARTY STATEMENT REPORT MODEL - AMOUNT CALCULATION EXPLANATION:
 *
 * This model handles the calculation of debit (Dr), credit (Cr), and balance amounts
 * for party statement reports. The calculations follow standard accounting principles:
 *
 * ACCOUNTING RULES:
 * - Debit (Dr): Amounts owed TO the party (increases party's balance)
 * - Credit (Cr): Amounts owed BY the party (decreases party's balance)
 * - Balance: Running total showing net amount owed
 *
 * TRANSACTION TYPES AND THEIR IMPACT:
 * - Sales: Dr = Total Sale, Cr = Cash Received, Balance = +Credit Amount (party owes us)
 * - Sales Return: Dr = Cash Paid, Cr = Total Return, Balance = -Credit Amount (we owe party)
 * - Purchase: Dr = Total Purchase, Cr = Cash Paid, Balance = -Credit Amount (we owe party)
 * - Purchase Return: Dr = Cash Received, Cr = Total Return, Balance = +Credit Amount (party owes us)
 * - Payment In: Dr = Cash Received, Balance = -Cash Amount (reduces what party owes)
 * - Payment Out: Cr = Cash Paid, Balance = +Cash Amount (increases what we owe)
 * - Expense: Dr = Total Expense, Cr = Cash Paid, Balance = -Credit Amount (we owe party)
 */

class PartyStatementReportModel {
  String? txnDate;
  String? txnDateBS;
  String? txnRefNumberChar;
  String? description;
  int? txnType;
  String? txnTypeText;
  double? drAmount;
  double? crAmount;
  double? txnBalanceAmount;

  PartyStatementReportModel({
    this.txnDate,
    this.txnDateBS,
    this.txnRefNumberChar,
    this.description,
    this.txnType,
    this.txnTypeText,
    this.drAmount,
    this.crAmount,
    this.txnBalanceAmount,
  });

  factory PartyStatementReportModel.fromJson(Map<String, dynamic> json) {
    // Parse date information
    DateTime txnDateTime = DateTime.parse(json["txn_date"]);
    String txnDate = DateFormat('y-MM-dd').format(txnDateTime);
    String txnDateBS = toDateBS(txnDateTime);

    // Initialize amounts with null safety
    double drAmount = 0.00;
    double crAmount = 0.00;
    double txnBalanceAmount = 0.00;

    // Parse raw amounts from JSON with null safety
    double cashAmount = parseDouble(json['txn_cash_amount']) ?? 0.00;
    double balanceAmount = parseDouble(json['txn_balance_amount']) ?? 0.00;
    double totalAmount =
        parseDouble((cashAmount + balanceAmount).toStringAsFixed(2)) ?? 0.00;

    String txnTypeText = TxnType.txnTypeText[json["txn_type"]] ?? "";
    String description = (json['description'] != null &&
            json['description'].toString().isNotEmpty)
        ? json['description']
        : txnTypeText;

    // TRANSACTION TYPE SPECIFIC CALCULATIONS:
    // Each transaction type follows specific accounting rules for Dr/Cr
    // Balance will be calculated as Dr - Cr in the controller for consistency

    if (TxnType.sales == json['txn_type']) {
      // SALES TRANSACTION:
      // When we make a sale to a party:
      // - Dr: Total sale amount (what party owes us)
      // - Cr: Cash received immediately
      drAmount = totalAmount; // Total sale amount
      crAmount = cashAmount; // Cash received
    } else if (TxnType.salesReturn == json['txn_type']) {
      // SALES RETURN TRANSACTION:
      // When party returns goods:
      // - Dr: Cash we paid back to party
      // - Cr: Total return amount
      drAmount = cashAmount; // Cash paid back
      crAmount = totalAmount; // Total return value
    } else if (TxnType.purchase == json['txn_type']) {
      // PURCHASE TRANSACTION:
      // When we purchase from a party:
      // - Dr: 0 (we don't owe party more)
      // - Cr: Total purchase amount (we owe party)
      drAmount = 0.00; // No debit
      crAmount = totalAmount; // We owe party
    } else if (TxnType.purchaseReturn == json['txn_type']) {
      // PURCHASE RETURN TRANSACTION:
      // When we return goods to party:
      // - Dr: Total return amount (party owes us)
      // - Cr: Cash we received from party
      drAmount = totalAmount; // Party owes us
      crAmount = cashAmount; // Cash received
    } else if (TxnType.paymentIn == json['txn_type']) {
      // PAYMENT IN TRANSACTION (Receipt):
      // When party pays us money:
      // - Dr: Cash received from party
      // - Cr: 0 (no credit component)
      drAmount = cashAmount; // Cash received
      crAmount = 0.00; // No credit
    } else if (TxnType.paymentOut == json['txn_type']) {
      // PAYMENT OUT TRANSACTION:
      // When we pay money to party:
      // - Dr: 0 (no debit component)
      // - Cr: Cash paid to party
      drAmount = 0.00; // No debit
      crAmount = cashAmount; // Cash paid
    } else if (TxnType.expense == json['txn_type']) {
      // EXPENSE TRANSACTION:
      // When we have an expense with a party:
      // - Dr: 0 (we don't owe party more)
      // - Cr: Total expense amount (we owe party)
      drAmount = 0.00; // No debit
      crAmount = totalAmount; // We owe party
    } else {
      // DEFAULT/UNKNOWN TRANSACTION TYPE:
      // Use basic cash logic
      drAmount = cashAmount;
      crAmount = 0.00;
    }

    // BALANCE WILL BE CALCULATED IN CONTROLLER AS: Dr - Cr
    // This ensures consistency across all calculations
    txnBalanceAmount = 0.00; // Placeholder, will be calculated in controller

    return PartyStatementReportModel(
      txnDate: txnDate,
      txnDateBS: txnDateBS,
      txnRefNumberChar: json['txn_ref_number_char'],
      description: description,
      txnType: json['txn_type'],
      txnTypeText: txnTypeText,
      drAmount:
          parseDouble(drAmount.toStringAsFixed(2)), // Ensure 2 decimal places
      crAmount:
          parseDouble(crAmount.toStringAsFixed(2)), // Ensure 2 decimal places
      txnBalanceAmount: parseDouble(
          txnBalanceAmount.toStringAsFixed(2)), // Ensure 2 decimal places
    );
  }
}
