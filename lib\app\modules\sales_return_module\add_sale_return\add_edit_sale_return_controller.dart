import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/unit_list_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/txn_image_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/sale_return_model.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/app/repository/sales_return_repository.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tuple/tuple.dart';

class AddEditSaleReturnController extends GetxController {
  final String tag = "Sales Return Controller";

  var _isLoading = true.obs;
  var _editFlag = false.obs;
  var _readOnlyFlag = false.obs;
  var _isVatEnabled = false.obs;
  var _isReceived = false.obs;
  var _iscashSaleReturnSelected = false.obs;

  bool get iscashSaleReturnSelected => _iscashSaleReturnSelected.value;
  bool get isLoading => _isLoading.value;
  bool get editFlag => _editFlag.value;
  bool get readOnlyFlag => _readOnlyFlag.value;

  bool get isVatEnabled => _isVatEnabled.value;
  bool get isReceived => _isReceived.value;

  set setiscashSaleReturnSelected(bool flag) {
    _iscashSaleReturnSelected.value = flag;
    _iscashSaleReturnSelected.refresh();
  }

  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  set setIsReceived(bool flag) {
    _isReceived.value = flag;
  }

  // for edit case, to check if same bill no is used in update case, not to give
  // duplicate error;
  String? previousBillNo;

  SaleReturnRepository saleReturnRepository = new SaleReturnRepository();

  LedgerRepository _ledgerRepository = new LedgerRepository();

  var transaction = SaleReturnModal(
          txnDateBS: currentDateBS,
          txnSubTotalAmount: 0,
          txnPaymentTypeId: PAYMENT_MODE_CASH_ID)
      .obs;

  var items = <LineItemDetailModel>[].obs;

  var images = <TxnImageModel>[].obs;

  var files = <File>[].obs;

  final formKey = GlobalKey<FormState>();

  final TextEditingController billNoCtrl = TextEditingController();

  final TextEditingController partyNameCtrl = TextEditingController();
  final TextEditingController mobileCtrl = TextEditingController();
  final TextEditingController addressCtrl = TextEditingController();
  final TextEditingController panNoCtrl = TextEditingController();

  final TextEditingController displayTextCtrl = TextEditingController();
  final TextEditingController subTotalAmountCtrl = TextEditingController();
  final TextEditingController discountPercentageCtrl = TextEditingController();
  final TextEditingController discountAmountCtrl = TextEditingController();
  final TextEditingController vatAmountCtrl = TextEditingController();
  final TextEditingController vatPercentCtrl = TextEditingController();

  final TextEditingController totalAmountCtrl = TextEditingController();
  final TextEditingController receivedAmountCtrl = TextEditingController();
  final TextEditingController dueAmountCtrl = TextEditingController();
  final TextEditingController descCtrl = TextEditingController();
  final TextEditingController paymentRefCtrl = TextEditingController();

  UnitListController unitListController = Get.put(UnitListController());

  var selectedLedger = LedgerDetailModel().obs;

  @override
  void onInit() async {
    // clear all cache images of bill
    imageCache.clear();
    super.onInit();
  }

  initialize() {
    _isLoading(false);
  }

  @override
  void dispose() {
    discountPercentageCtrl.dispose();
    billNoCtrl.dispose();
    partyNameCtrl.dispose();
    vatAmountCtrl.dispose();
    totalAmountCtrl.dispose();
    receivedAmountCtrl.dispose();
    dueAmountCtrl.dispose();
    descCtrl.dispose();
    mobileCtrl.dispose();
    addressCtrl.dispose();
    panNoCtrl.dispose();
    displayTextCtrl.dispose();
    subTotalAmountCtrl.dispose();
    discountAmountCtrl.dispose();
    vatPercentCtrl.dispose();
    paymentRefCtrl.dispose();
    super.dispose();
  }

  recalculateForItems() {
    double itemSubTotal = 0.00;
    items.forEach((LineItemDetailModel li) {
      itemSubTotal = parseDouble(
              (itemSubTotal + (li.totalAmount ?? 0.0)).toStringAsFixed(2)) ??
          0.00;
    });
    onSubTotalIndividualChange(itemSubTotal.toString());
  }

  recalculateDataForItem({String? editorTag}) {
    // handle value change in sale model in valid step:
    double subtoal = transaction.value.txnSubTotalAmount ?? 0.00;

    transaction.value.txnTaxableTotalAmount = parseDouble(
        (subtoal - (transaction.value.txnDiscountAmount ?? 0.00))
            .toStringAsFixed(2));

    transaction.value.txnTaxAmount = parseDouble(
        ((transaction.value.txnTaxPercent ?? 0.0) *
                0.01 *
                (transaction.value.txnTaxableTotalAmount ?? 0.00))
            .toStringAsFixed(2));

    transaction.value.txnTotalAmount = parseDouble(
        ((transaction.value.txnTaxableTotalAmount ?? 0.00) +
                (transaction.value.txnTaxAmount ?? 0.00))
            .toStringAsFixed(2));

    if (transaction.value.ledgerId != CASH_SALES_LEDGER_ID) {
      //for credit or if received is checked
      transaction.value.txnCashAmount = isReceived
          ? transaction.value.txnTotalAmount
          : (transaction.value.txnCashAmount ?? 0.00);

      transaction.value.txnBalanceAmount = isReceived
          ? 0.0
          : parseDouble(((transaction.value.txnTotalAmount ?? 0.00) -
                  (transaction.value.txnCashAmount ?? 0.00))
              .toStringAsFixed(2));
    } else {
      //for cash
      //total will always be cash amount for cash sale ledger
      transaction.value.txnCashAmount = transaction.value.txnTotalAmount;
      transaction.value.txnBalanceAmount = 0.00;
    }

    assignTransactionToTextFields(editorTAG: editorTag);
  }

  onvatPercentChange(String value, {String? editorTag}) {
    double taxPercentage = (parseDouble(value) ?? 0.00).toPrecision(2);

    transaction.value.txnTaxPercent = taxPercentage;

    transaction.value.txnTaxAmount = parseDouble(
        ((taxPercentage * (transaction.value.txnTaxableTotalAmount ?? 0.00)) *
                0.01)
            .toStringAsFixed(2));

    transaction.value.txnTotalAmount = parseDouble(
        ((transaction.value.txnTaxableTotalAmount ?? 0.0) +
                (transaction.value.txnTaxAmount ?? 0.00))
            .toStringAsFixed(2));

    changeReceivedAmount(
        transaction.value.ledgerId == CASH_SALES_LEDGER_ID
            ? transaction.value.txnTotalAmount.toString()
            : 0.0.toString(),
        editorTag: editorTag);
  }

  onvatAmountChange(String value, {String? editorTag}) {
    double vatAmount = parseDouble(value) ?? 0.00;
    double taxableTotal = transaction.value.txnTaxableTotalAmount ?? 0.00;

    transaction.value.txnTaxAmount = parseDouble(vatAmount.toStringAsFixed(2));

    // Prevent division by zero that causes NaN values
    double taxPercent =
        taxableTotal > 0 ? (vatAmount / taxableTotal) * 100 : 0.00;
    transaction.value.txnTaxPercent =
        parseDouble(taxPercent.toStringAsFixed(2));

    transaction.value.txnTotalAmount = parseDouble(
        ((taxableTotal) + (transaction.value.txnTaxAmount ?? 0.00))
            .toStringAsFixed(2));

    changeReceivedAmount(
        transaction.value.ledgerId == CASH_SALES_LEDGER_ID
            ? transaction.value.txnTotalAmount.toString()
            : 0.0.toString(),
        editorTag: editorTag);
  }

  changeReceivedAmount(String value, {String? editorTag}) {
    transaction.value.txnCashAmount = parseDouble(value);
    transaction.value.txnBalanceAmount = parseDouble(
        ((transaction.value.txnTotalAmount ?? 0.00) -
                (transaction.value.txnCashAmount ?? 0.00))
            .toStringAsFixed(2));

    print(transaction.value.txnTotalAmount);
    print(transaction.value.txnCashAmount);
    print(transaction.value.txnBalanceAmount);
    assignTransactionToTextFields(editorTAG: editorTag);
  }

  onSubTotalIndividualChange(String val, {String? editorTag}) {
    transaction.value.txnSubTotalAmount = (parseDouble(val) ?? 0.00);
    transaction.refresh();
    recalculateDataForItem(editorTag: editorTag);

    if (transaction.value.txnDiscountPercent != null &&
        transaction.value.txnDiscountPercent != 0.00) {
      updateDiscountPercentage(transaction.value.txnDiscountPercent.toString(),
          editorTag: editorTag);
      print("ya puge");
      recalculateDataForItem(editorTag: editorTag);
    }
  }

  onToggleVat(bool flag) {
    _isVatEnabled.value = flag;
    _isVatEnabled.refresh();
    double VAT = flag ? VAT_PERCENTAGE : 0.00;

    transaction.value.txnTaxPercent = VAT;
    onvatPercentChange(VAT.toString());
  }

  onChangeParty(LedgerDetailModel party) {
    // ignore: null_aware_in_condition
    if (party.ledgerId != null) {
      // Log.d("selected");
      transaction.value.ledgerId = party.ledgerId;
      partyNameCtrl.text = party.ledgerTitle ?? "";
      mobileCtrl.text = party.mobileNo ?? "";
      addressCtrl.text = party.address ?? "";
      panNoCtrl.text = party.tinNo ?? "";
      if (party.ledgerId == CASH_SALES_LEDGER_ID) {
        displayTextCtrl.text = (transaction.value.txnDisplayName == "" ||
                transaction.value.txnDisplayName == null)
            ? party.ledgerTitle!
            : transaction.value.txnDisplayName!;
        transaction.value.txnDisplayName = displayTextCtrl.text;
        _iscashSaleReturnSelected.value = true;
        _iscashSaleReturnSelected.refresh();
        transaction.value.txnCashAmount =
            transaction.value.txnTotalAmount ?? 0.0;
        transaction.value.txnBalanceAmount = 0.00;
      }
    } else {
      transaction.value.ledgerId = null;
      transaction.value.txnCashAmount = 0.00;
      transaction.value.txnBalanceAmount = transaction.value.txnTotalAmount;
      transaction.value.txnDisplayName = displayTextCtrl.text = "";
      partyNameCtrl.text = "";
      mobileCtrl.text = "";
      addressCtrl.text = "";
      panNoCtrl.text = "";
    }
    selectedLedger.value = party;
    selectedLedger.refresh();
    transaction.refresh();

    if ((transaction.value.txnSubTotalAmount ?? 0.0) > 0.00) {
      assignTransactionToTextFields();
    }
  }

  assignTransactionToTextFields({String? editorTAG}) {
    // Log.d("assignning to text ${transaction.value.toJson()}");

    // formKey.currentState.

    billNoCtrl.text = transaction.value.txnRefNumberChar ?? "";
    totalAmountCtrl.text =
        (transaction.value.txnTotalAmount ?? 0.0).toStringAsFixed(2);
    dueAmountCtrl.text =
        (transaction.value.txnBalanceAmount ?? 0.0).toStringAsFixed(2);
    displayTextCtrl.text = transaction.value.txnDisplayName ?? "";
    paymentRefCtrl.text = transaction.value.txnPaymentReference ?? "";
    descCtrl.text = transaction.value.txnDescription ?? "";
    // displayTextCtrl.text = transaction.value.txnDisplayName;

    // controller which triggers above method
    // check for editor tag, if match don't refresh

    if (editorTAG != 'txn_subtotal')
      subTotalAmountCtrl.text = transaction.value.txnSubTotalAmount != 0.0
          ? (transaction.value.txnSubTotalAmount ?? 0.00).toStringAsFixed(2)
          : "";

    if (editorTAG != 'txn_discount_percent')
      discountPercentageCtrl.text = transaction.value.txnDiscountPercent != 0.0
          ? (transaction.value.txnDiscountPercent ?? 0.00).toStringAsFixed(2)
          : "";

    if (editorTAG != 'txn_discount_amount')
      discountAmountCtrl.text = transaction.value.txnDiscountAmount != 0.0
          ? (transaction.value.txnDiscountAmount ?? 0.00).toStringAsFixed(2)
          : "";

    if (editorTAG != 'txn_tax_percent')
      vatPercentCtrl.text = transaction.value.txnTaxPercent != 0.0
          ? (transaction.value.txnTaxPercent ?? 0.00).toStringAsFixed(2)
          : "";

    if (editorTAG != 'txn_tax_amount')
      vatAmountCtrl.text =
          (transaction.value.txnTaxAmount?.abs() ?? 0.00).toString();

    if (editorTAG != 'txn_cash_amount')
      receivedAmountCtrl.text = transaction.value.txnCashAmount != 0.0
          ? (transaction.value.txnCashAmount ?? 0.00).toStringAsFixed(2)
          : "";
  }

  updateDiscountPercentage(String dis, {String? editorTag}) {
    transaction.value.txnDiscountPercent = (parseDouble(dis) ?? 0.00);

    double amt = parseDouble(((parseDouble(dis) ?? 0.00) *
                0.01 *
                (transaction.value.txnSubTotalAmount ?? 0.0))
            .toStringAsFixed(2)) ??
        0.00;

    transaction.value.txnDiscountAmount = amt;
    discountAmountCtrl.text = amt.toStringAsFixed(2);
  }

  updateDiscountAmount(String dis, {String? editorTag}) {
    double disAmt = (parseDouble(dis) ?? 0.00);
    double subTotal = (transaction.value.txnSubTotalAmount ?? 0.00);

    // Prevent division by zero that causes NaN values
    double disPercent = subTotal > 0 ? (disAmt / subTotal) * 100 : 0.00;

    transaction.value.txnDiscountAmount =
        parseDouble(disAmt.toStringAsFixed(2));
    transaction.value.txnDiscountPercent =
        parseDouble(disPercent.toStringAsFixed(2));
    transaction.refresh();
    recalculateDataForItem(editorTag: editorTag);
    assignTransactionToTextFields(editorTAG: "txn_discount_amount");
  }

  initEdit(saleID, readOnlyFlag) async {
    _isLoading(true);
    _isLoading.refresh();
    final tempDir = await getTemporaryDirectory();

    Tuple3<SaleReturnModal, List<LineItemDetailModel>, List<TxnImageModel>> dt =
        await saleReturnRepository.getSaleReturnById(saleID);

    LedgerDetailModel? party = await _ledgerRepository
        .getLedgerWithBalanceById(dt.item1.ledgerId ?? "");

    _editFlag.value = true;
    transaction.value = dt.item1;
    items.value = dt.item2;
    images.value = dt.item3;
    files.clear();
    List<File> prevFiles = [];

    await Future.wait(dt.item3.map((e) async {
      final file =
          await new File('${tempDir.path}/image-${e.sno}.${e.imageExt}')
              .create();
      file.writeAsBytesSync(e.imageBitmap!);
      prevFiles.add(file);
    }));
    files.addAll(prevFiles);
    files.forEach((element) {
      fillImage(element);
    });
    items.refresh();
    files.refresh();
    previousBillNo = dt.item1.txnRefNumberChar;
    onChangeParty(party);

    if ((dt.item1.txnTaxAmount ?? 0.00) > 0.0) {
      _isVatEnabled.value = true;
      _isVatEnabled.refresh();
    }

    assignTransactionToTextFields();
    _readOnlyFlag.value = readOnlyFlag;
    _isLoading(false);
    _isLoading.refresh();
  }

  fillImage(File element) async {
    final image = await element.readAsBytes();
    imageList.add(image);
    imageList.refresh();
  }

  var imageList = <Uint8List>[].obs;

  Future<List<TxnImageModel>> getTxnImageModelFromFiles(
      List<File> _files) async {
    List<TxnImageModel> txnImageModels = [];

    await Future.wait(_files.map((element) async {
      Tuple2<List<int>, String> compressedImage = await compressImage(element);
      txnImageModels.add(TxnImageModel(
          imageBitmap: compressedImage.item1, imageExt: compressedImage.item2));
    }));
    return txnImageModels;
  }

  Future<bool> checkLargeImage(List fls, {bool? preConvertFiles}) async {
    //preConvertFiles can be used to convert files list to txn image model once, so than it don't need re convert
    bool status = false;
    await Future.wait(fls.map((element) async {
      Tuple2<List<int>, String> compressedImage =
          await compressImage(File(element.path));
      // Log.d("file size is" + compressedImage.item1.length.toString());
      if (compressedImage.item1.length > MAX_IMAGE_SIZE) {
        status = true;
        // return;
      }
    }).toList());

    return status;
  }

  Future<bool> checkDuplicateBillNo() async {
    bool status = false;
    try {
      if (editFlag && transaction.value.txnRefNumberChar == previousBillNo) {
        status = false;
      } else {
        status = await saleReturnRepository
            .isBillDuplicate(transaction.value.txnRefNumberChar);
      }
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<String?> createSalesReturn() async {
    String? status;

    try {
      // String primaryKeyPrefix = await getPrimaryKeyPrefix();
      // transaction.value.txnId = primaryKeyPrefix + uuidV4;
      transaction.value.txnType = TxnType.salesReturn;
      transaction.value.txnDate =
          toDateAD(NepaliDateTime.parse(transaction.value.txnDateBS ?? ""));
      if (!([null, ""].contains(transaction.value.chequeIssueDateBS))) {
        transaction.value.chequeIssueDate = toDateAD(NepaliDateTime.parse(
            strTrim(transaction.value.chequeIssueDateBS ?? "")));
      }
      List<TxnImageModel> tempImages = await getTxnImageModelFromFiles(files);

      status = await saleReturnRepository.addSaleReturn(
          transaction.value, items, tempImages);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<bool> updateSalesReturn() async {
    bool status = false;

    try {
      transaction.value.txnDate = toDateAD(
          NepaliDateTime.parse(strTrim(transaction.value.txnDateBS ?? "")));
      if (!([null, ""].contains(transaction.value.chequeIssueDateBS))) {
        transaction.value.chequeIssueDate = toDateAD(NepaliDateTime.parse(
            strTrim(transaction.value.chequeIssueDateBS ?? "")));
      }
      List<TxnImageModel> tempImages = await getTxnImageModelFromFiles(files);
      status = await saleReturnRepository.updateSaleReturn(
          transaction.value, items, tempImages);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }
}
