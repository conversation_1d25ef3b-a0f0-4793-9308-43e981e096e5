import 'package:mobile_khaata_v2/app/model/database/item_modal.dart';
import 'package:mobile_khaata_v2/app/model/database/line_item_model.dart';
import 'package:mobile_khaata_v2/app/model/database/unit_modal.dart';

class BilledItemModel {
  ItemModel? item;
  BilledItemUnit? itemUnit;
  double? itemUnitPrice;
  double? itemQuantity;
  double? itemAmount;
}

class BilledItemUnit {
  String? unitId;
  String? unitName;
  String? unitShortName;
  String? unitType;
  double? conversionFactor;

  BilledItemUnit(
      {this.unitId,
      this.unitName,
      this.unitShortName,
      this.unitType,
      this.conversionFactor = 0.00});
}

LineItemModel billedToLine(BilledItemModel billedItemModel) {
  LineItemModel lineItemModel = LineItemModel();
  lineItemModel.itemId = billedItemModel.item?.itemId;
  lineItemModel.quantity = billedItemModel.itemQuantity;
  lineItemModel.pricePerUnit = billedItemModel.itemUnitPrice;
  lineItemModel.totalAmount = billedItemModel.itemAmount;
  lineItemModel.lineItemUnitId = billedItemModel.itemUnit?.unitId;
  lineItemModel.lineItemUnitConversionFactor =
      billedItemModel.itemUnit?.unitType == 'base'
          ? 0.0
          : billedItemModel.item?.unitConversionFactor;
  return lineItemModel;
}

BilledItemModel lineToBuild(
  LineItemModel lineItemModel,
  List<UnitModel> listUnit,
  List<ItemModel> itemList,
) {
  BilledItemModel billedItemModel = BilledItemModel();
  billedItemModel.itemUnitPrice = lineItemModel.pricePerUnit;
  billedItemModel.itemQuantity = lineItemModel.quantity;
  billedItemModel.itemAmount = lineItemModel.totalAmount;
  ItemModel item =
      itemList.firstWhere((element) => element.itemId == lineItemModel.itemId);
  UnitModel unitModel = listUnit
      .firstWhere((element) => element.unitId == lineItemModel.lineItemUnitId);
  UnitModel unitAltModel =
      listUnit.firstWhere((element) => element.unitId == item.alternateUnitId);

  billedItemModel.item = item;

  UnitModel unitMainModel = lineItemModel.lineItemUnitConversionFactor! > 0.0
      ? unitAltModel
      : unitModel;

  billedItemModel.itemUnit = BilledItemUnit(
      unitId: unitMainModel.unitId,
      unitName: unitMainModel.unitName,
      unitShortName: unitMainModel.unitShortName,
      unitType:
          lineItemModel.lineItemUnitConversionFactor! > 0.0 ? 'alt' : 'base');
  return billedItemModel;
}
