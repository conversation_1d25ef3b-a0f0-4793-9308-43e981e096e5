// import 'package:esewa_pnp/esewa.dart';
// import 'package:esewa_pnp/esewa_pnp.dart';
// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/app/modules/subscription_module/payment_subscription/payment_subscription_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

// import 'package:flutter_khalti/flutter_khalti.dart';

class PaymentResponse {
  String? planId;
  String? paymentType;
  String? productId;
  String? totalAmount;
  String? referenceId;

  PaymentResponse(
      {this.planId,
      this.productId,
      this.referenceId,
      this.totalAmount,
      this.paymentType});

  // static PaymentResponse fromEsewa(ESewaResult result) => PaymentResponse(
  //     productId: result.productId,
  //     referenceId: result.referenceId,
  //     totalAmount: result.totalAmount);
}

class PaymentSubscriptionView extends StatefulWidget {
  final String? id;
  final double? price;

  const PaymentSubscriptionView({super.key, this.id, this.price});

  @override
  _PaymentSubscriptionViewState createState() =>
      _PaymentSubscriptionViewState();
}

class _PaymentSubscriptionViewState extends State<PaymentSubscriptionView> {
  String? planId;
  double? amount;

  // ESewaConfiguration eSewaConfiguration = ESewaConfiguration(
  //     clientID: ESEWA_MERCHANT_KEY,
  //     secretKey: ESEWA_MERCHANT_SECRET,
  //     environment: ESewaConfiguration.ENVIRONMENT_LIVE
  //     );

  // FlutterKhalti khaltiConfiguration = FlutterKhalti.configure(
  //   publicKey: "test_public_key_08310395e70748f7b8a00100af7da18b",
  //   urlSchemeIOS: "KhaltiPayFlutterExampleScheme",
  // );

  final paymentSubscriptionController = PaymentSubscriptionController();

  @override
  void initState() {
    super.initState();
    paymentSubscriptionController.init();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      // height: 40,
      padding: const EdgeInsets.symmetric(horizontal: 18, vertical: 15),

      child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              "Select Payment Gateway",
              style: labelStyle2.copyWith(
                fontSize: 24,
              ),
            ),
            const SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () async {
                      // Log.d("clicked");
                      ProgressDialog progressDialog = ProgressDialog(context,
                          type: ProgressDialogType.normal,
                          isDismissible: false);
                      // try {
                      //   var res = await ESewaPnp(configuration: eSewaConfiguration).initPayment(payment: ESewaPayment(
                      //                   amount: widget.price.toInt(),
                      //                   productID: widget.id,
                      //                   productName: ESEWA_APP_NAME,
                      //                   callBackURL: ESEWA_CALLBACK_URL));
                      //   Log.d("eseswa payment resp  $res");
                      //
                      //   progressDialog.update(message: "Verifying payment. Please wait....");
                      //   await progressDialog.show();
                      //
                      //   Tuple2<bool, String> verifyResp = await paymentSubscriptionController.verifyEsewaPayment(res.referenceId);
                      //
                      //   await progressDialog.hide();
                      //
                      //   Navigator.of(context).pop(true);
                      //
                      // } on ESewaPaymentException catch (e) {
                      //   Log.d("error payment " + e.toString());
                      //   await progressDialog.hide();
                      //   Navigator.of(context).pop();
                      // }
                    },
                    child: Container(
                      clipBehavior: Clip.hardEdge,
                      decoration: BoxDecoration(
                          border: Border.all(color: esewaColor),
                          borderRadius: BorderRadius.circular(10)),
                      // height: 50,
                      padding: const EdgeInsets.symmetric(vertical: 10),
                      width: 125,
                      alignment: Alignment.center,
                      child: Column(
                        // mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(
                            'images/esewa.png',
                            height: 40,
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          Text(
                            "Pay With Esewa",
                            textAlign: TextAlign.center,
                            style: TextStyle(
                                color: esewaColor,
                                fontSize: 15,
                                fontWeight: FontWeight.bold),
                          )
                        ],
                      ),
                    ),
                  ),
                ),

                //TODO Implemet khalti payment later
                // Material(
                //   color: Colors.transparent,
                //   child: InkWell(
                //     onTap: () async {
                //       Log.d("clicked");
                //       try {
                //         var res =
                //             await ESewaPnp(configuration: eSewaConfiguration)
                //                 .initPayment(
                //                     payment: ESewaPayment(
                //                         amount: widget.price?.toInt(),
                //                         productID: widget.id,
                //                         // productID:pro
                //                         productName: ESEWA_APP_NAME,
                //                         callBackURL: ESEWA_CALLBACK_URL));
                //         Log.d("eseswa payment resp  $res");

                //         // if(res.)
                //         Tuple2<bool, String> verifyResp =
                //             await paymentSubscriptionController
                //                 .verifyEsewaPayment(res.referenceId);

                //         if (verifyResp.item1) {
                //           Navigator.of(context).pop(true);
                //         } else {
                //           Navigator.of(context).pop(true);
                //         }
                //       } on ESewaPaymentException catch (e) {
                //         Log.d("error payment " + e.toString());
                //         Navigator.of(context).pop();
                //       }
                //     },
                //     child: Container(
                //       clipBehavior: Clip.hardEdge,
                //       decoration: BoxDecoration(
                //           border: Border.all(color: khaltiColor),
                //           borderRadius: BorderRadius.circular(10)),
                //       // height: 50,
                //       padding: EdgeInsets.symmetric(vertical: 10),
                //       width: 140,
                //       alignment: Alignment.center,
                //       child: Column(
                //         // mainAxisAlignment: MainAxisAlignment.center,
                //         children: [
                //           Image.asset(
                //             'images/khalti.png',
                //             height: 30,
                //           ),
                //           SizedBox(
                //             height: 5,
                //           ),
                //           Text(
                //             "Pay With Khalti",
                //             style: labelStyle2.copyWith(color: khaltiColor),
                //           )
                //         ],
                //       ),
                //     ),
                //   ),
                // ),
              ],
            ),
          ]),
    );
  }
}
