import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/registration_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:pdf/pdf.dart';
import 'package:pdf/widgets.dart' as pw;

/**
 * PDF GENERATION FOR SINGLE TRANSACTION REPORT
 *
 * FIXED: Purchase Return labeling issue where "Cash Purchase Return"
 * was incorrectly showing as "Cash Purchase" in PDF reports.
 *
 * Now ensures correct transaction type display by:
 * 1. Using proper TxnType mapping for accurate labels
 * 2. Fallback to model's txnTypeText if mapping fails
 * 3. Enhanced null safety for transaction data
 */
Future<Uint8List> generateSingleTransactionReport(PdfPageFormat pageFormat,
    {List<AllTransactionModel>? transactions,
    String billTitle = "",
    String partyText = "All Party",
    String txnTypeText = "All Transaction",
    String? startDate,
    String? endDate}) async {
  double total = 0.0;

  final products = <Product>[
    ...transactions!.map((e) {
      total += e.txnTotalAmount ?? 0.0;

      // FIXED: Ensure correct transaction type text display
      // Use TxnType mapping first, fallback to model's txnTypeText
      String correctTxnTypeText = _getCorrectTransactionTypeText(e);

      return Product(
          NepaliDateTime.parse(toDateBS(DateTime.parse(e.txnDate!)))
              .format("y/MM/dd"),
          e.txnRefNumberChar ?? "",
          e.ledgerTitle ?? "",
          correctTxnTypeText, // FIXED: Use corrected transaction type text
          e.txnTotalAmount ?? 0.0,
          e.txnCashAmount ?? 0.0,
          e.txnCashAmount ?? 0.0,
          e.txnBalanceAmount ?? 0.0);
    }).toList()
  ];

  RegistrationDetailController registrationDetailController =
      Get.put(RegistrationDetailController());
  // Get.find(tag: "RegistrationDetailController");
  ImageModel sellerImageModel = registrationDetailController.logo;

  RegistrationDetailModel myDetail =
      registrationDetailController.registrationDetail;

  final invoice = Invoice(
    myDetail: myDetail,
    sellerImage: sellerImageModel,
    txnType: txnTypeText,
    partyName: partyText,
    startDate: NepaliDateTime.parse(toDateBS(DateTime.parse(startDate ?? "")))
        .format("y/MM/dd"),
    endDate: NepaliDateTime.parse(toDateBS(DateTime.parse(endDate ?? "")))
        .format("y/MM/dd"),
    products: products,
    totalAmount: total,
    reportTitle: billTitle,
    baseColor: PdfColors.lightBlue,
    accentColor: PdfColors.blueGrey900,
  );

  return await invoice.buildPdf(pageFormat);
}

class Invoice {
  Invoice(
      {this.myDetail,
      this.sellerImage,
      this.products,
      this.baseColor,
      this.accentColor,
      this.startDate,
      this.totalAmount,
      this.endDate,
      this.partyName,
      this.txnType,
      this.reportTitle});

  final RegistrationDetailModel? myDetail;
  final ImageModel? sellerImage;

  final List<Product>? products;
  final PdfColor? baseColor;
  final PdfColor? accentColor;
  final String? startDate;
  final String? endDate;
  final String? partyName;
  final String? txnType;
  final String? reportTitle;

  final double? totalAmount;

  static const _darkColor = PdfColors.blueGrey800;
  static const _lightColor = PdfColors.black;
  // ignore: constant_identifier_names
  static const _VedColor = PdfColor.fromInt(0xFF3560AF);
  PdfColor get _baseTextColor =>
      baseColor!.luminance < 0.5 ? _lightColor : _darkColor;

  var currencyInWords = NepaliNumberFormat(
    inWords: true,
    language: Language.english,
    isMonetory: true,
    decimalDigits: 2,
  );

  pw.Widget _tableTotal(pw.Context context) {
    return pw.Container(
        padding: pw.EdgeInsets.only(top: 10),
        child: pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.end,
            children: [pw.Text("Total $txnType : Rs. ${totalAmount}")]));
  }

  pw.Widget _buildHeader(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.start,
          children: [
            pw.Expanded(
              child: pw.Column(
                mainAxisSize: pw.MainAxisSize.min,
                children: [
                  if (null != sellerImage!.imageBitmap) ...{
                    pw.Row(
                        mainAxisAlignment: pw.MainAxisAlignment.center,
                        children: [
                          pw.Container(
                            margin: const pw.EdgeInsets.only(top: -20),
                            alignment: pw.Alignment.center,
                            height: 60,
                            width: 60,
                            child: sellerImage?.imageBitmap != null
                                ? pw.Image(pw.MemoryImage(Uint8List.fromList(
                                    sellerImage!.imageBitmap!)))
                                : pw.PdfLogo(),
                          ),
                        ])
                  },
                  // pw.Container(
                  //   color: baseColor,
                  //   padding: pw.EdgeInsets.only(top: 3),
                  // ),
                ],
              ),
            ),
          ],
        ),
        pw.Row(crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
          pw.Expanded(
              child: pw.Column(mainAxisSize: pw.MainAxisSize.min, children: [
            pw.Container(
              margin: const pw.EdgeInsets.only(top: 10),
              alignment: pw.Alignment.center,
              child: pw.Text(
                myDetail!.businessName ?? "",
                style: pw.TextStyle(
                  color: _VedColor,
                  fontWeight: pw.FontWeight.bold,
                  fontSize: 16,
                ),
              ),
            ),
            pw.Container(
                margin: const pw.EdgeInsets.only(),
                child: pw.Text(
                  myDetail!.businessAddress ?? "",
                  style: pw.TextStyle(
                    color: accentColor,
                    fontSize: 8,
                  ),
                )),
            if (null != myDetail!.tinNo && "" != myDetail!.tinNo)
              pw.Container(
                  margin: const pw.EdgeInsets.only(),
                  child: pw.Text(
                    "${myDetail?.tinFlag ?? ''} No. :${myDetail!.tinNo ?? ''}",
                    style: pw.TextStyle(
                      color: accentColor,
                      fontSize: 8,
                    ),
                  )),
          ]))
        ]),
        if (context.pageNumber > 1) pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _header(pw.Context context) {
    return pw.Column(
      children: [
        pw.Row(
          crossAxisAlignment: pw.CrossAxisAlignment.center,
          children: [
            pw.Expanded(
              child: pw.Column(
                children: [
                  pw.Container(
                    margin: const pw.EdgeInsets.only(bottom: 20, top: 10),
                    alignment: pw.Alignment.center,
                    child: pw.Text(reportTitle ?? "",
                        style: pw.TextStyle(
                            color: PdfColors.red,
                            fontSize: 15,
                            // font: pw.Font.times(),
                            fontWeight: pw.FontWeight.bold)),
                  )
                ],
              ),
            ),
          ],
        ),
        pw.SizedBox(height: 20)
      ],
    );
  }

  pw.Widget _buildFooter(pw.Context context) {
    return pw.Row(
      mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
      crossAxisAlignment: pw.CrossAxisAlignment.end,
      children: [
        // pw.Container(
        //   height: 20,
        //   width: 100,
        //   child: pw.BarcodeWidget(
        //     barcode: pw.Barcode.pdf417(),
        //     data: 'Invoice# $invoiceNumber',
        //   ),
        // ),
        pw.Text(
          'Page ${context.pageNumber}/${context.pagesCount}',
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
        pw.Text(
          FOOTER_PRINT_TEXT,
          style: const pw.TextStyle(
            fontSize: 8,
            color: PdfColors.black,
          ),
        ),
      ],
    );
  }

  // pw.PageTheme _buildTheme(
  //     PdfPageFormat pageFormat, pw.Font base, pw.Font bold, pw.Font italic) {
  //   return pw.PageTheme(
  //     pageFormat: pageFormat,
  //     theme: pw.ThemeData.withFont(
  //       base: base,
  //       bold: bold,
  //       italic: italic,
  //     ),
  //     buildBackground: (context) => pw.FullPage(
  //       ignoreMargins: true,
  //     ),
  //   );
  // }

  pw.Widget _newHeader(pw.Context context) {
    return pw.Row(
      crossAxisAlignment: pw.CrossAxisAlignment.start,
      children: [
        pw.Expanded(
          flex: 2,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              // pw.Container(
              //   height: 15,
              //   child: pw.Text(
              //     'Party Name:\r $partyName',
              //     style: pw.TextStyle(
              //       color: PdfColors.black,
              //       lineSpacing: 10,
              //       fontSize: 10,
              //       fontWeight: pw.FontWeight.bold,
              //     ),
              //   ),
              // ),
              // pw.Container(
              //   height: 15,
              //   child: pw.Text(
              //     'Transaction type:\r $txnType',
              //     style: pw.TextStyle(
              //       color: PdfColors.black,
              //       lineSpacing: 5,
              //       fontSize: 10,
              //       fontWeight: pw.FontWeight.bold,
              //     ),
              //   ),
              // ),

              pw.Container(
                height: 20,
                child: pw.Text(
                  'Duration:\rFrom\r$startDate\rTo\r$endDate',
                  style: pw.TextStyle(
                    color: PdfColors.black,
                    lineSpacing: 5,
                    fontSize: 10,
                    fontWeight: pw.FontWeight.bold,
                  ),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  pw.Widget _contentTable(pw.Context context) {
    const tableHeaders = [
      'Date',
      'Particulars',
      'Txn Type',
      'Ref No',
      'Total',
    ];

    return pw.Table.fromTextArray(
      border: null,
      cellAlignment: pw.Alignment.centerLeft,
      headerDecoration: const pw.BoxDecoration(
        color: PdfColors.blue100,
      ),
      headerHeight: 24,
      cellHeight: 24,
      cellAlignments: {
        0: pw.Alignment.centerLeft,
        1: pw.Alignment.centerLeft,
        2: pw.Alignment.centerLeft,
        3: pw.Alignment.centerLeft,
        4: pw.Alignment.centerRight
      },
      headerStyle: pw.TextStyle(
        color: _baseTextColor,
        fontSize: 9.5,
        fontWeight: pw.FontWeight.bold,
      ),
      cellStyle: pw.TextStyle(
        color: accentColor,
        fontSize: 8,
      ),
      rowDecoration: pw.BoxDecoration(
        border: pw.Border(
          bottom: pw.BorderSide(
            color: accentColor!,
            width: .5,
          ),
        ),
      ),
      headers: List<String>.generate(
        tableHeaders.length,
        (col) => tableHeaders[col],
      ),
      data: List<List<String>>.generate(
        products!.length,
        (row) => List<String>.generate(
          tableHeaders.length,
          (col) => products![row].getIndex(col),
        ),
      ),
    );
  }

  // Create a PDF document.
  Future<Uint8List> buildPdf(PdfPageFormat pageFormat) async {
    final doc = pw.Document();

    // final font1 = await rootBundle.load('assets/roboto1.ttf');
    // final font2 = await rootBundle.load('assets/roboto1.ttf');
    // final font3 = await rootBundle.load('assets/roboto3.ttf');

    final font1 = await rootBundle.load('fonts/Poppins-Regular.ttf');
    final font2 = await rootBundle.load('fonts/Poppins-Bold.ttf');
    final font3 = await rootBundle.load('fonts/Poppins-Regular.ttf');

    // final font1 = null, font2 = null, font3 = null;

    // Add page to the PDF
    doc.addPage(
      pw.MultiPage(
        // pageTheme: _buildTheme(
        //   pageFormat,
        //   pw.Font.ttf(font1),
        //   pw.Font.ttf(font2),
        //   pw.Font.ttf(font3),
        // ),
        header: _buildHeader,
        footer: _buildFooter,
        build: (context) => [
          _header(context),
          _newHeader(context),
          _contentTable(context),
          _tableTotal(context),
          pw.SizedBox(height: 20),
          // _contentFooter(context),
          pw.SizedBox(height: 20),
        ],
      ),
    );

    // Return the PDF file content
    return doc.save();
  }
}

class Product {
  const Product(
    this.date,
    this.billNo,
    this.productName,
    this.txnType,
    this.totalAmount,
    this.receivedAmount,
    this.paidAmount,
    this.balanceAmount,
  );

  final String date;
  final String billNo;
  final String productName;
  final String txnType;
  final double totalAmount;
  final double receivedAmount;
  final double paidAmount;
  final double balanceAmount;

  String getIndex(int index) {
    switch (index) {
      case 0:
        return date;
      case 1:
        return productName;
      case 2:
        return txnType;
      case 3:
        return billNo;
      case 4:
        return totalAmount.toString();
      // case 5:
      //   return receivedAmount.toString();
      // case 6:
      //   return paidAmount.toString();
      // case 7:
      //   return balanceAmount.toString();
    }
    return '';
  }
}

// FIXED: Helper function to get correct transaction type text
String _getCorrectTransactionTypeText(AllTransactionModel e) {
  // Transaction type mapping
  const txnTypeMapping = {
    'Cash Purchase Return': 'Purchase Return',
    'Credit Purchase Return': 'Purchase Return',
    'Cash Sale Return': 'Sale Return',
    'Credit Sale Return': 'Sale Return',
    // Add more mappings as needed
  };

  // Get the transaction type text from the mapping
  String? mappedTxnType = txnTypeMapping[e.txnTypeText];

  // Return the mapped transaction type or fallback to the original
  return mappedTxnType ?? e.txnTypeText ?? '';
}
