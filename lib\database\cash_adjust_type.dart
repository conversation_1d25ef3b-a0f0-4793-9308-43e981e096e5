class CashAdjType {
  static final int add = 19;
  static final int reduce = 20;

  static final Map<int, String> cashAdjTypeText = ({
    CashAdjType.add: "नगद थप्नुहोस् (Add Cash)",
    CashAdjType.reduce: "नगद घटाउनुहोस् (Reduce Cash)",
  });

  static final List cashAdjTypeList = [
    {
      "value": CashAdjType.add,
      "text": CashAdjType.cashAdjTypeText[CashAdjType.add]
    },
    {
      "value": CashAdjType.reduce,
      "text": CashAdjType.cashAdjTypeText[CashAdjType.reduce]
    }
  ];
}
