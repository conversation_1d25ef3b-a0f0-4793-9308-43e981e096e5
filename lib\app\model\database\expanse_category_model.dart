class ExpenseCategoryModel {
  ExpenseCategoryModel(
      {this.expenseCategoryId,
      this.expenseTitle,
      this.lastActivityType,
      this.lastActivityAt,
      this.lastActivityBy});

  String? expenseCategoryId;
  String? expenseTitle;

  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;

  factory ExpenseCategoryModel.fromJson(Map<String, dynamic> json) =>
      ExpenseCategoryModel(
          expenseCategoryId: json["expense_category_id"],
          expenseTitle: json["expense_title"],
          lastActivityType: json["last_activity_type"],
          lastActivityAt: json["last_activity_at"],
          lastActivityBy: json['last_activity_by']);

  Map<String, dynamic> toJson() => {
        "expense_category_id": expenseCategoryId,
        "expense_title": expenseTitle,
        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        "last_activity_by": lastActivityBy
      };
}
