import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/report/daybook_report_model.dart';
import 'package:mobile_khaata_v2/app/repository/report_repository.dart';

class DayBookReportController extends GetxController {
  final String tag = "DayBookReportController";

  final ReportRepository _reportRepository = ReportRepository();

  var _txnLoading = true.obs;
  bool get txnLoading => _txnLoading.value;

  List<DayBookReportModel> transactions = [];

  var moneyIn = 0.00.obs;
  var moneyOut = 0.00.obs;

  generateDayBookReport({required String startDate}) async {
    _txnLoading(true);
    List<Map<String, dynamic>> txnDataListJson =
        await _reportRepository.getDayBookTransaction(startDate: startDate);

    moneyIn.value = 0.00;
    moneyOut.value = 0.00;
    transactions.clear();

    transactions = txnDataListJson.map((txnData) {
      DayBookReportModel txn = DayBookReportModel.fromJson(txnData);
      moneyIn.value += ((txn.dayBookTxnType == DayBookTxnType.In)
          ? txn.txnCashAmount
          : 0.00)!;
      moneyOut.value += ((txn.dayBookTxnType == DayBookTxnType.Out)
          ? txn.txnCashAmount
          : 0.00)!;
      return txn;
    }).toList();

    _txnLoading(false);
  }
}
