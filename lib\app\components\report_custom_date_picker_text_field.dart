import 'package:flutter/material.dart';
import 'package:get/get_rx/src/rx_typedefs/rx_typedefs.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import 'package:nepali_utils/nepali_utils.dart';

// ignore: must_be_immutable
class ReportCustomDatePickerTextField extends StatelessWidget {
  final Function? onChange;
  final String? initialValue;
  final bool readOnly;
  String? hintText;
  TextEditingController? dateCtrl;
  NepaliDateTime? minBSDate;
  NepaliDateTime? maxBSDate;

  ReportCustomDatePickerTextField(
      {super.key,
      this.onChange,
      this.initialValue,
      this.readOnly = false,
      this.minBSDate,
      this.maxBSDate,
      this.hintText}) {
    dateCtrl = TextEditingController(text: initialValue);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: (readOnly)
            ? null
            : () async {
                String selectedDateTime = await nepaliDatePicker(
                    context, dateCtrl?.text ?? "", minBSDate, maxBSDate);

                dateCtrl!.text = selectedDateTime;
                onChange!(selectedDateTime);
              },
        child: AbsorbPointer(
          child: TextFormField(
            readOnly: readOnly,
            textAlign: TextAlign.center,
            style: formFieldTextStyle.copyWith(),
            decoration: formFieldStyle2.copyWith(
              border: InputBorder.none,
              prefixIcon: Icon(
                Icons.calendar_today,
                size: 16,
                color: colorPrimary,
              ),
              prefixIconConstraints:
                  const BoxConstraints(maxWidth: 30, minWidth: 30),
              hintText: hintText ?? "Date",
            ),
            controller: dateCtrl,
          ),
        ));
  }
}
