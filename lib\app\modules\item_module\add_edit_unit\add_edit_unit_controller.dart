import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/unit_modal.dart';
import 'package:mobile_khaata_v2/app/repository/unit_repository.dart';

class AddEditUnitController extends GetxController {
  final String tag = "ItemController";

  var _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  var _editFlag = false.obs;
  bool get editFlag => _editFlag.value;

  var _unit = UnitModel().obs;
  UnitModel get unit => _unit.value;
  UnitRepository _unitRepository = UnitRepository();

  final formKey = GlobalKey<FormState>();

  initEdit(String unitId) async {
    _isLoading(true);
    _unit.value = await _unitRepository.getUnitById(unitId);
    _editFlag.value = true;
    _isLoading(false);
  }

  Future<bool> createUnit() async {
    bool status = false;
    try {
      String unitId = await _unitRepository.insert(unit);

      if (null != unitId) status = true;
    } catch (e, trace) {
      // Log.e(tag, e.toString()+trace.toString());
    }
    return status;
  }

  Future<bool> updateUnit() async {
    bool status = false;
    try {
      status = await _unitRepository.update(unit);
    } catch (e, trace) {
      // Log.e(tag, e.toString()+trace.toString());
    }
    return status;
  }
}
