import 'package:flutter/material.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:mobile_khaata_v2/app/controllers/package_info_controller.dart';

import 'package:mobile_khaata_v2/utilities/styles.dart';

displayContactUsDialog(BuildContext context) async {
  await showDialog(
      context: context,
      useRootNavigator: true,
      barrierDismissible: true,
      builder: (_) {
        return AlertDialog(
          insetPadding:
              const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          contentPadding: EdgeInsets.zero,
          clipBehavior: Clip.hardEdge,
          // shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          content: SizedBox(
            width: MediaQuery.of(context).size.width - 50,
            child: ContactUsDialog(),
          ),
        );
      });
}

class ContactUsDialog extends StatelessWidget {
  final packageInfoController = PackageInfoController();
  ContactUsDialog({Key? key}) : super(key: key) {
    packageInfoController.getPackages();
  }
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: double.infinity,
            color: colorPrimary,
            padding: const EdgeInsets.symmetric(vertical: 5),
            child: const Text(
              "mobile खाता",
              style: TextStyle(
                  fontSize: 24,
                  color: Colors.white,
                  fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //=================================Address
                SizedBox(
                    width: double.infinity,
                    child: Text(
                      "Head Office",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: colorPrimary,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline),
                    )),
                const SizedBox(
                  height: 1,
                ),
                SizedBox(
                    width: double.infinity,
                    child: Text(
                      "Hupra Chaur - 4",
                      textAlign: TextAlign.center,
                      style: TextStyle(color: colorPrimary, fontSize: 14),
                    )),
                const SizedBox(
                  height: 1,
                ),
                SizedBox(
                    width: double.infinity,
                    child: Text(
                      "Hetauda Sub-Metropolitan",
                      textAlign: TextAlign.center,
                      style: TextStyle(color: colorPrimary, fontSize: 14),
                    )),
                const SizedBox(
                  height: 1,
                ),
                SizedBox(
                    width: double.infinity,
                    child: Text(
                      "Makwanpur, Bagmati Province, Nepal",
                      textAlign: TextAlign.center,
                      style: TextStyle(color: colorPrimary, fontSize: 14),
                    )),
                const SizedBox(
                  height: 10,
                ),

                //=================================Contact No
                SizedBox(
                    width: double.infinity,
                    child: Text(
                      "Contact Number",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: colorPrimary,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline),
                    )),
                const SizedBox(
                  height: 2,
                ),
                InkWell(
                    onTap: () {
                      // launch('tel:057590390');
                    },
                    child: Container(
                        padding: const EdgeInsets.only(bottom: 10),
                        width: double.infinity,
                        child: Text(
                          "(+977)-57590390",
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            color: colorPrimary,
                            fontSize: 14,
                          ),
                        ))),
                const SizedBox(
                  height: 15,
                ),

                //=================================Email
                SizedBox(
                    width: double.infinity,
                    child: Text(
                      "Email",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: colorPrimary,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline),
                    )),
                const SizedBox(
                  height: 2,
                ),
                InkWell(
                  onTap: () {
                    // launch('mailto:<EMAIL>');
                  },
                  child: Container(
                      padding: const EdgeInsets.only(bottom: 10),
                      width: double.infinity,
                      child: Text(
                        "<EMAIL>",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: colorPrimary,
                          fontSize: 16,
                        ),
                      )),
                ),
                const SizedBox(
                  height: 10,
                ),

                //=================================Website
                SizedBox(
                    width: double.infinity,
                    child: Text(
                      "Website",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: colorPrimary,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline),
                    )),
                const SizedBox(
                  height: 2,
                ),
                InkWell(
                  onTap: () {
                    // launch('https://www.mobilekhaata.com');
                  },
                  child: Container(
                      padding: const EdgeInsets.only(bottom: 10),
                      width: double.infinity,
                      child: Text(
                        "www.mobilekhaata.com",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: colorPrimary,
                          fontSize: 16,
                        ),
                      )),
                ),
                const SizedBox(
                  height: 10,
                ),

                //=================================Facebook
                SizedBox(
                    width: double.infinity,
                    child: Text(
                      "Facebook",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: colorPrimary,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline),
                    )),
                const SizedBox(
                  height: 2,
                ),
                InkWell(
                  onTap: () {
                    // launch('https://www.facebook.com/Mobilekhaata');
                  },
                  child: Container(
                      padding: const EdgeInsets.only(bottom: 10),
                      width: double.infinity,
                      child: Text(
                        "www.facebook.com/Mobilekhaata",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: colorPrimary,
                          fontSize: 16,
                        ),
                      )),
                ),
                const SizedBox(
                  height: 10,
                ),

                //=================================Youtube
                SizedBox(
                    width: double.infinity,
                    child: Text(
                      "Youtube",
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          color: colorPrimary,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          decoration: TextDecoration.underline),
                    )),
                const SizedBox(
                  height: 2,
                ),
                InkWell(
                  onTap: () {
                    // launch(
                    //     'https://www.youtube.com/channel/UCihICrdjhdgB-tT0ud8YgmA');
                  },
                  child: Container(
                      padding: const EdgeInsets.only(bottom: 10),
                      width: double.infinity,
                      child: Text(
                        "www.youtube.com",
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          color: colorPrimary,
                          fontSize: 16,
                        ),
                      )),
                ),
                const SizedBox(
                  height: 15,
                ),

                //=================================Version no
                Obx(() {
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text("Version : ${packageInfoController.versionName}",
                          style: TextStyle(
                              color: Colors.black.withOpacity(0.5),
                              fontSize: 14)),
                      // SizedBox(
                      //   width: 10,
                      // )
                    ],
                  );
                }),
                const SizedBox(
                  height: 15,
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
