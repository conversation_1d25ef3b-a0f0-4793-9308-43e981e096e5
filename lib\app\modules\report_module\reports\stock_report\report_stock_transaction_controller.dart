import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/item_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';
import 'package:mobile_khaata_v2/app/repository/report_repository.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

/**
 * STOCK REPORT CONTROLLER
 *
 * This controller manages various stock reports including:
 * - Stock Summary Report: Current stock levels and values
 * - Low Stock Report: Items below minimum stock levels
 * - Stock Detail Report: Stock movements over a period
 *
 * CALCULATION LOGIC:
 * - Balance Quantity = In Quantity - Out Quantity + Opening Stock
 * - Average Purchase Rate = (Total Purchase Amount + Opening Value) / (Total Purchase Qty + Opening Stock)
 * - Stock Value = Average Purchase Rate × Balance Quantity
 */
class ReportStockTransactionController extends GetxController {
  final String tag = "ReportStockTransactionController";

  ReportRepository _reportRepository = ReportRepository();

  var _txnLoading = true.obs;
  bool get txnLoading => _txnLoading.value;

  // FIXED: Make totalQty observable and reset properly
  var totalQty = 0.00.obs;
  var totalAvgValue = 0.00.obs;

  List<ItemDetailModel> itemDetailList = [];

  /**
   * GENERATE STOCK SUMMARY REPORT
   *
   * Creates a summary of all items with their current stock levels and values
   *
   * @param endDate - The date for which to calculate stock levels
   */
  generateStockSummaryReport({required String endDate}) async {
    _txnLoading(true);

    // FIXED: Reset totals before calculation to prevent accumulation
    totalQty.value = 0.00;
    totalAvgValue.value = 0.00;

    List<Map<String, dynamic>> dataListJson =
        await _reportRepository.getStockSummary(endDate);

    itemDetailList.clear();

    itemDetailList = dataListJson.map((row) {
      ItemDetailModel itemDtl = ItemDetailModel.fromJson(row);

      // CALCULATE BALANCE QUANTITY
      // Balance = In Quantity - Out Quantity (basic stock calculation)
      itemDtl.balanceQuantity =
          (itemDtl.inQuantity ?? 0.0) - (itemDtl.outQuantity ?? 0.0);

      // CALCULATE AVERAGE PURCHASE RATE
      // Avg Rate = (Total Purchase Amount + Opening Value) / (Total Purchase Qty + Opening Stock)
      // FIXED: Added null safety and division by zero check
      final totalPurchaseQty = (itemDtl.totalPurchaseQuantity ?? 0.0);
      final openingStock = (itemDtl.openingStock ?? 0.0);
      final totalPurchaseAmount = (itemDtl.totalPurchaseAmount ?? 0.0);
      final itemPurchaseUnitPrice = (itemDtl.itemPurchaseUnitPrice ?? 0.0);

      final denominator = totalPurchaseQty + openingStock;

      if (denominator == 0) {
        // No purchases or opening stock - use current purchase price or 0
        itemDtl.avgPurchaseRate = itemPurchaseUnitPrice;
      } else {
        // Calculate weighted average purchase rate
        final openingValue = openingStock * itemPurchaseUnitPrice;
        itemDtl.avgPurchaseRate =
            (totalPurchaseAmount + openingValue) / denominator;
      }

      // FIXED: Added null safety to prevent crashes
      final balanceQty = itemDtl.balanceQuantity ?? 0.0;
      final avgRate = itemDtl.avgPurchaseRate ?? 0.0;

      // ACCUMULATE TOTALS
      totalQty.value += balanceQty;
      totalAvgValue.value += (avgRate * balanceQty);

      return itemDtl;
    }).toList();

    _txnLoading(false);
  }

  /**
   * GENERATE LOW STOCK REPORT
   *
   * Identifies items that are below their minimum stock levels
   *
   * @param endDate - The date for which to check stock levels
   */
  generateLowStockReport({required String endDate}) async {
    _txnLoading(true);

    // Reset totals
    totalQty.value = 0.00;
    totalAvgValue.value = 0.00;

    List<Map<String, dynamic>> dataListJson =
        await _reportRepository.getStockSummary(endDate);

    itemDetailList.clear();

    for (int i = 0; i < dataListJson.length; i++) {
      ItemDetailModel itemDtl = ItemDetailModel.fromJson(dataListJson[i]);

      // Calculate current balance
      itemDtl.balanceQuantity =
          (itemDtl.inQuantity ?? 0.0) - (itemDtl.outQuantity ?? 0.0);

      // FIXED: Improved low stock detection logic
      final minStockQty = parseDouble(itemDtl.itemMinStockQuantity) ?? 0.0;
      final currentQty = itemDtl.balanceQuantity ?? 0.0;

      // Only include items that are below minimum stock level
      if (currentQty <= minStockQty && minStockQty > 0) {
        itemDetailList.add(itemDtl);

        // Add to totals for low stock items only
        totalQty.value += currentQty;
      }
    }

    _txnLoading(false);
  }

  /**
   * GENERATE STOCK DETAIL REPORT
   *
   * Shows detailed stock movements over a specific period
   *
   * @param startDate - Period start date
   * @param endDate - Period end date
   */
  generateStockDetailReport(
      {required String startDate, required String endDate}) async {
    _txnLoading(true);

    // Reset totals
    totalQty.value = 0.00;
    totalAvgValue.value = 0.00;

    List<Map<String, dynamic>> dataListJson = await _reportRepository
        .getStockDetail(startDate: startDate, endDate: endDate);

    List<Map<String, dynamic>> openingDataListJson =
        await ItemRepository().getOpeningStockBalance(openingDate: startDate);

    itemDetailList.clear();

    for (int i = 0; i < dataListJson.length; i++) {
      ItemDetailModel itemDtl = ItemDetailModel.fromJson(dataListJson[i]);

      // FIXED: Improved opening quantity handling with null safety
      final openingQty = i < openingDataListJson.length
          ? (openingDataListJson[i]["balance_quantity"] as double?) ?? 0.0
          : 0.0;

      itemDtl.openingQuantity = openingQty;

      // Calculate final balance: Opening + In - Out
      itemDtl.balanceQuantity = openingQty +
          ((itemDtl.inQuantity ?? 0.0) - (itemDtl.outQuantity ?? 0.0));

      itemDetailList.add(itemDtl);

      // FIXED: Proper type conversion and null safety
      totalQty.value += (itemDtl.balanceQuantity ?? 0.0);
    }

    _txnLoading(false);
  }
}
