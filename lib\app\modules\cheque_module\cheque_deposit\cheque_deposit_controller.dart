import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/payment_type_model.dart';
import 'package:mobile_khaata_v2/app/model/others/cheque_modal.dart';
import 'package:mobile_khaata_v2/app/repository/cheque_repository.dart';
import 'package:mobile_khaata_v2/app/repository/payment_type_repository.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';

import '../../../../utilities/constants.dart';

class ChequeDepositController extends GetxController {
  final String tag = "ChequeDepositController";

  var _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  var _editFlag = false.obs;
  bool get editFlag => _editFlag.value;

  var _readOnlyFlag = false.obs;
  bool get readOnlyFlag => _readOnlyFlag.value;
  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  ChequeModel _cheque = ChequeModel();
  ChequeModel get cheque => _cheque;

  final ChequeRepository _chequeRepository = ChequeRepository();

  List<PaymentTypeModel> banks = [];
  PaymentTypeRepository paymentTypeRepository = PaymentTypeRepository();

  final formKey = GlobalKey<FormState>();

  init(String chequeTxnId, {bool notIncludeCash = false}) async {
    _isLoading.value = true;

    // ignore: unnecessary_new
    if (!notIncludeCash) {
      banks.add(new PaymentTypeModel(
          pmtTypeId: PAYMENT_MODE_CASH_ID, pmtTypeShortName: "Cash"));
    }
    banks.addAll(await paymentTypeRepository.getAllBank());

    _cheque = (await _chequeRepository.getChequeById(chequeTxnId))!;

    if (null == cheque.chequeTransferredToAccId ||
        cheque.chequeTransferredToAccId!.isEmpty) {
      cheque.chequeTransferredToAccId = PAYMENT_MODE_CASH_ID;
      cheque.chequeTransferDateBS = currentDateBS;
    } else {
      _editFlag.value = true;
      readOnlyFlag = true;
    }
    // else if(null!=cheque.chequeTransferredToAccId && cheque.chequeTransferredToAccId.isNotEmpty){

    _isLoading.value = false;
  }

  Future<bool> closeCheque() async {
    bool status = false;
    try {
      ChequeModel newChequeModel = ChequeModel();
      newChequeModel = cheque;
      newChequeModel.chequeCurrentStatus = 1;
      newChequeModel.chequeTransferDate = toDateAD(
          NepaliDateTime.parse(newChequeModel.chequeTransferDateBS ?? ""));

      status = await _chequeRepository.updateChequeStatus(newChequeModel);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }
}
