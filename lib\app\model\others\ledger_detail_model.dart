import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class LedgerDetailModel {
  LedgerDetailModel(
      {this.ledgerId,
      this.ledgerTitle,
      this.contactPersonName,
      this.ledgerType,
      this.countryCode,
      this.mobileNo,
      this.ledgerPhoto,
      this.balanceAmount,
      this.lastTxnDate,
      this.lastTxnDateBS,
      this.address,
      this.tinNo,
      this.tinFlag,
      this.openingBalance,
      this.openingDate,
      this.openingDateBS,
      this.openingType,
      this.lastActivityType,
      this.lastActivityAt,
      this.lastActivityBy});

  String? ledgerId;
  String? ledgerTitle;
  String? contactPersonName;
  String? ledgerType;
  String? countryCode;
  String? mobileNo;
  String? ledgerPhoto;
  double? balanceAmount;
  String? lastTxnDate;
  String? lastTxnDateBS;
  String? address;
  String? tinNo;
  String? tinFlag;
  double? openingBalance;
  String? openingDate;
  String? openingDateBS;
  int? openingType;
  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;

  factory LedgerDetailModel.fromJson(Map<String, dynamic> json) {
    DateTime? openingDateTimeObj =
        DateTime.tryParse(json["opening_date"] ?? "");
    // Log.d("parsinng  $openingDateTimeObj $json");
    return LedgerDetailModel(
        ledgerId: json["ledger_id"],
        ledgerTitle: json["ledger_title"],
        contactPersonName: json["contact_person_name"],
        ledgerType: json["ledger_type"],
        countryCode: json["country_code"],
        mobileNo: json["mobile_no"],
        ledgerPhoto: json["ledger_photo"],
        balanceAmount: parseDouble(
            parseDouble(json["balance_amount"] ?? "0.00")!.toStringAsFixed(2)),
        tinNo: json['tin_no'],
        tinFlag: json['tin_flag'],
        openingBalance: json["opening_balance"],
        openingDate: (null != openingDateTimeObj)
            ? DateFormat('y-MM-dd').format(openingDateTimeObj)
            : '',
        openingDateBS:
            (null != openingDateTimeObj) ? toDateBS(openingDateTimeObj) : '',
        openingType: json["opening_type"] is int
            ? json["opening_type"]
            : int.tryParse(json["opening_type"]?.toString() ?? ""),
        address: json['address'],
        lastActivityType: json["last_activity_type"],
        lastActivityAt: json["last_activity_at"],
        lastActivityBy: json["last_activity_by"]);
  }

  Map<String, dynamic> toJson() => {
        "ledger_id": ledgerId,
        "ledger_title": ledgerTitle,
        "contact_person_name": contactPersonName,
        "ledger_type": ledgerType,
        "country_code": countryCode,
        "mobile_no": mobileNo,
        "ledger_photo": ledgerPhoto,
        "balance_amount": balanceAmount,
        "last_txn_date": lastTxnDate,
        "last_txn_date_bs": lastTxnDateBS,
        "tin_no": tinNo,
        "tin_flag": tinFlag,
        "opening_balance": openingBalance,
        "opening_date": openingDate,
        "opening_date_bs": openingDateBS,
        "opening_type": openingType,
        "address": address,
        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        "last_activity_by": lastActivityBy
      };

  @override
  String toString() {
    return '$ledgerTitle $ledgerId'.toLowerCase() +
        ' $ledgerTitle'.toUpperCase();
  }
}
