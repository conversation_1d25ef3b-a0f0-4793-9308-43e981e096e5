import 'package:mobile_khaata_v2/app/model/others/synced_batched_model.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';

class SyncedBatchRepository {
  final String tag = "SyncedBatchRepository";
  final String tableName = 'mk_synced_task';

  DatabaseHelper databaseHelper = new DatabaseHelper();

  Future<bool> pushSyncedBatchId(SyncedBatchModel syncedBatchModel,
      {dynamic dbClient}) async {
    bool status = false;

    if (null == dbClient) {
      dbClient = await databaseHelper.database;
    }
    await dbClient.insert(tableName, syncedBatchModel.toJson());
    status = true;
    return status;
  }

  Future<bool> deleteSyncedBatches(List<SyncedBatchModel> batchIDS,
      {dynamic dbClient}) async {
    bool status = false;
    if (null == dbClient) {
      dbClient = await databaseHelper.database;
    }
    await dbClient.transaction((txn) async {
      var batch = txn.batch();
      batchIDS.forEach((syncedBatch) {
        batch.delete(tableName,
            where: 'batch_id  = ?', whereArgs: [syncedBatch.batchID]);
      });
      await batch.commit(continueOnError: false);
      status = true;
    });
    return status;
  }

  Future<List<SyncedBatchModel>> getSyncedBatchIDs({dynamic dbClient}) async {
    List<SyncedBatchModel> syncedBatches = [];
    // Log.d("getting batched ids 0");
    try {
      if (null == dbClient) {
        // Log.d("getting batched ids 1");
        SharedPreferences _prefs = await SharedPreferences.getInstance();
        String databaseName = _prefs.getString(CurrentDatabase)!;

        // Log.d("database ==> init database");
        // Log.d("database ==>" + databaseName);

        dbClient = await databaseHelper.database;
      }
      // Log.d("getting batched ids");

      List<Map<String, dynamic>> syncedBatchesJson =
          await dbClient.query(tableName, limit: 100);

      syncedBatches =
          syncedBatchesJson.map((e) => SyncedBatchModel.fromJson(e)).toList();
      print("Synced batch IDs fetched: ${syncedBatches.length}");
    } catch (e) {
      print("error" + tag + e.toString());
    }

    return syncedBatches;
  }
}
