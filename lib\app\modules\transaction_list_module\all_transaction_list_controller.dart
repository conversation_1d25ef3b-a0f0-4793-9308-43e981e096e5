import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/repository/all_transaction_repository.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';

class AllTransactionListController extends GetxController {
  final String tag = "AllTransactionListController";

  var _txnLoading = true.obs;
  bool get txnLoading => _txnLoading.value;

  var _isSearching = true.obs;
  bool get isSearching => _isSearching.value;

  List<AllTransactionModel> transactions = [];

  var _filteredTransactions = <AllTransactionModel>[].obs;
  List<AllTransactionModel> get filteredTransactions => _filteredTransactions;

  List<int> selectedTypes = TxnType.allTypes;

  AllTransactionRepository allTransactionRepository =
      new AllTransactionRepository();

  init() {
    refresh();
  }

  refresh() async {
    _txnLoading(true);

    transactions = await allTransactionRepository.getAllTransactions(
        types: TxnType.financialTxnTypeList);

    // Log.d("This is transactions ${transactions.length}");

    _filteredTransactions.clear();
    _filteredTransactions.addAll(transactions);
    // Future.delayed(Duration(seconds: 10), () {
    _txnLoading(false);
    // });
  }

  getTransactionsFor({List<int>? types}) async {
    _isSearching(true);
    transactions =
        await allTransactionRepository.getAllTransactions(types: types);
    _filteredTransactions.clear();
    _filteredTransactions.addAll(transactions);
    _isSearching(false);
  }

  searchTransaction(String searchString) {
    _isSearching(true);
    _filteredTransactions.clear();
    transactions.forEach((AllTransactionModel item) {
      _filteredTransactions.addIf(
          item
              .toString()
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          item);
    });
    _isSearching(false);
  }
}
