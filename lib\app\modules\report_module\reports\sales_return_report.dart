import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/report_controllers/report_transaction_controller.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';

// ignore: must_be_immutable
class SalesReturnReport extends StatelessWidget {
  final ReportTransactionController _controller = ReportTransactionController();
  List<int> types = [TxnType.salesReturn];
  String startDate = currentDate;
  String endDate = currentDate;

  SalesReturnReport({super.key}) {
    generate();
  }

  generate() {
    _controller.generateReport(
      startDate: startDate,
      endDate: endDate,
      types: types,
    );
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        // resizeToAvoidBottomPadding: true,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          elevation: 0,
          titleSpacing: -5.0,
          backgroundColor: colorPrimary,
          title: const Text(
            "बिक्री फिर्ता रिपोर्ट\n(Sales Return Report)",
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold,
            ),
          ),
          actions: [
            PrintButton(
              onPressed: () {
                TransactionHelper.gotoSingleTransactionPrintPage(
                  context,
                  txnTypeText: TxnType.txnTypeText[types[0]],
                  pageTitle: "Sales Return Report",
                  transactions: _controller.transactions,
                  startDate: startDate,
                  endDate: endDate,
                );
              },
            )
          ],
        ),
        body: Container(
          color: Colors.black12,
          child: Column(
            children: [
              //=============================transaction date filter
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: ReportCustomDatePickerTextField(
                        initialValue: toDateBS(DateTime.parse(startDate)),
                        hintText: "From Date",
                        onChange: (selectedDate) {
                          startDate = toDateAD(
                            NepaliDateTime.parse(selectedDate),
                          );
                          generate();
                        },
                      ),
                    ),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Text(
                          "TO",
                          style: labelStyle2,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: ReportCustomDatePickerTextField(
                        initialValue: toDateBS(
                          DateTime.parse(endDate),
                        ),
                        hintText: "To Date",
                        onChange: (selectedDate) {
                          endDate = toDateAD(
                            NepaliDateTime.parse(selectedDate),
                          );
                          generate();
                        },
                      ),
                    ),
                  ],
                ),
              ),

              const Divider(
                height: 0,
                color: Colors.black54,
              ),

              Obx(() {
                if (_controller.txnLoading) {
                  return Container(
                    color: Colors.white,
                    child: const Center(
                      child: CircularProgressIndicator(),
                    ),
                  );
                }

                if (_controller.transactions.isEmpty) {
                  return Container(
                    color: Colors.white,
                    width: double.infinity,
                    child: const Center(
                      child: Text(
                        "No Records",
                        style: TextStyle(
                          color: Colors.black54,
                        ),
                      ),
                    ),
                  );
                } else {
                  return Expanded(
                    child: _TxnListView(
                      _controller.transactions,
                    ),
                  );
                }
              }),
            ],
          ),
        ),
        // extendBody: true,
        bottomNavigationBar: Container(
          height: 45,
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          color: colorPrimary,
          child: SingleChildScrollView(
            child: Obx(
              () {
                return DefaultTextStyle(
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: Text(
                          "No. of Txn: "
                          "${_controller.transactions.length}",
                        ),
                      ),
                      Expanded(
                        flex: 2,
                        child: Text(
                          "Sales Return Total: "
                          "${formatCurrencyAmount(_controller.totalAmount.value, false)}",
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class _TxnListView extends StatelessWidget {
  final List<AllTransactionModel> _transactionList;

  const _TxnListView(this._transactionList);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: _transactionList.length,
      // shrinkWrap: true,
      itemBuilder: (context, int index) {
        AllTransactionModel txn = _transactionList[index];

        return InkWell(
          // onTap: () => TransactionHelper.gotoTransactionEditPage(
          //     context, txn.txnId, txn.txnType),
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                DefaultTextStyle(
                  style: TextStyle(fontSize: 12, color: colorPrimary),
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 10,
                      horizontal: 5,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        //====================================1st Column
                        Expanded(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${txn.txnDisplayName != null && txn.txnDisplayName!.isNotEmpty ? txn.txnDisplayName : txn.ledgerTitle}${txn.txnDisplayName ?? txn.ledgerTitle}",
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(
                                height: 5,
                              ),
                              Text(
                                "${txn.txnDateBS}",
                                style: const TextStyle(
                                  color: Colors.black54,
                                ),
                              ),
                              Text(
                                (null != txn.txnRefNumberChar)
                                    ? "${txn.txnTypeText}: #${txn.txnRefNumberChar}"
                                    : "${txn.txnTypeText}",
                                textAlign: TextAlign.left,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          width: 20,
                        ),

                        //====================================2nd Column
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              //===============================================Discount
                              if (0.00 !=
                                  parseDouble(txn.txnDiscountAmount)) ...{
                                Text(
                                  "Dis: "
                                  "${formatCurrencyAmount(txn.txnDiscountAmount!, false)}",
                                  textAlign: TextAlign.right,
                                ),
                                const Divider(
                                  height: 10,
                                ),
                              },

                              Text(
                                "Total: ${formatCurrencyAmount(txn.txnTotalAmount!, false)}",
                                textAlign: TextAlign.right,
                              ),

                              //===============================================VAT
                              if (0.00 != parseDouble(txn.txnTaxAmount)) ...{
                                const Divider(
                                  height: 10,
                                ),
                                Text(
                                  "VAT@${txn.txnTaxPercent}%: "
                                  "${formatCurrencyAmount(txn.txnTaxAmount!, false)}",
                                  textAlign: TextAlign.right,
                                ),
                              },
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                //Add space if last element
                if (_transactionList.length - 1 == index) ...{const SizedBox()},
              ],
            ),
          ),
        );
      },
    );
  }
}
