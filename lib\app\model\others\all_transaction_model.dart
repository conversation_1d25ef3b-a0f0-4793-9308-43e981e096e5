import 'dart:convert';

import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class AllTransactionModel {
  AllTransactionModel({
    this.txnId,
    this.txnDate,
    this.txnDateBS,
    this.txnRefNumberChar,
    this.txnType,
    this.ledgerId,
    this.txnCashAmount = 0.00,
    this.txnBalanceAmount = 0.00,
    this.txnDiscountPercent = 0.00,
    this.txnDiscountAmount = 0.00,
    this.txnTaxPercent = 0.00,
    this.txnTaxAmount = 0.00,
    this.txnDescription,
    this.txnDisplayName,
    this.lastActivityType,
    this.lastActivityAt,
    this.lastActivityBy,
    this.txnSubTotalAmount,
    this.txnTaxableTotalAmount,
    this.txnTotalAmount,
    this.expenseCategoryId,
    this.expenseCategoryName,
    this.txnTypeText,
    this.ledgerTitle,
    this.ledgerAddress,
    this.ledgerTinFlag,
    this.ledgerTinNo,
  });

  String? txnId;
  String? txnDate;
  String? txnDateBS;
  String? txnRefNumberChar;
  int? txnType;
  String? ledgerId;

  String? expenseCategoryId;
  String? expenseCategoryName;
  String? txnTypeText;

  double? txnCashAmount;
  double? txnBalanceAmount;
  double? txnSubTotalAmount;
  double? txnTaxableTotalAmount;
  double? txnTotalAmount;

  double? txnDiscountPercent;
  double? txnDiscountAmount;
  double? txnTaxPercent;
  double? txnTaxAmount;
  String? txnDescription;
  String? txnDisplayName;

  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;

  String? ledgerTitle;
  String? ledgerAddress;
  String? ledgerTinNo;
  String? ledgerTinFlag;

  factory AllTransactionModel.fromJson(Map<String, dynamic> json) {
    // Format date
    DateTime txnDateTime = DateTime.parse(json["txn_date"]);
    String txnDate = DateFormat('y-MM-dd').format(txnDateTime);
    String txnDateBS = toDateBS(txnDateTime);

    // AMOUNT CALCULATION EXPLANATION:
    // For accounting purposes, the calculation order should be:
    // 1. SubTotal = Base amount before any adjustments
    // 2. Discount = Reduction from subtotal
    // 3. Taxable Amount = SubTotal - Discount
    // 4. Tax = Applied on taxable amount
    // 5. Total = Taxable Amount + Tax = Cash Amount + Balance Amount

    // Parse individual amounts with null safety
    double cashAmount = parseDouble(json['txn_cash_amount']) ?? 0.0;
    double balanceAmount = parseDouble(json['txn_balance_amount']) ?? 0.0;
    double taxAmount = parseDouble(json['txn_tax_amount']) ?? 0.0;
    double discountAmount = parseDouble(json['txn_discount_amount']) ?? 0.0;

    // CORRECTED CALCULATION ORDER:
    // Total = Cash Amount + Balance Amount (this is the final amount paid/received)
    double total =
        parseDouble((cashAmount + balanceAmount).toStringAsFixed(2)) ?? 0.0;

    // Taxable Amount = Total - Tax (amount before tax was applied)
    double taxableAmount =
        parseDouble((total - taxAmount).toStringAsFixed(2)) ?? 0.0;

    // SubTotal = Taxable Amount + Discount (original amount before discount and tax)
    double subTotal =
        parseDouble((taxableAmount + discountAmount).toStringAsFixed(2)) ?? 0.0;

    String? displayName = strTrim(json["txn_display_name"] ?? "");

    return AllTransactionModel(
      txnId: json["txn_id"],
      txnDate: txnDate,
      txnDateBS: txnDateBS,
      txnRefNumberChar: json["txn_ref_number_char"],
      txnType: json["txn_type"],
      txnTypeText: TxnType.txnTypeText[json["txn_type"]],
      ledgerId: json["ledger_id"],
      txnDiscountPercent: parseDouble(json["txn_discount_percent"]),
      txnTaxPercent: parseDouble(json["txn_tax_percent"]),
      txnDescription: json["txn_description"],
      txnDisplayName: displayName,
      lastActivityType: json["last_activity_type"],
      lastActivityAt: json["last_activity_at"],
      lastActivityBy: json['last_activity_by'],
      expenseCategoryId: json["expense_category_id"],
      expenseCategoryName: json["expense_title"],
      ledgerTitle: json['ledger_title'],
      ledgerAddress: json['ledger_address'],
      ledgerTinNo: json['tin_no'],
      ledgerTinFlag: json['tin_flag'],

      // AMOUNT ASSIGNMENTS WITH COMMENTS:
      txnCashAmount: cashAmount, // Cash portion of payment
      txnBalanceAmount: balanceAmount, // Credit/balance portion of payment
      txnTaxAmount: taxAmount, // Tax amount applied
      txnDiscountAmount: discountAmount, // Discount amount given
      txnTotalAmount: total, // Final total (cash + balance)
      txnTaxableTotalAmount: taxableAmount, // Amount before tax
      txnSubTotalAmount: subTotal, // Amount before discount and tax
    );
  }

  Map<String, dynamic> toJson() => {
        "txn_id": txnId,
        "txn_date": txnDate,
        "txn_ref_number_char": txnRefNumberChar,
        "txn_type": txnType,
        "ledger_id": ledgerId,
        "txn_cash_amount": txnCashAmount,
        "txn_balance_amount": txnBalanceAmount,
        "txn_discount_percent": txnDiscountPercent,
        "txn_discount_amount": txnDiscountAmount,
        "txn_tax_percent": txnTaxPercent,
        "txn_tax_amount": txnTaxAmount,
        "txn_description": txnDescription,
        "txn_display_name": txnDisplayName,
        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        "last_activity_by": lastActivityBy,
        "txn_subtotal": txnSubTotalAmount,
        "txn_taxable_total": txnTaxableTotalAmount,
        "txn_total": txnTotalAmount,
        "expense_category_id": expenseCategoryId,
        "expense_title": expenseCategoryName,
        'ledger_title': ledgerTitle,
        'ledger_address': ledgerAddress,
        'tin_no': ledgerTinNo,
        'tin_flag': ledgerTinFlag,
      };

  String toString() {
    return jsonEncode(this.toJson());
  }
}
