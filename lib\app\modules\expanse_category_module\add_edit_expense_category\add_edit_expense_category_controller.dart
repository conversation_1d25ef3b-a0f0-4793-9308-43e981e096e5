import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/expanse_category_model.dart';
import 'package:mobile_khaata_v2/app/repository/expense_category_repository.dart';

class AddEditExpenseCategoryController extends GetxController {
  final String tag = "AddEditExpenseCategoryController";

  var _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  var _editFlag = false.obs;
  bool get editFlag => _editFlag.value;

  ExpenseCategoryModel? _category;
  ExpenseCategoryModel? get category => _category;
  ExpensesCategoryRepository expensesCategoryRepository =
      ExpensesCategoryRepository();

  TextEditingController newExpenseTitleController = TextEditingController();

  final formKey = GlobalKey<FormState>();

  initEdit(String categoryId) async {
    _isLoading(true);
    _editFlag.value = true;
    _category =
        await expensesCategoryRepository.getExpenseCategoriesByID(categoryId);
    newExpenseTitleController.text = _category?.expenseTitle ?? "";
    _isLoading(false);
  }

  Future<bool> createCategory() async {
    if (newExpenseTitleController.text.trim().isEmpty) return false;
    bool status = false;
    _category =
        ExpenseCategoryModel(expenseTitle: newExpenseTitleController.text);
    try {
      await expensesCategoryRepository.insert(_category!);
      status = true;
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<bool> updateCategory() async {
    bool status = false;
    try {
      status = await expensesCategoryRepository.update(category!);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }
}
