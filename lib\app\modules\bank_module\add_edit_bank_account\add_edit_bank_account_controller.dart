import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/payment_type_model.dart';
import 'package:mobile_khaata_v2/app/repository/payment_type_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:sqflite/sqflite.dart';

class AddEditBankAccountController extends GetxController {
  final String tag = "AddEditBankAccountController";

  var _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  var _editFlag = false.obs;
  bool get editFlag => _editFlag.value;

  var _readOnlyFlag = false.obs;
  bool get readOnlyFlag => _readOnlyFlag.value;
  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  var _bank = PaymentTypeModel().obs;
  PaymentTypeModel get bank => _bank.value;

  final PaymentTypeRepository _paymentTypeRepository = PaymentTypeRepository();

  final formKey = GlobalKey<FormState>();

  @override
  Future<void> onInit() async {
    _isLoading(true);

    bank.pmtTypeOpeningDateBS = currentDateBS;

    if (null != bank.pmtTypeId) {
      await initEdit();
    }

    _isLoading(false);
    super.onInit();
  }

  initEdit() async {
    _bank.value = (await _paymentTypeRepository.getBankById(bank.pmtTypeId!))!;

    _editFlag.value = true;
    readOnlyFlag = true;
  }

  Future<bool> checkUniqueShortName() async {
    bool status = await _paymentTypeRepository
        .checkUniqueShortName(bank.pmtTypeShortName!, bankId: bank.pmtTypeId);
    return status;
  }

  Future<bool> createBank() async {
    bool status = false;

    PaymentTypeModel newBankModel;

    try {
      newBankModel = bank;

      if (null != newBankModel.pmtTypeOpeningBalance) {
        newBankModel.pmtTypeOpeningDate =
            toDateAD(NepaliDateTime.parse(bank.pmtTypeOpeningDateBS!));
      } else {
        newBankModel.pmtTypeOpeningDate = null;
      }

      DatabaseHelper? databaseHelper = DatabaseHelper();
      Database? dbClient = await databaseHelper.database;

      await dbClient!.transaction((dbBatchTxn) async {
        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;
        String pmtTypeId = await _paymentTypeRepository.insert(newBankModel,
            dbClient: dbBatchTxn, batchID: batchID);

        status = true;
      });
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<bool> updateBank() async {
    bool status = false;

    PaymentTypeModel newBankModel;

    try {
      newBankModel = bank;

      if (null != newBankModel.pmtTypeOpeningBalance) {
        newBankModel.pmtTypeOpeningDate =
            toDateAD(NepaliDateTime.parse(bank.pmtTypeOpeningDateBS!));
      } else {
        newBankModel.pmtTypeOpeningDate = null;
      }

      newBankModel.pmtTypeOpeningDate =
          toDateAD(NepaliDateTime.parse(bank.pmtTypeOpeningDateBS!));

      DatabaseHelper databaseHelper = DatabaseHelper();
      Database? dbClient = await databaseHelper.database;

      await dbClient!.transaction((dbBatchTxn) async {
        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        await _paymentTypeRepository.update(newBankModel,
            dbClient: dbBatchTxn, batchID: batchID);
        status = true;
      });
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }
}
