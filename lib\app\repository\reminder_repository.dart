import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/model/database/reminder_model.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';

import 'package:sqflite/sqflite.dart';
import 'package:tuple/tuple.dart';

class ReminderRepository {
  final String tag = "ReminderRepository";
  final String tableName = "mk_reminders";
  DatabaseHelper databaseHelper = DatabaseHelper();

  QueryRepository queryRepository = QueryRepository();

  //========================================================================================= SYNCING ACTIONS
  Future<String?> insert(ReminderModel reminder,
      {dynamic dbClient, String? batchID}) async {
    String? reminderId;

    dbClient ??= await databaseHelper.database;
    try {
      String primaryKeyPrefix = await getPrimaryKeyPrefix();
      reminder.reminderId = primaryKeyPrefix + uuidV4;

      reminder.lastActivityType = LastActivityType.New;
      reminder.lastActivityAt = currentDateTime;
      reminder.lastActivityBy = await getLastActivityBy();

      await dbClient.insert(tableName, reminder.toJson());

      QueryModel newQueryModel = QueryModel(
        tableName: tableName,
        queryType: QueryType.insert,
        data: reminder.toJson(),
      );
      await queryRepository.pushQuery(newQueryModel,
          batchID: batchID, dbClient: dbClient);

      pushPendingQueries(source: "TRIGGER", dbClient: dbClient);

      reminderId = reminder.reminderId ?? "";
    } catch (e) {
      // Log.e(tag, e.toString());
    }

    return reminderId;
  }

  Future<bool> update(ReminderModel reminder,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    reminder.lastActivityType = LastActivityType.Edit;
    reminder.lastActivityAt = currentDateTime;
    reminder.lastActivityBy = await getLastActivityBy();

    String whereClause = "reminder_id = ?";
    List<dynamic> whereArgs = [reminder.reminderId];

    await dbClient.update(tableName, reminder.toJson(),
        where: whereClause, whereArgs: whereArgs);
    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.update,
      whereArgs: whereArgs,
      whereClause: whereClause,
      data: reminder.toJson(),
    );
    await queryRepository.pushQuery(newQueryModel,
        dbClient: dbClient, batchID: batchID);
    pushPendingQueries(source: "TRIGGER", dbClient: dbClient);

    status = true;

    return status;
  }

  // Future<bool> delete(String reminderId,
  //     {dynamic dbClient, String batchID}) async {
  //   bool status = false;

  //   if (dbClient == null) {
  //     dbClient = await databaseHelper.database;
  //   }

  //   String whereClause = 'reminder_id = ?';
  //   List<dynamic> whereArgs = [reminderId];

  //   await dbClient.delete(tableName, where: whereClause, whereArgs: whereArgs);

  //   QueryModel newQueryModel = new QueryModel(
  //     tableName: tableName,
  //     queryType: QueryType.delete,
  //     whereArgs: whereArgs,
  //     whereClause: whereClause,
  //   );
  //   await queryRepository.pushQuery(newQueryModel,
  //       dbClient: dbClient, batchID: batchID);

  //   status = true;

  //   return status;
  // }

  Future<Tuple2<bool, String>> delete(String reminderId,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;
    String message = "";

    dbClient ??= await databaseHelper.database;
    String whereClause = "reminder_id = ?";
    List<dynamic> whereArgs = [reminderId];
    try {
      // can soft delete for given id
      dbClient.update(
          tableName, {"last_activity_type": LastActivityType.Delete},
          where: whereClause, whereArgs: whereArgs);

      QueryModel newQueryModel = QueryModel(
          tableName: tableName,
          queryType: QueryType.update,
          whereArgs: whereArgs,
          whereClause: whereClause,
          data: {"last_activity_type": LastActivityType.Delete});

      await queryRepository.pushQuery(newQueryModel,
          batchID: batchID, dbClient: dbClient);
      status = true;
      pushPendingQueries(source: "TRIGGER", dbClient: dbClient);

      message = "Reminder deleted successfully";
    } catch (e) {
      message = "Cannot delete Reminder at this moment. Please try again later";
    }

    return Tuple2(status, message);
  }

  //=========================================================================================NON SYNCING ACTIONS
  Future<ReminderModel> getReminderById(String reminderId) async {
    ReminderModel reminder = ReminderModel();
    try {
      Database? dbClient = await databaseHelper.database;

      Map<String, dynamic> json = (await dbClient!.rawQuery(
              "SELECT * FROM mk_reminders WHERE last_activity_type!=? AND reminder_id=?",
              [LastActivityType.Delete, reminderId]))
          .first;
      reminder = ReminderModel.fromJson(json);
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return reminder;
  }

  Future<List<ReminderModel>> getUpcomingReminders([String? dateUpto]) async {
    List<ReminderModel> reminderList = [];

    try {
      Database? dbClient = await databaseHelper.database;
      String query = """
      SELECT *, 'upcoming' AS reminder_category 
      FROM mk_reminders 
      WHERE last_activity_type!=3
      AND start_datetime < '$dateUpto'
      AND start_datetime >= '$currentDate'
      ORDER BY start_datetime ASC
      """;
      if ([null, ""].contains(dateUpto)) {
        query = """
      SELECT *, 'upcoming' AS reminder_category 
      FROM mk_reminders 
      WHERE last_activity_type!=3
      AND start_datetime >= '$currentDate'
      ORDER BY start_datetime ASC
      """;
      }

      List<Map<String, dynamic>> jsonList = [];

      jsonList = await dbClient!.rawQuery(query);

      reminderList =
          jsonList.map((row) => ReminderModel.fromJson(row)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return reminderList;
  }

  Future<List<ReminderModel>> getCompletedReminders() async {
    List<ReminderModel> reminderList = [];
    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> jsonList = [];

      jsonList = await dbClient!.rawQuery(
          "SELECT *, 'completed' AS reminder_category FROM mk_reminders WHERE last_activity_type!=? AND start_datetime <? ORDER BY start_datetime DESC",
          [LastActivityType.Delete, currentDate]);

      reminderList =
          jsonList.map((row) => ReminderModel.fromJson(row)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return reminderList;
  }

  Future<List<ReminderModel>> getAllReminders(
      [String reminderType = "all"]) async {
    List<ReminderModel> reminderList = [];
    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> jsonList = [];

      if ("completed" == reminderType) {
        jsonList = await dbClient!.rawQuery(
            "SELECT *, 'completed' AS reminder_category FROM mk_reminders WHERE last_activity_type!=? ORDER BY start_datetime DESC",
            [LastActivityType.Delete]);
      } else if ("pending" == reminderType) {
        jsonList = await dbClient!.rawQuery(
            "SELECT *, 'pending' AS reminder_category FROM mk_reminders WHERE last_activity_type!=? AND start_datetime<=? ORDER BY start_datetime DESC",
            [LastActivityType.Delete, currentDateTime]);
      } else if ("upcoming" == reminderType) {
        jsonList = await dbClient!.rawQuery(
            "SELECT *, 'upcoming' AS reminder_category FROM mk_reminders WHERE last_activity_type!=? AND start_datetime>? ORDER BY start_datetime ASC",
            [LastActivityType.Delete, currentDateTime]);
      } else {
        jsonList = await dbClient!.rawQuery(
            "SELECT *, "
            "CASE WHEN start_datetime<=?  THEN 'pending' "
            "WHEN start_datetime>?  THEN 'upcoming' "
            "ELSE 'completed' END AS reminder_category "
            "FROM mk_reminders WHERE last_activity_type!=3 ORDER BY start_datetime DESC",
            [LastActivityType.Delete, currentDateTime, currentDateTime]);
      }

      reminderList =
          jsonList.map((row) => ReminderModel.fromJson(row)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return reminderList;
  }

  Future<List<ReminderModel>> getRemindersFor(String dateTime) async {
    List<ReminderModel> reminderList = [];
    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> jsonList = [];

      jsonList = await dbClient!.rawQuery(
          "SELECT *, 'today' AS reminder_category FROM mk_reminders WHERE last_activity_type!=? AND start_datetime=? ORDER BY start_datetime DESC",
          [LastActivityType.Delete, dateTime]);

      // Log.d("reminders for tomorrow $jsonList");
      reminderList =
          jsonList.map((row) => ReminderModel.fromJson(row)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return reminderList;
  }

  Future<bool> createNextScheduleFor(String dateTime,
      {dynamic dbClient}) async {
    bool status = true;
    // ignore: unnecessary_null_comparison
    if (null == dateTime) {
      return status;
    }

    try {
      // if (null == dbClient) {
      //   dbClient = await databaseHelper.database;
      // }

      Database? dbClient = await databaseHelper.database;
      String query = """
          UPDATE $tableName
          SET start_datetime = 
          CASE WHEN reminder_period = 2 THEN date(start_datetime,'+1 day')
          WHEN reminder_period = 3 THEN date(start_datetime,'+7 day')
          WHEN reminder_period = 4 THEN date(start_datetime,'+1 month')
          WHEN reminder_period = 5 THEN date(start_datetime,'+1 year')
            ELSE start_datetime END
          WHERE start_datetime = $dateTime AND reminder_period <> 1
      """;
      await dbClient!.rawQuery(query);
      status = true;
    } catch (e) {
      // Log.e(tag, e.toString());
    }

    return status;
  }

  Future<bool> clearOldReminders({dynamic dbClient}) async {
    bool status = false;
    // CLEAR notification before CLEAR_NOTIFICATION_BEFORE_DAYS
    String dateBefore = DateFormat('y-MM-dd')
        .format(DateTime.now().subtract(const Duration(days: 7)));
    try {
      dbClient ??= await databaseHelper.database;
      // Database dbClient = await databaseHelper.database;
      await dbClient.delete(tableName,
          where: 'start_datetime < ? ', whereArgs: [dateBefore]);
      status = true;
    } catch (e) {
      // Log.e(tag, e.toString());
    }

    return status;
  }

  Future<List<ReminderModel>> getTodayReminders() async {
    List<ReminderModel> reminderList = [];
    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> jsonList = [];

      jsonList = await dbClient!.rawQuery(
          "SELECT *, 'today' AS reminder_category FROM mk_reminders WHERE last_activity_type!=? AND start_datetime=? ORDER BY start_datetime DESC",
          [LastActivityType.Delete, currentDateTime]);

      reminderList =
          jsonList.map((row) => ReminderModel.fromJson(row)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return reminderList;
  }
}
