import 'package:mobile_khaata_v2/app/model/database/query_model.dart';

class BatchedGroupModel {
  String? batchId;
  List<QueryModel>? queries;
  BatchedGroupModel({
    this.batchId,
    this.queries,
  });

  Map<String, dynamic> toJson() {
    return {
      'batch_id': batchId,
      'queries': queries?.map((x) => x.toJson()).toList(),
    };
  }

  factory BatchedGroupModel.fromJson(Map<String, dynamic> map) {
    return BatchedGroupModel(
      batchId: map['batch_id'],
      queries: List<QueryModel>.from(
          map['queries']?.map((x) => QueryModel.fromJson(x))),
    );
  }
}
