import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class PaymentTypeModel {
  PaymentTypeModel({
    this.pmtTypeId,
    this.pmtTypeShortName,
    this.pmtTypeBankName,
    this.pmtTypeBankAccountName,
    this.pmtTypeBankAccountNumber,
    this.pmtTypeBankAccountBranch,
    this.pmtTypeOpeningBalance,
    this.pmtTypeOpeningDate,
    this.pmtTypeOpeningDateBS,
    this.lastActivityType,
    this.lastActivityAt,
    this.lastActivityBy,
    this.pmtTypeCurrentBalance,
  });

  String? pmtTypeId;
  String? pmtTypeShortName;
  String? pmtTypeBankName;
  String? pmtTypeBankAccountName;
  String? pmtTypeBankAccountNumber;
  String? pmtTypeBankAccountBranch;
  double? pmtTypeOpeningBalance;
  String? pmtTypeOpeningDate;
  String? pmtTypeOpeningDateBS;
  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;

  double? pmtTypeCurrentBalance;

  factory PaymentTypeModel.fromJson(Map<String, dynamic> json) {
    DateTime opnDateTime =
        DateTime.parse(json["pmt_type_opening_date"] ?? currentDate);
    String opnDate = DateFormat('y-MM-dd').format(opnDateTime);
    String opnDateBS = toDateBS(opnDateTime);

    return PaymentTypeModel(
        pmtTypeId: json["pmt_type_id"],
        pmtTypeShortName: json["pmt_type_short_name"],
        pmtTypeBankName: json["pmt_type_bank_name"],
        pmtTypeBankAccountName: json["pmt_type_bank_account_name"],
        pmtTypeBankAccountNumber: json["pmt_type_bank_account_number"],
        pmtTypeBankAccountBranch: json["pmt_type_bank_account_branch"],
        pmtTypeOpeningBalance: parseDouble(json["pmt_type_opening_balance"]),
        pmtTypeOpeningDate: opnDate,
        pmtTypeOpeningDateBS: opnDateBS,
        lastActivityType: json["last_activity_type"],
        lastActivityAt: json["last_activity_at"],
        lastActivityBy: json['last_activity_by'],
        pmtTypeCurrentBalance: parseDouble(json['current_balance']));
  }

  Map<String, dynamic> toJson() => {
        "pmt_type_id": pmtTypeId,
        "pmt_type_short_name": pmtTypeShortName,
        "pmt_type_bank_name": pmtTypeBankName,
        "pmt_type_bank_account_name": pmtTypeBankAccountName,
        "pmt_type_bank_account_number": pmtTypeBankAccountNumber,
        "pmt_type_bank_account_branch": pmtTypeBankAccountBranch,
        "pmt_type_opening_balance": pmtTypeOpeningBalance,
        "pmt_type_opening_date": pmtTypeOpeningDate,
        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        'last_activity_by': lastActivityBy
      };
}
