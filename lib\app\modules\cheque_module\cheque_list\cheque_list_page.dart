// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/model/others/cheque_modal.dart';
import 'package:mobile_khaata_v2/app/modules/cheque_module/cheque_list/cheque_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:tuple/tuple.dart';

class ChequeListPage extends StatelessWidget {
  final String tag = "ChequeListPage";

  final controller =
      Get.put(ChequeListController(), tag: "ChequeListController");

  ChequeListPage({super.key}) {
    controller.chequeType = 0;
    controller.init();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: false,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        toolbarHeight: 60,
        backgroundColor: colorPrimary,
        elevation: 4,
        leading: BackButton(
          onPressed: () => Navigator.pop(context, false),
        ),
        centerTitle: false,
        titleSpacing: -10.0,
        title: const ListTile(
          contentPadding: EdgeInsets.only(right: 15),
          title: Text(
            "चेकहरु (Cheques)",
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
      ),
      body: Container(
        padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        child: Column(
          children: [
            const SizedBox(
              height: 10,
            ),

            //===================================Deposit To
            Row(
              // crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 1,
                  child: FormBuilderDropdown(
                    name: 'show',
                    style: formFieldTextStyle,
                    decoration: formFieldStyle.copyWith(labelText: "Show"),
                    items: controller.chequeTypes.map((row) {
                      return DropdownMenuItem(
                          value: row["value"], child: Text(row["text"]));
                    }).toList(),
                    initialValue: 0,
                    onChanged: (value) {
                      controller.chequeType = value as int;
                      controller.init();
                    },
                  ),
                ),
                const SizedBox(
                  width: 10,
                ),
                Expanded(
                  flex: 1,
                  child: FormBuilderDropdown(
                    name: 'sort_by',
                    style: formFieldTextStyle,
                    decoration: formFieldStyle.copyWith(labelText: "Sort By"),
                    items: [
                      {"value": "date", "text": "Cheque Date"},
                      {"value": "amount", "text": "Amount"}
                    ].map((sortBy) {
                      return DropdownMenuItem(
                          value: sortBy["value"],
                          child: Text("${sortBy['text']}"));
                    }).toList(),
                    initialValue: controller.listOrder,
                    onChanged: (value) {
                      controller.listOrder = value!;
                      controller.init();
                    },
                  ),
                ),
              ],
            ),

            Divider(
              height: 40,
              thickness: 1,
              color: colorPrimary,
            ),
            // const SizedBox(height: 20,),

            Obx(() {
              if (controller.isLoading) {
                return Container(
                    color: Colors.white,
                    child: const Center(child: CircularProgressIndicator()));
              }

              return Expanded(child: _ChequeListView(controller.chequeList));
            })
          ],
        ),
      ),
    ));
  }
}

class _ChequeListView extends StatelessWidget {
  final List<ChequeModel> _chequeList;

  const _ChequeListView(this._chequeList);

  @override
  Widget build(BuildContext context) {
    if (_chequeList.isEmpty) {
      return const Center(
        child: Text(
          'No cheques found',
          style: TextStyle(
            fontSize: 16,
            color: Colors.grey,
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }

    return ListView.builder(
      itemCount: _chequeList.length,
      shrinkWrap: true,
      itemBuilder: (ctx, int index) {
        ChequeModel cheque = _chequeList[index];

        Color txnColor = (TxnType.cashInTransaction.contains(cheque.txnType))
            ? colorGreenDark
            : colorOrangeDark;

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 5),
          child: Card(
            elevation: 4,
            shape: Border(left: BorderSide(color: txnColor, width: 5)),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DefaultTextStyle(
                    style: TextStyle(
                        fontWeight: FontWeight.bold, color: textColor),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(cheque.txnPartyName ?? ""),
                        Text(
                          cheque.txnTypeText ?? "",
                          style: const TextStyle(fontWeight: FontWeight.normal),
                        ),
                        Text(cheque.chequeIssueDateBS ?? ""),
                      ],
                    ),
                  ),
                  const Divider(),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text("Status: ${cheque.chequeCurrentStatusText}"),
                      const SizedBox(
                        height: 5,
                      ),
                      Text("Cheque no: ${cheque.txnPaymentReference ?? ''}"),
                      const SizedBox(
                        height: 5,
                      ),
                      Text(
                        "Amount: ${formatCurrencyAmount(cheque.txnCashAmount!)}",
                        style: TextStyle(
                            color: txnColor, fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      Text(
                        "Cheque Date: ${cheque.chequeIssueDateBS}",
                        style: TextStyle(color: txnColor),
                      ),
                      const SizedBox(
                        height: 5,
                      ),
                      Text("Txn Date: ${cheque.txnDateBS}"),
                      const SizedBox(
                        height: 10,
                      ),
                    ],
                  ),
                  if (0 == cheque.chequeCurrentStatus) ...{
                    if ("Deposit" == cheque.chequeTxnType) ...{
                      //================================== Deposit Button
                      Container(
                        margin: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 5),
                        width: double.infinity,
                        child: ElevatedButton(
                          // color: txnColor,
                          // splashColor: colorPrimaryLighter,
                          onPressed: () => ChequeListController()
                              .depositWithdrawOnClickHandler(
                                  context, cheque.txnId!),
                          child: Text(
                            cheque.chequeTxnType!,
                            style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                                letterSpacing: 1),
                          ),
                        ),
                      ),
                    } else if ("Withdraw" == cheque.chequeTxnType) ...{
                      //================================== Withdraw Button
                      Container(
                        margin: const EdgeInsets.symmetric(
                            horizontal: 20, vertical: 5),
                        width: double.infinity,
                        child: ElevatedButton(
                          // color: txnColor,
                          // splashColor: colorPrimaryLighter,
                          onPressed: () => ChequeListController()
                              .depositWithdrawOnClickHandler(
                                  context, cheque.txnId!),
                          child: Text(
                            cheque.chequeTxnType!,
                            style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 14,
                                letterSpacing: 1),
                          ),
                        ),
                      ),
                    }
                  } else ...{
                    Container(
                      margin: const EdgeInsets.symmetric(
                          horizontal: 20, vertical: 5),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          //================================== View Button
                          ElevatedButton(
                            // color: colorPrimary,
                            // splashColor: colorPrimaryLighter,
                            onPressed: () {
                              TransactionHelper.gotoTransactionEditPage(
                                  context, cheque.txnId!, cheque.txnType!);
                            },
                            child: const Padding(
                              padding: EdgeInsets.symmetric(horizontal: 20),
                              child: Text(
                                "View",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  letterSpacing: 1,
                                ),
                              ),
                            ),
                          ),

                          const SizedBox(
                            width: 10,
                          ),

                          //================================== Re-open Button
                          ElevatedButton(
                            // color: colorPrimary,
                            // splashColor: colorPrimaryLighter,
                            onPressed: () async {
                              showAlertDialog(
                                context,
                                okText: "YES",
                                hasCancel: true,
                                cancelText: "NO",
                                alertType: AlertType.Error,
                                alertTitle: "Confirm Re-open cheque",
                                onCloseButtonPressed: () async {
                                  print("CLose Button Pressede");
                                  // Navigator.of(_).pop();
                                  ProgressDialog progressDialog =
                                      ProgressDialog(context,
                                          type: ProgressDialogType.normal,
                                          isDismissible: false);
                                  progressDialog.update(
                                      message:
                                          "Checking Permission. Please wait....");
                                  await progressDialog.show();

                                  Tuple2<bool, String> checkResponse =
                                      await PermissionWrapperController()
                                          .requestForPermissionCheck(
                                    forPermission:
                                        PermissionManager.chequeReopen,
                                  );

                                  if (checkResponse.item1) {
                                    //has  permission
                                    progressDialog.update(
                                        message:
                                            "Re-opening cheque. Please wait....");

                                    bool status = await ChequeListController()
                                        .reOpenCheque(cheque);
                                    await progressDialog.hide();

                                    if (status) {
                                      showAlertDialog(
                                        context,
                                        barrierDismissible: false,
                                        alertType: AlertType.Success,
                                        alertTitle: "",
                                        onCloseButtonPressed: () {
                                          // Navigator.of(_).pop();

                                          //reload cheque list
                                          TransactionHelper
                                              .refreshPreviousPages();
                                        },
                                        message: "Cheque Re-opened "
                                            "Successfully.",
                                      );
                                    } else {
                                      showAlertDialog(
                                        context,
                                        alertType: AlertType.Error,
                                        alertTitle: "",
                                        message: "Failed.",
                                      );
                                    }
                                  } else {
                                    //no  permission
                                    await progressDialog.hide();
                                    showAlertDialog(
                                      context,
                                      alertType: AlertType.Error,
                                      alertTitle: "",
                                      message: checkResponse.item2,
                                    );
                                  }
                                },
                                message: "Are you sure you want "
                                    "to re-open this cheque?",
                              );
                            },
                            child: const Padding(
                              padding: EdgeInsets.symmetric(horizontal: 10),
                              child: Text(
                                "Re-Open",
                                style: TextStyle(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  letterSpacing: 1,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  }
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
