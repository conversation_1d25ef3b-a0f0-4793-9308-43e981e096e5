import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

enum DayBookTxnType { In, Out }

class DayBookReportModel {
  String? displayName;
  String? description;
  int? txnType;
  String? txnTypeText;
  double? txnTotalAmount;
  double? txnCashAmount;
  DayBookTxnType? dayBookTxnType;

  DayBookReportModel({
    this.displayName,
    this.description,
    this.txnType,
    this.txnTypeText,
    this.txnTotalAmount,
    this.txnCashAmount,
    this.dayBookTxnType,
  });

  factory DayBookReportModel.fromJson(Map<String, dynamic> json) {
    DayBookTxnType? dayBookTxnType;
    if (TxnType.cashInTransaction.contains(json['txn_type'])) {
      dayBookTxnType = DayBookTxnType.In;
    } else if (TxnType.cashOutTransaction.contains(json['txn_type'])) {
      dayBookTxnType = DayBookTxnType.Out;
    }

    String txnTypeText = TxnType.txnTypeText[json["txn_type"]] ?? "";
    // ignore: unnecessary_null_comparison
    String description = (null != strTrim(json['display_name']))
        ? json['display_name'] + "\n" + "[$txnTypeText]"
        : "[$txnTypeText]";

    // double txnTotalAmount = parseDouble((parseDouble(json['txn_balance_amount']) + parseDouble(json['txn_cash_amount'])).toStringAsFixed(2));

    return DayBookReportModel(
      displayName: json['display_name'],
      description: description,
      txnType: json['txn_type'],
      txnTypeText: txnTypeText,
      txnTotalAmount: json['total_amount'],
      txnCashAmount: json['txn_cash_amount'],
      dayBookTxnType: dayBookTxnType,
    );
  }
}
