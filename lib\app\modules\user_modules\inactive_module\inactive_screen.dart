// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/modules/home_module/logout_controller.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/inactive_module/inactive_controller.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

class InactivePage extends StatelessWidget {
  InactivePage({Key? key}) : super(key: key);

  final inactivePageController = InactivePageController();
  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // appBar: AppBar(),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          // mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
                child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const SizedBox(
                  height: 40,
                ),
                Icon(
                  FontAwesomeIcons.usersSlash,
                  color: colorRedLight,
                  size: 60,
                ),
                const SizedBox(
                  height: 40,
                ),
                Text(
                  "Your account has been blocked by admin. ",
                  style: TextStyle(color: colorPrimary, fontSize: 15),
                  textAlign: TextAlign.justify,
                ),
                const SizedBox(
                  height: 40,
                ),
                Center(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorPrimary,
                      elevation: 10,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                      disabledBackgroundColor: colorPrimaryLightest,
                    ),
                    child: const Padding(
                      padding:
                          EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                      child: Text(
                        "Retry",
                        style: TextStyle(
                            color: Colors.white,
                            fontSize: 15,
                            fontWeight: FontWeight.bold),
                      ),
                    ),
                    onPressed: () async {
                      ProgressDialog progressDialog = ProgressDialog(context,
                          type: ProgressDialogType.normal,
                          isDismissible: false);
                      progressDialog.update(
                          message: "Checking status. Please wait....");
                      await progressDialog.show();
                      Tuple2<int, String> checkResponse =
                          await inactivePageController
                              .recheckActiveFromNetwork();
                      await progressDialog.hide();
                      if (checkResponse.item1 != 0) {
                        // Navigator.pop(context, true);
                        await setActiveUser(active: 1);
                        Navigator.popAndPushNamed(context, '/home');
                        showToastMessage(context,
                            message: checkResponse.item2, duration: 2);
                        // Navigator.of(context).pop();
                      } else {
                        showToastMessage(context,
                            alertType: AlertType.Error,
                            message: checkResponse.item2,
                            duration: 2);
                      }
                    },
                  ),
                ),
                const SizedBox(
                  height: 40,
                ),
                Center(
                  child: ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: colorPrimary,
                        elevation: 10,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10.0),
                        ),
                        disabledBackgroundColor: colorPrimaryLightest,
                      ),
                      child: const Padding(
                        padding:
                            EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                        child: Text(
                          "Logout",
                          style: TextStyle(
                              color: Colors.white,
                              fontSize: 15,
                              fontWeight: FontWeight.bold),
                        ),
                      ),
                      onPressed: () async {
                        await logoutFromDevice(context);
                      }),
                ),
              ],
            )),
            Column(
              mainAxisAlignment: MainAxisAlignment.end,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                Image.asset(
                  'images/logo-bottom.png',
                  height: MediaQuery.of(context).size.width * 0.2,
                  fit: BoxFit.cover,
                ),
              ],
            ),
          ],
        ),
      ),
    ));
  }
}
