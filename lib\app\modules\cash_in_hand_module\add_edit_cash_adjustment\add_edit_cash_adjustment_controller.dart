import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/cash_adjustment_model.dart';
import 'package:mobile_khaata_v2/app/repository/cash_adjustment_repository.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:nepali_utils/nepali_utils.dart';

class AddEditCashAdjustmentController extends GetxController {
  final String tag = "AddEditAdjustmentController";

  var _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  var _editFlag = false.obs;
  bool get editFlag => _editFlag.value;

  var _readOnlyFlag = false.obs;
  bool get readOnlyFlag => _readOnlyFlag.value;
  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  var _cashAdjustmentTxn = CashAdjustmentModel().obs;
  CashAdjustmentModel get cashAdjustmentTxn => _cashAdjustmentTxn.value;

  CashAdjustmentRepository _cashAdjustmentRepository =
      CashAdjustmentRepository();

  final formKey = GlobalKey<FormState>();

  init() async {
    _isLoading(true);
    if (null != cashAdjustmentTxn.cashAdjId) {
      await initEdit();
    }
    _isLoading(false);
  }

  initEdit() async {
    _cashAdjustmentTxn.value = await _cashAdjustmentRepository
        .getAdjustmentById(cashAdjustmentTxn.cashAdjId ?? "");
    _editFlag.value = true;
    readOnlyFlag = true;
  }

  @override
  void onClose() {
    super.onClose();
  }

  Future<bool> createAdjustment() async {
    bool status = false;
    try {
      CashAdjustmentModel newCashAdjModel = CashAdjustmentModel();
      newCashAdjModel = cashAdjustmentTxn;
      newCashAdjModel.cashAdjDate =
          toDateAD(NepaliDateTime.parse(cashAdjustmentTxn.cashAdjDateBS ?? ""));

      String adjustmentId =
          await _cashAdjustmentRepository.insert(cashAdjustmentTxn);

      if (null != adjustmentId) status = true;
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<bool> updateAdjustment() async {
    bool status = false;
    try {
      CashAdjustmentModel newCashAdjModel = CashAdjustmentModel();
      newCashAdjModel = cashAdjustmentTxn;
      newCashAdjModel.cashAdjDate =
          toDateAD(NepaliDateTime.parse(cashAdjustmentTxn.cashAdjDateBS!));

      status = await _cashAdjustmentRepository.update(cashAdjustmentTxn);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }
}
