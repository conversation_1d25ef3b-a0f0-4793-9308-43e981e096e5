import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
import 'package:mobile_khaata_v2/app/model/others/item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/stock_summary_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/stock_report/report_stock_transaction_controller.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';

/**
 * LOW STOCK REPORT PAGE
 *
 * This page displays items that are below their minimum stock levels,
 * helping businesses identify when to reorder inventory.
 *
 * FEATURES:
 * - Date-based stock level checking
 * - Shows only items below minimum stock thresholds
 * - Item-wise current stock quantities and values
 * - Critical stock level alerts with visual indicators
 * - Print functionality for inventory management
 *
 * CALCULATIONS:
 * - Current Stock = In Quantity - Out Quantity + Opening Stock
 * - Stock Value = Purchase Unit Price × Current Stock Quantity
 * - Low Stock Alert = Current Stock <= Minimum Stock Level
 */
// ignore: must_be_immutable
class LowStockReport extends StatelessWidget {
  // FIXED: Make controller final for better performance
  final ReportStockTransactionController _controller =
      ReportStockTransactionController();
  String endDate = currentDate;

  LowStockReport({super.key}) {
    generate();
  }

  /**
   * GENERATE REPORT DATA
   *
   * Triggers low stock analysis for the selected date
   */
  generate() {
    _controller.generateLowStockReport(endDate: endDate);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      resizeToAvoidBottomInset: true,
      appBar: AppBar(
        elevation: 0,
        titleSpacing: -5.0,
        backgroundColor: colorPrimary,
        title: const Text(
          "कम स्टक रिपोर्ट\n(Low Stock Report)",
          style: TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
        actions: [
          PrintButton(
            onPressed: () {
              Navigator.of(context).pushNamed('/printStockSummaryReport',
                  arguments: StockSummaryPrintPage(
                    transactions: _controller.itemDetailList,
                    pageTitle: "Low Stock Report",
                    dateFor: endDate,
                  ));
            },
          )
        ],
      ),
      body: Container(
        color: Colors.black12,
        child: Column(
          children: [
            // DATE FILTER SECTION
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Text(
                    "As of Date:",
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(width: 10),
                  SizedBox(
                    width: 140,
                    child: ReportCustomDatePickerTextField(
                      initialValue: toDateBS(DateTime.parse(endDate)),
                      hintText: "Select Date",
                      onChange: (selectedDate) {
                        endDate = toDateAD(NepaliDateTime.parse(selectedDate));
                        generate();
                      },
                    ),
                  ),
                ],
              ),
            ),

            const Divider(
              height: 0,
              color: Colors.black54,
            ),

            // COLUMN HEADERS
            DefaultTextStyle(
              style: TextStyle(
                  fontSize: 12, color: textColor, fontWeight: FontWeight.bold),
              child: Container(
                color: Colors.white,
                padding:
                    const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: const [
                    // Item Name Column
                    Expanded(
                      flex: 2,
                      child: Text(
                        "Item Name",
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),

                    // Current Stock Column
                    Expanded(
                      flex: 1,
                      child: Text(
                        "Current Stock",
                        textAlign: TextAlign.center,
                      ),
                    ),

                    // Stock Value Column
                    Expanded(
                      flex: 1,
                      child: Text(
                        "Stock Value",
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const Divider(
              height: 0,
              color: Colors.black54,
            ),

            // MAIN CONTENT SECTION
            Obx(() {
              if (_controller.txnLoading) {
                return Container(
                    color: Colors.white,
                    child: const Center(child: CircularProgressIndicator()));
              }

              if (_controller.itemDetailList.isEmpty) {
                return Container(
                    color: Colors.white,
                    width: double.infinity,
                    child: const Center(
                        child: Text(
                      "No Low Stock Items Found\n(All items are above minimum levels)",
                      textAlign: TextAlign.center,
                      style: TextStyle(color: Colors.black54),
                    )));
              } else {
                return Expanded(
                    child: _LowStockListView(_controller.itemDetailList));
              }
            }),
          ],
        ),
      ),

      // BOTTOM SUMMARY BAR
      bottomNavigationBar: Container(
        height: 50,
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
        color: colorPrimary,
        child: SingleChildScrollView(
          child: Obx(() {
            return DefaultTextStyle(
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      "Low Stock Items: ${_controller.itemDetailList.length}",
                      textAlign: TextAlign.left,
                    ),
                  ),
                  Expanded(
                    child: Text(
                      "Total Qty: ${formatCurrencyAmount(_controller.totalQty.value, false)}",
                      textAlign: TextAlign.right,
                    ),
                  ),
                ],
              ),
            );
          }),
        ),
      ),
    ));
  }
}

/**
 * LOW STOCK LIST VIEW WIDGET
 *
 * Displays items that are below minimum stock levels with visual alerts
 */
class _LowStockListView extends StatelessWidget {
  final List<ItemDetailModel> _itemDetailList;

  const _LowStockListView(this._itemDetailList);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: _itemDetailList.length,
      itemBuilder: (context, int index) {
        ItemDetailModel item = _itemDetailList[index];

        return InkWell(
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                DefaultTextStyle(
                  style: TextStyle(fontSize: 12, color: colorPrimary),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // ITEM NAME SECTION WITH LOW STOCK INDICATOR
                        Expanded(
                          flex: 2,
                          child: Row(
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "${item.itemName ?? 'Unknown Item'}",
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 2,
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    ),
                                    // SHOW MINIMUM STOCK LEVEL
                                    Text(
                                      "Min Level: ${_formatQuantity(parseDouble(item.itemMinStockQuantity) ?? 0.0)}",
                                      style: TextStyle(
                                        fontSize: 10,
                                        color: Colors.grey[600],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),

                        // CURRENT STOCK SECTION - FIXED formatting and null safety
                        Expanded(
                          flex: 1,
                          child: Text(
                            _formatQuantity(
                                (item.balanceQuantity ?? 0.0).toDouble()),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: _getStockLevelColor(item),
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),

                        // STOCK VALUE SECTION - FIXED calculation with proper null safety
                        Expanded(
                          flex: 1,
                          child: Text(
                            formatCurrencyAmount(
                              _calculateStockValue(item),
                              false,
                            ),
                            textAlign: TextAlign.right,
                            style: const TextStyle(fontWeight: FontWeight.w500),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                // Add spacing for last element
                if (_itemDetailList.length - 1 == index) ...{
                  const SizedBox(height: 10),
                },
              ],
            ),
          ),
        );
      },
    );
  }

  /**
   * FORMAT QUANTITY FOR DISPLAY
   *
   * Formats quantity numbers for better readability
   * Removes unnecessary decimal places for whole numbers
   */
  String _formatQuantity(double quantity) {
    if (quantity == quantity.roundToDouble()) {
      // Whole number - show without decimals
      return quantity.round().toString();
    } else {
      // Decimal number - show with 2 decimal places
      return quantity.toStringAsFixed(2);
    }
  }

  /**
   * CALCULATE STOCK VALUE SAFELY
   *
   * Calculates item stock value with proper null safety
   * Stock Value = Purchase Unit Price × Current Stock Quantity
   */
  double _calculateStockValue(ItemDetailModel item) {
    final unitPrice = (item.itemPurchaseUnitPrice ?? 0.0).toDouble();
    final quantity = (item.balanceQuantity ?? 0.0).toDouble();
    return unitPrice * quantity;
  }

  /**
   * GET STOCK LEVEL COLOR INDICATOR
   *
   * Returns appropriate color based on stock criticality:
   * - Red: Critical (very low stock)
   * - Orange: Warning (at minimum level)
   * - Default: Normal
   */
  Color _getStockLevelColor(ItemDetailModel item) {
    final currentStock = (item.balanceQuantity ?? 0.0);
    final minStock = parseDouble(item.itemMinStockQuantity) ?? 0.0;

    if (currentStock <= 0) {
      // Out of stock - Critical
      return Colors.red;
    } else if (currentStock <= minStock * 0.5) {
      // Very low stock - Critical
      return Colors.red;
    } else if (currentStock <= minStock) {
      // At minimum level - Warning
      return Colors.orange;
    } else {
      // Normal level
      return colorPrimary;
    }
  }
}
