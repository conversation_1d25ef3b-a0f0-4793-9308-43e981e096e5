import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../../http/api_base_helper.dart';
import '../../../../utilities/logger.dart';
import '../../../../utilities/login_helper.dart';
import '../../../common_widgets/alerts.dart';
import '../../../model/others/company_model.dart';

class SelectCompanyController extends GetxController {
  final String tag = "CompanyListController";

  final _isLoading = true.obs;

  bool get isLoading => _isLoading.value;

  final _isError = false.obs;

  bool get isError => _isError.value;
  String errorMessage = "";

  List<CompanyModel> companies = [];
  List<CompanyModel> _filteredCompanies = [];

  List<CompanyModel> get filteredCompanies => _filteredCompanies;

  init() {
    // getCompanies();
  }

  @override
  onInit() {
    super.onInit();
    init();
  }

  refreshCompanies() {
    init();
  }

  Future<List<CompanyModel>> getCompanies(BuildContext? context) async {
    try {
      _isError(false);
      _isLoading(true);
      errorMessage = "";
      ApiBaseHelper apiBaseHelper = ApiBaseHelper();
      ApiResponse apiResponse = await apiBaseHelper
          .get(apiBaseHelper.CHILD_COMPANY_LIST, accessToken: true);
      Log.d("Companies");
      Log.d(apiResponse.status);
      if (apiResponse.status) {
        List<dynamic> linksJson = (apiResponse.data ?? []);
        companies = linksJson.map((e) => CompanyModel.fromJson(e)).toList();
        _filteredCompanies.clear();
        _filteredCompanies = companies;
        _isLoading(false);
        update();
        return companies;
      } else {
        //error in gettting links
        throw Exception("Something Went Wrong.");
      }
    } catch (e) {
      // Log.d("error parsing" + e.toString());
      _isError(true);
      errorMessage =
          "Cannot get Companies at this moment. Please try again later.";
      _isLoading(false);

      // FIXED: Add null check for context before showing toast
      // This prevents null check operator error when context is null
      if (context != null) {
        showToastMessage(
          context,
          message: errorMessage,
          alertType: AlertType.Error,
        );
      } else {
        // Alternative: Log the error when context is not available
        print("Error getting companies: $errorMessage");
      }
      return [];
    }
  }

  searchCompanies(String searchString) {
    _filteredCompanies.clear();
    for (var item in companies) {
      _filteredCompanies.addIf(
          item
              .toString()
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          item);
    }
  }

  Future<List<CompanyModel>> fetchCompanies() async {
    List<CompanyModel>? companies = await LoginHelper().getCompanies();
    return companies;
  }
}
