<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.techvedanta.mobile_khaata">
    <uses-permission android:name="android.permission.INTERNET"/> <!-- Add this -->
<!--    <uses-permission android:name="android.permission.READ_CONTACTS" />  -->
<!--    <uses-permission android:name="android.permission.WRITE_CONTACTS" />-->
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
<!--    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"/>-->
<!--    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES"/>-->
<!--    <uses-permission android:name="android.permission.READ_MEDIA_VIDEO"/>-->
<!--    <uses-permission android:name="android.permission.READ_MEDIA_AUDIO"/>-->
    <uses-permission android:name="com.google.android.gms.permission.AD_ID"/>




    <queries>
        <!-- If your app checks for SMS support -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="sms" />
        </intent>

        <!-- If your app checks for call support -->
        <intent>
            <action android:name="android.intent.action.VIEW" />
            <data android:scheme="tel" />
        </intent>
    </queries>
   <application

       android:label="mobile_khaata_v2"
        android:name="${applicationName}"
        android:icon="@mipmap/launcher_icon">
        <activity
            android:name=".MainActivity"
            android:exported="true"
            android:screenOrientation="portrait"
            android:launchMode="singleTop"
            android:theme="@style/Theme.AppCompat.Light.NoActionBar"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
              android:name="io.flutter.embedding.android.NormalTheme"
              android:resource="@style/NormalTheme"
              />
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />
            <provider
                android:name="androidx.core.content.FileProvider"
                android:authorities="${applicationId}.provider"
                android:exported="false"
                android:grantUriPermissions="true">
                <meta-data
                    android:name="android.support.FILE_PROVIDER_PATHS"
                    android:resource="@xml/provider_paths"/>
            </provider>
    </application>
</manifest>
