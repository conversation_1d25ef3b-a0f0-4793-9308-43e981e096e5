import 'dart:convert';

import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/ledger_model.dart';
import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/model/others/annex_item_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/image_repository.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';

import 'package:sqflite/sqflite.dart';
import 'package:sqflite/sqlite_api.dart';
import 'package:tuple/tuple.dart';

class LedgerRepository {
  final String tag = "LedgerRepository";
  final String tableName = "mk_ledger_master";

  DatabaseHelper databaseHelper = DatabaseHelper();
  QueryRepository queryRepository = QueryRepository();

  //========================================================================================= SYNCING ACTIONS
  Future<String> insert(LedgerModel ledger,
      {dynamic dbClient, String? batchID, bool shouldPush = true}) async {
    String ledgerID;

    dbClient ??= await databaseHelper.database;

    String primaryKeyPrefix = await getPrimaryKeyPrefix();
    ledger.ledgerId = primaryKeyPrefix + uuidV4;

    ledger.lastActivityAt = currentDateTime;
    ledger.ledgerTitle = toBeginningOfSentenceCase(ledger.ledgerTitle);
    ledger.lastActivityBy = await getLastActivityBy();
    ledger.lastActivityType = LastActivityType.New;

    print(await dbClient.insert(tableName, ledger.toJson()));
    print("Local database ma insert chai hunxa hunxa hunxa");
    print(tableName);
    print(ledger.toJson());

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.insert,
      data: ledger.toJson(),
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    ledgerID = ledger.ledgerId ?? "";

    if (shouldPush) {
      pushPendingQueries(
          singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);
    }

    return ledgerID;
  }

  Future<bool> update(LedgerModel ledger,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    ledger.lastActivityAt = currentDateTime;
    ledger.ledgerTitle = toBeginningOfSentenceCase(ledger.ledgerTitle);
    ledger.lastActivityBy = await getLastActivityBy();
    ledger.lastActivityType = LastActivityType.Edit;

    String whereClause = "ledger_id = ?";
    List<dynamic> whereArgs = [ledger.ledgerId];

    await dbClient.update(tableName, ledger.toJson(),
        where: whereClause, whereArgs: whereArgs);

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.update,
      data: ledger.toJson(),
      whereArgs: whereArgs,
      whereClause: whereClause,
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    pushPendingQueries(
        singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

    status = true;

    return status;
  }

  Future<Tuple2<bool, String>> delete(String ledgerId,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;
    String message = "";

    dbClient ??= await databaseHelper.database;
    String whereClause = "ledger_id = ?";
    List<dynamic> whereArgs = [ledgerId];
    Tuple2<bool, List<String>> hasRefRes =
        await DatabaseHelper.hasReferences(tablesWithColName: [
      {"table_name": "mk_transactions", "column_name": "ledger_id"}
    ], value: ledgerId);

    if (hasRefRes.item1) {
      //  there is  reference of this id in other table
      // so throw  error message
      message =
          "Cannot delete Party. Please clear all transactions related to party before deleting";
    } else {
      // can soft delete for given id
      dbClient.update(
          tableName, {"last_activity_type": LastActivityType.Delete},
          where: whereClause, whereArgs: whereArgs);

      QueryModel newQueryModel = QueryModel(
          tableName: tableName,
          queryType: QueryType.update,
          whereArgs: whereArgs,
          whereClause: whereClause,
          data: {"last_activity_type": LastActivityType.Delete});

      await queryRepository.pushQuery(newQueryModel,
          batchID: batchID, dbClient: dbClient);

      pushPendingQueries(
          singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

      status = true;
      message = "Party deleted successfully";
    }
    return Tuple2(status, message);
  }

  //=========================================================================================NON SYNCING ACTIONS
  Future<String> createIfNotExistByLedgerTitle(String ledgerTitle,
      {dynamic dbClient}) async {
    String ledgerId;

    dbClient ??= await databaseHelper.database;
    ledgerTitle = strTrim(ledgerTitle);

    List<Map<String, dynamic>> itemJsonList = await dbClient
        .query(tableName, where: "ledger_title = ?", whereArgs: [ledgerTitle]);
    if (itemJsonList.isNotEmpty) {
      ledgerId = itemJsonList[0]['ledger_id'];
    } else {
      ledgerId = await insert(
          LedgerModel(
            ledgerTitle: strTrim(ledgerTitle),
          ),
          dbClient: dbClient);
    }

    return ledgerId;
  }

  Future<bool> insertMultipleLedger(List<LedgerModel> items) async {
    bool status = false;
    Database? dbClient = await databaseHelper.database;

    String primaryKeyPrefix = await getPrimaryKeyPrefix();
    String batchID = primaryKeyPrefix + uuidV4;
    try {
      await dbClient!.transaction((batch) async {
        // var batch = txn.batch();
        await Future.wait(items.map((e) async {
          await insert(e, dbClient: batch, shouldPush: false, batchID: batchID);
        }).toList());
      });

      //push all data to sync
      pushPendingQueries(singleBatchId: batchID, source: "TRIGGER");
      status = true;
      // ignore: empty_catches
    } catch (e) {}

    return status;
  }

  Future<LedgerModel> getLedgerById(String ledgerId) async {
    LedgerModel ledgerModel = LedgerModel();
    try {
      Database? dbClient = await databaseHelper.database;

      Map<String, dynamic> jsonParty = (await dbClient!.rawQuery(
              "SELECT * FROM mk_ledger_master WHERE ledger_id=?", [ledgerId]))
          .first;
      ledgerModel = LedgerModel.fromJson(jsonParty);
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return ledgerModel;
  }

  Future<bool> checkMobileExists(String mobileNo,
      {String? excludeLedgerId}) async {
    bool exists = false;

    try {
      Database? dbClient = await databaseHelper.database;

      List<Map<String, dynamic>> result;

      if (excludeLedgerId != null && excludeLedgerId.isNotEmpty) {
        result = await dbClient!.rawQuery(
          "SELECT 1 FROM mk_ledger_master WHERE mobile_no = ? AND ledger_id != ? LIMIT 1",
          [mobileNo, excludeLedgerId],
        );
      } else {
        result = await dbClient!.rawQuery(
          "SELECT 1 FROM mk_ledger_master WHERE mobile_no = ? LIMIT 1",
          [mobileNo],
        );
      }

      exists = result.isNotEmpty;
    } catch (e) {
      // Handle or log the error as needed
      // print("Error checking mobile number: $e");
    }

    return exists;
  }

  // Future<List<LedgerModel>> getAllLedger() async {
  //   List<LedgerModel> ledgerModelList = [];
  //   try {
  //     Database? dbClient = await databaseHelper.database;
  //     final List<Map<String, dynamic>> result = await dbClient!.query(
  //       'mk_ledger_master',
  //
  //     );
  //
  //     print("Queried from DB: $result");
  //
  //     List<Map<String, dynamic>> jsonParty = (await dbClient!.rawQuery(
  //         "SELECT * FROM mk_ledger_master"));
  //     ledgerModelList = jsonParty
  //         .map((row) => LedgerModel.fromJson(row))
  //         .toList();
  //   } catch (e) {
  //     // Log.e(tag, e.toString());
  //   }
  //   return ledgerModelList;
  // }

  //used for dropdown search;
  Future<List<LedgerDetailModel>> getAllGeneralLedgersWithBalanceByTitle(
      String searchTitle,
      {List<String>? includedIDs}) async {
    List<LedgerDetailModel> ledgerList = [];
    try {
      Database? dbClient = await databaseHelper.database;

      String query = "SELECT lm.*, v_ledger_balance.balance_amount "
          "FROM mk_ledger_master lm "
          "LEFT JOIN v_ledger_balance ON lm.ledger_id=v_ledger_balance.ledger_id "
          "WHERE ledger_title LIKE ? AND last_activity_type!=? AND lm.ledger_type='G' ORDER BY ledger_title ASC Limit 20 ";

      List<Map<String, dynamic>> jsonLedgerData = await dbClient!
          .rawQuery(query, ["$searchTitle%", LastActivityType.Delete]);

      List<Map<String, dynamic>> finalJsonLedgerData = jsonLedgerData;

      if (includedIDs != null && includedIDs.isNotEmpty) {
        String includedIDString = "\"${includedIDs.join("\", \"")}\"";
        query = "SELECT lm.*, v_ledger_balance.balance_amount "
            "FROM mk_ledger_master lm "
            "LEFT JOIN v_ledger_balance ON lm.ledger_id=v_ledger_balance.ledger_id "
            "WHERE lm.ledger_id IN ($includedIDString) ";

        List<Map<String, dynamic>> includedLedgerData =
            await dbClient.rawQuery(query);
        finalJsonLedgerData = [...jsonLedgerData, ...includedLedgerData];

        final ids = finalJsonLedgerData.map((e) => e['ledger_id']).toSet();
        finalJsonLedgerData.retainWhere((x) => ids.remove(x['ledger_id']));
      }

      ledgerList = finalJsonLedgerData
          .map((row) => LedgerDetailModel.fromJson(row))
          .toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return ledgerList;
  }

  // combined with other methods
  Future<List<LedgerDetailModel>> getLedgerWithBalanceForIDs(
      List<String> ids) async {
    List<LedgerDetailModel> ledgerList = [];
    try {
      Database? dbClient = await databaseHelper.database;

      String includedIDString = "\"${ids.join("\", \"")}\"";

      String query = "SELECT lm.*, v_ledger_balance.balance_amount "
          "FROM mk_ledger_master lm "
          "LEFT JOIN v_ledger_balance ON lm.ledger_id=v_ledger_balance.ledger_id "
          "WHERE lm.ledger_id IN ($includedIDString) ";

      List<Map<String, dynamic>> includedLedgerData =
          await dbClient!.rawQuery(query);
      ledgerList =
          includedLedgerData.map((e) => LedgerDetailModel.fromJson(e)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return ledgerList;
  }

  Future<Tuple2<LedgerModel, ImageModel>> getLedgerDetailForAddEdit(
      String ledgerID) async {
    LedgerModel ledgerModel = LedgerModel();
    ImageModel imageModel = ImageModel();
    try {
      // Database dbClient = await databaseHelper.database;

      ledgerModel = await getLedgerById(ledgerID);

      if (null != ledgerModel.ledgerPhoto) {
        ImageRepository imageRepository = ImageRepository();
        imageModel =
            await imageRepository.getImageById(ledgerModel.ledgerPhoto ?? "");
      }
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return Tuple2(ledgerModel, imageModel);
  }

  Future<LedgerDetailModel> getLedgerWithBalanceById(String ledgerId) async {
    LedgerDetailModel ledgerModel = LedgerDetailModel();
    try {
      Database? dbClient = await databaseHelper.database;
      // Log.d("gettting  for $ledgerId");
      String query = "SELECT lm.*, v_ledger_balance.balance_amount "
          "FROM mk_ledger_master lm "
          "LEFT JOIN v_ledger_balance ON lm.ledger_id=v_ledger_balance.ledger_id  "
          "WHERE lm.ledger_id=?";

      List<Map<String, dynamic>> jsonParties =
          (await dbClient!.rawQuery(query, [ledgerId]));
      // Log.d("ledger datas ${jsonParties[0]}");
      ledgerModel = LedgerDetailModel.fromJson(jsonParties[0]);
    } catch (e) {
      // Log.e(tag, trace.toString() + e.toString());
    }
    return ledgerModel;
  }

  Future<List<LedgerDetailModel>> getAllLedgersWithBalance() async {
    List<LedgerDetailModel> ledgerList = [];
    try {
      Database? dbClient = await databaseHelper.database;
      String query;

      query = "SELECT lm.*, v_ledger_balance.balance_amount "
          "FROM mk_ledger_master lm "
          "LEFT JOIN v_ledger_balance ON lm.ledger_id=v_ledger_balance.ledger_id "
          "WHERE last_activity_type!=3 ORDER BY lm.ledger_title ASC ";

      List<Map<String, dynamic>> jsonLedgerData =
          await dbClient!.rawQuery(query);
      ledgerList =
          jsonLedgerData.map((row) => LedgerDetailModel.fromJson(row)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return ledgerList;
  }

  //paginated later (party list page)
  Future<List<LedgerDetailModel>> getAllGeneralLedgersWithBalance() async {
    List<LedgerDetailModel> ledgerList = [];
    // try {
    Database? dbClient = await databaseHelper.database;

    String query;
    query = "SELECT lm.*, lb.balance_amount FROM mk_ledger_master lm "
        "LEFT JOIN v_ledger_balance lb ON lb.ledger_id=lm.ledger_id "
        "WHERE lm.last_activity_type!=3 AND lm.ledger_type='G' ORDER BY lm.ledger_title ASC";

    List<Map<String, dynamic>> jsonLedgerData = await dbClient!.rawQuery(query);
    ledgerList =
        jsonLedgerData.map((row) => LedgerDetailModel.fromJson(row)).toList();

    // } catch (e) {
    //   print(e);
    // }
    return ledgerList;
  }

  //Credit List page
  Future<Tuple2<List<LedgerDetailModel>, List<LedgerDetailModel>>>
      getAllGeneralLedgerHavingDues() async {
    List<LedgerDetailModel> receivableLedgerList = [];
    List<LedgerDetailModel> payableLedgerList = [];
    try {
      Database? dbClient = await databaseHelper.database;

      String query = "SELECT lm.*, lb.balance_amount "
          "FROM mk_ledger_master lm "
          "LEFT JOIN v_ledger_balance lb ON lb.ledger_id=lm.ledger_id "
          "WHERE lm.last_activity_type!=3 AND lm.ledger_type='G' AND lb.balance_amount!=0 ORDER BY lm.ledger_title ASC";

      List<Map<String, dynamic>> jsonPartyList =
          await dbClient!.rawQuery(query);

      if (jsonPartyList.isNotEmpty) {
        jsonPartyList.map((row) {
          LedgerDetailModel ledger = LedgerDetailModel.fromJson(row);

          if (ledger.balanceAmount! > 0) {
            receivableLedgerList.add(ledger);
          } else if (ledger.balanceAmount! < 0) {
            payableLedgerList.add(ledger);
          }
        }).toList();
      }
    } catch (e) {
      // Log.e(tag, e.toString());
    }

    return Tuple2(receivableLedgerList, payableLedgerList);
  }

  //for report

  Future<double> getOpeningBalanceForPartyForDate(
      String openingDate, String ledgerID) async {
    double balanceAmount = 0.00;

    try {
      Database? dbClient = await databaseHelper.database;

      String query = "SELECT mk_ledger_master.ledger_id, "
          "IFNULL(SUM( "
          "CASE "
          "WHEN mk_transactions.txn_type = 1 THEN txn_balance_amount "
          "WHEN mk_transactions.txn_type = 2 THEN -txn_balance_amount "
          "WHEN mk_transactions.txn_type = 3 THEN -txn_cash_amount "
          "WHEN mk_transactions.txn_type = 4 THEN txn_cash_amount "
          "WHEN mk_transactions.txn_type = 7 THEN -txn_balance_amount  "
          "WHEN mk_transactions.txn_type = 8 THEN -txn_balance_amount "
          "WHEN mk_transactions.txn_type = 9 THEN txn_balance_amount "
          "END), 0.0) + IFNULL(CASE WHEN mk_ledger_master.opening_type = 6 THEN -mk_ledger_master.opening_balance ELSE mk_ledger_master.opening_balance END, 0.0) AS balance_amount "
          "FROM mk_ledger_master "
          "LEFT JOIN mk_transactions ON mk_ledger_master.ledger_id = mk_transactions.ledger_id AND mk_transactions.txn_date < '$openingDate'  AND mk_transactions.last_activity_type <> 3 "
          "WHERE mk_ledger_master.last_activity_type <> 3 "
          "AND mk_ledger_master.ledger_id = '$ledgerID' "
          "GROUP BY mk_ledger_master.ledger_id ";

      Map<String, dynamic> json = (await dbClient!.rawQuery(query)).first;
      balanceAmount = parseDouble(json["balance_amount"])!;

      // Log.d("got ledger opening balance $json");
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return balanceAmount;
  }

  Future<List<LedgerWithOpening>> getOpeningBalanceForAllPartyForDate(
      String openingDate) async {
    List<LedgerWithOpening> items = [];

    try {
      Database? dbClient = await databaseHelper.database;

      String query = "SELECT mk_ledger_master.ledger_id, "
          "IFNULL(SUM( "
          "CASE "
          "WHEN mk_transactions.txn_type = 1 THEN txn_balance_amount "
          "WHEN mk_transactions.txn_type = 2 THEN -txn_balance_amount "
          "WHEN mk_transactions.txn_type = 3 THEN -txn_cash_amount "
          "WHEN mk_transactions.txn_type = 4 THEN txn_cash_amount "
          "WHEN mk_transactions.txn_type = 7 THEN -txn_balance_amount  "
          "WHEN mk_transactions.txn_type = 8 THEN -txn_balance_amount "
          "WHEN mk_transactions.txn_type = 9 THEN txn_balance_amount "
          "END), 0.0) + IFNULL(CASE WHEN mk_ledger_master.opening_type = 6 THEN -mk_ledger_master.opening_balance ELSE mk_ledger_master.opening_balance END, 0.0) AS balance_amount "
          "FROM mk_ledger_master "
          "LEFT JOIN mk_transactions ON mk_ledger_master.ledger_id = mk_transactions.ledger_id AND mk_transactions.txn_date < '$openingDate'  AND mk_transactions.last_activity_type <> 3 "
          "WHERE mk_ledger_master.last_activity_type <> 3 "
          "AND mk_ledger_master.ledger_type = 'G' "
          "GROUP BY mk_ledger_master.ledger_id ";

      List<Map<String, dynamic>> jsonList = (await dbClient!.rawQuery(query));

      items = jsonList.map((e) => LedgerWithOpening.fromJson(e)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return items;
  }

  Future<List<LedgerDetailModel>> getAllGeneralLedgerWithDues(
      {String? dueType}) async {
    List<LedgerDetailModel> ledgers = [];
    try {
      Database? dbClient = await databaseHelper.database;

      String query = "SELECT lm.*, lb.balance_amount "
          "FROM mk_ledger_master lm "
          "LEFT JOIN v_ledger_balance lb ON lb.ledger_id=lm.ledger_id "
          "WHERE lm.last_activity_type!=3 AND lm.ledger_type='G' AND lb.balance_amount!=0 ORDER BY lm.ledger_title ASC";

      if ("receivable" == dueType) {
        query = "SELECT lm.*, lb.balance_amount "
            "FROM mk_ledger_master lm "
            "LEFT JOIN v_ledger_balance lb ON lb.ledger_id=lm.ledger_id "
            "WHERE lm.last_activity_type!=3 AND lm.ledger_type='G' AND lb.balance_amount > 0 ORDER BY lm.ledger_title ASC";
      } else if ("payable" == dueType) {
        query = "SELECT lm.*, lb.balance_amount "
            "FROM mk_ledger_master lm "
            "LEFT JOIN v_ledger_balance lb ON lb.ledger_id=lm.ledger_id "
            "WHERE lm.last_activity_type!=3 AND lm.ledger_type='G' AND lb.balance_amount < 0 ORDER BY lm.ledger_title ASC";
      }
      List<Map<String, dynamic>> jsonPartyList =
          await dbClient!.rawQuery(query);

      ledgers =
          jsonPartyList.map((row) => LedgerDetailModel.fromJson(row)).toList();
    } catch (e) {
      // Log.e(tag, e.toString());
    }

    return ledgers;
  }

  Future<List<LedgerDetailModel>> getAllReceivableGeneralLedger() async {
    List<LedgerDetailModel> ledgerList = [];
    try {
      Database? dbClient = await databaseHelper.database;

      String query = "SELECT lm.*, lb.balance_amount "
          "FROM mk_ledger_master lm "
          "LEFT JOIN v_ledger_balance lb ON lb.ledger_id=lm.ledger_id "
          "WHERE lm.last_activity_type!=? AND lm.ledger_type='G' AND lb.balance_amount > 0 ORDER BY lm.ledger_title ASC";

      List<Map<String, dynamic>> jsonPartyList =
          await dbClient!.rawQuery(query, [LastActivityType.Delete]);

      ledgerList = (jsonPartyList.isEmpty)
          ? []
          : jsonPartyList
              .map((row) => LedgerDetailModel.fromJson(row))
              .toList();
    } catch (e) {
      // Log.e(tag, e.toString());
    }

    return ledgerList;
  }

  Future<double> getTotalReceivable() async {
    double totalReceivable = 0.00;
    try {
      Database? dbClient = await databaseHelper.database;

      String query = "SELECT IFNULL(SUM(lb.balance_amount), 0.00) as total "
          "FROM v_ledger_balance lb "
          "INNER JOIN mk_ledger_master lm ON lb.ledger_id=lm.ledger_id AND lm.last_activity_type!=? AND lm.ledger_type='G' "
          "WHERE lb.balance_amount > 0";

      List<Map<String, dynamic>> data =
          await dbClient!.rawQuery(query, [LastActivityType.Delete]);
      if (data.isNotEmpty) {
        totalReceivable = data[0]['total'];
      }
    } catch (e) {
      // Log.e(tag, e.toString());
    }

    return totalReceivable;
  }

  Future<double> getTotalPayable() async {
    double totalPayable = 0.00;
    try {
      Database? dbClient = await databaseHelper.database;

      String query = "SELECT IFNULL(SUM(lb.balance_amount), 0.00) as total "
          "FROM v_ledger_balance lb "
          "INNER JOIN mk_ledger_master lm ON lb.ledger_id=lm.ledger_id AND lm.last_activity_type!=? AND lm.ledger_type='G' "
          "WHERE lb.balance_amount < 0";

      List<Map<String, dynamic>> data =
          await dbClient!.rawQuery(query, [LastActivityType.Delete]);

      if (data.isNotEmpty) {
        totalPayable = data[0]['total'];
      }
    } catch (e) {
      // Log.e(tag, e.toString());
    }

    // Log.d("total " + totalPayable.toString());

    return totalPayable;
  }

  Future<List<LedgerDetailModel>> getAllPayableGeneralLedger() async {
    List<LedgerDetailModel> ledgerList = [];
    try {
      Database? dbClient = await databaseHelper.database;

      String query = "SELECT lm.*, lb.balance_amount "
          "FROM mk_ledger_master lm "
          "LEFT JOIN v_ledger_balance lb ON lb.ledger_id=lm.ledger_id "
          "WHERE lm.last_activity_type!=? AND lm.ledger_type='G' AND lb.balance_amount < 0 ORDER BY lm.ledger_title ASC";

      List<Map<String, dynamic>> jsonPartyList =
          await dbClient!.rawQuery(query, [LastActivityType.Delete]);

      ledgerList = (jsonPartyList.isEmpty)
          ? []
          : jsonPartyList
              .map((row) => LedgerDetailModel.fromJson(row))
              .toList();

      return ledgerList;
    } catch (e) {
      // Log.e(tag, e.toString());
      return ledgerList;
    }
  }

  //wont be used later
  Future<List<LedgerModel>> getAllLedgers() async {
    List<LedgerModel> ledgerList = [];
    try {
      Database? dbClient = await databaseHelper.database;

      List<Map<String, dynamic>> jsonLedgerData = await dbClient!.rawQuery(
          "SELECT * FROM mk_ledger_master WHERE ledger_type='G' AND last_activity_type!=? ORDER BY ledger_title ASC",
          [LastActivityType.Delete]);
      ledgerList =
          jsonLedgerData.map((row) => LedgerModel.fromJson(row)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return ledgerList;
  }

  //wont be used later
  Future<List<LedgerModel>> getLedgersByTitle(String title) async {
    List<LedgerModel> ledgerList = [];
    try {
      Database? dbClient = await databaseHelper.database;

      List<Map<String, dynamic>> jsonLedgerData = await dbClient!.rawQuery(
          "SELECT * FROM mk_ledger_master WHERE ledger_title LIKE ? AND ledger_type='G' AND last_activity_type!=? ORDER BY ledger_title ASC Limit 20",
          ["$title%", LastActivityType.Delete]);
      ledgerList =
          jsonLedgerData.map((row) => LedgerModel.fromJson(row)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return ledgerList;
  }
}
