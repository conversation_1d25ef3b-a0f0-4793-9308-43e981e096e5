import 'package:flutter/material.dart';
import 'package:flutter_custom_clippers/flutter_custom_clippers.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/select_company_module/select_company_controller.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../../http/api_base_helper.dart';
import '../../../../main.dart';
import '../../../../utilities/logger.dart';
import '../../../../utilities/login_helper.dart';
import '../../../../utilities/styles.dart';
import '../../../../utilities/transaction_helper.dart';
import '../../../common_widgets/alerts.dart';
import '../../../model/others/company_model.dart';
import '../../../model/others/login_model.dart';
import '../otp_verification.dart';

class SelectCompanyScreen extends StatelessWidget {
  SelectCompanyScreen(
      {super.key, required this.loginModel, required this.loginResponse});

  final LoginModel loginModel;
  final LoginApiResponseModel loginResponse;

  CompanyModel? selectedCompany;

  final SelectCompanyController controller = SelectCompanyController();

  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        toolbarHeight: 60,
        elevation: 2,
        backgroundColor: colorPrimary,
        automaticallyImplyLeading: false,
        centerTitle: true,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Text(
              "mobile",
              style: TextStyle(
                  fontSize: 35, color: Colors.white, fontFamily: 'ArialBlack'),
            ),
            Text(
              " खाता",
              style: TextStyle(
                fontSize: 40,
                color: Colors.white,
                fontWeight: FontWeight.w800,
                fontFamily: 'ArialBlack',
              ),
            ),
          ],
        ),
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: Stack(
          fit: StackFit.expand,
          children: <Widget>[
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Container(
                width: double.infinity,
                height: screenHeight * 0.7,
                margin: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                child: ClipPath(
                  clipper: DiagonalPathClipperTwo(),
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 1, horizontal: 1),
                    decoration: BoxDecoration(
                        color: colorPrimary, boxShadow: downShadow),
                    child: ClipPath(
                      clipper: DiagonalPathClipperTwo(),
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 15),
                        decoration: BoxDecoration(
                          color: Colors.white,
                        ),
                        child: SingleChildScrollView(
                          physics: AlwaysScrollableScrollPhysics(),
                          // padding: EdgeInsets.only(bottom: 200),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                "Select Company",
                                style: TextStyle(
                                    fontSize: 20,
                                    color: colorPrimary,
                                    fontWeight: FontWeight.bold),
                              ),
                              SizedBox(height: 20),
                              FutureBuilder<List<CompanyModel>>(
                                  future: controller.fetchCompanies(),
                                  builder: (context, snapshot) {
                                    List<CompanyModel> filteredList = snapshot
                                        .data!
                                        .where((company) =>
                                            company.registrationDetail != null)
                                        .toList();
                                    return DropdownButtonFormField<
                                        CompanyModel>(
                                      decoration: InputDecoration(
                                        labelText: 'Company Name',
                                        border: OutlineInputBorder(),
                                      ),
                                      value: selectedCompany,
                                      items: filteredList.map((company) {
                                        return DropdownMenuItem<CompanyModel>(
                                          value: company,
                                          child: Text(
                                            company.registrationDetail!
                                                .businessName!,
                                            style:
                                                TextStyle(color: Colors.black),
                                          ),
                                        );
                                      }).toList(),
                                      onChanged: (value) {
                                        // setState(() {
                                        selectedCompany = value;
                                        print(selectedCompany);
                                        // });
                                      },
                                    );
                                  }),
                              SizedBox(height: 20),
                              ElevatedButton(
                                onPressed: () {
                                  if (selectedCompany != null) {
                                    loginModel.username =
                                        selectedCompany!.userName;
                                    // loginModel.password= selectedCompany;
                                    if (selectedCompany!.parentId != null) {
                                      loginModel.password =
                                          selectedCompany!.userName;
                                      loginModel.parentId =
                                          loginResponse.userId;
                                    }

                                    print(loginModel.username);
                                    print(loginModel.password);
                                    print(loginModel.parentId);
                                    print(loginModel);
                                    _doLogin(loginModel, context);
                                  } else {
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                          content:
                                              Text('Please select a company')),
                                    );
                                  }
                                },
                                child: Text('Login'),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: RotatedBox(
                quarterTurns: -2,
                child: ClipPath(
                  clipper: DiagonalPathClipperTwo(),
                  child: Container(
                    padding: EdgeInsets.only(
                      bottom: 20,
                    ),
                    width: double.infinity,
                    height: screenHeight * 0.18,
                    decoration:
                        BoxDecoration(color: colorPrimary, boxShadow: upShadow),
                    child: Center(
                      child: RotatedBox(
                        quarterTurns: 2,
                        child: Image.asset(
                          'images/logo-bottom.png',
                          height: screenHeight * 0.1,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    ));
  }

  Future<void> _doLogin(LoginModel login, BuildContext context) async {
    try {
      ProgressDialog _progressDialog = ProgressDialog(context,
          type: ProgressDialogType.normal, isDismissible: false);
      _progressDialog.update(message: "Please wait....");
      await _progressDialog.show();

      ApiBaseHelper apiBaseHelper = new ApiBaseHelper();
      ApiResponse apiResponse = await apiBaseHelper.post(
          apiBaseHelper.ACTION_AUTH_LOGIN, login.toJson(),
          accessToken: false);

      await _progressDialog.hide();

      if (apiResponse.status) {
        Log.d("api login respose ${apiResponse.data} ");
        LoginApiResponseModel _loginApiResponse =
            LoginApiResponseModel.fromJson(apiResponse.data);
        if (_loginApiResponse.operation == 'validate-otp') {
          Navigator.pushReplacementNamed(context, "/otp_verify",
              arguments: OtpVerifyScreenArguments(_loginApiResponse.token!,
                  _loginApiResponse.registeredMobileNo!));
          showToastMessage(context, message: apiResponse.msg!, duration: 4);
        } else {
          Log.d("resp is ${_loginApiResponse.toString()}");
          bool status = await LoginHelper().login(
            accessToken: _loginApiResponse.accessToken!,
            userName: _loginApiResponse.username!,
            fullName: _loginApiResponse.fullName!,
            subdata: {
              'expiry_info': apiResponse.data['expiry_info'],
              'agent_detail': apiResponse.data['agent_detail']
            },
            multiUserFlag: _loginApiResponse.multiUserFlag!,
            isExpired: _loginApiResponse.isAccountExpired!,
            permissions: _loginApiResponse.permissions ?? [],
          );

          print("Permissions eta ca hai" +
              await SharedPreferences.getInstance().then((prefs) =>
                  prefs.getStringList(Permissions)?.toString() ?? "[]"));

          if (status) {
            await refreshGlobalVariables();
            TransactionHelper.refreshPreviousPages();

            // //Reset/reload controller after login

            if (_loginApiResponse.isFirstLogin == 1) {
              showAlertDialog(
                context,
                alertType: AlertType.Success,
                alertTitle: "Welcome to Mobile खाता",
                barrierDismissible: false,
                onCloseButtonPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pushNamedAndRemoveUntil(
                      '/home', (Route<dynamic> route) => false);
                },
                message: apiResponse.msg ?? "",
              );
            } else {
              print("Hello worls print print");
              // Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (context) => SelectCompanyScreen(loginModel: _login,loginResponse: _loginApiResponse ,)));

              Navigator.pushReplacementNamed(context, "/home");
              showToastMessage(context,
                  message: apiResponse.msg ?? "", duration: 2);
            }
          }
        }
      } else {
        showAlertDialog(context,
            alertType: AlertType.Error,
            alertTitle: "Error",
            message: apiResponse.msg!);
      }
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
  }
}
