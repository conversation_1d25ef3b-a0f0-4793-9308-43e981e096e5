// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:webview_flutter/webview_flutter.dart';

class GeneralWebView extends StatefulWidget {
  final String? url;
  const GeneralWebView({Key? key, required this.url}) : super(key: key);

  @override
  _GeneralWebViewState createState() => _GeneralWebViewState();
}

class _GeneralWebViewState extends State<GeneralWebView> {
  bool isLoading = true;
  final _key = UniqueKey();

  WebViewController? controller;

  @override
  void initState() {
    controller = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            // Update loading bar.
          },
          onPageStarted: (String url) {
            setState(() {
              isLoading = true;
            });
          },
          onPageFinished: (String url) {
            setState(() {
              isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {},
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.startsWith('https://www.youtube.com/')) {
              return NavigationDecision.prevent;
            }
            return NavigationDecision.navigate;
          },
        ),
      )
      ..loadRequest(Uri.parse(widget.url!));

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        WebViewWidget(
          controller: controller!,
        ),

        // WebView(
        //   key: _key,
        //   initialUrl: widget.url,
        //   javascriptMode: JavascriptMode.unrestricted,
        //   onPageFinished: (finish) {
        //     setState(() {
        //       isLoading = false;
        //     });
        //   },
        // ),
        isLoading
            ? const Center(
                child: CircularProgressIndicator(),
              )
            : Stack(),
      ],
    );
  }
}

class GeneralWebViewPage extends StatelessWidget {
  final String? pageTitle;
  final String? pageURL;

  const GeneralWebViewPage(
      {Key? key, required this.pageTitle, required this.pageURL})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: colorPrimary,
        title: Text(pageTitle ?? "Webview"),
      ),
      body: GeneralWebView(
        url: pageURL ?? "",
      ),
    ));
  }
}
