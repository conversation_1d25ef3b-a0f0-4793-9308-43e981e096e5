// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/components/image_grid_gallery.dart';
import 'package:mobile_khaata_v2/app/components/ledger_autocomplete%20_%20textfield_with_add.dart';
import 'package:mobile_khaata_v2/app/components/payment_mode_selector.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_module.dart/add_purchase_bill_item/add_edit_purchase_bill_item_view.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_module.dart/detail_purchase/detail_purchase_controller.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';

import 'package:nepali_utils/nepali_utils.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

import '../../../../database/txn_type.dart';

extension ExtendedIterable<E> on Iterable<E> {
  /// Like Iterable<T>.map but callback have index as second argument
  Iterable<T> mapIndex<T>(T Function(E e, int i) f) {
    var i = 0;
    return map((e) => f(e, i++));
  }

  void forEachIndex(void Function(E e, int i) f) {
    var i = 0;
    for (var e in this) {
      f(e, i++);
    }
  }
}

class DetailPurchasePage extends StatelessWidget {
  final String tag = "Purchase Detail Page";

  final String? purchaseID;
  final bool? reaOnlyFlag;

  final purchaseController = DetailPurchaseController();

  DetailPurchasePage({super.key, this.purchaseID, this.reaOnlyFlag}) {
    purchaseController.onInit();

    if (null != purchaseID) {
      // initiate edit functionality
      purchaseController.initEdit(purchaseID, reaOnlyFlag ?? true);
    } else {
      purchaseController.initialize();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      bool iscashPurchase = purchaseController.transaction.value.ledgerId ==
          CASH_PURCHASE_LEDGER_ID;
      bool hasSubTotal =
          (purchaseController.transaction.value.txnSubTotalAmount != null &&
              purchaseController.transaction.value.txnSubTotalAmount! > 0.0);

      if (purchaseController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      } else {
        return SafeArea(
            child: Scaffold(
                resizeToAvoidBottomInset: true,
                appBar: AppBar(
                  toolbarHeight: 60,
                  elevation: 4,
                  leading: BackButton(
                    onPressed: () => Navigator.pop(context, false),
                  ),
                  centerTitle: false,
                  backgroundColor: colorPrimary,
                  titleSpacing: -5.0,
                  title: const Text(
                    "खरिद (Purchase Detail)",
                    style: TextStyle(
                        fontSize: 20,
                        color: Colors.white,
                        fontFamily: 'HelveticaRegular',
                        fontWeight: FontWeight.bold),
                  ),
                  actions: [
                    if (purchaseController.editFlag) ...{
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: InkWell(
                            onTap: () {
                              TransactionHelper.gotoTransactionEditPage(
                                  context, purchaseID ?? "", TxnType.purchase,
                                  forEdit: true);
                            },
                            child: (purchaseController.readOnlyFlag)
                                ? Column(
                                    children: const [
                                      Icon(
                                        Icons.mode_edit,
                                        color: Colors.white,
                                      ),
                                      Text(
                                        "Click here to Edit",
                                        style: TextStyle(
                                            color: Colors.white, fontSize: 10),
                                      ),
                                    ],
                                  )
                                : Column(
                                    children: const [
                                      Icon(
                                        Icons.close,
                                        color: Colors.white,
                                      ),
                                      Text(
                                        "Cancel",
                                        style: TextStyle(
                                            color: Colors.white, fontSize: 10),
                                      ),
                                    ],
                                  )),
                      ),
                    }
                  ],
                ),

                //===========================================================================Body Part
                body: Center(
                  child: Container(
                    color: backgroundColorShade,
                    child: GestureDetector(
                      onTap: () => FocusScope.of(context).unfocus(),
                      child: Form(
                        key: purchaseController.formKey,
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              Card(
                                elevation: 2,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 15),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      //===========================Bill No.
                                      Expanded(
                                        flex: 1,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "बिल न. ",
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            FormBuilderTextField(
                                              name: "bill_no",
                                              readOnly: purchaseController
                                                  .readOnlyFlag,
                                              autocorrect: false,
                                              keyboardType: TextInputType.text,
                                              textInputAction:
                                                  TextInputAction.done,
                                              textAlign: TextAlign.right,
                                              style: formFieldTextStyle,
                                              decoration:
                                                  formFieldStyle.copyWith(
                                                      labelText: "Bill No."),
                                              controller:
                                                  purchaseController.billNoCtrl,
                                              onChanged: (value) {
                                                purchaseController
                                                    .transaction
                                                    .value
                                                    .txnRefNumberChar = value;
                                                purchaseController.transaction
                                                    .refresh();
                                              },
                                            ),
                                          ],
                                        ),
                                      ),

                                      const SizedBox(
                                        width: 20,
                                      ),

                                      //===========================Transaction Date
                                      Expanded(
                                        flex: 1,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "मिति",
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            CustomDatePickerTextField(
                                              readOnly: purchaseController
                                                  .readOnlyFlag,
                                              maxBSDate: NepaliDateTime.now(),
                                              initialValue: purchaseController
                                                  .transaction.value.txnDateBS,
                                              onChange: (selectedDate) {
                                                purchaseController
                                                    .transaction
                                                    .value
                                                    .txnDateBS = selectedDate;
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              Card(
                                elevation: 2,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 10),
                                  child: Column(
                                    children: [
                                      //===============================================Party Balance
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Row(
                                            children: [
                                              SizedBox(
                                                width: 20,
                                                height: 20,
                                                child: Checkbox(
                                                  activeColor: colorPrimary,
                                                  checkColor: Colors.white,
                                                  value: purchaseController
                                                      .isCashPurchaseSelected,
                                                  onChanged:
                                                      purchaseController
                                                              .readOnlyFlag
                                                          ? null
                                                          : (value) {
                                                              purchaseController
                                                                      .setisCashPurchaseSelected =
                                                                  value!;
                                                              if (value) {
                                                                purchaseController.onChangeParty(LedgerDetailModel(
                                                                    ledgerId:
                                                                        CASH_PURCHASE_LEDGER_ID,
                                                                    ledgerTitle:
                                                                        CASH_PURCHASE_LEDGER_NAME));
                                                                purchaseController
                                                                    .onToggleVat(
                                                                        false);
                                                              } else {
                                                                purchaseController.onChangeParty(
                                                                    LedgerDetailModel(
                                                                        ledgerId:
                                                                            null));
                                                              }
                                                            },
                                                ),
                                              ),
                                              Text(
                                                  "  खुद्रा खरीद\n  (Cash Purchase)",
                                                  style: labelStyle2)
                                            ],
                                          ),
                                          RichText(
                                            textAlign: TextAlign.right,
                                            text: TextSpan(
                                                text: "पुरानो बाँकी: ",
                                                style:
                                                    TextStyle(color: textColor),
                                                children: [
                                                  if (null !=
                                                      purchaseController
                                                          .transaction
                                                          .value
                                                          .ledgerId) ...{
                                                    TextSpan(
                                                      text:
                                                          "${purchaseController.selectedLedger.value.balanceAmount ?? 0}",
                                                      style: TextStyle(
                                                          color: ((purchaseController
                                                                          .selectedLedger
                                                                          .value
                                                                          .balanceAmount ??
                                                                      0) >=
                                                                  0.0)
                                                              ? colorGreenDark
                                                              : colorRedLight),
                                                    )
                                                  }
                                                ]),
                                          )
                                        ],
                                      ),

                                      Container(
                                        height: 10,
                                      ),
                                      //===============================================Party Field
                                      if (!purchaseController
                                          .isCashPurchaseSelected)
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'आपूर्तिकर्ताको नाम',
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            LedgerAutoCompleteTextFieldWithAdd(
                                                // ignore: prefer_const_literals_to_create_immutables
                                                excludedIDS: [
                                                  CASH_SALES_LEDGER_ID
                                                ],
                                                enableFlag: !purchaseController
                                                    .readOnlyFlag,
                                                labelText: "Supplier Name",
                                                controller: purchaseController
                                                    .partyNameCtrl,
                                                onChangedFn: (value) {
                                                  // Log.d("called i text change");
                                                },
                                                ledgetID: purchaseController
                                                    .transaction.value.ledgerId,
                                                onSuggestionSelectedFn:
                                                    (LedgerDetailModel ledger) {
                                                  purchaseController
                                                      .onChangeParty(ledger);
                                                })
                                          ],
                                        ),
                                      ...iscashPurchase
                                          ? [
                                              const SizedBox(
                                                height: 10,
                                              ),
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    "खुद्रा आपूर्तिकर्ताको नाम",
                                                    style: labelStyle2,
                                                  ),
                                                  const SizedBox(height: 5.0),
                                                  FormBuilderTextField(
                                                    name: "display_text",
                                                    // readOnly:
                                                    //     (null == state.selectedLedger.ledgerId)
                                                    //         ? false
                                                    //         : true,
                                                    readOnly: purchaseController
                                                        .readOnlyFlag,
                                                    autocorrect: false,
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            labelText:
                                                                "Billing Name"),
                                                    controller:
                                                        purchaseController
                                                            .displayTextCtrl,
                                                    onChanged: (value) {
                                                      purchaseController
                                                              .transaction
                                                              .value
                                                              .txnDisplayName =
                                                          value;
                                                      purchaseController
                                                          .transaction
                                                          .refresh();
                                                    },
                                                  ),
                                                ],
                                              )
                                            ]
                                          : [],

                                      const SizedBox(
                                        height: 10,
                                      ),
                                      if (!iscashPurchase)
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            //===================================Mobile
                                            SizedBox(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.3,
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'फोन नम्बर',
                                                    style: labelStyle2,
                                                  ),
                                                  const SizedBox(height: 5.0),
                                                  FormBuilderTextField(
                                                      name: "mobile",
                                                      readOnly: true,
                                                      autocorrect: false,
                                                      keyboardType:
                                                          TextInputType.number,
                                                      textInputAction:
                                                          TextInputAction.done,
                                                      inputFormatters: [
                                                        FilteringTextInputFormatter
                                                            .digitsOnly
                                                      ],
                                                      style: formFieldTextStyle,
                                                      decoration: formFieldStyle
                                                          .copyWith(
                                                              labelText:
                                                                  "Contact no.",
                                                              hintText:
                                                                  "Contact no"),
                                                      controller:
                                                          purchaseController
                                                              .mobileCtrl),
                                                ],
                                              ),
                                            ),

                                            //===================================Address
                                            SizedBox(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.5,
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'ठेगाना',
                                                    style: labelStyle2,
                                                  ),
                                                  const SizedBox(height: 5.0),
                                                  FormBuilderTextField(
                                                    name: "address",
                                                    readOnly: true,
                                                    autocorrect: false,
                                                    keyboardType:
                                                        TextInputType.text,
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            labelText:
                                                                "Address"),
                                                    controller:
                                                        purchaseController
                                                            .addressCtrl,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      if (!iscashPurchase)
                                        const SizedBox(height: 10.0),

                                      //=====================================PAN No Field
                                      if (!iscashPurchase)
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "पान / मु. अ. कर नम्बर",
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            FormBuilderTextField(
                                                name: "pan_no",
                                                readOnly: true,
                                                autocorrect: false,
                                                keyboardType:
                                                    TextInputType.number,
                                                inputFormatters: [
                                                  FilteringTextInputFormatter
                                                      .digitsOnly
                                                ],
                                                textInputAction:
                                                    TextInputAction.done,
                                                style: formFieldTextStyle,
                                                decoration:
                                                    formFieldStyle.copyWith(
                                                        labelText:
                                                            "PAN/VAT No."),
                                                controller: purchaseController
                                                    .panNoCtrl),
                                          ],
                                        ),
                                    ],
                                  ),
                                ),
                              ),

                              //================================================Item Container
                              Card(
                                child: Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(0.0),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          width: double.infinity,
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 15, vertical: 8),
                                          decoration: BoxDecoration(
                                              color: colorPrimaryLight,
                                              borderRadius:
                                                  const BorderRadius.only(
                                                      topLeft:
                                                          Radius.circular(4),
                                                      topRight:
                                                          Radius.circular(4))),
                                          child: DefaultTextStyle(
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 15,
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                const Text(
                                                  "खरिद सामानहरु (Purchase Items)",
                                                ),
                                                Text(
                                                  "Total Item: ${purchaseController.items.length}",
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        Container(
                                          constraints: const BoxConstraints(
                                              maxHeight: 300),
                                          child: getItemListView(
                                              context,
                                              purchaseController.items,
                                              purchaseController),
                                        ),
                                      ],
                                    )),
                              ),

                              //===============================================Total Amount
                              Card(
                                elevation: 2,
                                child: Column(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 6, horizontal: 8),
                                      decoration: BoxDecoration(
                                          color: colorPrimaryLight,
                                          borderRadius: const BorderRadius.only(
                                              topLeft: Radius.circular(4),
                                              topRight: Radius.circular(4))),
                                      child: const Center(
                                          child: Text(
                                        "Bill Totals (जम्मा बिल)",
                                        style: TextStyle(
                                            color: Colors.white, fontSize: 16),
                                      )),
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 10,
                                      ),
                                      child: Column(
                                        children: [
                                          // =============================================Sub Total
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                "उप कुल",
                                                style: labelStyle2,
                                              ),
                                              const SizedBox(height: 5.0),
                                              FormBuilderTextField(
                                                name: "txn_subtotal",
                                                readOnly: purchaseController
                                                        .readOnlyFlag
                                                    ? true
                                                    : (purchaseController
                                                            .items.isNotEmpty)
                                                        ? true
                                                        : false,
                                                autocorrect: false,
                                                keyboardType:
                                                    const TextInputType
                                                        .numberWithOptions(
                                                        decimal: true),
                                                textInputAction:
                                                    TextInputAction.done,
                                                style: formFieldTextStyle,
                                                inputFormatters: [
                                                  FilteringTextInputFormatter
                                                      .allow(RegExp(
                                                          r'^(\d+)?\.?\d{0,2}'))
                                                ],
                                                maxLength: 10,
                                                decoration:
                                                    formFieldStyle.copyWith(
                                                        hintText: "Sub Total",
                                                        counterText: ''),
                                                textAlign: TextAlign.end,
                                                controller: purchaseController
                                                    .subTotalAmountCtrl,
                                                onChanged: (value) {
                                                  purchaseController
                                                      .onSubTotalIndividualChange(
                                                          value!,
                                                          editorTag:
                                                              'txn_subtotal');
                                                  purchaseController.transaction
                                                      .refresh();
                                                },
                                              ),
                                            ],
                                          ),
                                          const SizedBox(
                                            height: 20,
                                          ),

                                          // =============================================Discount
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                "छुट (Discount)",
                                                style: labelStyle2,
                                              ),
                                              const SizedBox(height: 5.0),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  SizedBox(
                                                    width: 80,
                                                    child: FormBuilderTextField(
                                                      name:
                                                          "txn_discount_percent",
                                                      readOnly: purchaseController
                                                              .readOnlyFlag ||
                                                          (!hasSubTotal),
                                                      autocorrect: false,
                                                      keyboardType:
                                                          const TextInputType
                                                              .numberWithOptions(
                                                              decimal: true),
                                                      textInputAction:
                                                          TextInputAction.done,
                                                      style: formFieldTextStyle,
                                                      decoration: formFieldStyle
                                                          .copyWith(
                                                              suffix:
                                                                  const Text(
                                                                      "%"),
                                                              labelText: "%"),
                                                      textAlign: TextAlign.end,
                                                      controller: purchaseController
                                                          .discountPercentageCtrl,
                                                      onChanged: (value) {
                                                        purchaseController
                                                            .updateDiscountPercentage(
                                                                value ?? "",
                                                                editorTag:
                                                                    'txn_discount_percent');
                                                      },
                                                    ),
                                                  ),
                                                  const SizedBox(
                                                    width: 20,
                                                  ),
                                                  Expanded(
                                                    child: FormBuilderTextField(
                                                        name:
                                                            "txn_discount_amount",
                                                        readOnly: purchaseController
                                                                .readOnlyFlag ||
                                                            (!hasSubTotal),
                                                        autocorrect: false,
                                                        keyboardType:
                                                            const TextInputType.numberWithOptions(
                                                                decimal: true),
                                                        textInputAction:
                                                            TextInputAction
                                                                .done,
                                                        style:
                                                            formFieldTextStyle,
                                                        decoration: formFieldStyle
                                                            .copyWith(
                                                                labelText:
                                                                    "छुट रकम (Dis. Amount)"),
                                                        textAlign:
                                                            TextAlign.end,
                                                        controller:
                                                            purchaseController
                                                                .discountAmountCtrl,
                                                        onChanged: (value) {
                                                          purchaseController
                                                              .updateDiscountAmount(
                                                                  value ?? "",
                                                                  editorTag:
                                                                      'txn_discount_amount');
                                                        }),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                          const SizedBox(
                                            height: 25,
                                          ),

                                          //====================================================VAT
                                          ...iscashPurchase
                                              ? []
                                              : [
                                                  Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Row(
                                                        children: [
                                                          SizedBox(
                                                            width: 20,
                                                            height: 20,
                                                            child: Checkbox(
                                                              activeColor:
                                                                  colorPrimary,
                                                              checkColor:
                                                                  Colors.white,
                                                              value: purchaseController
                                                                  .isVatEnabled,
                                                              onChanged: (purchaseController
                                                                          .readOnlyFlag ||
                                                                      (!hasSubTotal))
                                                                  ? null
                                                                  : (value) {
                                                                      purchaseController
                                                                          .onToggleVat(
                                                                              value!);
                                                                    },
                                                            ),
                                                          ),
                                                          Text(
                                                              "  मु.अ. कर (VAT)",
                                                              style:
                                                                  labelStyle2)
                                                        ],
                                                      ),
                                                      const SizedBox(
                                                          height: 10.0),
                                                      Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          SizedBox(
                                                            width: 80,
                                                            child:
                                                                FormBuilderTextField(
                                                              name:
                                                                  "txn_tax_percent",
                                                              readOnly: true,
                                                              autocorrect:
                                                                  false,
                                                              keyboardType:
                                                                  const TextInputType
                                                                      .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                              textInputAction:
                                                                  TextInputAction
                                                                      .done,
                                                              style:
                                                                  formFieldTextStyle,
                                                              decoration: formFieldStyle
                                                                  .copyWith(
                                                                      suffix:
                                                                          const Text(
                                                                              "%"),
                                                                      labelText:
                                                                          "%"),
                                                              textAlign:
                                                                  TextAlign.end,
                                                              controller:
                                                                  purchaseController
                                                                      .vatPercentCtrl,
                                                              onChanged:
                                                                  (value) {
                                                                purchaseController
                                                                    .onvatPercentChange(
                                                                        value!,
                                                                        editorTag:
                                                                            'txn_tax_percent');
                                                              },
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                            width: 20,
                                                          ),
                                                          Expanded(
                                                            child:
                                                                FormBuilderTextField(
                                                              name:
                                                                  "txn_tax_amount",
                                                              readOnly: true,
                                                              autocorrect:
                                                                  false,
                                                              keyboardType:
                                                                  const TextInputType
                                                                      .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                              textInputAction:
                                                                  TextInputAction
                                                                      .done,
                                                              style:
                                                                  formFieldTextStyle,
                                                              decoration: formFieldStyle
                                                                  .copyWith(
                                                                      labelText:
                                                                          "मु.अ. कर रकम (VAT Amount) "),
                                                              textAlign:
                                                                  TextAlign.end,
                                                              controller:
                                                                  purchaseController
                                                                      .vatAmountCtrl,
                                                              onChanged:
                                                                  (value) {
                                                                purchaseController
                                                                    .onvatAmountChange(
                                                                        value!,
                                                                        editorTag:
                                                                            'txn_tax_amount');
                                                              },
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                  const SizedBox(
                                                    height: 5,
                                                  ),
                                                ],

                                          const Divider(
                                            height: 5,
                                          ),
                                          const Divider(
                                            height: 0,
                                          ),
                                          const SizedBox(
                                            height: 15,
                                          ),

                                          // =============================================Total Amount
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                "कुल रकम",
                                                style: labelStyle2,
                                              ),
                                              const SizedBox(
                                                height: 5,
                                              ),
                                              FormBuilderTextField(
                                                  name: "txn_total",
                                                  readOnly: true,
                                                  autocorrect: false,
                                                  keyboardType:
                                                      const TextInputType
                                                          .numberWithOptions(
                                                          decimal: true),
                                                  textInputAction:
                                                      TextInputAction.done,
                                                  style: formFieldTextStyle,
                                                  decoration:
                                                      formFieldStyle.copyWith(
                                                          labelText:
                                                              "Total Amount"),
                                                  textAlign: TextAlign.end,
                                                  controller: purchaseController
                                                      .totalAmountCtrl),
                                            ],
                                          ),
                                          const SizedBox(
                                            height: 25,
                                          ),

                                          // =============================================Received Amount

                                          Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.end,
                                            children: [
                                              Expanded(
                                                  child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    children: [
                                                      SizedBox(
                                                        width: 20,
                                                        height: 20,
                                                        child: Checkbox(
                                                          activeColor:
                                                              colorPrimary,
                                                          checkColor:
                                                              Colors.white,
                                                          value: iscashPurchase
                                                              ? true
                                                              : (purchaseController
                                                                  .isReceived),
                                                          onChanged: (purchaseController
                                                                      .readOnlyFlag ||
                                                                  iscashPurchase ||
                                                                  (!hasSubTotal))
                                                              ? null
                                                              : (value) {
                                                                  purchaseController
                                                                          .setIsReceived =
                                                                      value!;
                                                                  if (value) {
                                                                    purchaseController
                                                                        .transaction
                                                                        .value
                                                                        .txnCashAmount = purchaseController
                                                                            .transaction
                                                                            .value
                                                                            .txnTotalAmount ??
                                                                        0.0;
                                                                    purchaseController
                                                                        .transaction
                                                                        .value
                                                                        .txnBalanceAmount = 0.00;
                                                                  } else {
                                                                    purchaseController
                                                                        .transaction
                                                                        .value
                                                                        .txnCashAmount = 0.0;

                                                                    purchaseController
                                                                        .transaction
                                                                        .value
                                                                        .txnBalanceAmount = purchaseController
                                                                            .transaction
                                                                            .value
                                                                            .txnTotalAmount ??
                                                                        0.0;
                                                                  }
                                                                  purchaseController
                                                                      .assignTransactionToTextFields();
                                                                },
                                                        ),
                                                      ),
                                                      Text(" भुक्तानी रकम",
                                                          style: labelStyle2)
                                                    ],
                                                  ),
                                                  const SizedBox(
                                                    height: 10,
                                                  ),
                                                  PaymentModeSelector(
                                                    onChangedFn: (v) {
                                                      purchaseController
                                                          .transaction
                                                          .value
                                                          .txnPaymentTypeId = v;

                                                      purchaseController
                                                              .transaction
                                                              .value
                                                              .txnPaymentReference =
                                                          null;
                                                      purchaseController
                                                              .transaction
                                                              .value
                                                              .chequeIssueDateBS =
                                                          null;
                                                      purchaseController
                                                          .transaction
                                                          .refresh();
                                                    },
                                                    paymentModeID:
                                                        purchaseController
                                                            .transaction
                                                            .value
                                                            .txnPaymentTypeId,
                                                    enableFlag:
                                                        (purchaseController
                                                            .readOnlyFlag),
                                                  )
                                                ],
                                              )),
                                              const SizedBox(
                                                width: 10,
                                              ),
                                              Expanded(
                                                child: FormBuilderTextField(
                                                  name: "txn_cash_amount",
                                                  readOnly: (purchaseController
                                                          .readOnlyFlag ||
                                                      !hasSubTotal ||
                                                      iscashPurchase),
                                                  autocorrect: false,
                                                  keyboardType:
                                                      const TextInputType
                                                          .numberWithOptions(
                                                          decimal: true),
                                                  textInputAction:
                                                      TextInputAction.done,
                                                  inputFormatters: [
                                                    FilteringTextInputFormatter
                                                        .digitsOnly
                                                  ],
                                                  style: formFieldTextStyle,
                                                  decoration:
                                                      formFieldStyle.copyWith(
                                                          labelText:
                                                              "Paid Amount"),
                                                  textAlign: TextAlign.end,
                                                  controller: purchaseController
                                                      .receivedAmountCtrl,
                                                  onChanged: (value) {
                                                    purchaseController
                                                        .changeReceivedAmount(
                                                            value!,
                                                            editorTag:
                                                                'txn_cash_amount');
                                                  },
                                                ),
                                              )
                                            ],
                                          ),

                                          ...(purchaseController.transaction
                                                      .value.txnPaymentTypeId !=
                                                  PAYMENT_MODE_CASH_ID)
                                              ? [
                                                  const SizedBox(
                                                    height: 25,
                                                  ),
                                                  Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text("चेक/भौचर न.",
                                                          style: labelStyle2),
                                                      const SizedBox(
                                                        height: 10,
                                                      ),
                                                      TextField(
                                                          autocorrect: false,
                                                          readOnly:
                                                              purchaseController
                                                                  .readOnlyFlag,
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration: formFieldStyle
                                                              .copyWith(
                                                                  labelText:
                                                                      "Cheque/Voucher No."),
                                                          controller:
                                                              purchaseController
                                                                  .paymentRefCtrl,
                                                          onChanged: (v) {
                                                            purchaseController
                                                                .transaction
                                                                .value
                                                                .txnPaymentReference = v;
                                                            purchaseController
                                                                .transaction
                                                                .refresh();
                                                          }),
                                                    ],
                                                  ),
                                                ]
                                              : [],
                                          if (purchaseController.transaction
                                                  .value.txnPaymentTypeId ==
                                              PAYMENT_MODE_CHEQUE_ID) ...[
                                            const SizedBox(
                                              height: 25,
                                            ),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text("चेक मिति",
                                                    style: labelStyle2),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                CustomDatePickerTextField(
                                                  labelText: "Cheque Date",
                                                  readOnly: purchaseController
                                                      .readOnlyFlag,
                                                  // maxBSDate: NepaliDateTime.now(),
                                                  initialValue:
                                                      purchaseController
                                                          .transaction
                                                          .value
                                                          .chequeIssueDateBS,
                                                  onChange: (selectedDate) {
                                                    purchaseController
                                                            .transaction
                                                            .value
                                                            .chequeIssueDateBS =
                                                        selectedDate;
                                                  },
                                                ),
                                              ],
                                            )
                                          ],

                                          const SizedBox(
                                            height: 25,
                                          ),

                                          // =============================================Balance Amount
                                          ...iscashPurchase
                                              ? []
                                              : [
                                                  Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text("बाँकी रहेको रकम",
                                                          style: labelStyle2),
                                                      const SizedBox(
                                                        height: 10,
                                                      ),
                                                      FormBuilderTextField(
                                                          name:
                                                              "txn_balance_amount",
                                                          readOnly: true,
                                                          autocorrect: false,
                                                          keyboardType:
                                                              const TextInputType.numberWithOptions(
                                                                  decimal:
                                                                      true),
                                                          textInputAction:
                                                              TextInputAction
                                                                  .done,
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration: formFieldStyle
                                                              .copyWith(
                                                                  labelText:
                                                                      "Balance Due"),
                                                          textAlign:
                                                              TextAlign.end,
                                                          controller:
                                                              purchaseController
                                                                  .dueAmountCtrl),
                                                    ],
                                                  ),
                                                  const SizedBox(
                                                    height: 20,
                                                  ),
                                                ],
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              //===============================================Image Preview Grid
                              SizedBox(
                                width: double.infinity,
                                child: Card(
                                  elevation: 2,
                                  child: SizedBox(
                                      height: 140,
                                      width: 100,
                                      child: ImageGalleryGrid(
                                          images: purchaseController.files)),
                                ),
                              ),

                              //===============================================Description
                              Card(
                                elevation: 2,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 10),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "कैफियत",
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      FormBuilderTextField(
                                        name: "description",
                                        readOnly:
                                            purchaseController.readOnlyFlag,
                                        autocorrect: false,
                                        textAlign: TextAlign.start,
                                        textInputAction:
                                            TextInputAction.newline,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Remarks"),
                                        minLines: 4,
                                        maxLines: 4,
                                        controller: purchaseController.descCtrl,
                                        onChanged: (value) {
                                          purchaseController.transaction.value
                                              .txnDescription = value;
                                          purchaseController.transaction
                                              .refresh();
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                //=================================================Save button
                bottomNavigationBar: BottomSaveCancelButton(
                  shadow: false,
                  hasDelete: true,
                  onDeleteBtnPressedFn: () async {
                    showAlertDialog(context,
                        okText: "YES",
                        hasCancel: true,
                        cancelText: "NO",
                        alertType: AlertType.Error,
                        alertTitle: "Confirm Delete",
                        onCloseButtonPressed: () async {
                      // Navigator.of(_).pop();
                      ProgressDialog progressDialog = ProgressDialog(context,
                          type: ProgressDialogType.normal,
                          isDismissible: false);
                      progressDialog.update(
                          message: "Checking Permission. Please wait....");
                      await progressDialog.show();
                      Tuple2<bool, String> checkResp =
                          await PermissionWrapperController()
                              .requestForPermissionCheck(
                                  forPermission:
                                      PermissionManager.purchaseDelete);
                      if (checkResp.item1) {
                        //has  permission
                        progressDialog.update(
                            message: "Deleting Data. Please wait....");
                        Tuple2<bool, String> deleteResp =
                            await TransactionRepository()
                                .delete(purchaseID ?? "");
                        await progressDialog.hide();
                        if (deleteResp.item1) {
                          //  data deleted
                          TransactionHelper.refreshPreviousPages();
                          showAlertDialog(context,
                              barrierDismissible: false,
                              alertType: AlertType.Success,
                              alertTitle: "", onCloseButtonPressed: () {
                            // Navigator.of(_).pop();
                            Navigator.of(context).pop();
                          }, message: deleteResp.item2);
                        } else {
                          //cannot  delete  data
                          showAlertDialog(context,
                              alertType: AlertType.Error,
                              alertTitle: "",
                              message: deleteResp.item2);
                        }
                      } else {
                        await progressDialog.hide();
                        showAlertDialog(context,
                            alertType: AlertType.Error,
                            alertTitle: "",
                            message: checkResp.item2);
                      }
                    },
                        message:
                            "Are you sure you  want to  delete this purchase record?");
                    // Log.d("pressed");
                  },
                  enableFlag: true,
                  saveText: "Share",
                  onSaveBtnPressedFn: () {
                    TransactionHelper.goToPrintPage(
                        context, purchaseID ?? "", TxnType.purchase);
                  },
                )));
      }
    });
  }
}

Widget getItemListView(
  BuildContext context,
  List<LineItemDetailModel> items,
  DetailPurchaseController purchaseController,
) {
  // return Container(height: 40, color: Colors.red);
  ScrollController scrollController = ScrollController();
  var listView = Scrollbar(
    controller: scrollController,
    thumbVisibility: true,
    child: ListView.builder(
        controller: scrollController,
        itemCount: items.length,
        shrinkWrap: true,
        itemBuilder: (context, int index) {
          LineItemDetailModel item = items[index];

          return Row(children: [
            Expanded(
                child: GestureDetector(
              onTap: purchaseController.readOnlyFlag
                  ? null
                  : () async {
                      var returnedData = await showDialog(
                          context: context,
                          useRootNavigator: true,
                          barrierDismissible: false,
                          builder: (dC) {
                            return AlertDialog(
                                insetPadding: const EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 10),
                                contentPadding: EdgeInsets.zero,
                                clipBehavior: Clip.hardEdge,
                                content: SizedBox(
                                  width: MediaQuery.of(context).size.width - 20,
                                  child: AddEditPurchaseBilledItem(
                                    lineItemModel: item,
                                  ),
                                ));
                          });
                      if (null != returnedData) {
                        if (returnedData.deleteFlag) {
                          purchaseController.items.removeAt(index);
                        } else if (null != returnedData.billedItem) {
                          purchaseController.items.replaceRange(
                              index, 1, [returnedData.billedItem]);
                          purchaseController.recalculateForItems();
                        }

                        purchaseController.recalculateForItems();
                      }
                    },
              child: Card(
                elevation: 2,
                margin: EdgeInsets.only(
                    left: 10,
                    right: 10,
                    top: (0 == index) ? 15 : 8,
                    bottom: ((items.length - 1) == index) ? 20 : 8),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                  decoration: const BoxDecoration(color: Colors.black12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // =============================================item name
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              item.itemName ?? "",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: colorPrimaryDark),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 5,
                      ),

                      // =============================================gross amount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const SizedBox(
                            width: 75,
                            child: Text(
                              "Amount",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  color: Colors.black54),
                            ),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              "${item.quantity} ${item.lineItemUnitName ?? ""} X ${formatCurrencyAmount(item.pricePerUnit!)} = ${formatCurrencyAmount(item.grossAmount!, false)}",
                              textAlign: TextAlign.right,
                              style: TextStyle(fontSize: 12, color: textColor),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 5,
                      ),

                      // =============================================Discount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            width: 100,
                            child: Text(
                              "Discount(%): ${item.discountPercent}",
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  color: Colors.black54),
                            ),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              " = ${formatCurrencyAmount(item.discountAmount!, false)}",
                              textAlign: TextAlign.right,
                              style: TextStyle(fontSize: 12, color: textColor),
                            ),
                          ),
                        ],
                      ),
                      const Divider(
                        height: 5,
                      ),
                      const Divider(
                        height: 0,
                      ),

                      // =============================================netAmount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              formatCurrencyAmount(item.totalAmount!, false),
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: colorPrimary),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            )),
          ]);
        }),
  );

  return listView;
}
