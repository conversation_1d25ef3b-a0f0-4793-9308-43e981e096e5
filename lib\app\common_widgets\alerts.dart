// ignore_for_file: constant_identifier_names

import 'package:another_flushbar/flushbar.dart';
import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

enum AlertType { Error, Success, Normal }

void showToastMessage(BuildContext context,
    {required String message,
    AlertType alertType = AlertType.Success,
    int? duration}) {
  Color backgroundColor = Colors.green;

  if ((null == duration)) {
    duration = 2;
  } else {
    duration = duration;
  }

  if (AlertType.Error == alertType) {
    backgroundColor = Colors.red;
  } else if (AlertType.Normal == alertType) {
    backgroundColor = Colors.black26;
  }

  Flushbar(
    isDismissible: false,
    messageText: Text(
      message,
      textAlign: TextAlign.center,
      style: const TextStyle(
          fontSize: 15.0, color: Colors.white, fontFamily: "HelveticaRegular"),
    ),
    backgroundColor: backgroundColor.withOpacity(0.8),
    duration: Duration(seconds: duration),
  ).show(context);
}

void showAlertDialog(BuildContext context,
    {required AlertType alertType,
    required String alertTitle,
    required String message,
    bool barrierDismissible = true,
    Icon? alertIcon,
    Function? onCloseButtonPressed,
    bool hasCancel = false,
    String cancelText = "Cancel",
    String okText = "OK"}) {
  Color textColor;

  if (AlertType.Error == alertType) {
    textColor = colorRedLight;
    alertIcon = Icon(
      Icons.error_outline,
      color: textColor,
      size: 24,
    );
  } else if (AlertType.Success == alertType) {
    textColor = colorGreen;
    if ((null == alertIcon)) {
      alertIcon = Icon(Icons.info_outline, color: textColor);
    } else {
      alertIcon = alertIcon;
    }
  } else {
    textColor = colorPrimary;
    alertIcon = (null == alertIcon)
        ? Icon(Icons.info_outline, color: textColor)
        : alertIcon;
  }

  showDialog(
    context: context,
    barrierDismissible: barrierDismissible,
    builder: (BuildContext dialogContext) {
      return AlertDialog(
        titlePadding: EdgeInsets.zero,
        title: Container(
          padding: const EdgeInsets.only(left: 10, right: 10, top: 15),
          child: Container(
            padding: const EdgeInsets.only(bottom: 10),
            decoration: const BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.black12))),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                alertIcon!,
                const Text(" "),
                Text(
                  alertTitle,
                  style: TextStyle(
                      color: textColor,
                      fontSize: 16,
                      fontWeight: FontWeight.bold),
                )
              ],
            ),
          ),
        ),
        contentPadding: const EdgeInsets.all(0.0),
        content: Container(
          padding:
              const EdgeInsets.only(left: 15, right: 15, top: 15, bottom: 10),
          child: Text(
            message.contains("{\"message\"")
                ? RegExp(r'"message":"(.*?)"').firstMatch(message)?.group(1) ??
                    'Unknown error'
                : message,
            style: TextStyle(color: textColor, fontSize: 14),
          ),
        ),
        elevation: 24.0,
        actions: <Widget>[
          if (hasCancel)
            ElevatedButton(
              child: Text(cancelText),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ElevatedButton(
            onPressed: null != onCloseButtonPressed
                ? () {
                    Navigator.pop(dialogContext);

                    onCloseButtonPressed();
                  }
                : () {
                    Navigator.pop(dialogContext);
                  },
            child: Text(okText),
          )
        ],
      );
    },
  );
}
