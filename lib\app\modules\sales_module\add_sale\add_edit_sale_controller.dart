import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:form_builder_image_picker/form_builder_image_picker.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/components/debouncher.dart';
import 'package:mobile_khaata_v2/app/controllers/unit_list_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/txn_image_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/sale_model.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/app/repository/sales_repository.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:image_picker/image_picker.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tuple/tuple.dart';

import '../../../../utilities/shared_pref_helper1.dart';

class SaleController extends GetxController {
  final String tag = "Sale Controller";

  var _isLoading = true.obs;
  var _editFlag = false.obs;
  var _readOnlyFlag = false.obs;
  var _isVatEnabled = false.obs;
  var _isReceived = false.obs;
  var _isCashSaleSelected = false.obs;

  bool get isCashSaleSelected => _isCashSaleSelected.value;
  bool get isLoading => _isLoading.value;
  bool get editFlag => _editFlag.value;
  bool get readOnlyFlag => _readOnlyFlag.value;

  bool get isVatEnabled => _isVatEnabled.value;
  bool get isReceived => _isReceived.value;

  set isCashSaleSelected(bool flag) {
    _isCashSaleSelected.value = flag;
    _isCashSaleSelected.refresh();
  }

  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  set isReceived(bool flag) {
    _isReceived.value = flag;
  }

  var _fileUploadPermission = false.obs;
  bool get fileUploadPermission => _fileUploadPermission.value;

  final SharedPrefHelper1 prefsHelper = SharedPrefHelper1();

  // for edit case, to check if same bill no is used in update case, not to give
  // duplicate error;
  String? previousBillNo;

  SaleRepository saleRepository = new SaleRepository();

  LedgerRepository _ledgerRepository = new LedgerRepository();

  var transaction = SaleModel(
          txnDateBS: currentDateBS,
          txnSubTotalAmount: 0,
          txnPaymentTypeId: PAYMENT_MODE_CASH_ID)
      .obs;

  var items = <LineItemDetailModel>[].obs;

  var images = <TxnImageModel>[].obs;

  var files = <File>[].obs;

  final formKey = GlobalKey<FormState>();

  final TextEditingController billNoCtrl = TextEditingController();

  final TextEditingController partyNameCtrl = TextEditingController();
  final TextEditingController mobileCtrl = TextEditingController();
  final TextEditingController addressCtrl = TextEditingController();
  final TextEditingController panNoCtrl = TextEditingController();

  final TextEditingController displayTextCtrl = TextEditingController();
  final TextEditingController subTotalAmountCtrl = TextEditingController();
  final TextEditingController discountPercentageCtrl = TextEditingController();
  final TextEditingController discountAmountCtrl = TextEditingController();
  final TextEditingController vatAmountCtrl = TextEditingController();
  final TextEditingController vatPercentCtrl = TextEditingController();

  final TextEditingController totalAmountCtrl = TextEditingController();
  final TextEditingController receivedAmountCtrl = TextEditingController();
  final TextEditingController dueAmountCtrl = TextEditingController();
  final TextEditingController descCtrl = TextEditingController();
  final TextEditingController paymentRefCtrl = TextEditingController();

  UnitListController unitListController = Get.put(UnitListController());

  var selectedLedger = LedgerDetailModel().obs;

  @override
  void onInit() async {
    // cache images of bill
    _fileUploadPermission.value =
        await prefsHelper.checkPermission("File Upload");
    imageCache.clear();
    super.onInit();
  }

  reset() {
    _isVatEnabled(false);
    _isReceived(false);
    _isCashSaleSelected(false);
    selectedLedger.value = LedgerDetailModel();
    items.value = [];
    transaction.value = SaleModel(
        txnDateBS: currentDateBS,
        txnSubTotalAmount: 0,
        txnPaymentTypeId: PAYMENT_MODE_CASH_ID);
    files.clear();
    images.value = [];
  }

  initialize() {
    _isLoading(false);
  }

  @override
  void dispose() {
    discountPercentageCtrl.dispose();
    billNoCtrl.dispose();
    partyNameCtrl.dispose();
    vatAmountCtrl.dispose();
    totalAmountCtrl.dispose();
    receivedAmountCtrl.dispose();
    dueAmountCtrl.dispose();
    descCtrl.dispose();
    mobileCtrl.dispose();
    addressCtrl.dispose();
    panNoCtrl.dispose();
    displayTextCtrl.dispose();
    subTotalAmountCtrl.dispose();
    discountAmountCtrl.dispose();
    vatPercentCtrl.dispose();
    paymentRefCtrl.dispose();

    super.dispose();
  }

  recalculateForItems() {
    double itemSubTotal = 0.00;
    items
      ..forEach((LineItemDetailModel li) {
        itemSubTotal = parseDouble(
                (itemSubTotal + (li.totalAmount ?? 0.00)).toStringAsFixed(2)) ??
            0.00;
      });
    print("thsi is total val $itemSubTotal");
    onSubTotalIndividualChange(itemSubTotal.toString());
  }

  recalculateDataForItem({String? editorTag}) {
    // handle value change in sale model in valid step:
    double subtoal = transaction.value.txnSubTotalAmount ?? 0.00;

    transaction.value.txnTaxableTotalAmount = parseDouble(
        (subtoal - (transaction.value.txnDiscountAmount ?? 0.00))
            .toStringAsFixed(2));

    transaction.value.txnTaxAmount = parseDouble(
        ((transaction.value.txnTaxPercent ?? 0.0) *
                0.01 *
                (transaction.value.txnTaxableTotalAmount ?? 0.00))
            .toStringAsFixed(2));

    transaction.value.txnTotalAmount = parseDouble(
        (transaction.value.txnTaxableTotalAmount ??
                0.00 + (transaction.value.txnTaxAmount ?? 0.00))
            .toStringAsFixed(2));

    if (transaction.value.ledgerId != CASH_SALES_LEDGER_ID) {
      //for credit or if received is checked
      transaction.value.txnCashAmount = isReceived
          ? transaction.value.txnTotalAmount
          : (transaction.value.txnCashAmount ?? 0.00);

      transaction.value.txnBalanceAmount = isReceived
          ? 0.0
          : parseDouble((transaction.value.txnTotalAmount ??
                  0.00 - (transaction.value.txnCashAmount ?? 0.00))
              .toStringAsFixed(2));
    } else {
      //for cash
      //total will always be cash amount for cash sale ledger
      transaction.value.txnCashAmount = transaction.value.txnTotalAmount;
      transaction.value.txnBalanceAmount = 0.00;
    }

    assignTransactionToTextFields(editorTAG: editorTag);
  }

  onvatPercentChange(String value, {String? editorTag}) {
    double taxPercentage = (parseDouble(value) ?? 0.00).toPrecision(2);

    transaction.value.txnTaxPercent = taxPercentage;

    transaction.value.txnTaxAmount = parseDouble(
        ((taxPercentage * (transaction.value.txnTaxableTotalAmount ?? 0.00)) *
                0.01)
            .toStringAsFixed(2));

    transaction.value.txnTotalAmount = parseDouble(
        ((transaction.value.txnTaxableTotalAmount ?? 0.0) +
                (transaction.value.txnTaxAmount ?? 0.00))
            .toStringAsFixed(2));

    changeReceivedAmount(
        transaction.value.ledgerId == CASH_SALES_LEDGER_ID
            ? transaction.value.txnTotalAmount.toString()
            : 0.0.toString(),
        editorTag: editorTag);
  }

  onvatAmountChange(String value, {String? editorTag}) {
    transaction.value.txnTaxAmount = parseDouble(value);
    transaction.value.txnTaxPercent = parseDouble(((parseDouble(value) ??
                0.00 / (transaction.value.txnTaxableTotalAmount ?? 0.00)) *
            0.01)
        .toStringAsFixed(2));

    transaction.value.txnTotalAmount = parseDouble(
        (transaction.value.txnTaxableTotalAmount ??
                0.00 + (transaction.value.txnTaxAmount ?? 0.00))
            .toStringAsFixed(2));
    changeReceivedAmount(
        transaction.value.ledgerId == CASH_SALES_LEDGER_ID
            ? transaction.value.txnTotalAmount.toString()
            : 0.0.toString(),
        editorTag: editorTag);
  }

  changeReceivedAmount(String value, {String? editorTag}) {
    double txnCashAmt = (parseDouble(value) ?? 0.00);
    double txnBalanceAmt = transaction.value.txnTotalAmount! - txnCashAmt;
    transaction.value.txnCashAmount = txnCashAmt;
    transaction.value.txnBalanceAmount = txnBalanceAmt;
    if (txnCashAmt > 0) {
      _isReceived(true);
      _isReceived.refresh();
    }
    assignTransactionToTextFields(editorTAG: editorTag);

    // transaction.value.txnCashAmount = parseDouble(value);
    // transaction.value.txnBalanceAmount = parseDouble(
    //     (transaction.value.txnTotalAmount ??
    //             0.00 - (transaction.value.txnCashAmount ?? 0.00))
    //         .toStringAsFixed(2));
  }

  onSubTotalIndividualChange(String val, {String? editorTag}) {
    double subTotal = (parseDouble(val) ?? 0.00);
    transaction.value.txnSubTotalAmount = subTotal;
    transaction.refresh();

    recalculateDataForItem(editorTag: editorTag);

    // if (transaction.value.txnDiscountPercent != null &&
    //     transaction.value.txnDiscountPercent != 0.00) {
    //   updateDiscountPercentage(transaction.value.txnDiscountPercent.toString(),
    //       editorTag: editorTag);
    //   recalculateDataForItem(editorTag: editorTag);
    // }
  }

  onToggleVat(bool flag) {
    _isVatEnabled.value = flag;
    _isVatEnabled.refresh();
    double VAT = flag ? VAT_PERCENTAGE : 0.00;

    transaction.value.txnTaxPercent = VAT;
    onvatPercentChange(VAT.toString());
  }

  onChangeParty(LedgerDetailModel party) {
    // print("this is id hai ${party.ledgerId}");
    // ignore: null_aware_in_condition
    // Log.d("selected ${party.toJson()}");
    if (party.ledgerId != null) {
      transaction.value.ledgerId = party.ledgerId;
      partyNameCtrl.text = party.ledgerTitle ?? "";
      mobileCtrl.text = party.mobileNo ?? "";
      addressCtrl.text = party.address ?? "";
      panNoCtrl.text = party.tinNo ?? "";
      if (party.ledgerId == CASH_SALES_LEDGER_ID) {
        displayTextCtrl.text = (transaction.value.txnDisplayName == "" ||
                transaction.value.txnDisplayName == null)
            ? party.ledgerTitle!
            : transaction.value.txnDisplayName!;
        transaction.value.txnDisplayName = displayTextCtrl.text;

        _isCashSaleSelected.value = true;
        _isCashSaleSelected.refresh();
        transaction.value.txnCashAmount =
            transaction.value.txnTotalAmount ?? 0.0;
        transaction.value.txnBalanceAmount = 0.00;
      }
    } else {
      transaction.value.ledgerId = null;
      transaction.value.txnCashAmount = 0.00;
      transaction.value.txnBalanceAmount = transaction.value.txnTotalAmount;
      transaction.value.txnDisplayName = displayTextCtrl.text = "";
      partyNameCtrl.text = "";
      mobileCtrl.text = "";
      addressCtrl.text = "";
      panNoCtrl.text = "";
      Log.d("yo id ho hai => ${party.ledgerId}");
    }
    selectedLedger.value = party;
    selectedLedger.refresh();
    transaction.refresh();

    if ((transaction.value.txnSubTotalAmount ?? 0.0) > 0.00) {
      assignTransactionToTextFields();
    }
  }

  assignTransactionToTextFields({String? editorTAG}) {
    // Log.d("assignning to text ${transaction.value.toJson()}");

    // formKey.currentState.

    billNoCtrl.text = transaction.value.txnRefNumberChar ?? "";
    totalAmountCtrl.text =
        (transaction.value.txnTotalAmount ?? 0.0).toStringAsFixed(2);
    dueAmountCtrl.text =
        (transaction.value.txnBalanceAmount ?? 0.0).toStringAsFixed(2);
    displayTextCtrl.text = transaction.value.txnDisplayName ?? "";
    paymentRefCtrl.text = transaction.value.txnPaymentReference ?? "";
    descCtrl.text = transaction.value.txnDescription ?? "";
    // displayTextCtrl.text = transaction.value.txnDisplayName;

    // controller which triggers above method
    // check for editor tag, if match don't refresh

    if (editorTAG != 'txn_subtotal')
      subTotalAmountCtrl.text = transaction.value.txnSubTotalAmount != 0.0
          ? (transaction.value.txnSubTotalAmount ?? 0.00).toStringAsFixed(2)
          : "";

    if (editorTAG != 'txn_discount_percent')
      discountPercentageCtrl.text = transaction.value.txnDiscountPercent != 0.0
          ? (transaction.value.txnDiscountPercent ?? 0.00).toStringAsFixed(2)
          : "";

    if (editorTAG != 'txn_discount_amount')
      discountAmountCtrl.text = transaction.value.txnDiscountAmount != 0.0
          ? (transaction.value.txnDiscountAmount ?? 0.00).toStringAsFixed(2)
          : "";

    if (editorTAG != 'txn_tax_percent')
      vatPercentCtrl.text = transaction.value.txnTaxPercent != 0.0
          ? (transaction.value.txnTaxPercent ?? 0.00).toStringAsFixed(2)
          : "";

    if (editorTAG != 'txn_tax_amount')
      vatAmountCtrl.text =
          (transaction.value.txnTaxAmount?.abs() ?? 0.00).toString();

    if (editorTAG != 'txn_cash_amount') {
      receivedAmountCtrl.text = transaction.value.txnCashAmount != 0.0
          ? (transaction.value.txnCashAmount ?? 0.00).toStringAsFixed(2)
          : "";
    }
  }

  updateDiscountPercentage(String dis, {String? editorTag}) {
    // Allow empty string without forcing "0.00" immediately
    if (dis.isEmpty) {
      discountAmountCtrl.text = "0.00";
      return;
    }
    ;

    double discountPercent = parseDouble(dis) ?? 0.00;
    transaction.value.txnDiscountPercent = discountPercent;

    double amt = parseDouble(((discountPercent *
                0.01 *
                (transaction.value.txnSubTotalAmount ?? 0.0))
            .toStringAsFixed(2))) ??
        0.00;

    transaction.value.txnDiscountAmount = amt;

    // If discount is 0, explicitly set discountAmountCtrl to "0.00"
    discountAmountCtrl.text = amt == 0.00 ? "0.00" : amt.toStringAsFixed(2);
  }

  late Debouncer<String> discountAmtDebouncer =
      Debouncer(const Duration(milliseconds: 2000), (value) {
    updateDiscountAmount(value);
  }, '');

  updateDiscountAmount(String dis, {String? editorTag}) {
    double disAmt = (parseDouble(dis) ?? 0.00);
    print("this is dis amt $disAmt");

    double subTotal = (transaction.value.txnSubTotalAmount ?? 0.00);

    double disPercent = (disAmt / subTotal) * 100;
    print("this is dis percent $disPercent");

    transaction.value.txnDiscountAmount = disAmt;
    transaction.value.txnDiscountPercent = disPercent;
    transaction.refresh();
    recalculateDataForItem(editorTag: editorTag);
    assignTransactionToTextFields(editorTAG: "txn_discount_amount");
  }

  initEdit(saleID, readOnlyFlag) async {
    _isLoading(true);
    _isLoading.refresh();
    final tempDir = await getTemporaryDirectory();

    Tuple3<SaleModel, List<LineItemDetailModel>, List<TxnImageModel>> dt =
        await saleRepository.getSaleById(saleID);

    Log.d("got  sale item  ${dt.item1.ledgerId}");
    LedgerDetailModel party =
        await _ledgerRepository.getLedgerWithBalanceById(dt.item1.ledgerId!);
    Log.d("this is party ${party.ledgerTitle}");

    _editFlag.value = true;
    transaction.value = dt.item1;
    transaction.refresh();
    items.value = dt.item2;
    images.value = dt.item3;
    files.clear();
    List<File> prevFiles = [];

    await Future.wait(
      dt.item3.map(
        (e) async {
          final file =
              await new File('${tempDir.path}/image-${e.sno}.${e.imageExt}')
                  .create();
          file.writeAsBytesSync(e.imageBitmap!);
          prevFiles.add(file);
        },
      ),
    );
    files.addAll(prevFiles);
    items.refresh();
    files.refresh();

    previousBillNo = dt.item1.txnRefNumberChar;
    onChangeParty(party);

    if ((dt.item1.txnTaxAmount ?? 0.00) > 0.0) {
      _isVatEnabled.value = true;
      _isVatEnabled.refresh();
    }

    assignTransactionToTextFields();
    _readOnlyFlag.value = readOnlyFlag;
    // Future.delayed(Duration(seconds: 2), () {
    //   _isLoading(false);
    // });
    _isLoading(false);
    _isLoading.refresh();
  }

  var imageList = [].obs;

  Future<List<TxnImageModel>> getTxnImageModelFromFiles(
      List<File>? _files) async {
    List<TxnImageModel> txnImageModels = [];

    if (_files != null) {
      await Future.wait(_files.map((element) async {
        Tuple2<List<int>, String> compressedImage =
            await compressImage((element));
        txnImageModels.add(TxnImageModel(
            imageBitmap: compressedImage.item1,
            imageExt: compressedImage.item2));
      }));
    }
    return txnImageModels;
  }

  Future<bool> checkLargeImage(List<File> fls, {bool? preConvertFiles}) async {
    //preConvertFiles can be used to convert files list to txn image model once, so than it don't need re convert
    bool status = false;
    // print("====>");
    // print("fls => ${fls.length}");
    // fls.forEach((element) {
    //   print(element.path);
    // });
    // await Future.wait(fls.map((element) async {
    //   Tuple2<List<int>, String> compressedImage =
    //       await compressImage(File(element.path));
    //   // Log.d("file size is" + compressedImage.item1.length.toString());
    //   if (compressedImage.item1.length > MAX_IMAGE_SIZE) {
    //     status = true;
    //     // return;
    //   }
    // }).toList());

    return status;
  }

  Future<bool> checkDuplicateBillNo() async {
    bool status = false;
    try {
      if (editFlag && transaction.value.txnRefNumberChar == previousBillNo) {
        status = false;
      } else {
        status = await saleRepository
            .isBillDuplicate(transaction.value.txnRefNumberChar);
      }
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<String?> createSale() async {
    String? status;

    try {
      //  assign  id in repo code
      // String primaryKeyPrefix = await getPrimaryKeyPrefix();
      // transaction.value.txnId = primaryKeyPrefix + uuidV4;

      transaction.value.txnType = TxnType.sales;
      transaction.value.txnDate = toDateAD(
          NepaliDateTime.parse(strTrim(transaction.value.txnDateBS ?? "")));

      if (!([null, ""].contains(transaction.value.chequeIssueDateBS))) {
        transaction.value.chequeIssueDate = toDateAD(NepaliDateTime.parse(
            strTrim(transaction.value.chequeIssueDateBS ?? "")));
      }
      List<TxnImageModel> tempImages = await getTxnImageModelFromFiles(files);

      status =
          await saleRepository.addSale(transaction.value, items, tempImages);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<bool> updateSale() async {
    bool status = false;

    try {
      transaction.value.txnDate = toDateAD(
          NepaliDateTime.parse(strTrim(transaction.value.txnDateBS ?? "")));
      if (!([null, ""].contains(transaction.value.chequeIssueDateBS))) {
        transaction.value.chequeIssueDate = toDateAD(NepaliDateTime.parse(
            strTrim(transaction.value.chequeIssueDateBS ?? "")));
      }
      List<TxnImageModel>? tempImages;
      if (files.isNotEmpty) {
        tempImages = await getTxnImageModelFromFiles(files);
      }
      status =
          await saleRepository.updateSale(transaction.value, items, tempImages);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }
}
