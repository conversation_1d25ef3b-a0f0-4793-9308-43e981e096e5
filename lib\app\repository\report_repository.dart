import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/others/annex_item_model.dart';
import 'package:mobile_khaata_v2/app/repository/bank_adjustment_repository.dart';
import 'package:mobile_khaata_v2/app/repository/cash_adjustment_repository.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:sqflite/sqflite.dart';
import 'package:tuple/tuple.dart';

class ReportRepository {
  final String tag = "ReportRepository";
  DatabaseHelper databaseHelper = DatabaseHelper();

  Future<Map<String, dynamic>> getAllTransactions(
      {required String startDate,
      required String endDate,
      required List<int> types,
      String? expenseCategoryID,
      String? ledgerID}) async {
    double totalAmt = 0.00;
    double totalSale = 0.00;
    double totalSaleReturn = 0.00;
    double totalPurchase = 0.00;
    double totalPurchaseReturn = 0.00;
    double totalExpense = 0.00;
    double totalPaymentIn = 0.00;
    double totalPaymentOut = 0.00;
    List<AllTransactionModel> txnList = [];

    if (types.isEmpty) {
      types = TxnType.allTypes;
    }

    try {
      Database? dbClient = await databaseHelper.database;

      String typesIN = types.join(",");

      String query = 'SELECT mk_transactions.*, '
          'mk_expense_category.expense_title, '
          'mk_ledger_master.ledger_title, mk_ledger_master.address AS ledger_address, mk_ledger_master.tin_no, mk_ledger_master.tin_flag '
          'FROM mk_transactions '
          'LEFT JOIN mk_expense_category ON mk_expense_category.expense_category_id = mk_transactions.expense_category_id  '
          'LEFT JOIN mk_ledger_master ON mk_ledger_master.ledger_id = mk_transactions.ledger_id  '
          'WHERE mk_transactions.last_activity_type!=? AND mk_transactions.txn_type IN ($typesIN) '
          'AND mk_transactions.txn_date between ? AND ? ';
      if (null != ledgerID) {
        query = '${query}AND mk_transactions.ledger_id = "$ledgerID" ';
      }
      if (null != expenseCategoryID) {
        query =
            '${query}AND mk_transactions.expense_category_id = "$expenseCategoryID" ';
      }

      query = '${query}ORDER BY mk_transactions.txn_date ASC';
      List<Map<String, dynamic>> txnDataListJson = (await dbClient!
          .rawQuery(query, [LastActivityType.Delete, startDate, endDate]));

      // Log.d("got transaction list ${txnDataListJson}");

      txnList = txnDataListJson.map((txnData) {
        AllTransactionModel txn = AllTransactionModel.fromJson(txnData);
        totalAmt += txn.txnTotalAmount!;

        totalSale +=
            ((TxnType.sales == txn.txnType) ? txn.txnTotalAmount : 0.00)!;
        totalSaleReturn +=
            ((TxnType.salesReturn == txn.txnType) ? txn.txnTotalAmount : 0.00)!;
        totalPurchase +=
            ((TxnType.purchase == txn.txnType) ? txn.txnTotalAmount : 0.00)!;
        totalPurchaseReturn += ((TxnType.purchaseReturn == txn.txnType)
            ? txn.txnTotalAmount
            : 0.00)!;
        totalExpense +=
            ((TxnType.expense == txn.txnType) ? txn.txnTotalAmount : 0.00)!;
        totalPaymentIn +=
            ((TxnType.paymentIn == txn.txnType) ? txn.txnTotalAmount : 0.00)!;
        totalPaymentOut +=
            ((TxnType.paymentOut == txn.txnType) ? txn.txnTotalAmount : 0.00)!;

        return txn;
      }).toList();
    } catch (e) {
      // Log.e(tag, e.toString());
    }

    Map<String, dynamic> returnValues = {};
    returnValues['txnList'] = txnList;
    returnValues['totalAmt'] = totalAmt;
    returnValues['totalSale'] = totalSale;
    returnValues['totalSaleReturn'] = totalSaleReturn;
    returnValues['totalPurchase'] = totalPurchase;
    returnValues['totalPurchaseReturn'] = totalPurchaseReturn;
    returnValues['totalExpense'] = totalExpense;
    returnValues['totalPaymentIn'] = totalPaymentIn;
    returnValues['totalPaymentOut'] = totalPaymentOut;
    // debugPrint(returnValues['txnList'].length.toString());
    // debugPrint(returnValues['txnList'][10].toString());

    return returnValues;
  }

  Future<List<Map<String, dynamic>>> getDayBookTransaction(
      {required String startDate}) async {
    List<int> types = [
      TxnType.sales,
      TxnType.salesReturn,
      TxnType.purchase,
      TxnType.purchaseReturn,
      TxnType.paymentIn,
      TxnType.paymentOut,
      TxnType.expense,
      TxnType.addCash,
      TxnType.reduceCash
    ];

    List<Map<String, dynamic>> txnDataListJson = [];

    try {
      Database? dbClient = await databaseHelper.database;

      String typesIN = types.join(",");

      String query = "SELECT "
          " '' AS display_name, "
          "cash_adj_amount AS total_amount, "
          "cash_adj_id AS txn_id, "
          "cash_adj_date AS txn_date, "
          "cash_adj_type AS txn_type, "
          "cash_adj_amount AS txn_cash_amount, "
          "last_activity_at "
          "FROM mk_cash_adjustments "
          "WHERE last_activity_type<>${LastActivityType.Delete} "
          "AND cash_adj_date='$startDate' "
          "UNION ALL "
          "SELECT "
          " mk_payment_types.pmt_type_short_name AS display_name, "
          " bank_adj_amount AS total_amount, "
          "bank_adj_id AS txn_id, "
          "bank_adj_date AS txn_date, "
          "bank_adj_type AS txn_type, "
          "bank_adj_amount AS txn_cash_amount, "
          "mk_bank_adjustments.last_activity_at "
          "FROM mk_bank_adjustments "
          "INNER JOIN mk_payment_types ON mk_bank_adjustments.bank_adj_bank_id=mk_payment_types.pmt_type_id AND mk_payment_types.last_activity_type<>3 "
          "WHERE mk_bank_adjustments.last_activity_type<>${LastActivityType.Delete} "
          "AND (mk_bank_adjustments.bank_adj_type='${TxnType.bankDeposit}' OR mk_bank_adjustments.bank_adj_type='${TxnType.bankWithdrawn}') "
          "AND mk_bank_adjustments.bank_adj_date='$startDate' "
          "UNION ALL "
          "SELECT "
          "CASE WHEN txn.txn_display_name IS NULL OR txn.txn_display_name = '' THEN mk_ledger_master.ledger_title ELSE txn.txn_display_name END AS display_name, "
          "(IFNULL(txn.txn_balance_amount,0.00) + IFNULL(txn.txn_cash_amount,0.00)) AS total_amount, "
          "txn.txn_id, "
          "txn.txn_date, "
          "txn.txn_type, "
          "txn.txn_cash_amount, "
          "txn.last_activity_at "
          "FROM mk_transactions txn "
          "LEFT JOIN mk_expense_category ON mk_expense_category.expense_category_id = txn.expense_category_id AND mk_expense_category.last_activity_type<>${LastActivityType.Delete} "
          "INNER JOIN mk_ledger_master ON mk_ledger_master.ledger_id = txn.ledger_id  AND mk_ledger_master.last_activity_type<>${LastActivityType.Delete} "
          "WHERE txn.last_activity_type<>${LastActivityType.Delete} AND txn.txn_type IN ($typesIN) "
          "AND txn.txn_date='$startDate' "
          "ORDER BY last_activity_at DESC;";

      txnDataListJson = (await dbClient!.rawQuery(query));

      // Log.d("got transaction list ${txnDataListJson}");
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return txnDataListJson;
  }

  Future<List<Map<String, dynamic>>> getAllTransactions2({
    required String startDate,
    required String endDate,
    required List<int> types,
    String? ledgerID,
  }) async {
    List<Map<String, dynamic>> txnDataListJson = [];

    if (types.isEmpty) {
      types = TxnType.allTypes;
    }

    try {
      Database? dbClient = await databaseHelper.database;

      String typesIN = types.join(",");

      String query = 'SELECT mk_transactions.*, '
          'mk_expense_category.expense_title, '
          'mk_ledger_master.ledger_title, mk_ledger_master.address AS ledger_address, mk_ledger_master.tin_no, mk_ledger_master.tin_flag '
          'FROM mk_transactions '
          'LEFT JOIN mk_expense_category ON mk_expense_category.expense_category_id = mk_transactions.expense_category_id  '
          'LEFT JOIN mk_ledger_master ON mk_ledger_master.ledger_id = mk_transactions.ledger_id  '
          'WHERE mk_transactions.last_activity_type!=? AND mk_transactions.txn_type IN ($typesIN) '
          'AND mk_transactions.txn_date between ? AND ? ';
      if (null != ledgerID) {
        query = '${query}AND mk_transactions.ledger_id = "$ledgerID" ';
      }

      query = '${query}ORDER BY mk_transactions.txn_date ASC';
      txnDataListJson = (await dbClient!
          .rawQuery(query, [LastActivityType.Delete, startDate, endDate]));
      debugPrint("got transaction list ${txnDataListJson.last}");

      // Log.d("got transaction list ${txnDataListJson.first}");
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return txnDataListJson;
  }

  Future<List<Map<String, dynamic>>> getPartyTransactions(
      {required String startDate,
      required String endDate,
      List<int>? types,
      String? ledgerID}) async {
    List<Map<String, dynamic>> txnDataListJson = [];

    if (null == types || types.isEmpty) {
      types = TxnType.ledgerTxnTypeList;
    }

    try {
      Database? dbClient = await databaseHelper.database;

      String typesIN = types.join(",");

      String query = 'SELECT mk_transactions.*, '
          'mk_expense_category.expense_title, '
          'mk_ledger_master.ledger_title, mk_ledger_master.address AS ledger_address, mk_ledger_master.tin_no, mk_ledger_master.tin_flag '
          'FROM mk_transactions '
          'LEFT JOIN mk_expense_category ON mk_expense_category.expense_category_id = mk_transactions.expense_category_id  '
          'LEFT JOIN mk_ledger_master ON mk_ledger_master.ledger_id = mk_transactions.ledger_id  '
          'WHERE mk_transactions.last_activity_type!=? AND mk_transactions.txn_type IN ($typesIN) '
          'AND mk_transactions.txn_date between ? AND ? ';
      if (null != ledgerID) {
        query = '${query}AND mk_transactions.ledger_id = "$ledgerID" ';
      }

      query = '${query}ORDER BY mk_transactions.txn_date ASC';
      txnDataListJson = (await dbClient!
          .rawQuery(query, [LastActivityType.Delete, startDate, endDate]));

      // Log.d("got transaction list ${txnDataListJson}");
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return txnDataListJson;
  }

  Future<List<Map<String, dynamic>>> getStockSummary(String endDate) async {
    List<Map<String, dynamic>> dataListJson = [];

    try {
      Database? dbClient = await databaseHelper.database;

      String query = """ SELECT itm.item_id, itm.item_name, 
          IFNULL(itm.opening_stock, 0.00) AS opening_stock,
          IFNULL(itm.item_purchase_unit_price, 0.00) AS item_purchase_unit_price, 
          IFNULL(itm.item_min_stock_quantity, 0.00) AS item_min_stock_quantity, 
          (IFNULL(SUM( 
          CASE 
          WHEN txn.txn_type = 2 THEN quantity 
          WHEN txn.txn_type = 8 THEN quantity 
          WHEN txn.txn_type = 11 THEN quantity 
          END), 0.00)+ IFNULL(itm.opening_stock, 0.00)) AS in_quantity, 
          IFNULL(SUM( 
          CASE 
          WHEN txn.txn_type = 1 THEN quantity 
          WHEN txn.txn_type = 9 THEN quantity 
          WHEN txn.txn_type = 12 THEN quantity 
          END), 0.00) AS out_quantity,

          IFNULL(SUM( 
          CASE 
          WHEN txn.txn_type = 2 THEN quantity 
          WHEN txn.txn_type = 11 THEN quantity 
          END), 0.00) AS total_purchase_quantity, 

          IFNULL(SUM( 
          CASE 
          WHEN txn.txn_type = 2 THEN total_amount 
          WHEN txn.txn_type = 11 THEN total_amount 
          ELSE 0.00
          END), 0.00) AS total_purchase_amount

          FROM mk_items itm 
          LEFT JOIN (${ItemRepository().itemTransactionBaseQuery(closingDate: endDate)}) AS txn ON itm.item_id = txn.item_id 
          WHERE itm.last_activity_type<>3 
          GROUP BY itm.item_id 
          ORDER BY item_name """;

      dataListJson = (await dbClient!.rawQuery(query));
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return dataListJson;
  }

  Future<List<Map<String, dynamic>>> getStockDetail(
      {required String startDate, required String endDate}) async {
    List<Map<String, dynamic>> dataListJson = [];

    try {
      Database? dbClient = await databaseHelper.database;

      String query = """
       SELECT itm.item_id, itm.item_name, 
          IFNULL(itm.item_purchase_unit_price, 0.00) AS item_purchase_unit_price, 
          IFNULL(itm.item_min_stock_quantity, 0.00) AS item_min_stock_quantity, 
          IFNULL(SUM( 
          CASE 
          WHEN txn.txn_type = 2 THEN quantity 
          WHEN txn.txn_type = 8 THEN quantity 
          WHEN txn.txn_type = 11 THEN quantity 
          END), 0.00) AS in_quantity, 

          IFNULL(SUM( 
          CASE 
          WHEN txn.txn_type = 1 THEN quantity 
          WHEN txn.txn_type = 9 THEN quantity 
          WHEN txn.txn_type = 12 THEN quantity 
          END), 0.00) AS out_quantity

          FROM mk_items itm 
          LEFT JOIN (${ItemRepository().itemTransactionBaseQuery(openingDate: startDate, closingDate: endDate)}) AS txn ON itm.item_id = txn.item_id 
          WHERE itm.last_activity_type<>3 
          GROUP BY itm.item_id 
          ORDER BY item_name ASC ;
       """;

      dataListJson = (await dbClient!.rawQuery(query));

      // Log.d("got stock list ${dataListJson}");
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return dataListJson;
  }

  Future<List<Map<String, dynamic>>> getExpenseCategorySummary(
      {required String startDate, required String endDate}) async {
    List<Map<String, dynamic>> dataListJson = [];

    try {
      Database? dbClient = await databaseHelper.database;

      String query = 'SELECT mk_transactions.txn_date, '
          'IFNULL(SUM(mk_transactions.txn_cash_amount), 0.00) AS txn_cash_amount, '
          'IFNULL(SUM(mk_transactions.txn_balance_amount), 0.00) AS txn_balance_amount, '
          'mk_expense_category.expense_title, '
          'mk_ledger_master.ledger_title '
          'FROM mk_transactions '
          'INNER JOIN mk_expense_category ON mk_expense_category.expense_category_id = mk_transactions.expense_category_id  '
          'LEFT JOIN mk_ledger_master ON mk_ledger_master.ledger_id = mk_transactions.ledger_id  '
          'WHERE mk_transactions.last_activity_type<>${LastActivityType.Delete} AND mk_transactions.txn_type=${TxnType.expense} '
          'AND mk_transactions.txn_date between ? AND ? '
          'GROUP BY mk_transactions.expense_category_id '
          'ORDER BY mk_expense_category.expense_title ';
      dataListJson = (await dbClient!.rawQuery(query, [startDate, endDate]));

      // Log.d("got expense category list ${dataListJson}");
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return dataListJson;
  }

  Future<List<Map<String, dynamic>>> getDiscountSummary({
    required String startDate,
    required String endDate,
    required List<int> types,
    String? ledgerID,
  }) async {
    List<Map<String, dynamic>> txnDataListJson = [];

    if (types.isEmpty) {
      types = TxnType.allTypes;
    }

    try {
      Database? dbClient = await databaseHelper.database;

      String typesIN = types.join(",");

      String query = "SELECT ledger_title, "
          "SUM(CASE WHEN txn_type=${TxnType.sales} THEN txn_discount_amount ELSE 0.00 END) AS sale_discount_amount, "
          "SUM(CASE WHEN txn_type=${TxnType.purchase} THEN txn_discount_amount ELSE 0.00 END) AS purchase_discount_amount "
          "FROM (SELECT mk_transactions.txn_type, "
          "mk_transactions.txn_discount_amount, "
          "mk_ledger_master.ledger_id, mk_ledger_master.ledger_title "
          "FROM mk_transactions "
          "INNER JOIN mk_ledger_master ON mk_ledger_master.ledger_id=mk_transactions.ledger_id AND mk_ledger_master.last_activity_type<>${LastActivityType.Delete} "
          "WHERE mk_transactions.last_activity_type<>${LastActivityType.Delete} AND mk_transactions.txn_type IN ($typesIN) "
          "AND mk_transactions.txn_date between ? AND ? ) AS txn "
          "GROUP BY ledger_id ORDER BY ledger_title ";

      txnDataListJson = (await dbClient!.rawQuery(query, [startDate, endDate]));

      // Log.d("got transaction list ${txnDataListJson}");
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return txnDataListJson;
  }

  Future<Tuple2<double, List<Map<String, dynamic>>>> getBankTransaction(
      {required String bankId,
      required String startDate,
      required String endDate}) async {
    List<Map<String, dynamic>> txnDataListJson = [];
    double openingBalance = 0.00;

    try {
      Database? dbClient = await databaseHelper.database;

      openingBalance = await BankAdjustmentRepository()
          .getOpeningBankBalance(bankId: bankId, openingDate: startDate);
      txnDataListJson = await dbClient!.rawQuery(BankAdjustmentRepository()
          .bankBaseQuery(
              bankId: bankId, openingDate: startDate, closingDate: endDate));

      // Log.d(txnDataListJson);
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return Tuple2(openingBalance, txnDataListJson);
  }

  Future<List<AnnexItemModel>> getAnnexReport({
    required String startDate,
    required String endDate,
  }) async {
    List<AnnexItemModel> items = [];

    try {
      Database? dbClient = await databaseHelper.database;
      String query = """
          Select l.ledger_id,l.ledger_title,l.tin_no,l.tin_flag,
          IFNULL(SUM( 
          CASE 
          WHEN txn.txn_type = 1 AND txn.txn_tax_amount > 0.0 THEN (txn.txn_balance_amount + txn.txn_cash_amount) 
          END), 0.00) AS sales_total_with_vat,
          IFNULL(SUM( 
          CASE 
          WHEN txn.txn_type = 2 AND txn.txn_tax_amount > 0.0 THEN (txn.txn_balance_amount + txn.txn_cash_amount) 
          END), 0.00) AS purchase_total_with_vat,
          IFNULL(SUM( 
          CASE 
          WHEN txn.txn_type = 8 AND txn.txn_tax_amount > 0.0 THEN (txn.txn_balance_amount + txn.txn_cash_amount) 
          END), 0.00) AS sales_return_total_with_vat,
          IFNULL(SUM( 
          CASE 
          WHEN txn.txn_type = 9 AND txn.txn_tax_amount > 0.0 THEN (txn.txn_balance_amount + txn.txn_cash_amount) 
          END), 0.00) AS purchase_return_total_with_vat
          From mk_ledger_master l
          LEFT JOIN mk_transactions AS txn ON txn.ledger_id = l.ledger_id AND txn.last_activity_type<>3 AND txn.txn_date BETWEEN '$startDate' AND '$endDate'
          WHERE l.last_activity_type<>3
          AND l.ledger_type = 'G'
          GROUP BY l.ledger_id
      """;

      List<Map<String, dynamic>> jsonItem = await dbClient!.rawQuery(query);
      items = jsonItem.map((e) => AnnexItemModel.fromJson(e)).toList();
      // Log.d("got data $jsonItem");
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return items;
  }

  Future<Tuple2<double, List<Map<String, dynamic>>>> getCashTransaction(
      {required String startDate, required String endDate}) async {
    List<Map<String, dynamic>> txnDataListJson = [];
    double openingBalance = 0.00;

    try {
      Database? dbClient = await databaseHelper.database;

      openingBalance = await CashAdjustmentRepository()
          .getOpeningCashInHandBalance(openingDate: startDate);
      txnDataListJson = await dbClient!.rawQuery(CashAdjustmentRepository()
          .cashInHandBaseQuery(openingDate: startDate, closingDate: endDate));

      // Log.d(txnDataListJson);
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return Tuple2(openingBalance, txnDataListJson);
  }
}
