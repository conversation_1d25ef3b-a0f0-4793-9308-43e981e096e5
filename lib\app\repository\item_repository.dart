import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/model/database/item_modal.dart';
import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/model/others/item_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/item_transaction_model.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/item_type.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';

import 'package:sqflite/sqflite.dart';
import 'package:tuple/tuple.dart';

class ItemRepository {
  final String tag = "ItemRepository";
  final String tableName = "mk_items";
  DatabaseHelper databaseHelper = DatabaseHelper();
  QueryRepository queryRepository = QueryRepository();

  //========================================================================================= SYNCING ACTIONS
  Future<String> insert(ItemModel itemModel,
      {dynamic dbClient, String? batchID, bool runSyncFlag = true}) async {
    String itemId;

    dbClient ??= await databaseHelper.database;

    String primaryKeyPrefix = await getPrimaryKeyPrefix();
    itemModel.itemId = primaryKeyPrefix + uuidV4;

    itemModel.itemName = toBeginningOfSentenceCase(itemModel.itemName);
    itemModel.lastActivityAt = currentDateTime;
    itemModel.lastActivityBy = await getLastActivityBy();
    itemModel.lastActivityType = LastActivityType.New;

    await dbClient.insert(tableName, itemModel.toJson());

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.insert,
      data: itemModel.toJson(),
    );

    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    // Called from controller always, so  not needed
    if (runSyncFlag) {
      pushPendingQueries(
          source: "TRIGGER", dbClient: dbClient, singleBatchId: batchID);
    }
    // pushPendingQueries(source: "TRIGGER", dbClient: dbClient);

    itemId = itemModel.itemId!;
    return itemId;
  }

  Future<bool> update(ItemModel itemModel,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    itemModel.itemName = toBeginningOfSentenceCase(itemModel.itemName);
    itemModel.lastActivityAt = currentDateTime;
    itemModel.lastActivityBy = await getLastActivityBy();
    itemModel.lastActivityType = LastActivityType.Edit;

    String whereClause = "item_id = ?";
    List<dynamic> whereArgs = [itemModel.itemId];

    await dbClient.update(tableName, itemModel.toJson(),
        where: whereClause, whereArgs: whereArgs);

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.update,
      whereArgs: whereArgs,
      whereClause: whereClause,
      data: itemModel.toJson(),
    );
    await queryRepository.pushQuery(newQueryModel,
        dbClient: dbClient, batchID: batchID);

    // Called from controller always, so  not needed

    // pushPendingQueries(source: "TRIGGER", dbClient: dbClient);
    status = true;

    return status;
  }

  Future<Tuple2<bool, String>> delete(String itemID,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;
    String message = "";

    dbClient ??= await databaseHelper.database;
    String whereClause = "item_id = ?";
    List<dynamic> whereArgs = [itemID];
    Tuple2<bool, List<String>> hasRefRes =
        await DatabaseHelper.hasReferences(tablesWithColName: [
      {"table_name": "mk_line_items", "column_name": "item_id"},
      {"table_name": "mk_item_adjustments", "column_name": "item_adj_item_id"}
    ], value: itemID);

    if (hasRefRes.item1) {
      //  there is  reference of this id in other table
      // so throw  error message
      message =
          "Cannot delete Item. Please clear all transactions and adjustments related to item before deleting";
    } else {
      // can soft delete for given id
      dbClient.update(
          tableName, {"last_activity_type": LastActivityType.Delete},
          where: whereClause, whereArgs: whereArgs);

      QueryModel newQueryModel = QueryModel(
          tableName: tableName,
          queryType: QueryType.update,
          whereArgs: whereArgs,
          whereClause: whereClause,
          data: {"last_activity_type": LastActivityType.Delete});

      await queryRepository.pushQuery(newQueryModel,
          batchID: batchID, dbClient: dbClient);
      status = true;

      pushPendingQueries(source: "TRIGGER", dbClient: dbClient);

      message = "Item deleted successfully";
    }

    return Tuple2(status, message);
  }

  //=========================================================================================NON SYNCING ACTIONS
  Future<bool> checkUniqueItemName(String itemName, {String? itemId}) async {
    bool isUnique = await DatabaseHelper.isUnique(
      tableName: "mk_items",
      columnName: "item_name",
      checkValue: strTrim(itemName),
      keyColumn: "item_id",
      keyValue: itemId,
    );

    return isUnique;
  }

  Future<bool> checkUniqueItemCode(String itemCode, {String? itemId}) async {
    bool isUnique = await DatabaseHelper.isUnique(
      tableName: "mk_items",
      columnName: "item_code",
      checkValue: strTrim(itemCode),
      keyColumn: "item_id",
      keyValue: itemId,
    );

    return isUnique;
  }

  Future<String> createIfNotExistByItemName(String itemName,
      {dynamic dbClient, String? batchID}) async {
    String itemId;

    dbClient ??= await databaseHelper.database;

    itemName = toBeginningOfSentenceCase(strTrim(itemName)) ?? "";

    List<Map<String, dynamic>> itemJsonList = (await dbClient.rawQuery(
        "SELECT item_id FROM mk_items WHERE last_activity_type!=3 AND item_name = ?",
        [itemName]));

    if (itemJsonList.isNotEmpty) {
      itemId = itemJsonList[0]['item_id'];
      // item already exist, so return if of that item
    } else {
      itemId = await insert(
          ItemModel(
              itemType: ItemType.product,
              itemName: strTrim(itemName),
              itemSaleUnitPrice: 0.0,
              itemPurchaseUnitPrice: 0.0,
              unitConversionFactor: 0.0),
          dbClient: dbClient,
          batchID: batchID);
    }

    return itemId;
  }

  Future<bool> insertMultipleItem(List<ItemModel> items) async {
    bool status = false;
    Database? dbClient = await databaseHelper.database;

    String primaryKeyPrefix = await getPrimaryKeyPrefix();
    String batchID = primaryKeyPrefix + uuidV4;
    try {
      await dbClient!.transaction((batch) async {
        // var batch = txn.batch();
        await Future.wait(items.map((e) async {
          await insert(e,
              dbClient: batch, runSyncFlag: false, batchID: batchID);
        }).toList());
      });

      //push all data to sync
      pushPendingQueries(singleBatchId: batchID, source: "TRIGGER");
      status = true;
    } catch (e) {
      // Log.d("error $tag $e");
    }

    return status;
  }

  Future<ItemModel> getItemDetailForAddEdit(String itemId) async {
    ItemModel itemModel;

    Database? dbClient = await databaseHelper.database;

    List<Map<String, dynamic>> jsonItems = (await dbClient!.rawQuery(
        "SELECT * FROM mk_items WHERE last_activity_type!=? AND item_id=?",
        [LastActivityType.Delete, itemId]));
    debugPrint("jsonItem: $jsonItems");
    itemModel = ItemModel.fromJson(jsonItems.first);

    return itemModel;
  }

  Future<List<ItemModel>> getAllItems(String activeItemFlag) async {
    List<ItemModel> itemList = [];
    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> jsonItemList = [];

      if ("active" == activeItemFlag) {
        jsonItemList = await dbClient!.rawQuery(
            "SELECT * FROM mk_items WHERE last_activity_type!=? AND item_is_active=? ORDER BY item_name ASC",
            [LastActivityType.Delete, 1]);
      } else if ("inactive" == activeItemFlag) {
        jsonItemList = await dbClient!.rawQuery(
            "SELECT * FROM mk_items WHERE last_activity_type!=? AND item_is_active=? ORDER BY item_name ASC",
            [LastActivityType.Delete, 0]);
      } else {
        jsonItemList = await dbClient!.rawQuery(
            "SELECT * FROM mk_items WHERE last_activity_type!=? ORDER BY item_name ASC",
            [LastActivityType.Delete]);
      }

      itemList = jsonItemList.map((row) => ItemModel.fromJson(row)).toList();
    } catch (e) {
      // Log.e(tag, e.toString());
    }

    return itemList;
  }

  Future<ItemModel> getItemById(String itemId, {dynamic dbClient}) async {
    ItemModel item = ItemModel();

    dbClient ??= await databaseHelper.database;

    Map<String, dynamic> jsonItem = (await dbClient.rawQuery(
            "SELECT * FROM mk_items WHERE item_id=? AND last_activity_type!=3",
            [itemId]))
        .first;
    item = ItemModel.fromJson(jsonItem);

    return item;
  }

  Future<List<ItemModel>> getItemsByTitle(String title,
      {List<String>? includes, String activeItemFlag = "active"}) async {
    List<ItemModel> itemList = [];

    try {
      Database? dbClient = await databaseHelper.database;
      List<Map<String, dynamic>> jsonItemList = [];
      List<Map<String, dynamic>> includedItemList = [];

      if ("active" == activeItemFlag) {
        jsonItemList = await dbClient!.rawQuery(
            "SELECT * FROM mk_items WHERE item_name LIKE ? AND last_activity_type!=? AND item_is_active=? ORDER BY item_name ASC Limit 20",
            ["$title%", LastActivityType.Delete, 1]);
      } else if ("inactive" == activeItemFlag) {
        jsonItemList = await dbClient!.rawQuery(
            "SELECT * FROM mk_items WHERE item_name LIKE ? AND last_activity_type!=? AND item_is_active=? ORDER BY item_name ASC Limit 20",
            ["$title%", LastActivityType.Delete, 0]);
      } else {
        jsonItemList = await dbClient!.rawQuery(
            "SELECT * FROM mk_items WHERE item_name LIKE ? AND last_activity_type!=? ORDER BY item_name ASC Limit 20",
            ["$title%", LastActivityType.Delete]);
      }

      List<Map<String, dynamic>> listWithIncludes = jsonItemList;

      if (includes != null && includes.isNotEmpty) {
        String includedIDString = "\"${includes.join("\", \"")}\"";
        includedItemList = await dbClient.rawQuery(
            "SELECT * FROM mk_items WHERE item_id IN ($includedIDString)");
        listWithIncludes = [...jsonItemList, ...includedItemList];
        //done because toset,tolist was not getting distinct objects
        final ids = listWithIncludes.map((e) => e['item_id']).toSet();
        listWithIncludes.retainWhere((x) => ids.remove(x['item_id']));
      }

      itemList =
          listWithIncludes.map((row) => ItemModel.fromJson(row)).toList();
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return itemList;
  }

  Future<List<ItemDetailModel>> getAllActiveItemsDetail() async {
    List<ItemDetailModel> itemList = [];
    // try {
    Database? dbClient = await databaseHelper.database;

    String query = "SELECT item.*, "
        "IFNULL(bal.balance_quantity,0.00) AS balance_quantity, "
        "pu.unit_name, "
        "pu.unit_short_name, "
        "su.unit_name AS alt_unit_name, "
        "su.unit_short_name AS alt_unit_short_name "
        "FROM mk_items item "
        "LEFT JOIN v_item_balance bal ON bal.item_id=item.item_id "
        "LEFT JOIN mk_item_units pu ON pu.unit_id = item.base_unit_id "
        "LEFT JOIN mk_item_units su ON su.unit_id = item.alternate_unit_id  "
        "WHERE  item.last_activity_type!=3 AND item.item_is_active=1 "
        "ORDER BY item_name ASC;";

    List<Map<String, dynamic>> jsonItemList = (await dbClient!.rawQuery(query));
    itemList =
        jsonItemList.map((row) => ItemDetailModel.fromJson(row)).toList();
    // } catch (e) {
    //   // Log.e(tag, e.toString() + trace.toString());
    // }

    return itemList;
  }

  Future<ItemDetailModel> getItemsDetailById(String itemId) async {
    ItemDetailModel item = ItemDetailModel();
    try {
      Database? dbClient = await databaseHelper.database;

      String query = "SELECT item.*, "
          "IFNULL(bal.balance_quantity,0.00) AS balance_quantity, "
          "pu.unit_name, "
          "pu.unit_short_name, "
          "su.unit_name AS alt_unit_name, "
          "su.unit_short_name AS alt_unit_short_name "
          "FROM mk_items item "
          "LEFT JOIN v_item_balance bal ON bal.item_id=item.item_id "
          "LEFT JOIN mk_item_units pu ON pu.unit_id = item.base_unit_id "
          "LEFT JOIN mk_item_units su ON su.unit_id = item.alternate_unit_id  "
          // "WHERE item.item_id=? AND item.last_activity_type!=3;";
          "WHERE item.item_id=? ;";
      //AND item.last_activity_type!=3 allow to give detail for deleted item also

      Map<String, dynamic> jsonItem =
          (await dbClient!.rawQuery(query, [itemId])).first;
      item = ItemDetailModel.fromJson(jsonItem);
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return item;
  }

  String itemTransactionBaseQuery(
      {String? openingDate,
      String? closingDate,
      String? date,
      String? itemId}) {
    String query = "SELECT "
        "itm.item_id AS item_id, "
        "adj.item_adj_id AS txn_id, "
        "adj.item_adj_date AS txn_date, "
        "adj.item_adj_type AS txn_type, "
        "IFNULL(adj.item_adj_quantity, 0) AS quantity, "
        "adj.item_adj_atprice AS unit_price, "
        "(adj.item_adj_atprice * adj.item_adj_quantity) AS total_amount, "
        "adj.last_activity_at "
        "FROM mk_items itm "
        "INNER JOIN mk_item_adjustments adj ON itm.item_id = adj.item_adj_item_id AND adj.last_activity_type<>3 "
        // "AND adj.item_adj_date<'$openingDate' "
        "WHERE itm.item_type=1 AND itm.last_activity_type<>3 ";

    query += (null != itemId) ? " AND itm.item_id='$itemId' " : "";

    if (null != openingDate && null != closingDate) {
      query +=
          " AND adj.item_adj_date BETWEEN '$openingDate' AND '$closingDate' ";
    } else if (null != openingDate) {
      query += " AND adj.item_adj_date < '$openingDate'  ";
    } else if (null != closingDate) {
      query += " AND adj.item_adj_date >= '$closingDate'  ";
    } else if (null != date) {
      query += " AND adj.item_adj_date = '$date'  ";
    }

    query += "UNION ALL "
        "SELECT "
        "itm.item_id, "
        "txn.txn_id AS txn_id, "
        "txn.txn_date AS txn_date, "
        "txn.txn_type AS txn_type, "
        "CASE WHEN line_item_unit_conversion_factor>0 THEN line.quantity*line_item_unit_conversion_factor "
        "ELSE line.quantity END AS quantity, "
        "line.price_per_unit AS unit_price, "
        "line.total_amount, "
        "txn.last_activity_at "
        "FROM mk_items itm "
        "INNER JOIN mk_line_items line ON itm.item_id = line.item_id "
        "INNER JOIN mk_transactions txn ON txn.txn_id=line.txn_id AND txn.last_activity_type<>3 "
        "WHERE itm.item_type=1 AND itm.last_activity_type<>3 ";

    query += (null != itemId) ? " AND itm.item_id='$itemId' " : "";

    if (null != openingDate && null != closingDate) {
      query += " AND txn.txn_date BETWEEN '$openingDate' AND '$closingDate' ";
    } else if (null != openingDate) {
      query += " AND txn.txn_date < '$openingDate'  ";
    } else if (null != closingDate) {
      query += " AND txn.txn_date >= '$closingDate'  ";
    } else if (null != date) {
      query += " AND txn.txn_date = '$date'  ";
    }

    query += "ORDER BY txn.txn_date DESC, txn.last_activity_at DESC ";

    return query;
  }

  Future<List<ItemTransactionModel>> getTransactionForItem(
      String itemID) async {
    List<ItemTransactionModel> list = [];
    try {
      Database? dbClient = await databaseHelper.database;

      String baseQuery = itemTransactionBaseQuery(itemId: itemID);

      String sql = "SELECT item.*, unit.unit_short_name, txn.* "
          "FROM ($baseQuery) AS txn "
          "INNER JOIN mk_items item ON item.item_id = txn.item_id "
          "LEFT JOIN mk_item_units unit ON unit.unit_id=item.base_unit_id "
          "WHERE item.item_id='$itemID' "
          "ORDER BY txn_date DESC, last_activity_at DESC ";

      List<Map<String, dynamic>> jsonItems = await dbClient!.rawQuery(sql);
      // Log.d("got item txn $jsonItems");

      list = jsonItems.map((e) => ItemTransactionModel.fromJson(e)).toList();
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return list;
  }

  Future<List<Map<String, dynamic>>> getOpeningStockBalance(
      {String? openingDate, String? itemId}) async {
    List<Map<String, dynamic>> dataListJson = [];

    try {
      Database? dbClient = await databaseHelper.database;

      String baseQuery =
          itemTransactionBaseQuery(itemId: itemId, openingDate: openingDate);

      String query = "SELECT itm.item_id, itm.item_name, "
          "(IFNULL(SUM( "
          "CASE "
          "WHEN txn.txn_type = 1 THEN -quantity "
          "WHEN txn.txn_type = 2 THEN quantity "
          "WHEN txn.txn_type = 8 THEN quantity "
          "WHEN txn.txn_type = 9 THEN -quantity "
          "WHEN txn.txn_type = 11 THEN quantity "
          "WHEN txn.txn_type = 12 THEN -quantity "
          "END), 0.00)+ IFNULL(itm.opening_stock, 0.00)) AS balance_quantity "
          "FROM mk_items itm "
          "LEFT JOIN ($baseQuery) AS txn ON itm.item_id = txn.item_id "
          "WHERE itm.last_activity_type<>3 "
          "GROUP BY itm.item_id "
          "ORDER BY item_name ASC ";

      dataListJson = (await dbClient!.rawQuery(query));

      // Log.d("got opn stock list ${dataListJson}");
    } catch (e) {
      // Log.e(tag, e.toString());
    }
    return dataListJson;
  }

  Future<bool> replaceItemForSync() async {
    List<ItemModel> itemList = [];

    Database? dbClient = await databaseHelper.database;
    List<Map<String, dynamic>> jsonItemList = [];

    jsonItemList = (await dbClient!.rawQuery("SELECT * FROM mk_items"));

    print(jsonItemList);

    itemList = jsonItemList.map((row) => ItemModel.fromJson(row)).toList();

    String primaryKeyPrefix = await getPrimaryKeyPrefix();
    String batchID = primaryKeyPrefix + uuidV4;

    // ignore: avoid_function_literals_in_foreach_calls
    itemList.forEach((itemModel) async {
      QueryModel newQueryModel = QueryModel(
        tableName: tableName,
        queryType: QueryType.insert,
        data: itemModel.toJson(),
      );

      await queryRepository.pushQuery(newQueryModel,
          batchID: batchID, dbClient: dbClient);

      // Log.d(newQueryModel.toJson());
    });

    return pushPendingQueries(
        source: "TRIGGER", dbClient: dbClient, singleBatchId: batchID);
  }
}
