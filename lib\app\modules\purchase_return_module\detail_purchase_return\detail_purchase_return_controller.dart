import 'dart:io';

import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/unit_list_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/txn_image_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/purchase_return_model.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/app/repository/purchase_return_repository.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tuple/tuple.dart';

class DetailPurchaseReturnController extends GetxController {
  final String tag = "DetailPurchaseReturnController";

  var _isLoading = true.obs;
  var _editFlag = false.obs;
  var _readOnlyFlag = false.obs;
  var _isVatEnabled = false.obs;
  var _isReceived = false.obs;
  var _iscashPurchaseReturnSelected = false.obs;

  bool get iscashPurchaseReturnSelected => _iscashPurchaseReturnSelected.value;
  bool get isLoading => _isLoading.value;
  bool get editFlag => _editFlag.value;
  bool get readOnlyFlag => _readOnlyFlag.value;

  bool get isVatEnabled => _isVatEnabled.value;
  bool get isReceived => _isReceived.value;

  set setiscashPurchaseReturnSelected(bool flag) {
    _iscashPurchaseReturnSelected.value = flag;
    _iscashPurchaseReturnSelected.refresh();
  }

  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  set setIsReceived(bool flag) {
    _isReceived.value = flag;
  }

  // for edit case, to check if same bill no is used in update case, not to give
  // duplicate error;
  String? previousBillNo;

  PurchaseReturnRepository purchaseReturnRepository =
      PurchaseReturnRepository();

  final LedgerRepository _ledgerRepository = LedgerRepository();

  var transaction =
      PurchaseReturnModal(txnDateBS: currentDateBS, txnSubTotalAmount: 0).obs;

  List<LineItemDetailModel> items = [];

  List<TxnImageModel> images = [];

  List<File> files = [];

  final formKey = GlobalKey<FormState>();

  final TextEditingController billNoCtrl = TextEditingController();

  final TextEditingController partyNameCtrl = TextEditingController();
  final TextEditingController mobileCtrl = TextEditingController();
  final TextEditingController addressCtrl = TextEditingController();
  final TextEditingController panNoCtrl = TextEditingController();

  final TextEditingController displayTextCtrl = TextEditingController();
  final TextEditingController subTotalAmountCtrl = TextEditingController();
  final TextEditingController discountPercentageCtrl = TextEditingController();
  final TextEditingController discountAmountCtrl = TextEditingController();
  final TextEditingController vatAmountCtrl = TextEditingController();
  final TextEditingController vatPercentCtrl = TextEditingController();

  final TextEditingController totalAmountCtrl = TextEditingController();
  final TextEditingController receivedAmountCtrl = TextEditingController();
  final TextEditingController dueAmountCtrl = TextEditingController();
  final TextEditingController descCtrl = TextEditingController();
  final TextEditingController paymentRefCtrl = TextEditingController();

  UnitListController unitListController = Get.put(UnitListController());

  var selectedLedger = LedgerDetailModel().obs;

  @override
  void onInit() async {
    // clear all cache images of bill
    imageCache.clear();
    super.onInit();
  }

  initialize() {
    _isLoading(false);
  }

  @override
  void dispose() {
    discountPercentageCtrl.dispose();
    billNoCtrl.dispose();
    partyNameCtrl.dispose();
    vatAmountCtrl.dispose();
    totalAmountCtrl.dispose();
    receivedAmountCtrl.dispose();
    dueAmountCtrl.dispose();
    descCtrl.dispose();
    mobileCtrl.dispose();
    addressCtrl.dispose();
    panNoCtrl.dispose();
    displayTextCtrl.dispose();
    subTotalAmountCtrl.dispose();
    discountAmountCtrl.dispose();
    vatPercentCtrl.dispose();
    paymentRefCtrl.dispose();

    super.dispose();
  }

  recalculateForItems() {
    double itemSubTotal = 0.00;
    for (var li in items) {
      itemSubTotal = parseDouble(
          (itemSubTotal + (li.totalAmount ?? 0.0)).toStringAsFixed(2))!;
    }
    onSubTotalIndividualChange(itemSubTotal.toString());
  }

  recalculateDataForItem({String? editorTag}) {
    // handle value change in purchase return model in valid step:

    double subtoal = transaction.value.txnSubTotalAmount ?? 0.00;

    transaction.value.txnTaxableTotalAmount = parseDouble(
        (subtoal - (transaction.value.txnDiscountAmount ?? 0.00))
            .toStringAsFixed(2));

    transaction.value.txnTaxAmount = parseDouble(
        ((transaction.value.txnTaxPercent ?? 0.0) *
                0.01 *
                transaction.value.txnTaxableTotalAmount!)
            .toStringAsFixed(2));

    transaction.value.txnTotalAmount = parseDouble(
        (transaction.value.txnTaxableTotalAmount! +
                transaction.value.txnTaxAmount!)
            .toStringAsFixed(2));

    if (transaction.value.ledgerId != CASH_PURCHASE_LEDGER_ID) {
      //for credit or if received is checked
      transaction.value.txnCashAmount = isReceived
          ? transaction.value.txnTotalAmount
          : (transaction.value.txnCashAmount ?? 0.00);

      transaction.value.txnBalanceAmount = isReceived
          ? 0.0
          : parseDouble((transaction.value.txnTotalAmount! -
                  transaction.value.txnCashAmount!)
              .toStringAsFixed(2));
    } else {
      //for cash
      //total will always be cash amount for cash ledger
      transaction.value.txnCashAmount = transaction.value.txnTotalAmount;
      transaction.value.txnBalanceAmount = 0.00;
    }

    assignTransactionToTextFields(editorTAG: editorTag ?? "");
  }

  onvatPercentChange(String value, {String? editorTag}) {
    double taxPercentage = parseDouble(value)!.toPrecision(2);

    transaction.value.txnTaxPercent = taxPercentage;

    transaction.value.txnTaxAmount = parseDouble(
        ((taxPercentage * (transaction.value.txnTaxableTotalAmount ?? 0.00)) *
                0.01)
            .toStringAsFixed(2));

    transaction.value.txnTotalAmount = parseDouble(
        ((transaction.value.txnTaxableTotalAmount ?? 0.0) +
                transaction.value.txnTaxAmount!)
            .toStringAsFixed(2));

    changeReceivedAmount(
        transaction.value.ledgerId == CASH_PURCHASE_LEDGER_ID
            ? transaction.value.txnTotalAmount.toString()
            : 0.0.toString(),
        editorTag: editorTag ?? "");
  }

  onvatAmountChange(String value, {required String editorTag}) {
    transaction.value.txnTaxAmount = parseDouble(value);
    transaction.value.txnTaxPercent = parseDouble(
        ((parseDouble(value)! / transaction.value.txnTaxableTotalAmount!) *
                0.01)
            .toStringAsFixed(2));
    transaction.value.txnTotalAmount = parseDouble(
        (transaction.value.txnTaxableTotalAmount! +
                transaction.value.txnTaxAmount!)
            .toStringAsFixed(2));
    changeReceivedAmount(
        transaction.value.ledgerId == CASH_PURCHASE_LEDGER_ID
            ? transaction.value.txnTotalAmount.toString()
            : 0.0.toString(),
        editorTag: editorTag);
  }

  changeReceivedAmount(String value, {String? editorTag}) {
    transaction.value.txnCashAmount = parseDouble(value);
    transaction.value.txnBalanceAmount = parseDouble(
        (transaction.value.txnTotalAmount! - transaction.value.txnCashAmount!)
            .toStringAsFixed(2));
    assignTransactionToTextFields(editorTAG: editorTag ?? "");
  }

  onSubTotalIndividualChange(String val, {String? editorTag}) {
    transaction.value.txnSubTotalAmount = parseDouble(val);

    updateDiscountPercentage((transaction.value.txnDiscountPercent ?? 0.00),
        editorTag: editorTag ?? "");
    // recalculateDataForItem(editorTag: editorTag);
  }

  onToggleVat(bool flag) {
    _isVatEnabled.value = flag;
    _isVatEnabled.refresh();
    double VAT = flag ? VAT_PERCENTAGE : 0.00;

    transaction.value.txnTaxPercent = VAT;
    onvatPercentChange(VAT.toString());
  }

  onChangeParty(LedgerDetailModel party) {
    // ignore: null_aware_in_condition
    if (party.ledgerId != null) {
      // Log.d("selected ${transaction.value.toJson()}");
      transaction.value.ledgerId = party.ledgerId;
      partyNameCtrl.text = party.ledgerTitle ?? "";
      mobileCtrl.text = party.mobileNo ?? "";
      addressCtrl.text = party.address ?? "";
      panNoCtrl.text = party.tinNo ?? "";
      if (party.ledgerId == CASH_PURCHASE_LEDGER_ID) {
        displayTextCtrl.text = (transaction.value.txnDisplayName == "" ||
                transaction.value.txnDisplayName == null)
            ? party.ledgerTitle!
            : transaction.value.txnDisplayName!;
        transaction.value.txnDisplayName = displayTextCtrl.text;
        _iscashPurchaseReturnSelected.value = true;
        _iscashPurchaseReturnSelected.refresh();
        transaction.value.txnCashAmount =
            transaction.value.txnTotalAmount ?? 0.0;
        transaction.value.txnBalanceAmount = 0.00;
      }
    } else {
      transaction.value.ledgerId = null;
      transaction.value.txnCashAmount = 0.00;
      transaction.value.txnBalanceAmount = transaction.value.txnTotalAmount;
      partyNameCtrl.text = "";
      mobileCtrl.text = "";
      addressCtrl.text = "";
      panNoCtrl.text = "";
    }
    selectedLedger.value = party;
    selectedLedger.refresh();
    transaction.refresh();

    if ((transaction.value.txnSubTotalAmount ?? 0.0) > 0.00) {
      assignTransactionToTextFields();
    }
  }

  assignTransactionToTextFields({String? editorTAG}) {
    // Log.d("assignning to text ${transaction.value.toJson()}");

    // formKey.currentState.
    billNoCtrl.text = transaction.value.txnRefNumberChar ?? "";
    totalAmountCtrl.text =
        (transaction.value.txnTotalAmount ?? 0.0).toStringAsFixed(2);
    dueAmountCtrl.text =
        (transaction.value.txnBalanceAmount ?? 0.0).toStringAsFixed(2);
    displayTextCtrl.text = transaction.value.txnDisplayName ?? "";
    paymentRefCtrl.text = transaction.value.txnPaymentReference ?? "";

    descCtrl.text = transaction.value.txnDescription ?? "";
    displayTextCtrl.text = transaction.value.txnDisplayName ?? "";

    // // controller which triggers above method
    // // check for editor tag, if match don't refresh

    if (editorTAG != 'txn_subtotal') {
      subTotalAmountCtrl.text =
          (transaction.value.txnSubTotalAmount ?? 0.00).toStringAsFixed(2);
    }

    if (editorTAG != 'txn_discount_percent') {
      discountPercentageCtrl.text =
          (transaction.value.txnDiscountPercent ?? 0.00).toStringAsFixed(2);
    }

    if (editorTAG != 'txn_discount_amount') {
      discountAmountCtrl.text =
          (transaction.value.txnDiscountAmount ?? 0.00).toStringAsFixed(2);
    }

    if (editorTAG != 'txn_tax_percent') {
      vatPercentCtrl.text =
          (transaction.value.txnTaxPercent ?? 0.00).toStringAsFixed(2);
    }

    if (editorTAG != 'txn_tax_amount') {
      vatAmountCtrl.text =
          (transaction.value.txnTaxAmount ?? 0.00).toStringAsFixed(2);
    }

    if (editorTAG != 'txn_cash_amount') {
      receivedAmountCtrl.text = transaction.value.txnCashAmount != 0.0
          ? (transaction.value.txnCashAmount ?? 0.00).toStringAsFixed(2)
          : "";
    }
  }

  updateDiscountPercentage(double dis, {String? editorTag}) {
    transaction.value.txnDiscountPercent = dis;
    print("this is dis percent $dis");
    // double amt = parseDouble((parseDouble(dis)! *
    //         0.01 *
    //         (transaction.value.txnSubTotalAmount ?? 0.0))
    //     .toStringAsFixed(2))!;
    // transaction.value.txnDiscountAmount = amt;
    // transaction.refresh();
    // recalculateDataForItem(editorTag: editorTag);
  }

  updateDiscountAmount(String dis, {String? editorTag}) {
    transaction.value.txnDiscountAmount = parseDouble(dis);
    transaction.value.txnDiscountPercent =
        (((parseDouble(dis)! / transaction.value.txnSubTotalAmount!) * 100.0)
            .toPrecision(2));
    transaction.refresh();
    recalculateDataForItem(editorTag: editorTag);
  }

  initEdit(purchaseReturnID, readOnlyFlag) async {
    _isLoading(true);
    _isLoading.refresh();
    final tempDir = await getTemporaryDirectory();

    Tuple3<PurchaseReturnModal, List<LineItemDetailModel>, List<TxnImageModel>>
        dt =
        await purchaseReturnRepository.getPurchaseReturnById(purchaseReturnID);

    // print("this is the value of dt ==> ${dt}");

    LedgerDetailModel party = await _ledgerRepository
        .getLedgerWithBalanceById(dt.item1.ledgerId ?? "");

    // print("this is party $party");

    _editFlag.value = true;
    // print("this is editflag value ${_editFlag.value}");
    transaction.value = dt.item1;
    // print("this is purchase return Modal ${transaction}");

    items = dt.item2;
    // print("this are items ${items.length}");
    images = dt.item3;
    files.clear();
    List<File> prevFiles = [];

    await Future.wait(dt.item3.map((e) async {
      final file =
          await File('${tempDir.path}/image-${e.sno}.${e.imageExt}').create();
      file.writeAsBytesSync(e.imageBitmap!);
      prevFiles.add(file);
    }));
    files.addAll(prevFiles);

    // print("this are files ${files}");

    previousBillNo = dt.item1.txnRefNumberChar;

    // yaha deke check garne
    onChangeParty(party);

    if (dt.item1.txnTaxAmount! > 0.0) {
      _isVatEnabled.value = true;
      _isVatEnabled.refresh();
    }
    // if(dt.item1.txn)
    if (dt.item1.txnCashAmount! > 0) {
      _isReceived(true);
      _isReceived.refresh();
    }

    // // assignTransactionToTextFields();
    _readOnlyFlag.value = readOnlyFlag;
    _isLoading(false);
    _isLoading.refresh();
    // print("this is flag ==> ${readOnlyFlag}");
  }

  Future<List<TxnImageModel>> getTxnImageModelFromFiles(
      List<File> files) async {
    List<TxnImageModel> txnImageModels = [];

    await Future.wait(files.map((element) async {
      Tuple2<List<int>, String> compressedImage = await compressImage(element);
      txnImageModels.add(TxnImageModel(
          imageBitmap: compressedImage.item1, imageExt: compressedImage.item2));
    }));
    return txnImageModels;
  }

  Future<bool> checkLargeImage(List fls, {bool? preConvertFiles}) async {
    //preConvertFiles can be used to convert files list to txn image model once, so than it don't need re convert
    bool status = false;
    await Future.wait(fls.map((element) async {
      Tuple2<List<int>, String> compressedImage = await compressImage(element);
      // Log.d("file size is" + compressedImage.item1.length.toString());
      if (compressedImage.item1.length > MAX_IMAGE_SIZE) {
        status = true;
        // return;
      }
    }).toList());

    return status;
  }

  Future<bool> checkDuplicateBillNo() async {
    bool status = false;
    try {
      if (editFlag && transaction.value.txnRefNumberChar == previousBillNo) {
        status = false;
      } else {
        status = await purchaseReturnRepository
            .isBillDuplicate(transaction.value.txnRefNumberChar);
      }
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  // Future<bool> createPurchaseReturn() async {
  //   bool status = false;

  //   try {
  //     // String primaryKeyPrefix = await getPrimaryKeyPrefix();
  //     // transaction.value.txnId = primaryKeyPrefix + uuidV4;
  //     transaction.value.txnType = TxnType.purchaseReturn;
  //     transaction.value.txnDate =
  //         toDateAD(NepaliDateTime.parse(transaction.value.txnDateBS));

  //     List<TxnImageModel> tempImages =
  //         await getTxnImageModelFromFiles(files.value);

  //     status = await purchaseReturnRepository.addPurchaseReturn(
  //         transaction.value, items, tempImages);
  //   } catch (e, trace) {
  //     Log.e(tag, e.toString() + trace.toString());
  //   }
  //   return status;
  // }

  Future<bool> updatePurchaseReturn() async {
    bool status = false;

    try {
      transaction.value.txnDate =
          toDateAD(NepaliDateTime.parse(strTrim(transaction.value.txnDateBS!)));

      List<TxnImageModel> tempImages = await getTxnImageModelFromFiles(files);
      status = await purchaseReturnRepository.updatePurchaseReturn(
          transaction.value, items, tempImages);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }
}
