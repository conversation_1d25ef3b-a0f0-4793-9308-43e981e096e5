// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';

import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';

import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_dropdown.dart';
import 'package:mobile_khaata_v2/app/model/database/item_modal.dart';
import 'package:mobile_khaata_v2/app/modules/item_module/add_edit_item/add_edit_item_controller.dart';
import 'package:mobile_khaata_v2/database/item_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

Future<ItemModel> displayItemAddDialog(BuildContext context,
    {required String itemName}) async {
  ItemModel returnedData = await showDialog(
      context: context,
      useRootNavigator: true,
      barrierDismissible: false,
      builder: (_) {
        return AlertDialog(
          insetPadding:
              const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          contentPadding: EdgeInsets.zero,
          clipBehavior: Clip.hardEdge,
          // shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
          content: SizedBox(
            width: MediaQuery.of(context).size.width - 20,
            child: AddItemDialogPage(
              itemName: itemName,
            ),
          ),
        );
      });
  return returnedData;
}

class AddItemDialogPage extends StatelessWidget {
  final String tag = "AddItemDialogPage";
  final String? itemName;
  final addEditItemController = AddEditItemController();

  AddItemDialogPage({super.key, this.itemName}) {
    addEditItemController.item.itemType = ItemType.product;
    addEditItemController.item.itemName = itemName ?? "";
    addEditItemController.onInit();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (addEditItemController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }

      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: () {
              FocusScope.of(context).requestFocus(FocusNode());
            },
            child: Form(
              key: addEditItemController.formKey,
              child: SingleChildScrollView(
                child: Container(
                  margin: const EdgeInsets.only(top: 10),
                  padding: const EdgeInsets.only(
                    left: 15,
                    right: 15,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const SizedBox(height: 5.0),

                      //===============================================Item Name
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "सामानको नाम",
                            style: labelStyle2,
                          ),
                          const SizedBox(height: 5.0),
                          FormBuilderTextField(
                            name: "item_name",
                            autocorrect: false,
                            keyboardType: TextInputType.text,
                            textInputAction: TextInputAction.next,
                            style: formFieldTextStyle,
                            decoration:
                                formFieldStyle.copyWith(labelText: "Item Name"),
                            readOnly: addEditItemController.readOnlyFlag,
                            initialValue: addEditItemController.item.itemName,
                            onChanged: (value) {
                              addEditItemController.item.itemName =
                                  strTrim(value ?? "");
                            },
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return "सामानको नाम राख्नुहोस् (Fill Item Name)";
                              }
                              return null;
                            },
                          ),
                        ],
                      ),

                      const SizedBox(
                        height: 20,
                      ),

                      //===============================================Item Type Field
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "सामानको किसिम",
                            style: labelStyle2,
                          ),
                          const SizedBox(height: 5.0),
                          FormBuilderDropdown(
                            name: 'item_type',
                            style: formFieldTextStyle,
                            decoration:
                                formFieldStyle.copyWith(labelText: "Item Type"),
                            items: ItemType.itemTypeList.map((row) {
                              return DropdownMenuItem(
                                  value: row["value"],
                                  child: Text(row["text"]));
                            }).toList(),
                            // readOnly: addEditItemController.editFlag,
                            initialValue: addEditItemController.item.itemType,
                            onChanged: (value) {
                              addEditItemController.item.itemType =
                                  value as int?;
                              addEditItemController.itemRefresh();
                            },
                            validator: (value) {
                              if (value == null || value == 0) {
                                return "Please select Item type.";
                              }
                              return null;
                            },
                          ),
                        ],
                      ),

                      const SizedBox(height: 25.0),

                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "प्राथमिक एकाइ",
                            style: labelStyle2,
                          ),
                          const SizedBox(height: 5.0),
                          CustomDropdown(
                            readOnly: addEditItemController.readOnlyFlag,
                            style: formFieldTextStyle,
                            decoration: formFieldStyle,
                            placeholder: "Primary Unit",
                            options: addEditItemController.unitMap,
                            onChange: (value) {
                              addEditItemController
                                  .primaryUnitOnSelectHandler(value);
                            },
                            allowClear: true,
                            value: addEditItemController.item.baseUnitId,
                          ),
                        ],
                      ),

                      const SizedBox(height: 25.0),
                    ],
                  ),
                ),
              ),
            ),
          ),
          BottomSaveCancelButton(
            shadow: false,
            onSaveBtnPressedFn: (addEditItemController.readOnlyFlag)
                ? null
                : () async {
                    FocusScope.of(context).unfocus();
                    if (addEditItemController.formKey.currentState!
                        .validate()) {
                      ProgressDialog progressDialog = ProgressDialog(context,
                          type: ProgressDialogType.normal,
                          isDismissible: false);
                      progressDialog.update(
                          message: "Saving data. Please wait....");
                      await progressDialog.show();

                      bool status = false;
                      try {
                        if (!await addEditItemController
                            .checkUniqueItemName()) {
                          throw CustomException(
                              "सामान पहिल्यै थपिएको छ\n(Item  already exist)");
                        }

                        if (null == addEditItemController.item.baseUnitId) {
                          throw CustomException(
                              "प्राथमिक एकाइ छनौट गर्नुहोस्\n(Please select Primary Unit)");
                        }

                        if (!addEditItemController.editFlag) {
                          status = await addEditItemController.createItem();
                        } else {
                          status = await addEditItemController.updateItem();
                        }
                      } on CustomException catch (e) {
                        await progressDialog.hide();
                        showAlertDialog(context,
                            alertType: AlertType.Error,
                            alertTitle: "Error",
                            message: e.toString());
                        return;
                      } catch (e, trace) {
                        // Log.e(tag, e.toString() + trace.toString());
                      }
                      await progressDialog.hide();

                      if (status) {
                        Navigator.pop(context, addEditItemController.item);
                        String message = (addEditItemController.editFlag)
                            ? "Item Updated Successfully."
                            : "Item Created Successfully.";
                        showToastMessage(context,
                            message: message, duration: 2);
                      }
                    }
                  },
          ),
        ],
      );
    });
  }
}
