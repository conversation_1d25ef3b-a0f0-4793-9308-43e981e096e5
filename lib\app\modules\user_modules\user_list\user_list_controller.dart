import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/user_modal.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';

import 'package:tuple/tuple.dart';

class UserListController extends GetxController {
  final String tag = "UserListController";

  final _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  final _isError = false.obs;
  bool get isError => _isError.value;
  String errorMessage = "";

  List<UserModel> users = [];
  List<UserModel> _filteredUsers = [];
  List<UserModel> get filteredUsers => _filteredUsers;

  init() {
    getUsers();
  }

  @override
  onInit() {
    super.onInit();
    init();
  }

  refreshUsers() {
    init();
  }

  getUsers() async {
    _isError(false);
    _isLoading(true);
    errorMessage = "";
    ApiBaseHelper apiBaseHelper = ApiBaseHelper();
    ApiResponse apiResponse =
        await apiBaseHelper.get(apiBaseHelper.LIST_USER, accessToken: true);
    // Log.d("users");
    // Log.d(apiResponse.status);
    if (apiResponse.status) {
      try {
        List<dynamic> linksJson = (apiResponse.data ?? []);
        users = linksJson.map((e) => UserModel.fromJson(e)).toList();
        _filteredUsers.clear();
        _filteredUsers = users;
        _isLoading(false);
        update();
      } catch (e) {
        // Log.d("error parsing" + e.toString());
        _isError(true);
        errorMessage =
            "Cannot get users at this moment. Please try again later.";
        _isLoading(false);
      }
    } else {
      //error in gettting links
      _isError(true);
      errorMessage = apiResponse.msg ?? "";
      _isLoading(false);
    }
  }

  searchUsers(String searchString) {
    _filteredUsers.clear();
    for (var item in users) {
      _filteredUsers.addIf(
          item
              .toString()
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          item);
    }
  }

  Future<Tuple2<bool, String>> makeActiveInactive(int userId, int flag) async {
    bool status = false;
    String message = "";
    // ignore: unnecessary_null_comparison
    if (null != userId) {
      try {
        ApiBaseHelper apiBaseHelper = ApiBaseHelper();
        ApiResponse apiResponse = await apiBaseHelper.post(
            apiBaseHelper.TOGGLE_USER_STATUS + userId.toString(),
            {'is_active': flag},
            accessToken: true);

        if (apiResponse.status) {
          status = true;

          // update within the list
          _filteredUsers.map((element) {
            if (element.userId == userId) {
              element.isActive = flag;
              // Log.d("updating for ${element.toJson()}");
            }
          }).toList();
          // Log.d("new filtered ${filteredUsers.toString()}");
          users.map((element) {
            if (element.userId == userId) {
              element.isActive = flag;
            }
          }).toList();
          update();
        } else {}
        message = apiResponse.msg ?? "";
      } catch (e) {
        message = "Cannot get users at this moment. Please try again later.";
      }
    } else {
      message = "User id not provided";
    }

    return Tuple2(status, message);
  }
}
