import 'package:mobile_khaata_v2/app/model/database/notification_model.dart';
import 'package:mobile_khaata_v2/app/repository/notification_repository.dart';
import 'package:mobile_khaata_v2/database/notification_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/notification_helper.dart';

import '../main.dart';

Future<bool> notificationToPushNotification() async {
  List<NotificationModel> notifications =
      await NotificationRepository().getNotificationFor(date: tomorrowDate);

  int pushedAmount = 0;
  await Future.wait(notifications
      .map((e) async {
        if (![NotificationType.server, NotificationType.lowStock]
            .contains(e.notificationType)) {
          pushedAmount += 1;
          await showNotificationAt(
              flutterLocalNotificationsPlugin, e.notificationID ?? 0,
              body: e.subtitle ?? "",
              title: e.title ?? "",
              scheduledNotificationDateTime:
                  DateTime.parse(e.date ?? "").add(const Duration(hours: 10)));
        }
      })
      .toList()
      .toList());
  return pushedAmount > 0;
}
