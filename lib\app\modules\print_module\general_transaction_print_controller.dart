import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/app/repository/line_item_repository.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';

class GeneralTransactionPrintCotroller extends GetxController {
  final _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  final _mainTransaction = AllTransactionModel().obs;
  // ignore: deprecated_member_use
  List<LineItemDetailModel> _items = [];
  final _party = LedgerDetailModel().obs;

  AllTransactionModel get mainTransaction => _mainTransaction.value;
  List<LineItemDetailModel> get items => _items;
  LedgerDetailModel get party => _party.value;

  TransactionRepository transactionRepository = TransactionRepository();
  LineItemRepository lineItemRepository = LineItemRepository();
  LedgerRepository ledgerRepository = LedgerRepository();

  @override
  void onInit() {
    super.onInit();
  }

  getTransactionDetails(String txnID, int txnType) async {
    _isLoading(true);
    AllTransactionModel? allTransactionModel =
        await transactionRepository.getTransactionDetailById(txnID);
    if (allTransactionModel == null) return;
    LedgerDetailModel ledgerDetailModel = await ledgerRepository
        .getLedgerWithBalanceById(allTransactionModel.ledgerId!);

    Log.d("txn is ${allTransactionModel.toJson()}");
    if (null == allTransactionModel.txnDisplayName ||
        "" == allTransactionModel.txnDisplayName) {
      allTransactionModel.txnDisplayName = ledgerDetailModel.ledgerTitle;
    }
    Log.d("txn is ${allTransactionModel.toJson()}");

    _mainTransaction(allTransactionModel);
    _party(ledgerDetailModel);

    List<LineItemDetailModel> lineItems =
        await lineItemRepository.getLineDetailItemsForTransaction(txnID);
    _items.clear();
    _items.addAll(lineItems);

    Log.d("got party  ${party.toJson()}");
    _isLoading(false);
  }
}
