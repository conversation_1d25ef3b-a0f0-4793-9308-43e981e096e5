import 'package:get/get.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';

import 'package:tuple/tuple.dart';

import '../../../../main.dart';

class SecurityMenuPageController extends GetxController {
  final _isLoading = false.obs;

  bool get isLoading => _isLoading.value;

  final _multiUserValue = isMultiUser.obs;

  int get multiUserValue => _multiUserValue.value;

  final _isWebEnabledValue = isWebEnabled.obs;

  bool get isWebEnabledValue => _isWebEnabledValue.value;

  init() {}

  setmultiUserValue(int val) {
    _multiUserValue(val);
    _multiUserValue.refresh();
  }

  setWebEnabledValue(bool val) {
    _isWebEnabledValue(val);
    _isWebEnabledValue.refresh();
  }

  Future<Tuple2<bool, String>> toggleEnableWeb() async {
    bool status = false;
    String message = "";
    bool isSyncedAll = await pushPendingQueries(all: true);
    // isSyncedAll = isSyncedAll && (await pullPendingQueries(pullCount: -1));

    if (!isSyncedAll) {
      return Tuple2(status, "Failed to sync data. Please try  again later");
    }

    try {
      ApiBaseHelper apiBaseHelper = ApiBaseHelper();
      ApiResponse apiResponse = await apiBaseHelper
          .post(apiBaseHelper.DISABLE_WEB, {}, accessToken: true);

      if (apiResponse.status) {
        status = true;
        // isMultiUser = flag;
        // update within the list
      } else {}
      message = apiResponse.msg ?? "";
    } catch (e) {
      message = FAILED_OPERATION_ERROR;
    }

    return Tuple2(status, message);
  }

  Future<Tuple2<bool, String>> toggleMultiUser(int flag) async {
    bool status = false;
    String message = "";
    bool isSyncedAll = await pushPendingQueries(all: true);
    // isSyncedAll = isSyncedAll && (await pullPendingQueries(pullCount: -1));

    if (!isSyncedAll) {
      return Tuple2(status, "Failed to sync data. Please try  again later");
    }

    try {
      ApiBaseHelper apiBaseHelper = ApiBaseHelper();
      ApiResponse apiResponse = await apiBaseHelper.post(
          apiBaseHelper.TOGGLE_MULTI_USER, {'multi_user_status': flag},
          accessToken: true);

      if (apiResponse.status) {
        status = true;
        // isMultiUser = flag;
        // update within the list
      } else {}
      message = apiResponse.msg ?? "";
    } catch (e) {
      message = FAILED_OPERATION_ERROR;
    }

    return Tuple2(status, message);
  }
}
