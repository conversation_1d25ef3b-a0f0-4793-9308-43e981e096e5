import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/item_modal.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';
import 'package:mobile_khaata_v2/app/repository/unit_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';

import 'package:nepali_utils/nepali_utils.dart';
import 'package:sqflite/sqflite.dart';

class AddEditItemController extends GetxController {
  final String tag = "AddEditItemController";

  var _isLoading = false.obs;
  bool get isLoading => _isLoading.value;

  var _editFlag = false.obs;
  bool get editFlag => _editFlag.value;

  var _readOnlyFlag = false.obs;
  bool get readOnlyFlag => _readOnlyFlag.value;
  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  var _isMinStockNotifyEnabled = false.obs;
  bool get isMinStockNotifyEnabled => _isMinStockNotifyEnabled.value;
  set isMinStockNotifyEnabled(bool flag) {
    _isMinStockNotifyEnabled.value = flag;
    _isMinStockNotifyEnabled.refresh();
  }

  var _item = ItemModel().obs;
  ItemModel get item => _item.value;

  ItemRepository _itemRepository = ItemRepository();
  UnitRepository _unitRepository = UnitRepository();

  List<Map<String, dynamic>> unitMap = [];

  itemRefresh() {
    _item.refresh();
  }

  final formKey = GlobalKey<FormState>();

  @override
  Future<void> onInit() async {
    _isLoading(true);
    unitMap = (await _unitRepository.getAllUnits())
        .map((e) => {"key": e.unitId, "value": e.displayTitle})
        .toList();

    item.openingDateBS = currentDateBS;

    _isLoading(false);
    super.onInit();
  }

  @override
  void onClose() {
    super.onClose();
  }

  initEdit(String itemId) async {
    _isLoading(true);

    _item.value = await _itemRepository.getItemDetailForAddEdit(itemId);

    _editFlag.value = true;
    _readOnlyFlag.value = true;

    _isLoading(false);
  }

  itemTypeOnChangeHandler(value) {
    item.itemType = value;
    itemRefresh();
  }

  primaryUnitOnSelectHandler(unitId) {
    item.baseUnitId = unitId;
    itemRefresh();
  }

  secondaryUnitOnSelectHandler(unitId) {
    item.alternateUnitId = unitId;
    itemRefresh();
  }

  Future<bool> checkUniqueItemName() async {
    bool status = await _itemRepository.checkUniqueItemName(item.itemName ?? "",
        itemId: item.itemId);
    return status;
  }

  Future<bool> checkUniqueItemCode() async {
    bool status = await _itemRepository.checkUniqueItemName(item.itemCode ?? "",
        itemId: item.itemId);
    return status;
  }

  Future<bool> createItem() async {
    bool status = false;

    ItemModel newItemModel;

    try {
      newItemModel = item;
      newItemModel.openingDate = (null != strTrim(newItemModel.openingDateBS!))
          ? toDateAD(NepaliDateTime.parse(newItemModel.openingDateBS!))
          : null;

      if (null == newItemModel.alternateUnitId) {
        newItemModel.unitConversionFactor = 0;
      }

      DatabaseHelper databaseHelper = new DatabaseHelper();
      Database? dbClient = await databaseHelper.database;
      if (dbClient == null) return status;

      await dbClient.transaction((dbBatchTxn) async {
        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        String itemId = await _itemRepository.insert(newItemModel,
            dbClient: dbBatchTxn, batchID: batchID);

        pushPendingQueries(
            singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

        // await dbBatchTxn.commit(noResult: true, continueOnError: false);

        status = true;
      });
    } catch (e, trace) {
      Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<bool> updateItem() async {
    bool status = false;

    ItemModel newItemModel;

    try {
      newItemModel = item;

      // newItemModel.openingDate = (null != strTrim(newItemModel.openingDateBS!))
      //     ? toDateAD(NepaliDateTime.parse(newItemModel.openingDateBS!))
      //     : null;

      if (newItemModel.openingDate != null &&
          newItemModel.openingDateBS!.isNotEmpty) {
        newItemModel.openingDate =
            toDateAD(NepaliDateTime.parse(newItemModel.openingDateBS!));
      }

      if (null == newItemModel.alternateUnitId) {
        newItemModel.unitConversionFactor = 0;
      }

      DatabaseHelper databaseHelper = new DatabaseHelper();
      Database? dbClient = await databaseHelper.database;
      if (dbClient == null) return status;

      await dbClient.transaction((dbBatchTxn) async {
        // var dbBatchTxn = dnTxnClient.batch();

        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        await _itemRepository.update(newItemModel,
            dbClient: dbBatchTxn, batchID: batchID);

        pushPendingQueries(
            singleBatchId: batchID, source: "TRIGGER", dbClient: dbClient);

        // await dbBatchTxn.commit(noResult: true, continueOnError: false);

        status = true;
      });
    } catch (e, trace) {
      Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }
}
