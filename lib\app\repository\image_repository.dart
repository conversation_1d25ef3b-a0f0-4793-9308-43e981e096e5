import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/query_model.dart';
import 'package:mobile_khaata_v2/app/repository/query_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/last_activity_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class ImageRepository {
  final String tag = "ImageRepository";
  final String tableName = 'mk_images';
  DatabaseHelper databaseHelper = DatabaseHelper();
  QueryRepository queryRepository = QueryRepository();

  //========================================================================================= SYNCING ACTIONS
  Future<String> insert(ImageModel imageModel,
      {dynamic dbClient, String? batchID}) async {
    String imageId;

    dbClient ??= await databaseHelper.database;

    String primaryKeyPrefix = await getPrimaryKeyPrefix();
    imageId = primaryKeyPrefix + uuidV4;
    imageModel.imageId = imageId;

    imageModel.lastActivityAt = currentDateTime;
    imageModel.lastActivityBy = await getLastActivityBy();
    imageModel.lastActivityType = LastActivityType.New;

    await dbClient.insert(tableName, imageModel.toJson());

    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.insert,
      data: imageModel.toJson(),
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    return imageId;
  }

  Future<bool> update(ImageModel imageModel,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    imageModel.lastActivityAt = currentDateTime;
    imageModel.lastActivityBy = await getLastActivityBy();
    imageModel.lastActivityType = LastActivityType.Edit;

    String whereClause = "image_id = ?";
    List<dynamic> whereArgs = [imageModel.imageId];

    await dbClient.update(tableName, imageModel.toJson(),
        where: whereClause, whereArgs: whereArgs);
    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.update,
      whereArgs: whereArgs,
      whereClause: whereClause,
      data: imageModel.toJson(),
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);

    status = true;

    return status;
  }

  Future<bool> delete(String imageID,
      {dynamic dbClient, String? batchID}) async {
    bool status = false;

    dbClient ??= await databaseHelper.database;

    String whereClause = "image_id = ?";
    List<dynamic> whereArgs = [imageID];

    await dbClient.delete(tableName, where: whereClause, whereArgs: whereArgs);
    QueryModel newQueryModel = QueryModel(
      tableName: tableName,
      queryType: QueryType.delete,
      whereArgs: whereArgs,
      whereClause: whereClause,
    );
    await queryRepository.pushQuery(newQueryModel,
        batchID: batchID, dbClient: dbClient);
    status = true;

    return status;
  }

  //=========================================================================================NON SYNCING ACTIONS
  Future<ImageModel> getImageById(String imageId, {dynamic dbClient}) async {
    ImageModel imageModel = ImageModel();
    try {
      dbClient ??= await databaseHelper.database;

      Map<String, dynamic> imageData = (await dbClient.rawQuery(
              "SELECT im.* FROM mk_images im WHERE im.image_id=? AND im.last_activity_type!=?",
              [imageId, LastActivityType.Delete]))
          .first;
      imageModel = ImageModel.fromJson(imageData);
    } catch (e) {
      // Log.e(tag, e.toString() + trace.toString());
    }

    return imageModel;
  }
}
