import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/dashboard_item_controller.dart';
import 'package:mobile_khaata_v2/app/modules/credit_list_module/credit_list_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/expense_transaction_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/purchase_report.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/sales_report.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class DashboardViewItem extends StatefulWidget {
  @override
  State<DashboardViewItem> createState() => _DashboardViewItemState();
}

class _DashboardViewItemState extends State<DashboardViewItem> {
  final dashboardItemController =
      Get.put(DashboardItemController(), tag: "DashboardItemController");

  @override
  void initState() {
    // TODO: implement initState
    // DashboardViewItem({super.key}) {
    init();
    // dashboardItemController.init();
    // }
  }

  init() async {
    await Future.delayed(Duration(milliseconds: 500));
    await dashboardItemController.init();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(
      () {
        if (dashboardItemController.isLoading) {
          return Container(
            height: MediaQuery.of(context).size.height * 0.16,
            color: Colors.white,
            child: Center(
              child: CircularProgressIndicator(
                color: colorPrimary,
              ),
            ),
          );
        }
        if (dashboardItemController.hasPermission.value == false) {
          return const SizedBox(
            height: 0,
            width: 0,
          );
        } else {
          return Container(
            height: MediaQuery.of(context).size.height * 0.16,
            color: backgroundColorShade,
            child: ListView(
              scrollDirection: Axis.horizontal,
              children: [
                const SizedBox(
                  width: 5,
                ),
                Container(
                  margin:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  child: Card(
                    elevation: 0,
                    color: Colors.white,
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          Navigator.pushNamed(context, "/cashInHandDetail");
                        },
                        child: Container(
                          constraints: const BoxConstraints(minWidth: 150),
                          decoration: BoxDecoration(
                              border: Border.all(color: colorPrimary),
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(8))),
                          padding: const EdgeInsets.symmetric(
                              vertical: 8, horizontal: 10),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Image.asset(
                                    "images/cash_in_hand-blue.png",
                                    height: 26,
                                  ),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  const Text(
                                    "नगद मौज्दात\n(Cash In Hand)",
                                    style: TextStyle(
                                        color: Colors.black, fontSize: 12),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 8,
                              ),
                              Text(
                                formatCurrencyAmount(
                                    dashboardItemController.totalCashInHand),
                                style: TextStyle(
                                    color: colorPrimary, fontSize: 15),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  child: Card(
                    elevation: 0,
                    color: Colors.white,
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          Navigator.pushNamed(context, "/creditList");
                        },
                        child: Container(
                          constraints: const BoxConstraints(minWidth: 150),
                          decoration: BoxDecoration(
                              border: Border.all(color: colorPrimary),
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(8))),
                          padding: const EdgeInsets.symmetric(
                              vertical: 8, horizontal: 10),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Transform.rotate(
                                    angle: (3.14 / 1.3),
                                    alignment: Alignment.center,
                                    child: CircleAvatar(
                                      radius: 12,
                                      backgroundColor: colorGreenDark,
                                      child: const Icon(
                                        Icons.arrow_forward,
                                        color: Colors.white,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  const Text(
                                    "लिनुपर्ने\n(To Receive)",
                                    style: TextStyle(
                                        color: Colors.black, fontSize: 12),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 8,
                              ),
                              Text(
                                formatCurrencyAmount(
                                    dashboardItemController.totalToReceive),
                                style: TextStyle(
                                    color: colorPrimary, fontSize: 15),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  child: Card(
                    elevation: 0,
                    color: Colors.white,
                    child: InkWell(
                      onTap: () {
                        Navigator.pushNamed(context, "/salesReport",
                            arguments: SalesReport(
                              receivedStart: dashboardItemController.startDate,
                              // receivedEnd: dashboardItemController.endDate,
                            ));
                        // Navigator.pushNamed(context, '/testPrint');
                      },
                      child: Container(
                        constraints: const BoxConstraints(minWidth: 150),
                        decoration: BoxDecoration(
                            border: Border.all(color: colorPrimary),
                            borderRadius:
                                const BorderRadius.all(Radius.circular(8))),
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 10),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  "images/sales-blue.png",
                                  height: 26,
                                ),
                                const SizedBox(
                                  width: 5,
                                ),
                                Text(
                                  "बिक्री (Sales)\n(${dashboardItemController.salesMonth})",
                                  style: const TextStyle(
                                      color: Colors.black, fontSize: 12),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 8,
                            ),
                            Text(
                              formatCurrencyAmount(
                                  dashboardItemController.totalSales),
                              style:
                                  TextStyle(color: colorPrimary, fontSize: 15),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  child: Card(
                    elevation: 0,
                    color: Colors.white,
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: () {
                          Navigator.pushNamed(context, "/creditList",
                              arguments: const CreditListPage(
                                initialPage: 1,
                              ));
                        },
                        child: Container(
                          constraints: const BoxConstraints(minWidth: 150),
                          decoration: BoxDecoration(
                              border: Border.all(color: colorPrimary),
                              borderRadius:
                                  const BorderRadius.all(Radius.circular(8))),
                          padding: const EdgeInsets.symmetric(
                              vertical: 8, horizontal: 10),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Transform.rotate(
                                    angle: (-3.14 / 4),
                                    alignment: Alignment.center,
                                    child: CircleAvatar(
                                      radius: 12,
                                      backgroundColor: colorRedLight,
                                      child: const Icon(
                                        Icons.arrow_forward,
                                        color: Colors.white,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(
                                    width: 5,
                                  ),
                                  const Text(
                                    "तिर्नुपर्ने (To Pay)",
                                    style: TextStyle(
                                        color: Colors.black, fontSize: 12),
                                  ),
                                ],
                              ),
                              const SizedBox(
                                height: 8,
                              ),
                              Text(
                                formatCurrencyAmount(
                                    dashboardItemController.totalToPay.abs()),
                                style: TextStyle(
                                    color: colorPrimary, fontSize: 15),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  child: Card(
                    elevation: 0,
                    color: Colors.white,
                    child: InkWell(
                      onTap: () {
                        Navigator.pushNamed(context, "/purchaseReport",
                            arguments: PurchaseReport(
                              receivedStart: dashboardItemController.startDate,
                              // receivedEnd: dashboardItemController.endDate,
                            ));
                        // Navigator.pushNamed(context, '/testPrint');
                      },
                      child: Container(
                        constraints: const BoxConstraints(minWidth: 150),
                        decoration: BoxDecoration(
                            border: Border.all(color: colorPrimary),
                            borderRadius:
                                const BorderRadius.all(Radius.circular(8))),
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 10),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  "images/purchase-blue.png",
                                  height: 26,
                                ),
                                const SizedBox(
                                  width: 5,
                                ),
                                Text(
                                  "खरिद (Purchase)\n(${dashboardItemController.purchaseMonth})",
                                  style: const TextStyle(
                                      color: Colors.black, fontSize: 12),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 8,
                            ),
                            Text(
                              formatCurrencyAmount(
                                  dashboardItemController.totalPurchase),
                              style:
                                  TextStyle(color: colorPrimary, fontSize: 15),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
                  child: Card(
                    elevation: 0,
                    color: Colors.white,
                    child: InkWell(
                      onTap: () {
                        Navigator.pushNamed(
                            context, "/expenseTransactionReport",
                            arguments: ExpenseTransactionReport(
                              receivedStart: dashboardItemController.startDate,
                              // receivedEnd: dashboardItemController.endDate,
                            ));
                        // Navigator.pushNamed(context, '/testPrint');
                      },
                      child: Container(
                        constraints: const BoxConstraints(minWidth: 150),
                        decoration: BoxDecoration(
                            border: Border.all(color: colorPrimary),
                            borderRadius:
                                const BorderRadius.all(Radius.circular(8))),
                        padding: const EdgeInsets.symmetric(
                            vertical: 8, horizontal: 10),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Image.asset(
                                  "images/expense-small.png",
                                  height: 26,
                                ),
                                const SizedBox(
                                  width: 5,
                                ),
                                Text(
                                  "खर्च (Expenses)\n(${dashboardItemController.expenseMonth})",
                                  style: const TextStyle(
                                      color: Colors.black, fontSize: 12),
                                ),
                              ],
                            ),
                            const SizedBox(
                              height: 8,
                            ),
                            Text(
                              formatCurrencyAmount(
                                  dashboardItemController.totalExpenses),
                              style:
                                  TextStyle(color: colorPrimary, fontSize: 15),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(
                  width: 5,
                ),
              ],
            ),
          );
        }
      },
    );
  }
}
