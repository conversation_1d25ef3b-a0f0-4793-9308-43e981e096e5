import 'package:mobile_khaata_v2/app/model/database/notification_model.dart';
import 'package:mobile_khaata_v2/app/model/others/item_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/notification_repository.dart';
import 'package:mobile_khaata_v2/app/repository/report_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/database/notification_type.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/notification_helper.dart';

generateNotificationForLowStockItem() async {
  List<ItemDetailModel> lowStockList = [];

  List<Map<String, dynamic>> dataListJson =
      await ReportRepository().getStockSummary(currentDate);

  for (int i = 0; i < dataListJson.length; i++) {
    ItemDetailModel itemDtl = ItemDetailModel.fromJson(dataListJson[i]);
    itemDtl.balanceQuantity = itemDtl.inQuantity! - itemDtl.outQuantity!;

    if (itemDtl.balanceQuantity! <=
            parseDouble(itemDtl.itemMinStockQuantity)! &&
        0 < itemDtl.itemMinStockQuantity!) {
      //Check if already notified for  today

      //if not, local push  notificarion, and add to notification list

      lowStockList.add(itemDtl);

      NotificationModel lowStockNotification = NotificationModel(
          title: "Low Stock Notification",
          subtitle: "Item ${itemDtl.itemName} is getting low on stock.",
          date: currentDate,
          notificationType: NotificationType.lowStock,
          reminderId: itemDtl.itemId,
          notificationLinkType: NotificationLinkType.INTERNAL,
          linkURL: "/itemDetail",
          params: {"id": itemDtl.itemId});

      bool doesNotificationExistForToday =
          await DatabaseHelper.doesExistForGivenAttribute(
              {"notification_date": currentDate, "reminder_id": itemDtl.itemId},
              'mk_notifications');

      if (!doesNotificationExistForToday) {
        //only show and create notification if not  shown for today

        await NotificationRepository().insert(lowStockNotification);

        showNotificationAt(
            flutterLocalNotificationsPlugin, DateTime.now().millisecond % 19,
            body: lowStockNotification.subtitle ?? "",
            title: lowStockNotification.title ?? "",
            scheduledNotificationDateTime:
                DateTime.now().add(const Duration(seconds: 1)));
      }
    }
  }
}
