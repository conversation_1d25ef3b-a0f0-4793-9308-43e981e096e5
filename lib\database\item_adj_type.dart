class ItemAdjType {
  static final int opening = 10;
  static final int add = 11;
  static final int reduce = 12;

  static final Map<int, String> itemAdjTypeText = ({
    ItemAdjType.opening: "Opening Stock",
    ItemAdjType.add: "Add Stock",
    ItemAdjType.reduce: "Reduce Stock",
  });

  static final List itemAdjTypeList = [
    {
      "value": ItemAdjType.add,
      "text": ItemAdjType.itemAdjTypeText[ItemAdjType.add]
    },
    {
      "value": ItemAdjType.reduce,
      "text": ItemAdjType.itemAdjTypeText[ItemAdjType.reduce]
    }
  ];
}
