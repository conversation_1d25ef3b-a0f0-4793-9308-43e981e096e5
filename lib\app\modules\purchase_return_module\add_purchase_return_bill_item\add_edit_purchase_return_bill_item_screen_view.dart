import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/bill_item_response.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/item_autocomplete_textfield_with_add.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/purchase_return_module/add_purchase_return_bill_item/add_edit_purchase_return_bill_item_controller.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class AddEditPurchaseReturnBilledItemScreenView extends StatelessWidget {
  final String tag = "Sales Item Add/Edit View";
  final LineItemDetailModel? lineItemModel;

  final purchaseReturnController = AddEditPurchaseReturnBillItemController();

  AddEditPurchaseReturnBilledItemScreenView({this.lineItemModel}) {
    if (this.lineItemModel != null) {
      purchaseReturnController.initEdit(this.lineItemModel!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (false) {
      } else {
        return Column(mainAxisSize: MainAxisSize.min, children: [
          Container(
            child: GestureDetector(
              onTap: () => FocusScope.of(context).unfocus(),
              child: Container(
                child: Form(
                  key: purchaseReturnController.formKey,
                  child: Container(
                    child: Container(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 10, vertical: 10),
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    //===============================================Item
                                    Expanded(
                                      flex: 2,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'सामान छन्नुहोस्',
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          ItemAutoCompleteTextFieldWithAdd(
                                              controller:
                                                  purchaseReturnController
                                                      .itemNameCtrl,
                                              onChangedFn: (value) {
                                                // state.selectedItem = null;
                                              },
                                              onSuggestionSelectedFn: (item) {
                                                purchaseReturnController
                                                    .itemOnSelectHandler(
                                                        item.itemId,
                                                        unitID: item.baseUnitId,
                                                        item: item);
                                              })
                                        ],
                                      ),
                                    ),

                                    SizedBox(
                                      width: 10,
                                    ),

                                    //===============================================Unit
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'एकाइ',
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          DropdownButtonFormField(
                                            isExpanded: true,
                                            value: purchaseReturnController
                                                .billedItem.lineItemUnitId,
                                            decoration: formFieldStyle.copyWith(
                                              hintText: "Unit",
                                            ),
                                            items: purchaseReturnController
                                                .unitList
                                                .map((billedItemUnit) {
                                              return DropdownMenuItem(
                                                value: billedItemUnit.unitId,
                                                child: Text(
                                                    "${billedItemUnit.unitName} (${billedItemUnit.unitShortName})"),
                                              );
                                            }).toList(),
                                            onChanged: (value) =>
                                                purchaseReturnController
                                                    .unitOnSelectHandler(value),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 15,
                                ),

                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    //===============================================Quantity
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'परिमाण',
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          FormBuilderTextField(
                                            name: "qty",
                                            autocorrect: false,
                                            keyboardType:
                                                TextInputType.numberWithOptions(
                                                    decimal: true),
                                            inputFormatters: [
                                              FilteringTextInputFormatter.allow(
                                                  RegExp(r'^(\d+)?\.?\d{0,2}'))
                                            ],
                                            textInputAction:
                                                TextInputAction.next,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                labelText: "Quantity"),
                                            textAlign: TextAlign.end,
                                            controller: purchaseReturnController
                                                .qtyCtrl,
                                            onChanged: (value) =>
                                                purchaseReturnController
                                                    .qtyOnChangeHandler(value),
                                          ),
                                        ],
                                      ),
                                    ),

                                    SizedBox(
                                      width: 10,
                                    ),

                                    //===============================================Price/Rate
                                    Expanded(
                                      flex: 1,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            'मूल्य प्रति एकाइ',
                                            style: labelStyle2,
                                          ),
                                          SizedBox(height: 5.0),
                                          FormBuilderTextField(
                                            name: "rate",
                                            autocorrect: false,
                                            keyboardType:
                                                TextInputType.numberWithOptions(
                                                    decimal: true),
                                            inputFormatters: [
                                              FilteringTextInputFormatter.allow(
                                                  RegExp(r'^(\d+)?\.?\d{0,2}'))
                                            ],
                                            textInputAction:
                                                TextInputAction.done,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                labelText: "Price/Unit"),
                                            textAlign: TextAlign.end,
                                            controller: purchaseReturnController
                                                .rateCtrl,
                                            onChanged: (value) =>
                                                purchaseReturnController
                                                    .rateOnChangeHandler(value),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 15,
                                ),

                                //===============================================Amount
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'रकम',
                                      style: labelStyle2,
                                    ),
                                    SizedBox(height: 5.0),
                                    FormBuilderTextField(
                                        name: "amount",
                                        autocorrect: false,
                                        keyboardType:
                                            TextInputType.numberWithOptions(
                                                decimal: true),
                                        inputFormatters: [
                                          FilteringTextInputFormatter.allow(
                                              RegExp(r'^(\d+)?\.?\d{0,2}'))
                                        ],
                                        textInputAction: TextInputAction.done,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Amount"),
                                        textAlign: TextAlign.end,
                                        controller: purchaseReturnController
                                            .grossAmountCtrl,
                                        onChanged: (value) {
                                          debugPrint("amount value");
                                          debugPrint(value);

                                          debugPrint(value);
                                          purchaseReturnController
                                                  .grossAmountCtrl.selection =
                                              TextSelection.fromPosition(
                                                  TextPosition(
                                                      offset:
                                                          purchaseReturnController
                                                              .grossAmountCtrl
                                                              .text
                                                              .length));
                                          purchaseReturnController
                                              .amountOnChangeHandler(value);
                                        }),
                                  ],
                                ),
                                SizedBox(
                                  height: 15,
                                ),

                                // =============================================Discount
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "छुट (Discount)",
                                      style: labelStyle2,
                                    ),
                                    SizedBox(height: 5.0),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Container(
                                          width: 80,
                                          child: FormBuilderTextField(
                                            name: "txn_discount_percent",
                                            autocorrect: false,
                                            keyboardType:
                                                TextInputType.numberWithOptions(
                                                    decimal: true),
                                            textInputAction:
                                                TextInputAction.done,
                                            style: formFieldTextStyle,
                                            decoration: formFieldStyle.copyWith(
                                                suffix: Text("%"),
                                                labelText: "%"),
                                            textAlign: TextAlign.end,
                                            controller: purchaseReturnController
                                                .discountPercentageCtrl,
                                            onChanged: (value) {
                                              debugPrint(
                                                  purchaseReturnController
                                                      .discountPercentageCtrl
                                                      .text);
                                              purchaseReturnController
                                                      .discountPercentageCtrl
                                                      .selection =
                                                  TextSelection.fromPosition(
                                                      TextPosition(
                                                          offset: purchaseReturnController
                                                              .discountPercentageCtrl
                                                              .text
                                                              .length));
                                              if (value == "NaN") {
                                                purchaseReturnController
                                                    .discountPercentOnChangeHandler(
                                                        "0.00");
                                              } else {
                                                purchaseReturnController
                                                    .discountPercentOnChangeHandler(
                                                        value);
                                              }
                                            },
                                          ),
                                        ),
                                        SizedBox(
                                          width: 20,
                                        ),
                                        Expanded(
                                          child: Container(
                                            child: FormBuilderTextField(
                                              name: "txn_discount_amount",
                                              autocorrect: false,
                                              readOnly: purchaseReturnController
                                                  .editFlag,
                                              keyboardType: TextInputType
                                                  .numberWithOptions(
                                                      decimal: true),
                                              textInputAction:
                                                  TextInputAction.done,
                                              style: formFieldTextStyle,
                                              decoration: formFieldStyle.copyWith(
                                                  labelText:
                                                      "छुट रकम (Dis. Amount)"),
                                              textAlign: TextAlign.end,
                                              controller:
                                                  purchaseReturnController
                                                      .discountAmountCtrl,
                                              onChanged: (value) {
                                                purchaseReturnController
                                                        .discountAmountCtrl
                                                        .selection =
                                                    TextSelection.fromPosition(
                                                        TextPosition(
                                                            offset: purchaseReturnController
                                                                .discountAmountCtrl
                                                                .text
                                                                .length));

                                                if (value == "NaN") {
                                                  purchaseReturnController
                                                      .discountAmountOnChangeHandler(
                                                          "0.00");
                                                } else {
                                                  purchaseReturnController
                                                      .discountAmountOnChangeHandler(
                                                          value);
                                                }
                                              },
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 10,
                                ),

                                //===============================================Net Amount
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'खुद रकम',
                                      style: labelStyle2,
                                    ),
                                    SizedBox(height: 5.0),
                                    FormBuilderTextField(
                                      name: "amount",
                                      autocorrect: false,
                                      readOnly: true,
                                      keyboardType:
                                          TextInputType.numberWithOptions(
                                              decimal: true),
                                      inputFormatters: [
                                        FilteringTextInputFormatter.allow(
                                            RegExp(r'^(\d+)?\.?\d{0,2}'))
                                      ],
                                      textInputAction: TextInputAction.done,
                                      style: formFieldTextStyle,
                                      decoration: formFieldStyle.copyWith(
                                          labelText: "Net Amount"),
                                      textAlign: TextAlign.end,
                                      controller: purchaseReturnController
                                          .netAmountCtrl,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
          BottomSaveCancelButton(
            shadow: false,
            onSaveBtnPressedFn: () {
              debugPrint("Hello world");
              if (purchaseReturnController.formKey.currentState!.validate()) {
                if (purchaseReturnController.itemNameCtrl.text.isEmpty) {
                  showToastMessage(context,
                      message: "सामान खाली राख्न मिल्दैन |",
                      alertType: AlertType.Error);
                  return;
                }

                if (purchaseReturnController.qtyCtrl.text.isEmpty ||
                    0 >= parseDouble(purchaseReturnController.qtyCtrl.text)!) {
                  showToastMessage(context,
                      message:
                          "परिमाण (Quantity) खाली वा शून्य राख्न मिल्दैन |",
                      alertType: AlertType.Error);
                  return;
                }

                if (purchaseReturnController.rateCtrl.text.isEmpty ||
                    0 >= parseDouble(purchaseReturnController.rateCtrl.text)!) {
                  showToastMessage(context,
                      message: "मूल्य (Price) खाली वा शून्य राख्न मिल्दैन |",
                      alertType: AlertType.Error);
                  return;
                }
                if ((purchaseReturnController.billedItem.discountPercent ??
                        0.00) >
                    100) {
                  showToastMessage(context,
                      message:
                          "छुट १००% भन्दा ठूलो हुन सक्दैन | \nDiscount can't be greater than 100%.",
                      alertType: AlertType.Error);
                  return;
                }
                purchaseReturnController.billedItem.itemId =
                    purchaseReturnController.selectedItem.value.itemId;
                purchaseReturnController.billedItem.itemName =
                    strTrim(purchaseReturnController.itemNameCtrl.text);

                purchaseReturnController.billedItem.lineItemUnitId =
                    purchaseReturnController.selectedUnit.unitId;
                purchaseReturnController.billedItem.lineItemUnitName =
                    purchaseReturnController.selectedUnit.unitShortName;

                purchaseReturnController.billedItem.pricePerUnit =
                    parseDouble(purchaseReturnController.rateCtrl.text);
                purchaseReturnController.billedItem.quantity =
                    parseDouble(purchaseReturnController.qtyCtrl.text);
                purchaseReturnController.billedItem.discountPercent =
                    parseDouble(
                        purchaseReturnController.discountPercentageCtrl.text);
                purchaseReturnController.billedItem.discountAmount =
                    parseDouble(
                        purchaseReturnController.discountAmountCtrl.text);
                purchaseReturnController.billedItem.grossAmount =
                    parseDouble(purchaseReturnController.grossAmountCtrl.text);
                purchaseReturnController.billedItem.totalAmount =
                    parseDouble(purchaseReturnController.netAmountCtrl.text);

                AddSaleBilledItemResponse _addSaleBilledItemResponse =
                    AddSaleBilledItemResponse(
                        newFlag: false,
                        billedItem: purchaseReturnController.billedItem);
                Navigator.pop(context, _addSaleBilledItemResponse);
              }
            },
          ),
        ]);
      }
    });
  }
}
