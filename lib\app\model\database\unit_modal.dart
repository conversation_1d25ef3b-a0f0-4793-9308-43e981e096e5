// ignore_for_file: unnecessary_this

class UnitModel {
  UnitModel(
      {this.unitId,
      this.unitName,
      this.unitShortName,
      this.lastActivityType,
      this.lastActivityAt,
      this.lastActivityBy});

  String? unitId;
  String? unitName;
  String? unitShortName;

  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;

  factory UnitModel.fromJson(Map<String, dynamic> json) => UnitModel(
      unitId: json["unit_id"],
      unitName: json["unit_name"],
      unitShortName: json["unit_short_name"],
      lastActivityType: json["last_activity_type"],
      lastActivityAt: json["last_activity_at"],
      lastActivityBy: json['last_activity_by']);

  Map<String, dynamic> toJson() => {
        "unit_id": unitId,
        "unit_name": unitName,
        "unit_short_name": unitShortName,
        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        "last_activity_by": lastActivityBy
      };

  String get displayTitle {
    if (this.unitName != null &&
        this.unitName!.isNotEmpty &&
        this.unitShortName != null &&
        this.unitShortName!.isNotEmpty) {
      return '${this.unitName} (${this.unitShortName})';
    } else {
      return "";
    }
  }
}
