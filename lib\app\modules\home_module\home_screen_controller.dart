import 'dart:async';

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:localstorage/localstorage.dart';

import 'package:in_app_review/in_app_review.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/controllers/registration_detail_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/notification_model.dart';
import 'package:mobile_khaata_v2/app/modules/home_module/home_view.dart';
import 'package:mobile_khaata_v2/app/modules/home_module/logout_controller.dart';
import 'package:mobile_khaata_v2/app/repository/item_repository.dart';
import 'package:mobile_khaata_v2/app/repository/notification_repository.dart';
import 'package:mobile_khaata_v2/app/repository/reminder_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:mobile_khaata_v2/utilities/login_helper.dart';
import 'package:mobile_khaata_v2/utilities/reminder_to_notification.dart';
import 'package:mobile_khaata_v2/utilities/shared_pref_helper1.dart';
import 'package:mobile_khaata_v2/utilities/stock_low_notification_helper.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

Duration syncInterval = Duration(minutes: 10);
Duration reminderToNotificationInterval = Duration(minutes: 10);
Duration lowStockNotificationInterval = Duration(minutes: 10);

class HomeScreen extends StatefulWidget {
  @override
  HomeScreenController createState() => HomeScreenController();
}

class HomeScreenController extends State<HomeScreen>
    with WidgetsBindingObserver {
  final String tag = "Home Screen";

  Timer? timer, notificationTimer, lowStockAlertTimer;

  var hasNotification = false.obs;

  DatabaseHelper databaseHelper = new DatabaseHelper();
  bool isExpired = false;

  final InAppReview inAppReview = InAppReview.instance;

  RegistrationDetailController registrationDetailController = Get.put(
      RegistrationDetailController(),
      tag: "RegistrationDetailController");

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    if (state == AppLifecycleState.resumed) {
      // came back to Foreground

      // check if any notification  exist
      checkForNotification();

      if (!timer!.isActive) {
        timer = Timer.periodic(syncInterval, (Timer t) => pushPullAction());
      }
      if (!notificationTimer!.isActive) {
        notificationTimer = Timer.periodic(reminderToNotificationInterval,
            (Timer t) => notificationGenerationAction());
      }
      checkExpiry();
    }
  }

  checkForNotification() async {
    List<NotificationModel> notifications =
        await NotificationRepository().getNotificationFor(date: currentDate);
    if (notifications.length > 0) {
      hasNotification.value = true;
    } else {
      hasNotification.value = false;
    }
    hasNotification.refresh();
  }

  checkExpiry() async {
    bool status = await SharedPrefHelper1().isExpired;
    this.setState(() {
      isExpired = status;
    });
  }

  notificationGenerationAction() async {
    //process all reminders of tomorrow, to  schedule notification at  10 am

    // Log.d("running notificationGenerationAction");
    // reminder to  notification  table
    bool isCreated =
        await NotificationRepository().createNotificationFromReminder();
    //notification  to  push notification queue
    bool isPushed = false;
    if (isCreated) {
      isPushed = await notificationToPushNotification();
    }

    // update new date for scheduled reminders of  tomorrow
    if (isPushed) {
      await ReminderRepository().createNextScheduleFor(tomorrowDate);
    }
  }

  remindLowStockAction() async {
    // DatabaseHelper.doesExistForGivenAttribute(
    //     {"item_name": "Pencil", "item_is_active": 1}, "mk_items");

    await generateNotificationForLowStockItem();
    checkForNotification();
  }

  pushPullAction() async {
    // await pullPendingQueries();
    await pushPendingQueries();
    // Log.d("push  pull done");
  }

  deleteOldReminder() async {
    //run in init state, to clear old reminders
    // await ReminderRepository().clearOldReminders();
  }

  @override
  void initState() {
    registrationDetailController.onInit();
    checkAdmin();
    checkMultiUser();

    //delete  old reminder
    deleteOldReminder();

    //sync queries  and reminder to notification  action at  beginning
    pushPullAction();
    notificationGenerationAction();
    remindLowStockAction();

    // check if any notification  exist
    checkForNotification();

    //run  periodic task foreground
    if (timer.isNull) {
      // Log.d("running timer");
      timer = Timer.periodic(syncInterval, (Timer t) => pushPullAction());
    }
    if (notificationTimer.isNull) {
      notificationTimer = Timer.periodic(reminderToNotificationInterval,
          (Timer t) => notificationGenerationAction());
    }
    if (lowStockAlertTimer != null) {
      lowStockAlertTimer = Timer.periodic(lowStockNotificationInterval,
          (Timer t) => generateNotificationForLowStockItem());
    }
    // timer.

    WidgetsBinding.instance.addObserver(this);

    syncBackItem();

    super.initState();
  }

  void syncBackItem() async {
    final storage = new LocalStorage(MobileSettings);
    await storage.ready;
    bool backItemSynced = storage.getItem("BackItemSynced") ?? false;

    if (!backItemSynced) {
      ProgressDialog progressDialog = ProgressDialog(context,
          type: ProgressDialogType.normal, isDismissible: false);
      progressDialog.update(message: "Syncing Products. Please wait....");
      await progressDialog.show();

      ItemRepository repo = new ItemRepository();
      bool result = await repo.replaceItemForSync();

      if (result) {
        storage.setItem("BackItemSynced", true);
      }

      await progressDialog.hide();

      showAlertDialog(context,
          alertType: result ? AlertType.Success : AlertType.Error,
          alertTitle: "ब्याकअप जानकारी \n(Backup Information)",
          message: "Product Synced Successfully.");
    }
  }

  void checkAdmin() async {
    isAdmin = await LoginHelper().isAdminUser;
  }

  void checkMultiUser() async {
    isMultiUser = await SharedPrefHelper1().isMultiUser;
  }

  void checkreview() async {
    // checks in app review automtically
    // Log.d("checkig for review");
    if (await inAppReview.isAvailable()) {
      // Log.d("available for review");
      inAppReview.requestReview();
    }
  }

  @override
  void dispose() {
    databaseHelper.dispose();
    timer?.cancel();
    lowStockAlertTimer?.cancel();
    notificationTimer?.cancel();

    WidgetsBinding.instance.removeObserver(this);

    // Log.d("disposed main");
    super.dispose();
  }

  @override
  Widget build(BuildContext context) => HomeScreenView(
        state: this,
      );

  userOtherInfoEditButtonOnClickHandler() async {
    Navigator.pop(context, true);

    final dynamic pageResult = await Navigator.pushNamed(
          context,
          "/userOtherInfo",
        ) ??
        false;
    if (pageResult) {
      // refreshFirmData();
    }
  }

  logoutButtonOnClickHandler({bool isSwitch = false}) async {
    await logoutFromDevice(context, isSwitch: isSwitch);
  }
}
