import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/payment_type_model.dart';
import 'package:mobile_khaata_v2/app/model/others/bank_transaction_model.dart';
import 'package:mobile_khaata_v2/app/repository/payment_type_repository.dart';
import 'package:mobile_khaata_v2/app/repository/report_repository.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

import 'package:tuple/tuple.dart';

class BankReportController extends GetxController {
  final String tag = "BankReportController";

  ReportRepository _reportRepository = ReportRepository();

  var _txnLoading = false.obs;
  bool get txnLoading => _txnLoading.value;

  var _bankLoading = true.obs;
  bool get bankLoading => _bankLoading.value;

  var bankTransactions = <BankTransactionModel>[];
  var banks = <PaymentTypeModel>[].obs;

  var closingBalance = 0.00.obs;

  loadBankList() async {
    _bankLoading(true);
    banks.clear();
    banks.addAll(await PaymentTypeRepository().getAllBank());
    _bankLoading(false);
  }

  generateBankStatementReport(
      {required String bankId,
      required String startDate,
      required String endDate}) async {
    _txnLoading(true);

    Tuple2<double, List<Map<String, dynamic>>> data =
        await _reportRepository.getBankTransaction(
            bankId: bankId, startDate: startDate, endDate: endDate);

    // Log.d("got balance ${data.item1}");
    // Log.d("got txn ${data.item2}");

    bankTransactions.clear();
    closingBalance.value = 0.00;

    bankTransactions.add(BankTransactionModel(
      txnDateBS: toDateBS(DateTime.parse(startDate)),
      txnDate: startDate,
      txnAmount: data.item1,
      bankTransactionType: (data.item1 >= 0)
          ? TxnType.increaseBankBalance
          : TxnType.decreaseBankBalance,
      bankTransactionDesc: 'Balance B/F',
      txnTypeText: "",
    ));

    closingBalance.value = data.item1;

    for (int i = 0; i < data.item2.length; i++) {
      BankTransactionModel txn = BankTransactionModel.fromJson(data.item2[i]);
      closingBalance.value += txn.txnAmount ?? 0.00;
      txn.bankTransactionDesc = (null != strTrim(txn.bankTransactionDesc ?? ""))
          ? txn.bankTransactionDesc ?? "" + "\n" + "[${txn.txnTypeText}]"
          : "[${txn.txnTypeText}]";
      bankTransactions.add(txn);
    }

    _txnLoading(false);
  }
}
