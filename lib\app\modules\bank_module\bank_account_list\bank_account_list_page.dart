import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/payment_type_model.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_account_detail/bank_account_detail_page.dart';
import 'package:mobile_khaata_v2/app/modules/bank_module/bank_account_list/bank_account_list_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class BankAccountListPage extends StatelessWidget {
  final String tag = "BankAccountListPage";

  final controller =
      Get.put(BankAccountListController(), tag: "BankAccountListController");

  BankAccountListPage({super.key}) {
    controller.init();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: false,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        toolbarHeight: 60,
        elevation: 4,
        leading: BackButton(
          onPressed: () => Navigator.pop(context, false),
        ),
        centerTitle: false,
        titleSpacing: -10.0,
        backgroundColor: colorPrimary,
        title: const ListTile(
          contentPadding: EdgeInsets.only(right: 15),
          title: Text(
            "बैंक/सहकारी खाताहरूको सूची\n(Bank/Co-operatives Account List)",
            style: TextStyle(
                fontSize: 16,
                color: Colors.white,
                fontFamily: 'HelveticaRegular',
                fontWeight: FontWeight.bold),
          ),
        ),
      ),
      body: Column(
        children: [
          Obx(() {
            if (controller.isLoading) {
              return Container(
                  color: Colors.white,
                  child: const Center(child: CircularProgressIndicator()));
            }

            return Expanded(
                child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              child: _BankListView(controller.bankList),
            ));
          })
        ],
      ),
      floatingActionButton: FloatingActionButton.extended(
        heroTag: 'addEditBank',
        onPressed: () {
          Navigator.pushNamed(context, "/addEditBank");
        },
        icon: const Icon(Icons.add_circle),
        label: const Text('Add Account'),
        backgroundColor: colorPrimary,
      ),
    ));
  }
}

class _BankListView extends StatelessWidget {
  final List<PaymentTypeModel> _bankList;

  const _BankListView(this._bankList);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
        itemCount: _bankList.length,
        padding: const EdgeInsets.only(bottom: 80),
        shrinkWrap: true,
        itemBuilder: (ctx, int index) {
          PaymentTypeModel bank = _bankList[index];

          return Card(
            elevation: 4,
            shape: Border(left: BorderSide(color: colorPrimary, width: 5)),
            child: InkWell(
              onTap: () {
                Navigator.of(context).pushNamed('/bankAccountDetail',
                    arguments: BankAccountDetailPage(
                      bankId: bank.pmtTypeId,
                    ));
              },
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    DefaultTextStyle(
                      style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: textColor,
                          fontSize: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(bank.pmtTypeBankName ?? ""),
                        ],
                      ),
                    ),
                    const Divider(),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            const Expanded(flex: 1, child: Text("A/C Name:")),
                            Expanded(
                                flex: 1,
                                child: Text("${bank.pmtTypeBankAccountName}")),
                          ],
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Row(
                          children: [
                            const Expanded(flex: 1, child: Text("A/C No:")),
                            Expanded(
                                flex: 1,
                                child:
                                    Text("${bank.pmtTypeBankAccountNumber}")),
                          ],
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Row(
                          children: [
                            const Expanded(flex: 1, child: Text("A/C Branch:")),
                            Expanded(
                                flex: 1,
                                child:
                                    Text(bank.pmtTypeBankAccountBranch ?? '-')),
                          ],
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        Row(
                          children: [
                            const Expanded(
                                flex: 1, child: Text("Display Name:")),
                            Expanded(
                                flex: 1,
                                child: Text("${bank.pmtTypeShortName}")),
                          ],
                        ),
                      ],
                    ),
                    const Divider(),
                    DefaultTextStyle(
                      style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: textColor,
                          fontSize: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            formatCurrencyAmount(bank.pmtTypeCurrentBalance!),
                            style: TextStyle(
                                color: (bank.pmtTypeCurrentBalance! >= 0)
                                    ? colorGreenDark
                                    : colorOrangeDark),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }
}
