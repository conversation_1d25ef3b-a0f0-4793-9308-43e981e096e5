import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/sales_purchase_vat_report_print.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/report_controllers/report_transaction_controller.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';

// ignore: must_be_immutable
class VATPurchaseRegisterReport extends StatelessWidget {
  ReportTransactionController _controller = ReportTransactionController();
  List<int> types = [TxnType.purchase];
  String startDate = currentDate;
  String endDate = currentDate;

  VATPurchaseRegisterReport({super.key}) {
    generate();
  }

  generate() {
    _controller.generateVATPurchaseReport(
        startDate: startDate, endDate: endDate, types: types);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        // resizeToAvoidBottomPadding: true,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          elevation: 0,
          titleSpacing: -5.0,
          centerTitle: false,
          backgroundColor: colorPrimary,
          title: const Text(
            "भ्याट खरीद रिपोर्ट\n(VAT Purchase Register) ",
            style: TextStyle(
              fontSize: 16,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold,
            ),
          ),
          actions: [
            PrintButton(
              onPressed: () {
                Navigator.pushNamed(
                  context,
                  '/printSalesPurchaseVatReport',
                  arguments: SalesPurchaseVatPrintPage(
                    pageTitle: "VAT Purchase Report",
                    transactions: _controller.transactions,
                    txnTypeText: "Purchase",
                    startDate: startDate,
                    endDate: endDate,
                  ),
                );
              },
            )
          ],
        ),
        body: Container(
          color: Colors.black12,
          child: Column(
            children: [
              //=============================transaction date filter
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Row(
                  children: [
                    Expanded(
                      flex: 2,
                      child: ReportCustomDatePickerTextField(
                        initialValue: toDateBS(DateTime.parse(startDate)),
                        hintText: "From Date",
                        onChange: (selectedDate) {
                          startDate =
                              toDateAD(NepaliDateTime.parse(selectedDate));
                          generate();
                        },
                      ),
                    ),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10),
                        child: Text(
                          "TO",
                          style: labelStyle2,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: ReportCustomDatePickerTextField(
                        initialValue: toDateBS(DateTime.parse(endDate)),
                        hintText: "To Date",
                        onChange: (selectedDate) {
                          endDate =
                              toDateAD(NepaliDateTime.parse(selectedDate));
                          generate();
                        },
                      ),
                    ),
                  ],
                ),
              ),

              const Divider(
                height: 0,
                color: Colors.black54,
              ),

              Obx(
                () {
                  if (_controller.txnLoading) {
                    return Container(
                      color: Colors.white,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    );
                  }

                  if (_controller.transactions.isEmpty) {
                    return Container(
                      color: Colors.white,
                      width: double.infinity,
                      child: const Center(
                        child: Text(
                          "No Records",
                          style: TextStyle(color: Colors.black54),
                        ),
                      ),
                    );
                  } else {
                    return Expanded(
                      child: _TxnListView(_controller.transactions),
                    );
                  }
                },
              ),
            ],
          ),
        ),
        // extendBody: true,
        bottomNavigationBar: Container(
          height: 100,
          padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
          color: colorPrimary,
          child: SingleChildScrollView(
            child: Obx(
              () {
                return DefaultTextStyle(
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          const Expanded(
                            flex: 1,
                            child: Text("Total Taxable Amount: "),
                          ),
                          Expanded(
                            flex: 1,
                            child: Text(
                              formatCurrencyAmount(
                                _controller.totalBillAmount.value,
                                false,
                              ),
                              textAlign: TextAlign.right,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Row(
                        children: [
                          const Expanded(
                            flex: 1,
                            child: Text("Total VAT: "),
                          ),
                          Expanded(
                            flex: 1,
                            child: Text(
                              formatCurrencyAmount(
                                _controller.totalTaxAmount.value,
                                false,
                              ),
                              textAlign: TextAlign.right,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 10,
                      ),
                      Row(
                        children: [
                          const Expanded(
                            flex: 1,
                            child: Text("Total Purchase:  "),
                          ),
                          Expanded(
                            flex: 1,
                            child: Text(
                              formatCurrencyAmount(
                                _controller.totalAmount.value,
                                false,
                              ),
                              textAlign: TextAlign.right,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

class _TxnListView extends StatelessWidget {
  final List<AllTransactionModel> _transactionList;

  const _TxnListView(this._transactionList);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: _transactionList.length,
      // shrinkWrap: true,
      itemBuilder: (context, int index) {
        AllTransactionModel txn = _transactionList[index];

        return InkWell(
          // onTap: () => TransactionHelper.gotoTransactionEditPage(
          //     context, txn.txnId, txn.txnType),
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                DefaultTextStyle(
                  style: TextStyle(fontSize: 12, color: colorPrimary),
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        //====================================1st Column
                        Expanded(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${txn.txnDateBS}",
                                style: const TextStyle(color: Colors.black54),
                              ),
                              const SizedBox(
                                height: 5,
                              ),
                              Text(
                                "${txn.ledgerTitle ?? txn.txnDisplayName}",
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                                style: const TextStyle(
                                    fontSize: 12, fontWeight: FontWeight.bold),
                              ),
                              const SizedBox(
                                height: 5,
                              ),
                              if (null != txn.ledgerTinNo) ...{
                                Text(
                                  "${txn.ledgerTinFlag} No: ${txn.ledgerTinNo}",
                                  overflow: TextOverflow.ellipsis,
                                  maxLines: 2,
                                  style: const TextStyle(
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold),
                                ),
                                const SizedBox(
                                  height: 5,
                                ),
                              },
                              Text(
                                (null != txn.txnRefNumberChar)
                                    ? "${txn.txnTypeText}: #${txn.txnRefNumberChar}"
                                    : "${txn.txnTypeText}",
                                textAlign: TextAlign.left,
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          width: 20,
                        ),

                        //====================================3rd Column
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              //===============================================VAT
                              if (0.00 != parseDouble(txn.txnTaxAmount)) ...{
                                Text(
                                  "Taxable Amt: ${formatCurrencyAmount((txn.txnCashAmount! + txn.txnBalanceAmount!) - txn.txnTaxAmount!, false)}",
                                  textAlign: TextAlign.right,
                                ),
                                const Divider(
                                  height: 10,
                                ),
                                Text(
                                  "VAT@${txn.txnTaxPercent}%: ${formatCurrencyAmount(txn.txnTaxAmount!, false)}",
                                  textAlign: TextAlign.right,
                                ),
                                // ignore: equal_elements_in_set
                                const Divider(
                                  height: 10,
                                ),
                              },

                              Text(
                                "Total: ${formatCurrencyAmount(txn.txnCashAmount! + txn.txnBalanceAmount!, false)}",
                                textAlign: TextAlign.right,
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                //Add space if last element
                if (_transactionList.length - 1 == index) ...{
                  const SizedBox(
                      // height: 100,
                      )
                },
              ],
            ),
          ),
        );
      },
    );
  }
}
