import 'dart:io';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/controllers/unit_list_controller.dart';
import 'package:mobile_khaata_v2/app/model/database/txn_image_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/purchase_model.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/app/repository/purchase_repository.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:tuple/tuple.dart';

class AddEditPurchaseController extends GetxController {
  final String tag = "Purchase Controller";

  var _isLoading = true.obs;
  var _editFlag = false.obs;
  var _readOnlyFlag = false.obs;
  var _isVatEnabled = false.obs;
  var _isReceived = false.obs;
  var _isCashPurchaseSelected = false.obs;

  bool get isCashPurchaseSelected => _isCashPurchaseSelected.value;
  bool get isLoading => _isLoading.value;
  bool get editFlag => _editFlag.value;
  bool get readOnlyFlag => _readOnlyFlag.value;

  bool get isVatEnabled => _isVatEnabled.value;
  bool get isReceived => _isReceived.value;

  set setisCashPurchaseSelected(bool flag) {
    _isCashPurchaseSelected.value = flag;
    _isCashPurchaseSelected.refresh();
  }

  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  set setIsReceived(bool flag) {
    _isReceived.value = flag;
  }

  bool get setIsReceived => _isReceived.value;

  // for edit case, to check if same bill no is used in update case, not to give
  // duplicate error;
  String? previousBillNo;

  PurchaseRepository purchaseRepository = new PurchaseRepository();

  LedgerRepository _ledgerRepository = new LedgerRepository();

  var transaction = PurchaseModel(
          txnDateBS: currentDateBS,
          txnSubTotalAmount: 0,
          txnPaymentTypeId: PAYMENT_MODE_CASH_ID)
      .obs;

  var items = <LineItemDetailModel>[].obs;

  var images = <TxnImageModel>[].obs;

  var files = <File>[].obs;

  final formKey = GlobalKey<FormState>();

  final TextEditingController billNoCtrl = TextEditingController();

  final TextEditingController partyNameCtrl = TextEditingController();
  final TextEditingController mobileCtrl = TextEditingController();
  final TextEditingController addressCtrl = TextEditingController();
  final TextEditingController panNoCtrl = TextEditingController();

  final TextEditingController displayTextCtrl =
      TextEditingController(text: "Cash Purchase");
  final TextEditingController subTotalAmountCtrl = TextEditingController();
  final TextEditingController discountPercentageCtrl = TextEditingController();
  final TextEditingController discountAmountCtrl = TextEditingController();
  final TextEditingController vatAmountCtrl = TextEditingController();
  final TextEditingController vatPercentCtrl = TextEditingController();

  final TextEditingController totalAmountCtrl = TextEditingController();
  final TextEditingController receivedAmountCtrl = TextEditingController();
  final TextEditingController dueAmountCtrl = TextEditingController();
  final TextEditingController descCtrl = TextEditingController();
  final TextEditingController paymentRefCtrl = TextEditingController();

  UnitListController unitListController = Get.put(UnitListController());

  var selectedLedger = LedgerDetailModel().obs;

  @override
  void onInit() async {
    // clear all cache images of bill
    imageCache.clear();
    super.onInit();
  }

  initialize() {
    _isLoading(false);
  }

  @override
  void dispose() {
    discountPercentageCtrl.dispose();
    billNoCtrl.dispose();
    partyNameCtrl.dispose();
    vatAmountCtrl.dispose();
    totalAmountCtrl.dispose();
    receivedAmountCtrl.dispose();
    dueAmountCtrl.dispose();
    descCtrl.dispose();
    mobileCtrl.dispose();
    addressCtrl.dispose();
    panNoCtrl.dispose();
    displayTextCtrl.dispose();
    subTotalAmountCtrl.dispose();
    discountAmountCtrl.dispose();
    vatPercentCtrl.dispose();
    paymentRefCtrl.dispose();

    super.dispose();
  }

  recalculateForItems() {
    double itemSubTotal = 0.00;
    items
      ..forEach((LineItemDetailModel li) {
        itemSubTotal = parseDouble(
            (itemSubTotal + (li.totalAmount ?? 0.0)).toStringAsFixed(2))!;
      });
    subTotalAmountCtrl.text = itemSubTotal.toStringAsFixed(2);

    onSubTotalIndividualChange(itemSubTotal.toString());

    // For cash purchase, set paid amount to total amount (not subtotal)
    if (isCashPurchaseSelected) {
      // Wait for calculations to complete, then set to total amount
      Future.delayed(Duration(milliseconds: 50), () {
        receivedAmountCtrl.text =
            (transaction.value.txnTotalAmount ?? itemSubTotal)
                .toStringAsFixed(2);
        transaction.value.txnCashAmount =
            transaction.value.txnTotalAmount ?? itemSubTotal;
        transaction.value.txnBalanceAmount = 0.00;
        transaction.refresh();
      });
    }
  }

  onvatPercentChange(String value, {String? editorTag}) {
    double vat = (parseDouble(value) ?? 0.00);
    transaction.value.txnTaxPercent = vat;
    recalculateDataForItem(editorTag: editorTag);
    // double taxPercentage = (parseDouble(value) ?? 0.00).toPrecision(2);

    // transaction.value.txnTaxPercent = taxPercentage;

    // transaction.value.txnTaxAmount = parseDouble(
    //     ((taxPercentage * (transaction.value.txnTaxableTotalAmount ?? 0.00)) *
    //             0.01)
    //         .toStringAsFixed(2));

    // transaction.value.txnTotalAmount = parseDouble(
    //     ((transaction.value.txnTaxableTotalAmount ?? 0.0) +
    //             (transaction.value.txnTaxAmount ?? 0.00))
    //         .toStringAsFixed(2));

    // changeReceivedAmount(
    //     transaction.value.ledgerId == CASH_PURCHASE_LEDGER_ID
    //         ? transaction.value.txnTotalAmount.toString()
    //         : 0.0.toString(),
    //     editorTag: editorTag);
  }

  onvatAmountChange(String value, {String? editorTag}) {
    double vatAmt = (parseDouble(value) ?? 0.00);
    recalculateDataForItem(editorTag: editorTag);
    // transaction.value.txnTaxAmount = parseDouble(value);
    // transaction.value.txnTaxPercent = parseDouble(((parseDouble(value) ??
    //             0.00 / (transaction.value.txnTaxableTotalAmount ?? 0.00)) *
    //         0.01)
    //     .toStringAsFixed(2));
    // transaction.value.txnTotalAmount = parseDouble(
    //     (transaction.value.txnTaxableTotalAmount ??
    //             0.00 + (transaction.value.txnTaxAmount ?? 0.00))
    //         .toStringAsFixed(2));
    // changeReceivedAmount(
    //     transaction.value.ledgerId == CASH_PURCHASE_LEDGER_ID
    //         ? transaction.value.txnTotalAmount.toString()
    //         : 0.0.toString(),
    //     editorTag: editorTag);
  }

  changeReceivedAmount(String value, {String? editorTag}) {
    double txnCashAmt = (parseDouble(value) ?? 0.00);
    double txnBalanceAmt = transaction.value.txnTotalAmount! - txnCashAmt;
    if (txnCashAmt > 0) {
      _isReceived(true);
      _isReceived.refresh();
    }
    transaction.value.txnCashAmount = txnCashAmt;
    transaction.value.txnBalanceAmount = txnBalanceAmt;
    assignTransactionToTextFields(editorTAG: editorTag);
  }

  onSubTotalIndividualChange(String val, {String? editorTag}) {
    double subTotal = (parseDouble(val) ?? 0.00);
    transaction.value.txnSubTotalAmount = subTotal;
    transaction.refresh();
    recalculateDataForItem(editorTag: editorTag);
  }

  recalculateDataForItem({String? editorTag}) {
    // assignTransactionToTextFields(editorTAG: editorTag);

    if (editorTag == 'txn_subtotal') {
      transaction.value.txnTotalAmount = transaction.value.txnSubTotalAmount;
      transaction.value.txnBalanceAmount = transaction.value.txnSubTotalAmount;
      transaction.value.txnTaxableTotalAmount =
          transaction.value.txnSubTotalAmount;
      transaction.refresh();
    }

    if (editorTag == 'txn_discount_percent') {
      double disPer = (transaction.value.txnDiscountPercent ?? 0.00);
      double subTotal = (transaction.value.txnSubTotalAmount ?? 0.00);
      double disAmt = (subTotal * disPer * 0.01);
      transaction.value.txnDiscountAmount =
          parseDouble(disAmt.toStringAsFixed(2));

      double total = subTotal - (transaction.value.txnDiscountAmount ?? 0.00);
      transaction.value.txnTaxableTotalAmount =
          parseDouble(total.toStringAsFixed(2));
      transaction.value.txnTotalAmount = parseDouble(total.toStringAsFixed(2));
      transaction.value.txnBalanceAmount =
          parseDouble(total.toStringAsFixed(2));
      transaction.refresh();
    }

    if (editorTag == 'txn_discount_amount') {
      double disAmt = (transaction.value.txnDiscountAmount ?? 0.00);
      double subTotal = (transaction.value.txnSubTotalAmount ?? 0.00);

      // Prevent division by zero
      double disPercent = subTotal > 0 ? (disAmt / subTotal) * 100 : 0.00;
      transaction.value.txnDiscountPercent =
          parseDouble(disPercent.toStringAsFixed(2));

      double total = subTotal - disAmt;
      transaction.value.txnTaxableTotalAmount =
          parseDouble(total.toStringAsFixed(2));
      transaction.value.txnTotalAmount = parseDouble(total.toStringAsFixed(2));
      transaction.value.txnBalanceAmount =
          parseDouble(total.toStringAsFixed(2));
      transaction.refresh();
    }

    if (editorTag == 'txn_tax_percent') {
      double taxPer = (transaction.value.txnTaxPercent ?? 0.00);
      double taxableTotal = (transaction.value.txnTaxableTotalAmount ?? 0.00);
      double taxAmt = (taxPer * 0.01 * taxableTotal);
      transaction.value.txnTaxAmount = parseDouble(taxAmt.toStringAsFixed(2));

      double total = taxableTotal + (transaction.value.txnTaxAmount ?? 0.00);
      transaction.value.txnTotalAmount = parseDouble(total.toStringAsFixed(2));
      transaction.value.txnBalanceAmount =
          parseDouble(total.toStringAsFixed(2));
      transaction.refresh();
      print("this is tax amt ${transaction.value.txnTaxAmount}");
    }

    if (editorTag == 'txn_tax_amount') {
      double taxAmt = (transaction.value.txnTaxAmount ?? 0.00);
      double taxableTotal = (transaction.value.txnTaxableTotalAmount ?? 0.00);
      double total = taxableTotal + taxAmt;
      transaction.value.txnTotalAmount = parseDouble(total.toStringAsFixed(2));
      transaction.value.txnBalanceAmount =
          parseDouble(total.toStringAsFixed(2));
      transaction.refresh();
    }
    assignTransactionToTextFields(editorTAG: editorTag);
  }

  onToggleVat(bool flag) {
    _isVatEnabled.value = flag;
    _isVatEnabled.refresh();
    double VAT = flag ? VAT_PERCENTAGE : 0.00;

    transaction.value.txnTaxPercent = VAT;
    onvatPercentChange(VAT.toString());
  }

  onChangeParty(LedgerDetailModel party) {
    // ignore: null_aware_in_condition
    if (party.ledgerId != null) {
      transaction.value.ledgerId = party.ledgerId;
      partyNameCtrl.text = party.ledgerTitle ?? "";
      mobileCtrl.text = party.mobileNo ?? "";
      addressCtrl.text = party.address ?? "";
      panNoCtrl.text = party.tinNo ?? "";
      if (party.ledgerId == CASH_PURCHASE_LEDGER_ID) {
        // displayTextCtrl.text = (transaction.value.txnDisplayName == "" ||
        //         transaction.value.txnDisplayName == null)
        //     ? party.ledgerTitle
        //     : transaction.value.txnDisplayName;
        transaction.value.txnDisplayName = displayTextCtrl.text;

        _isCashPurchaseSelected.value = true;
        _isCashPurchaseSelected.refresh();
        transaction.value.txnCashAmount =
            transaction.value.txnTotalAmount ?? 0.0;
        transaction.value.txnBalanceAmount = 0.00;
      }
    } else {
      transaction.value.ledgerId = null;
      transaction.value.txnCashAmount = 0.00;
      transaction.value.txnBalanceAmount = transaction.value.txnTotalAmount;
      transaction.value.txnDisplayName = displayTextCtrl.text = "";
      partyNameCtrl.text = "";
      mobileCtrl.text = "";
      addressCtrl.text = "";
      panNoCtrl.text = "";
    }
    selectedLedger.value = party;
    selectedLedger.refresh();
    transaction.refresh();

    if ((transaction.value.txnSubTotalAmount ?? 0.0) > 0.00) {
      assignTransactionToTextFields();
    }
  }

  assignTransactionToTextFields({String? editorTAG}) {
    // Log.d("assignning to text ${transaction.value.toJson()}");

    // formKey.currentState.
    billNoCtrl.text = transaction.value.txnRefNumberChar ?? "";
    totalAmountCtrl.text =
        (transaction.value.txnTotalAmount ?? 0.0).toStringAsFixed(2);
    dueAmountCtrl.text =
        (transaction.value.txnBalanceAmount ?? 0.0).toStringAsFixed(2);
    displayTextCtrl.text = transaction.value.txnDisplayName ?? "";
    paymentRefCtrl.text = transaction.value.txnPaymentReference ?? "";
    descCtrl.text = transaction.value.txnDescription ?? "";

    // controller which triggers above method
    // check for editor tag, if match don't refresh

    if (editorTAG != 'txn_subtotal')
      subTotalAmountCtrl.text = transaction.value.txnSubTotalAmount != 0.0
          ? (transaction.value.txnSubTotalAmount ?? 0.00).toStringAsFixed(2)
          : "";

    if (editorTAG != 'txn_discount_percent')
      discountPercentageCtrl.text = transaction.value.txnDiscountPercent != 0.0
          ? (transaction.value.txnDiscountPercent ?? 0.00).toStringAsFixed(2)
          : "";

    if (editorTAG != 'txn_discount_amount')
      discountAmountCtrl.text = transaction.value.txnDiscountAmount != 0.0
          ? (transaction.value.txnDiscountAmount ?? 0.00).toStringAsFixed(2)
          : "";

    if (editorTAG != 'txn_tax_percent')
      vatPercentCtrl.text = transaction.value.txnTaxPercent != 0.0
          ? (transaction.value.txnTaxPercent ?? 0.00).toStringAsFixed(2)
          : "";

    if (editorTAG != 'txn_tax_amount')
      vatAmountCtrl.text =
          (transaction.value.txnTaxAmount ?? 0.0).toStringAsFixed(2);

    if (editorTAG != 'txn_cash_amount') {
      receivedAmountCtrl.text = transaction.value.txnCashAmount != 0.0
          ? (transaction.value.txnCashAmount ?? 0.00).toStringAsFixed(2)
          : "";
    }
  }

  updateDiscountPercentage(String dis, {String? editorTag}) {
    transaction.value.txnDiscountPercent = (parseDouble(dis) ?? 0.00);
    transaction.refresh();
    recalculateDataForItem(editorTag: editorTag);
  }

  updateDiscountAmount(String dis, {String? editorTag}) {
    double disAmt = (parseDouble(dis) ?? 0.00);
    transaction.value.txnDiscountAmount = disAmt;
    transaction.refresh();
    recalculateDataForItem(editorTag: editorTag);
  }

  initEdit(saleID, readOnlyFlag) async {
    _isLoading(true);
    _isLoading.refresh();
    final tempDir = await getTemporaryDirectory();

    Tuple3<PurchaseModel, List<LineItemDetailModel>, List<TxnImageModel>> dt =
        await purchaseRepository.getPurchaseById(saleID);

    LedgerDetailModel party = await _ledgerRepository
        .getLedgerWithBalanceById(dt.item1.ledgerId ?? "");

    _editFlag.value = true;
    transaction.value = dt.item1;
    items.value = dt.item2;
    images.value = dt.item3;
    files.clear();
    List<File> prevFiles = [];

    await Future.wait(dt.item3.map((e) async {
      final file =
          await new File('${tempDir.path}/image-${e.sno}.${e.imageExt}')
              .create();
      file.writeAsBytesSync(e.imageBitmap!);
      prevFiles.add(file);
    }));
    files.addAll(prevFiles);
    items.refresh();
    files.refresh();
    previousBillNo = dt.item1.txnRefNumberChar;
    onChangeParty(party);

    if ((dt.item1.txnTaxAmount ?? 0.00) > 0.0) {
      _isVatEnabled.value = true;
      _isVatEnabled.refresh();
    }

    // assignTransactionToTextFields();
    _readOnlyFlag.value = readOnlyFlag;
    _isLoading(false);
    _isLoading.refresh();
  }

  Future<List<TxnImageModel>> getTxnImageModelFromFiles(
      List<File> _files) async {
    List<TxnImageModel> txnImageModels = [];

    await Future.wait(_files.map((element) async {
      Tuple2<List<int>, String> compressedImage = await compressImage(element);
      txnImageModels.add(TxnImageModel(
          imageBitmap: compressedImage.item1, imageExt: compressedImage.item2));
    }));
    return txnImageModels;
  }

  Future<bool> checkLargeImage(List fls, {bool? preConvertFiles}) async {
    //preConvertFiles can be used to convert files list to txn image model once, so than it don't need re convert
    bool status = false;
    await Future.wait(fls.map((element) async {
      Tuple2<List<int>, String> compressedImage =
          await compressImage(File(element.path));
      // Log.d("file size is" + compressedImage.item1.length.toString());
      if (compressedImage.item1.length > MAX_IMAGE_SIZE) {
        status = true;
        // return;
      }
    }).toList());

    return status;
  }

  Future<bool> checkDuplicateBillNo() async {
    bool status = false;
    try {
      if (editFlag && transaction.value.txnRefNumberChar == previousBillNo) {
        status = false;
      } else {
        status = await purchaseRepository
            .isBillDuplicate(transaction.value.txnRefNumberChar ?? "");
      }
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<String?> createPurchase() async {
    String? status;

    try {
      // String primaryKeyPrefix = await getPrimaryKeyPrefix();
      // transaction.value.txnId = primaryKeyPrefix + uuidV4;
      transaction.value.txnType = TxnType.purchase;
      transaction.value.txnDate =
          toDateAD(NepaliDateTime.parse(transaction.value.txnDateBS ?? ""));
      if (!([null, ""].contains(transaction.value.chequeIssueDateBS))) {
        transaction.value.chequeIssueDate = toDateAD(NepaliDateTime.parse(
            strTrim(transaction.value.chequeIssueDateBS ?? "")));
      }
      List<TxnImageModel> tempImages =
          await getTxnImageModelFromFiles(files.value);

      status = await purchaseRepository.addPurchase(
          transaction.value, items, tempImages);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<bool> updatePurchase() async {
    bool status = false;

    try {
      transaction.value.txnDate = toDateAD(
          NepaliDateTime.parse(strTrim(transaction.value.txnDateBS ?? "")));
      if (!([null, ""].contains(transaction.value.chequeIssueDateBS))) {
        transaction.value.chequeIssueDate = toDateAD(NepaliDateTime.parse(
            strTrim(transaction.value.chequeIssueDateBS ?? "")));
      }
      List<TxnImageModel> tempImages =
          await getTxnImageModelFromFiles(files.value);
      status = await purchaseRepository.updatePurchase(
          transaction.value, items, tempImages);
    } catch (e, trace) {
      Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }
}
