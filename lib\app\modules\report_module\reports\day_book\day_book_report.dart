import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/custom_button.dart';
import 'package:mobile_khaata_v2/app/components/report_custom_date_picker_text_field.dart';
import 'package:mobile_khaata_v2/app/model/report/daybook_report_model.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/daybook_report_print_page.dart';
import 'package:mobile_khaata_v2/app/modules/report_module/reports/day_book/day_book_report_controller.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

import 'package:nepali_date_picker/nepali_date_picker.dart';

// ignore: must_be_immutable
class DayBookReport extends StatelessWidget {
  DayBookReportController _controller = DayBookReportController();

  String startDate = currentDate;

  DayBookReport() {
    generate();
  }

  generate() {
    _controller.generateDayBookReport(startDate: startDate);
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        // resizeToAvoidBottomPadding: true,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          elevation: 0,
          titleSpacing: -5.0,
          backgroundColor: colorPrimary,
          title: Text(
            "दैनिक रिपोर्ट (Day Book)",
            style: TextStyle(
                fontSize: 18,
                color: Colors.white,
                fontFamily: 'HelveticaRegular',
                fontWeight: FontWeight.bold),
          ),
          actions: [
            PrintButton(
              onPressed: () {
                Navigator.pushNamed(context, '/printDayBook',
                    arguments: DaybookReportPrintPage(
                      transactions: _controller.transactions,
                      dateFor: startDate,
                    ));
              },
            )
          ],
        ),
        body: Container(
          color: Colors.black12,
          child: Column(
            children: [
              //=============================transaction date filter
              Container(
                padding: EdgeInsets.symmetric(horizontal: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      "Date",
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    Container(
                      child: ReportCustomDatePickerTextField(
                        initialValue: toDateBS(DateTime.parse(startDate)),
                        hintText: "From Date",
                        onChange: (selectedDate) {
                          startDate =
                              toDateAD(NepaliDateTime.parse(selectedDate));
                          generate();
                        },
                      ),
                      width: 140,
                    ),
                  ],
                ),
              ),

              Divider(
                height: 0,
                color: Colors.black54,
              ),

              DefaultTextStyle(
                style: TextStyle(
                    fontSize: 14,
                    color: textColor,
                    fontWeight: FontWeight.bold),
                child: Container(
                  color: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      //====================================1st Column
                      Expanded(
                        flex: 1,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Particulars",
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                              style: TextStyle(
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),

                      //====================================2nd Column
                      Expanded(
                        flex: 1,
                        child: Text(
                          "Total",
                          textAlign: TextAlign.right,
                        ),
                      ),

                      //====================================3rd Column
                      Expanded(
                        flex: 1,
                        child: Text(
                          "Money In",
                          textAlign: TextAlign.right,
                        ),
                      ),

                      //====================================4th Column
                      Expanded(
                        flex: 1,
                        child: Text(
                          "Money Out",
                          textAlign: TextAlign.right,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Divider(
                height: 0,
                color: Colors.black54,
              ),

              Obx(() {
                if (_controller.txnLoading) {
                  return Container(
                      color: Colors.white,
                      child: Center(child: CircularProgressIndicator()));
                }

                if (_controller.transactions.isEmpty) {
                  return Container(
                      color: Colors.white,
                      width: double.infinity,
                      child: Center(
                          child: Text(
                        "No Records",
                        style: TextStyle(color: Colors.black54),
                      )));
                } else {
                  return Expanded(
                      child: _TxnListView(_controller.transactions));
                }
              }),
            ],
          ),
        ),
        // extendBody: true,
        bottomNavigationBar: Container(
          height: 75,
          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
          color: colorPrimary,
          child: SingleChildScrollView(child: Container(
            child: Obx(() {
              return DefaultTextStyle(
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 15,
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text("Total: "),
                          flex: 1,
                        ),
                        Expanded(flex: 1, child: Text("")),
                        Expanded(
                            flex: 2,
                            child: Text(
                              formatCurrencyAmount(
                                  _controller.moneyIn.value, false),
                              textAlign: TextAlign.right,
                            )),
                        Expanded(
                            flex: 2,
                            child: Text(
                              formatCurrencyAmount(
                                  _controller.moneyOut.value, false),
                              textAlign: TextAlign.right,
                            )),
                      ],
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: Text("MoneyIn-MoneyOut: "),
                          flex: 1,
                        ),
                        Expanded(
                            flex: 1,
                            child: Text(
                              formatCurrencyAmount(
                                  (_controller.moneyIn.value -
                                      _controller.moneyOut.value),
                                  false),
                              textAlign: TextAlign.right,
                            )),
                      ],
                    ),
                  ],
                ),
              );
            }),
          )),
        ),
      ),
    );
  }
}

class _TxnListView extends StatelessWidget {
  final List<DayBookReportModel> _transactionList;

  _TxnListView(this._transactionList);

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      itemCount: _transactionList.length,
      itemBuilder: (context, int index) {
        DayBookReportModel txn = _transactionList[index];

        return InkWell(
          // onTap: () => TransactionHelper.gotoTransactionEditPage(
          //     context, txn.txnId, txn.txnType),
          child: Container(
            color: Colors.white,
            child: Column(
              children: [
                DefaultTextStyle(
                  style: TextStyle(fontSize: 12, color: colorPrimary),
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        //====================================1st Column
                        Expanded(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "${txn.description}",
                                overflow: TextOverflow.ellipsis,
                                maxLines: 2,
                              ),
                            ],
                          ),
                        ),

                        //====================================2nd Column
                        Expanded(
                          flex: 1,
                          child: Text(
                            formatCurrencyAmount(txn.txnTotalAmount!, false),
                            textAlign: TextAlign.right,
                          ),
                        ),

                        //====================================3rd Column
                        Expanded(
                          flex: 1,
                          child: Text(
                            (TxnType.cashInTransaction.contains(txn.txnType))
                                ? formatCurrencyAmount(
                                    txn.txnCashAmount!, false)
                                : '',
                            textAlign: TextAlign.right,
                          ),
                        ),

                        //====================================4th Column
                        Expanded(
                          flex: 1,
                          child: Text(
                            (TxnType.cashOutTransaction.contains(txn.txnType))
                                ? formatCurrencyAmount(
                                    txn.txnCashAmount!, false)
                                : '',
                            textAlign: TextAlign.right,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                Divider(
                  height: 4,
                  color: Colors.black54,
                ),

                //Add space if last element
                if (_transactionList.length - 1 == index) ...{
                  SizedBox(
                      // height: 100,
                      )
                },
              ],
            ),
          ),
        );
      },
    );
  }
}
