import 'dart:io';

import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/app/modules/gallery_module/image_preview_screen.dart';

class ImageGalleryGrid extends StatefulWidget {
  final List<File>? images;
  const ImageGalleryGrid({Key? key, this.images}) : super(key: key);

  @override
  // ignore: library_private_types_in_public_api
  _ImageGalleryGridState createState() => _ImageGalleryGridState();
}

class _ImageGalleryGridState extends State<ImageGalleryGrid> {
  @override
  Widget build(BuildContext context) {
    return Wrap(
      children: [
        ...widget.images!.asMap().keys.map((k) {
          return Container(
            padding:
                const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
            child: GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => GalleryPhotoViewWrapper(
                      galleryItems: widget.images,
                      backgroundDecoration: const BoxDecoration(
                        color: Colors.black,
                      ),
                      initialIndex: k,
                      scrollDirection: Axis.horizontal,
                    ),
                  ),
                );
              },
              child: <PERSON>(
                tag: k,
                child: Image.file(
                  widget.images![k],
                  height: 110.0,
                  width: 110.0,
                  fit: BoxFit.cover,
                ),
              ),
            ),
          );
        }).toList()
      ],
    );
  }
}
