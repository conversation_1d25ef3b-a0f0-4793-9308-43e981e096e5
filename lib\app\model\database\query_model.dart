import 'dart:convert';

import 'package:flutter/foundation.dart';

enum QueryType { insert, update, delete, replace, rawQuery, operation }

T enumFromString<T>(Iterable<T> values, String value) {
  return values.firstWhere(
    (type) => type.toString().split(".").last == value,
  );
}

class QueryModel {
  QueryType? queryType;
  String? whereClause;
  String? tableName;
  Map<String, dynamic>? data;
  List<dynamic>? whereArgs;
  String? queryId;
  String? createdAt;
  String? batchID;

  QueryModel(
      {this.queryType,
      this.tableName,
      this.whereClause,
      this.data,
      this.whereArgs,
      this.queryId,
      this.createdAt,
      this.batchID});

  Map<String, dynamic> toJson() {
    // Log.d("encoded json" + jsonEncode(data));

    // Log.d("dart json to string" + DartJson(data).toString());

    return {
      "query_type": describeEnum(queryType!),
      "table_name": tableName,
      "where_clause": whereClause,
      "data": jsonEncode(data),
      "where_args": jsonEncode(whereArgs),
      'created_at': createdAt,
      "query_id": queryId,
      "batch_id": batchID
    };
  }

  factory QueryModel.fromJson(Map<String, dynamic> map) {
    // Log.d("decoded json" + jsonDecode(map['data'] ?? "{}").toString());

    // Log.d("dart json" + DartJson(map['data'] ?? "{}").toString());

    QueryModel q = new QueryModel(
        queryType: enumFromString(QueryType.values, map['query_type']),
        tableName: map['table_name'],
        whereClause: map['where_clause'],
        data: jsonDecode(map['data'] ?? "{}"),
        whereArgs: jsonDecode(map['where_args'] ?? "[]"),
        createdAt: map['created_at'],
        queryId: (map['query_id']).toString(),
        batchID: map['batch_id']);

    // Log.d(q);
    return q;
  }
}
