name: mobile_khaata_v2
description: A new Flutter project.

# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+1

environment:
  sdk: ">=2.18.0-97.0.dev <3.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
#  esewa_flutter_sdk:
#    path: ./esewa_flutter_sdk

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  pdf: ^3.8.1
  flutter_custom_clippers: ^2.0.0
  localstorage: ^4.0.0+1
  tuple: ^2.0.0
  shared_preferences: ^2.0.15
  sqflite: ^2.0.3
  get: ^4.6.5
  http: ^0.13.4
  connectivity_plus: ^5.0.2
  jwt_decode: ^0.3.1
  uuid: ^3.0.6
  font_awesome_flutter: ^10.1.0
  youtube_player_flutter: ^8.1.0

  package_info: ^2.0.2
  intl: ^0.17.0
  flutter_form_builder: ^7.5.0

  another_flushbar: ^1.10.29
  progress_dialog_null_safe: ^1.0.7
  pin_code_fields: ^7.4.0

  flutter_local_notifications: ^12.0.4
  rxdart: ^0.27.5
  timezone: ^0.9.1
  dio: ^4.0.6
  flutter_archive: ^5.0.0
  in_app_review: ^2.0.10
  nepali_utils: ^3.0.3
  flutter_pdfview: ^1.2.5
  printing: ^5.9.2
  open_file: ^3.2.1
  device_info_plus: ^4.0.0
  nepali_date_picker:
    path: packages/nepali_date_picker
  flutter_speed_dial: ^6.0.0
  flutter_share: ^2.0.0
  flutter_image_compress: ^1.1.1
  form_builder_image_picker: ^3.1.0
  photo_view: ^0.14.0
  gallery_saver: ^2.3.2
  permission_handler: ^10.0.0
  rx: ^0.1.3
  sticky_headers: ^0.3.0+2
  share: ^2.0.4
  path_provider: ^2.0.11

  in_app_update: ^4.0.1
  excel: ^2.0.1
  file_picker: ^4.6.1
  url_launcher: ^6.1.6
  webview_flutter: ^4.0.1
  contacts_service: ^0.6.3
  flutter_typeahead: ^3.2.1
  logger: ^2.3.0
#  esewa_pnp: ^2.0.1
#  esewa_pnp_forked: ^3.0.1
  flutter_inappwebview: ^5.4.3+7
  crypto: ^3.0.3

dev_dependencies:
  flutter_launcher_icons: ^0.11.0
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg
  assets:
    - assets/mobilekhata.svg
    - assets/roboto1.ttf
    - assets/roboto2.ttf
    - assets/roboto3.ttf
    - assets/invoice.svg

    - images/partytemplate.png
    - images/itemExcelTemplate.png
    - images/about_us-blue.png
    - images/about_us-white.png
    - images/account-blue.png
    - images/account-white.png
    - images/bell.png
    - images/back.png
    - images/backup-white.png
    - images/backup-blue.png
    - images/camera.png
    - images/calendar_mark-white.png
    - images/cash_in_hand-blue.png
    - images/cash_in_hand-white.png
    - images/contact-blue.png
    - images/contact-white.png
    - images/credit-small.png
    - images/circle-color.png
    - images/circle-white.png
    - images/expense-small.png
    - images/faq-blue.png
    - images/faq-white.png
    - images/general_info-blue.png
    - images/general_info-white.png
    - images/home.png
    - images/invite_earn-blue.png
    - images/logo-appbar.png
    - images/logo-bottom.png
    - images/logo-splash.png
    - images/logout-blue.png
    - images/lock-blue.png
    - images/lock-white.png
    - images/menu-blue.png
    - images/menu-white.png
    - images/or.png
    - images/product-blue.png
    - images/product-white.png
    - images/profile.png
    - images/payment-blue.png
    - images/purchase-small.png
    - images/purchase-blue.png
    - images/report-small.png
    - images/reminder-blue.png
    - images/reminder-white.png
    - images/sales-small.png
    - images/sales-blue.png
    - images/setting-blue.png
    - images/setting-white.png
    - images/sms-white.png
    - images/terms-blue.png
    - images/terms-white.png
    - images/user-blue.png
    - images/user-white.png
    - images/vat-small-white.png
    - images/nepal_map_outline.png
    - images/nepal_flag.gif
    - images/verification-blue.png
    - images/khalti.png
    - images/esewa.png

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:da
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

  fonts:
    - family: ArialBlack
      fonts:
        - asset: fonts/arial-black.ttf
    - family: HelveticaRegular
      fonts:
        - asset: fonts/helvetica-neue-regular.ttf
    - family: HelveticaBold
      fonts:
        - asset: fonts/helvetica-neue-bold.ttf
    - family: NotoRegular
      fonts:
        - asset: fonts/noto-regular.ttf
    - family: NotoBold
      fonts:
        - asset: fonts/noto-bold.ttf
    - family: PoppinsBold
      fonts:
        - asset: fonts/Poppins-Bold.ttf
    - family: PoppinsRegular
      fonts:
        - asset: fonts/Poppins-Regular.ttf
