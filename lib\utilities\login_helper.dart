import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:jwt_decode/jwt_decode.dart';

import 'package:localstorage/localstorage.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:mobile_khaata_v2/utilities/shared_pref_helper1.dart';

import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sqflite/sqflite.dart';

import '../app/model/others/company_model.dart';
import '../main.dart';

class LoginHelper {
  static final LoginHelper _instance = LoginHelper._internal();

  LoginHelper._internal();

  factory LoginHelper() => _instance;

  SharedPreferences? _prefs;

  Future<bool> get isLoggedIn async {
    _prefs = await SharedPreferences.getInstance();

    bool status = (_prefs!.getBool(LoginStatus) ?? false);

    return status;
  }

  Future<String> get accessToken async {
    _prefs = await SharedPreferences.getInstance();

    String token = (_prefs!.getString(AccessToken) ?? "");

    return token;
  }

  Future<String> get getLastActivityBy async {
    _prefs = await SharedPreferences.getInstance();

    String lastActivityBy = (_prefs!.getString(FullNameKey) ?? '');
    lastActivityBy =
        lastActivityBy + '-' + (_prefs!.getString(UserNameKey) ?? '');
    return lastActivityBy;
  }

  Future<bool> get isAdminUser async {
    String accessToken = await this.accessToken;
    Map<String, dynamic> decodedToken = Jwt.parseJwt(accessToken);
    // Log.d("decodedToken=======");
    // Log.d(decodedToken);
    return decodedToken['is_admin'];
  }

  Future<Map<String, dynamic>> get decodedAccessToken async {
    String accessToken = await this.accessToken;
    Map<String, dynamic> decodedToken = Jwt.parseJwt(accessToken);

    return decodedToken;
  }

  Future<String> getUser() async {
    String user = "";
    _prefs = await SharedPreferences.getInstance();
    user = _prefs!.getString(UserNameKey) ?? "";
    // Log.d("user name" + user);
    return user;
  }

  Future<bool> login(
      {required String accessToken,
      required String userName,
      required String fullName,
      bool isExpired = false,
      required List<dynamic> permissions,
      dynamic subdata,
      int multiUserFlag = 1}) async {
    _prefs = await SharedPreferences.getInstance();

    isUserLoggedIn = true;

    await _prefs!.setBool(LoginStatus, true);
    await _prefs!.setString(AccessToken, accessToken);
    await _prefs!.setString(UserNameKey, userName);
    await _prefs!.setString(FullNameKey, fullName);

    SharedPrefHelper1().setUnauthorized(false);

    await setExpiredUser(flag: isExpired);
    await setMultiUser(status: multiUserFlag);

    print("permissions===");
    print(permissions);
    await _prefs!.setString(SubDetailKey, jsonEncode(subdata ?? {}));
    await _prefs!.setStringList(
        Permissions, permissions.map((e) => e.toString()).toList());

    Map<String, dynamic> decodedToken = Jwt.parseJwt(accessToken);
    Log.d("decoded token" + decodedToken.toString());
    String databaseName = decodedToken['sub'].toString() + databaseSuffix;
    Log.d("decoded token" + databaseName);

    await _prefs!.setString(CurrentDatabase, databaseName);
    return true;
  }

  Future<bool> logout() async {
    try {
      _prefs = await SharedPreferences.getInstance();

      isUserLoggedIn = false;
      final storage = new LocalStorage(MobileSettings);
      await storage.ready;
      Map<String, dynamic> decodedToken = await decodedAccessToken;
      String databaseName = decodedToken['sub'].toString() + databaseSuffix;
      await storage.deleteItem(databaseName);

      await _prefs!.remove(LoginStatus);
      await _prefs!.remove(AccessToken);
      await _prefs!.remove(UserNameKey);
      await _prefs!.remove(FullNameKey);
      await _prefs!.remove(Permissions);
      // await _prefs.remove(UserNameKey);

      await _prefs!.remove(CurrentDatabase);

      await _prefs!.remove(UnauthorizedKey);
      await _prefs!.remove(SubDetailKey);
      await _prefs!.remove(ActiveKey);
      await _prefs!.remove(MultiUserKey);
      await _prefs!.remove(ExpiredKey);

      //Reset all global variables to default
      resetGlobalVariables();

      DatabaseHelper.destroy();
      String dbPath = (await getApplicationDocumentsDirectory()).path;

      deleteDatabase(dbPath + "/" + databaseName);
      return true;
    } catch (e, trace) {
      Log.e(trace.toString(), e.toString());
      return false;
    }
  }

  Future<bool> storeCompanies(List<CompanyModel> companies) async {
    _prefs = await SharedPreferences.getInstance();
    String companyString =
        jsonEncode(companies.map((e) => e.toJson()).toList());
    await _prefs!.setString(Companies, companyString);
    return true;
  }

  Future<List<CompanyModel>> getCompanies() async {
    _prefs = await SharedPreferences.getInstance();
    String? companies = _prefs!.getString(Companies);
    if (companies != null) {
      List<dynamic> companyList = jsonDecode(companies);
      List<CompanyModel> companyModels =
          companyList.map((e) => CompanyModel.fromJson(e)).toList();
      return companyModels;
    }
    return [];
  }
}
