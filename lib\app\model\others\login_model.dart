class LoginModel {
  LoginModel(
      {this.username,
      this.subusername,
      this.password,
      this.deviceId,
      this.deviceName,
      this.devicePlatform,
      this.deviceModel,
      this.deviceManufacturer,
      this.parentId,
      this.isAdmin});

  String? username;
  String? subusername;
  String? password;
  String? deviceId;
  String? deviceName;
  String? devicePlatform;
  String? deviceModel;
  String? deviceManufacturer;
  int? parentId;
  bool? isAdmin;

  factory LoginModel.fromJson(Map<String, dynamic> json) => LoginModel(
        username: json["username"],
        subusername: json['sub_username'],
        password: json["password"],
        deviceId: json["device_id"],
        deviceName: json["device_name"],
        devicePlatform: json["device_platform"],
        deviceModel: json["device_model"],
        deviceManufacturer: json["device_manufacturer"],
        parentId: json['parent_id'],
        isAdmin: json["is_admin"],
      );

  Map<String, dynamic> toJson() => {
        "username": username,
        "sub_username": subusername,
        "password": password,
        "device_id": deviceId,
        "device_name": deviceName,
        "device_platform": devicePlatform,
        "device_model": deviceModel,
        "device_manufacturer": deviceManufacturer,
        "parent_id": parentId,
        "is_admin": (isAdmin!) ? '1' : '0',
      };
}

class LoginApiResponseModel {
  LoginApiResponseModel(
      {this.operation,
      this.token,
      this.username,
      this.fullName,
      this.accessToken,
      this.tokenType,
      this.registeredMobileNo,
      this.isFirstLogin,
      this.isAdmin,
      this.permissions,
      this.isAccountExpired,
      this.expiringDate,
      this.userId,
      this.multiUserFlag});

  String? operation;
  String? token;
  String? username;
  String? fullName;
  String? accessToken;
  String? tokenType;
  // String? deviceId;
  bool? isAdmin;
  int? userId;
  String? registeredMobileNo;
  int? isFirstLogin;
  bool? isAccountExpired;
  int? multiUserFlag;
  String? expiringDate;
  List<dynamic>? permissions;

  factory LoginApiResponseModel.fromJson(Map<String, dynamic> json) {
    // Log.d("multi user flag ${json['multi_user_flag']}");
    // Log.d(json['multi_user_flag'].runtimeType);
    return LoginApiResponseModel(
      operation: json["operation"],
      token: json["token"],
      username: json["username"],
      fullName: json["full_name"],
      accessToken: json["access_token"],
      tokenType: json["token_type"],
      userId: json["user_id"] != null
          ? int.parse(json["user_id"].toString())
          : null,
      isAdmin: json["is_admin"].toString() == "1" || json["is_admin"] == true,
      registeredMobileNo: json["registered_mobile_no"],
      isFirstLogin: json["is_first_login"],
      isAccountExpired: json["is_account_expired"],
      expiringDate: json['account_expiry_date'],
      multiUserFlag: null != json['multi_user_flag']
          ? int.tryParse(json['multi_user_flag'].toString())
          : 0,
      permissions: json['permissions'] ?? null,
    );
  }
}
