// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/expanse_model.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_module/expense_list/expense_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/expanse_module/expense_list/expense_list_single_category.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class ExpensesListPage extends StatefulWidget {
  const ExpensesListPage({super.key});

  @override
  _ExpensesListPageState createState() => _ExpensesListPageState();
}

class _ExpensesListPageState extends State<ExpensesListPage> {
  final String tag = "ExpensesListPage";

  final expensesListController =
      Get.put(ExpensesListController(), tag: "ExpensesListController");

  bool showSearchBar = false;
  final FocusNode searchBoxFocus = FocusNode();
  String searchBoxPlaceholder = "खर्च खोज्नुहोस् (Expenses Search)";

  @override
  void initState() {
    expensesListController.init();
    super.initState();
  }

  @override
  void dispose() {
    expensesListController.changeADDate(currentDate);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      // resizeToAvoidBottomPadding: false,
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        toolbarHeight: 60,
        elevation: 0,
        leading: BackButton(
          onPressed: () => Navigator.pop(context, false),
        ),
        titleSpacing: -5.0,
        centerTitle: false,
        backgroundColor: colorPrimary,
        title: const Text(
          "खर्चहरु (Expenses) ",
          style: TextStyle(
              fontSize: 18,
              color: Colors.white,
              fontFamily: 'HelveticaRegular',
              fontWeight: FontWeight.bold),
        ),
        // actions: <Widget>[
        //   IconButton(
        //     icon: (showSearchBar)
        //         ? Icon(
        //             Icons.cancel,
        //             color: Colors.white,
        //           )
        //         : Icon(
        //             Icons.search,
        //             color: Colors.white,
        //           ),
        //     onPressed: () => searchButtonOnPressedHandler(),
        //   )
        // ],
      ),
      body: Container(
        height: MediaQuery.of(context).size.height,
        decoration: BoxDecoration(
          color: backgroundColorShade,
        ),
        child: Container(
          height: MediaQuery.of(context).size.height,
          padding: const EdgeInsets.only(bottom: 60),
          child: _ExpensesListView(expensesListController),
        ),
      ),
      floatingActionButton: SizedBox(
        width: 45,
        child: FloatingActionButton(
          onPressed: () =>
              expensesListController.addButtonOnPressedHandler(context),
          backgroundColor: colorPrimary,
          elevation: 6,
          child: const Icon(
            Icons.add,
            size: 25,
            color: Colors.white,
          ),
        ),
      ),
    ));
  }
}

class _ExpensesListView extends StatelessWidget {
  final ExpensesListController expensesListController;

  const _ExpensesListView(this.expensesListController);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      double totalTodaysExpenses = 0.0;
      expensesListController.expenses.map((e) {
        totalTodaysExpenses += e.txnTotalAmount ?? 0;
      }).toList();
      return Container(
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
            border: Border.all(color: Theme.of(context).primaryColor, width: 2),
            borderRadius: BorderRadius.circular(6)),
        margin: const EdgeInsets.all(10.0),
        child: Column(children: [
          Container(
            padding:
                const EdgeInsets.only(left: 10, right: 10, top: 6, bottom: 6),
            color: Theme.of(context).primaryColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "खर्चहरु",
                  style: labelStyle2.copyWith(color: Colors.white),
                ),
                InkWell(
                  onTap: () async {
                    String selectedDateTime = await nepaliDatePicker(
                        context, expensesListController.selectedBSDate);
                    expensesListController.changeBSDate(selectedDateTime);
                  },
                  child: Text(expensesListController.selectedBSDate,
                      style: labelStyle2.copyWith(color: Colors.white)),
                )
              ],
            ),
          ),
          Expanded(
            child: expensesListController.isLoading
                ? Container(
                    color: Colors.white,
                    child: const Center(child: CircularProgressIndicator()))
                : expensesListController.expenses.isEmpty
                    ? const SizedBox(
                        width: double.infinity,
                        child: Center(
                            child: Text(
                          "No Records",
                          style: TextStyle(color: Colors.black54),
                        )))
                    : ListView.builder(
                        itemCount: expensesListController.expenses.length,
                        shrinkWrap: true,
                        itemBuilder: (context, int index) {
                          ExpenseModel expense =
                              expensesListController.expenses[index];

                          return Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: () async {
                                await showModalBottomSheet(
                                    context: context,
                                    builder: (_) {
                                      return ExpenseListSingleCategory(
                                        categoryID: expense.expenseCategoryId,
                                        categoryName:
                                            expense.expenseCategoryName,
                                        categoryTotal: expense.txnTotalAmount,
                                        expensesListController:
                                            expensesListController,
                                      );
                                    });
                              },
                              child: Container(
                                padding: const EdgeInsets.only(
                                    left: 5, right: 5, top: 10, bottom: 10),
                                decoration: BoxDecoration(
                                    border: Border(
                                        bottom: expensesListController
                                                        .expenses.length -
                                                    1 ==
                                                index
                                            ? BorderSide.none
                                            : BorderSide(
                                                width: 1,
                                                color: Colors.black
                                                    .withOpacity(0.1)))),
                                child: Row(
                                  children: [
                                    SizedBox(
                                        width: 30,
                                        child: Text(
                                          "${index + 1}.",
                                          style: labelStyle2.copyWith(),
                                        )),
                                    Expanded(
                                        flex: 1,
                                        child: Container(
                                          // color: Colors.red,
                                          width: double.infinity,
                                          alignment: Alignment.centerLeft,
                                          child: Text(
                                            expense.expenseCategoryName ?? "",
                                            style: labelStyle2.copyWith(),
                                          ),
                                        )),
                                    Container(
                                        alignment: Alignment.centerRight,
                                        // width: 90,
                                        child: Text(
                                          formatCurrencyAmount(
                                              expense.txnTotalAmount ?? 0,
                                              true),
                                          style: labelStyle2.copyWith(),
                                        )),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
          ),
          Container(
            padding:
                const EdgeInsets.only(left: 10, right: 10, top: 6, bottom: 6),
            color: Theme.of(context).primaryColor,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "कुल खर्च :",
                  style: labelStyle2.copyWith(color: Colors.white),
                ),
                Text(
                  formatCurrencyAmount(totalTodaysExpenses, true),
                  style: labelStyle2.copyWith(color: Colors.white),
                )
              ],
            ),
          ),
        ]),
      );
    });
  }
}
