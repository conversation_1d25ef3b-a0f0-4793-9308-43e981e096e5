class CompanyModel {
  final String? userName;
  final int? parentId;
  final String? authToken;
  final String? webAuthToken;
  final List<String>? permissions;
  final RegistrationDetail? registrationDetail;

  CompanyModel({
    this.userName,
    this.parentId,
    this.authToken,
    this.webAuthToken,
    this.permissions,
    this.registrationDetail,
  });

  factory CompanyModel.fromJson(Map<String, dynamic> json) {
    return CompanyModel(
      userName: json['user_name'],
      parentId: json['parent_id'],
      authToken: json['auth_token'],
      webAuthToken: json['web_auth_token'],
      permissions: json['permissions'] != null
          ? List<String>.from(json['permissions'])
          : [],
      registrationDetail: json['registration_detail'] != null
          ? RegistrationDetail.fromJson(json['registration_detail'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_name': userName,
      'parent_id': parentId,
      'auth_token': authToken,
      'web_auth_token': webAuthToken,
      'permissions': permissions,
      'registration_detail': registrationDetail?.toJson(),
    };
  }
}

class RegistrationDetail {
  final int? userId;
  final String? businessName;
  final String? businessAddress;
  final String? wardNo;
  final String? provinceId;
  final String? provinceTitle;
  final String? districtId;
  final String? districtTitle;
  final String? vdcMncId;
  final String? vdcMncTitle;
  final String? gpsLat;
  final String? gpsLng;
  final String? businessCategoryId;
  final String? businessCategoryTitle;
  final String? businessTypeId;
  final String? businessTypeTitle;
  final String? tinNo;
  final String? tinFlag;
  final String? fullName;
  final String? countryCode;
  final String? primaryMobileNo;
  final String? gender;
  final String? altCountryCode;
  final String? altMobileNo;
  final String? email;
  final String? updatedAt;

  RegistrationDetail({
    this.userId,
    this.businessName,
    this.businessAddress,
    this.wardNo,
    this.provinceId,
    this.provinceTitle,
    this.districtId,
    this.districtTitle,
    this.vdcMncId,
    this.vdcMncTitle,
    this.gpsLat,
    this.gpsLng,
    this.businessCategoryId,
    this.businessCategoryTitle,
    this.businessTypeId,
    this.businessTypeTitle,
    this.tinNo,
    this.tinFlag,
    this.fullName,
    this.countryCode,
    this.primaryMobileNo,
    this.gender,
    this.altCountryCode,
    this.altMobileNo,
    this.email,
    this.updatedAt,
  });

  factory RegistrationDetail.fromJson(Map<String, dynamic> json) {
    return RegistrationDetail(
      userId: json['user_id'] != null
          ? int.tryParse(json['user_id'].toString())
          : null,
      businessName: json['business_name'],
      businessAddress: json['business_address'],
      wardNo: json['ward_no'],
      provinceId: json['province_id'],
      provinceTitle: json['province_title'],
      districtId: json['district_id'],
      districtTitle: json['district_title'],
      vdcMncId: json['vdc_mnc_id'],
      vdcMncTitle: json['vdc_mnc_title'],
      gpsLat: json['gps_lat'],
      gpsLng: json['gps_lng'],
      businessCategoryId: json['business_category_id'],
      businessCategoryTitle: json['business_category_title'],
      businessTypeId: json['business_type_id'],
      businessTypeTitle: json['business_type_title'],
      tinNo: json['tin_no'],
      tinFlag: json['tin_flag'],
      fullName: json['full_name'],
      countryCode: json['country_code'],
      primaryMobileNo: json['primary_mobile_no'],
      gender: json['gender'],
      altCountryCode: json['alt_country_code'],
      altMobileNo: json['alt_mobile_no'],
      email: json['email'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'business_name': businessName,
      'business_address': businessAddress,
      'ward_no': wardNo,
      'province_id': provinceId,
      'province_title': provinceTitle,
      'district_id': districtId,
      'district_title': districtTitle,
      'vdc_mnc_id': vdcMncId,
      'vdc_mnc_title': vdcMncTitle,
      'gps_lat': gpsLat,
      'gps_lng': gpsLng,
      'business_category_id': businessCategoryId,
      'business_category_title': businessCategoryTitle,
      'business_type_id': businessTypeId,
      'business_type_title': businessTypeTitle,
      'tin_no': tinNo,
      'tin_flag': tinFlag,
      'full_name': fullName,
      'country_code': countryCode,
      'primary_mobile_no': primaryMobileNo,
      'gender': gender,
      'alt_country_code': altCountryCode,
      'alt_mobile_no': altMobileNo,
      'email': email,
      'updated_at': updatedAt,
    };
  }
}
