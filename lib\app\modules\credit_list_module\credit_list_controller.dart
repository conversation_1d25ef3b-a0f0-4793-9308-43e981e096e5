import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';

import 'package:tuple/tuple.dart';

class CreditListController extends GetxController {
  var _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  var _receivable = <LedgerDetailModel>[].obs;
  List<LedgerDetailModel> get receivable => _receivable;

  var _payable = <LedgerDetailModel>[].obs;
  List<LedgerDetailModel> get payable => _payable;

  var _filtered = <LedgerDetailModel>[].obs;
  List<LedgerDetailModel> get filtered => _filtered;

  LedgerRepository _ledgerRepository = LedgerRepository();

  init() async {
    _isLoading.value = true;
    Tuple2<List<LedgerDetailModel>, List<LedgerDetailModel>> data =
        await _ledgerRepository.getAllGeneralLedgerHavingDues();
    _receivable(data.item1);
    _payable(data.item2);
    _receivable.refresh();
    _payable.refresh();

    _filtered.clear();
    _filtered.addAll(_receivable);
    _filtered.addAll(_payable);

    _isLoading.value = false;
    _isLoading.refresh();
  }

  search(String searchString) {
    _isLoading.value = true;
    _filtered.clear();
    _filtered.refresh();

    _receivable.forEach((ledger) {
      _filtered.addIf(
          ledger.ledgerTitle!
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          ledger);
    });

    _payable.forEach((ledger) {
      _filtered.addIf(
          ledger.ledgerTitle!
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          ledger);
    });
    _filtered.refresh();
    print("this is list ${filtered.length}");
    _isLoading.value = false;
  }
}
