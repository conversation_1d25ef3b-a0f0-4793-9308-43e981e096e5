import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/image_model.dart';
import 'package:mobile_khaata_v2/app/model/database/ledger_model.dart';
import 'package:mobile_khaata_v2/app/repository/image_repository.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/database/database_helpler.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/sync_actions.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:path_provider/path_provider.dart';
import 'package:sqflite/sqflite.dart';
import 'package:tuple/tuple.dart';

import '../../../../utilities/shared_pref_helper1.dart';

class AddEditPartyController extends GetxController {
  final String tag = "PartyAddEditController";

  final _isLoading = false.obs;
  final _editFlag = false.obs;
  final _readOnlyFlag = false.obs;

  bool get isLoading => _isLoading.value;

  bool get editFlag => _editFlag.value;

  bool get readOnlyFlag => _readOnlyFlag.value;

  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  final formKey = GlobalKey<FormState>();

  final LedgerRepository _ledgerRepository = LedgerRepository();

  var partyMain = LedgerModel().obs;

  LedgerModel get party => partyMain.value;

  ImageModel? partyImageModel;

  File? partyImage;

  TextEditingController titleController = TextEditingController();
  TextEditingController mobileController = TextEditingController();

  var _fileUploadPermission = false.obs;

  bool get fileUploadPermission => _fileUploadPermission.value;

  final SharedPrefHelper1 prefsHelper = SharedPrefHelper1();

  // Add reactive variables for image picker
  final RxList<dynamic> _imagePickerValues = <dynamic>[].obs;
  List<dynamic> get imagePickerValues => _imagePickerValues;

  @override
  Future<void> onInit() async {
    imageCache.clear();
    _isLoading(true);

    party.openingDateBS = currentDateBS;
    _fileUploadPermission.value =
        await prefsHelper.checkPermission("File Upload");

    _isLoading(false);
    super.onInit();
  }

  Uint8List? imageFile;

  @override
  void onClose() {
    titleController.dispose();
    mobileController.dispose();
    super.onClose();
  }

  initEdit(partyId, readOnlyFlag) async {
    _isLoading(true);

    _readOnlyFlag.value = readOnlyFlag;
    _editFlag.value = true;

    Tuple2<LedgerModel, ImageModel> dt =
        await _ledgerRepository.getLedgerDetailForAddEdit(partyId);

    partyMain.value = dt.item1;
    titleController.text = party.ledgerTitle ?? "";
    mobileController.text = party.mobileNo ?? "";

    if (dt.item2.imageBitmap != null) {
      //image not null
      final tempDir = await getTemporaryDirectory();
      final file =
          await File('${tempDir.path}/image-000.${dt.item2.imageExt}').create();
      file.writeAsBytesSync(dt.item2.imageBitmap!);
      partyImage = file;
      imageFile = await file.readAsBytes();
      partyImageModel = dt.item2;

      // Update image picker values
      _imagePickerValues.value = [partyImage];
    } else {
      _imagePickerValues.value = [];
    }

    partyMain.refresh();
    _editFlag.refresh();
    _isLoading(false);

    _isLoading.refresh();
  }

  imagePickerOnChangeHandler(value) async {
    // Update the reactive list first
    _imagePickerValues.value = value;

    if (value.length > 0) {
      Tuple2<List<int>, String> selectedImage =
          await compressImage(File(value[0].path));
      partyImageModel = ImageModel(
          imageBitmap: selectedImage.item1, imageExt: selectedImage.item2);
      partyImage = File(value[0].path);
    } else {
      partyImageModel = null;
      partyImage = null;
    }
  }

  bool checkIfLargeImage() {
    //preConvertFiles can be used to convert files list to txn image model once, so than it don't need re convert
    bool status = false;
    if (null != partyImageModel) {
      if (partyImageModel!.imageBitmap!.length > MAX_IMAGE_SIZE) {
        status = true;
      }
    }
    return status;
  }

  Future<bool> checkPhone(String excludeLedgerId) async {
    bool status = false;

    DatabaseHelper databaseHelper = DatabaseHelper();
    try {
      Database? dbClient = await databaseHelper.database;
      if (dbClient == null) return status;

      LedgerRepository repository = LedgerRepository();
      return !await repository.checkMobileExists(mobileController.text,
          excludeLedgerId: excludeLedgerId);
    } catch (err) {
      return status;
    }
  }

  Future<bool> saveParty() async {
    bool status = false;

    ImageModel? profileImage = partyImageModel;
    LedgerModel newLedgerModel = party;
    newLedgerModel.openingDate =
        toDateAD(NepaliDateTime.parse(newLedgerModel.openingDateBS ?? ""));

    DatabaseHelper databaseHelper = DatabaseHelper();

    try {
      Database? dbClient = await databaseHelper.database;
      if (dbClient == null) return status;

      print(newLedgerModel.openingType);

      await dbClient.transaction((dbBatchTxn) async {
        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        if (profileImage != null) {
          ImageRepository imageRepository = ImageRepository();

          String imageId = await imageRepository.insert(profileImage,
              dbClient: dbBatchTxn, batchID: batchID);
          newLedgerModel.ledgerPhoto = imageId;
        }

        String ledgerId = await _ledgerRepository.insert(newLedgerModel,
            dbClient: dbBatchTxn, batchID: batchID);

        pushPendingQueries(
            showNotificationAfterResult: false,
            singleBatchId: batchID,
            dbClient: dbBatchTxn);

        // pullPendingQueries(
        //   showNotificationAfterResult: false,
        //   pullCount: 1,
        // );

        status = true;
      });
    } catch (err) {
      print(err);
    }

    return status;
  }

  Future<bool> updateParty() async {
    bool status = false;

    ImageModel? profileImage;
    LedgerModel newLedgerModel;

    try {
      party.ledgerTitle = strTrim(party.ledgerTitle ?? "");

      profileImage = partyImageModel;
      newLedgerModel = party;
      newLedgerModel.openingDate =
          // ignore: unnecessary_null_comparison
          (null != strTrim(newLedgerModel.openingDateBS ?? ""))
              ? toDateAD(
                  NepaliDateTime.parse(newLedgerModel.openingDateBS ?? ""))
              : null;

      DatabaseHelper databaseHelper = DatabaseHelper();
      Database? dbClient = await databaseHelper.database;

      await dbClient!.transaction((dbBatchTxn) async {
        String primaryKeyPrefix = await getPrimaryKeyPrefix();
        String batchID = primaryKeyPrefix + uuidV4;

        ImageRepository imageRepository = ImageRepository();
        if (newLedgerModel.ledgerPhoto != null) {
          //ledger had previous image, so delete it
          await imageRepository.delete(newLedgerModel.ledgerPhoto ?? "",
              dbClient: dbBatchTxn, batchID: batchID);
          newLedgerModel.ledgerPhoto = null;
        }

        if (profileImage != null) {
          String imageId = await imageRepository.insert(profileImage,
              dbClient: dbBatchTxn, batchID: batchID);
          newLedgerModel.ledgerPhoto = imageId;
        }

        await _ledgerRepository.update(newLedgerModel,
            dbClient: dbBatchTxn, batchID: batchID);

        pushPendingQueries(
            showNotificationAfterResult: false,
            singleBatchId: batchID,
            dbClient: dbBatchTxn);

        status = true;
      });
    } catch (e, trace) {
      print("yo error ho $e");
      // Log.e(tag, e.toString() + trace.toString());
    }

    return status;
  }

  void clearSelectedImage() {
    // Clear the selected image from image picker
    _imagePickerValues.clear();
    partyImageModel = null;
    partyImage = null;
    imageFile = null;

    // Refresh the UI
    update();
  }
}
