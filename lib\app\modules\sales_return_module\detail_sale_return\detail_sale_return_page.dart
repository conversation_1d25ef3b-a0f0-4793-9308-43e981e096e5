// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:form_builder_image_picker/form_builder_image_picker.dart';
import 'package:get/get_state_manager/get_state_manager.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/components/image_grid_gallery.dart';
import 'package:mobile_khaata_v2/app/components/ledger_autocomplete%20_%20textfield_with_add.dart';
import 'package:mobile_khaata_v2/app/components/payment_mode_selector.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/model/others/line_item_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/modules/sales_return_module/add_sale_return_bill_item/add_edit_sale_return_bill_item_screen.dart';
import 'package:mobile_khaata_v2/app/modules/sales_return_module/detail_sale_return/detail_sale_return_controller.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';

import 'package:nepali_utils/nepali_utils.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

import '../../../../database/txn_type.dart';

extension ExtendedIterable<E> on Iterable<E> {
  /// Like Iterable<T>.map but callback have index as second argument
  Iterable<T> mapIndex<T>(T Function(E e, int i) f) {
    var i = 0;
    return map((e) => f(e, i++));
  }

  void forEachIndex(void Function(E e, int i) f) {
    var i = 0;
    forEach((e) => f(e, i++));
  }
}

class DetailSaleReturnPage extends StatelessWidget {
  final String tag = "Sale Return Detail Page";

  final String? salesReturnID;
  final bool? reaOnlyFlag;

  final saleReturnController = DetailSaleReturnController();

  DetailSaleReturnPage({super.key, this.salesReturnID, this.reaOnlyFlag}) {
    saleReturnController.onInit();

    if (null != salesReturnID) {
      // initiate edit functionality
      saleReturnController.initEdit(salesReturnID, reaOnlyFlag ?? true);
    } else {
      saleReturnController.initialize();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      bool iscashSaleReturn = saleReturnController.transaction.value.ledgerId ==
          CASH_SALES_LEDGER_ID;
      bool hasSubTotal =
          (saleReturnController.transaction.value.txnSubTotalAmount != null &&
              saleReturnController.transaction.value.txnSubTotalAmount! > 0.0);

      if (saleReturnController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      } else {
        return SafeArea(
            child: Scaffold(
                resizeToAvoidBottomInset: true,
                appBar: AppBar(
                  toolbarHeight: 60,
                  elevation: 4,
                  backgroundColor: colorPrimary,
                  leading: BackButton(
                    onPressed: () => Navigator.pop(context, false),
                  ),
                  centerTitle: false,
                  titleSpacing: -5.0,
                  title: const Text(
                    "बिक्री फिर्ता\n(Sales Return Detail)",
                    style: TextStyle(
                        fontSize: 17,
                        color: Colors.white,
                        fontFamily: 'HelveticaRegular',
                        fontWeight: FontWeight.bold),
                  ),
                  actions: [
                    if (saleReturnController.editFlag) ...{
                      Container(
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: InkWell(
                            // elevation: 0,
                            // textColor: Colors.black54,
                            // color: Colors.transparent,
                            // padding: EdgeInsets.zero,
                            // splashColor: colorPrimaryLight,
                            // shape: RoundedRectangleBorder(
                            //     borderRadius: BorderRadius.circular(10)),
                            onTap: () {
                              TransactionHelper.gotoTransactionEditPage(context,
                                  salesReturnID ?? "", TxnType.salesReturn,
                                  forEdit: true);
                            },
                            child: (saleReturnController.readOnlyFlag)
                                ? Column(
                                    children: const [
                                      Icon(
                                        Icons.mode_edit,
                                        color: Colors.white,
                                      ),
                                      Text(
                                        "Click here to Edit",
                                        style: TextStyle(
                                            color: Colors.white, fontSize: 10),
                                      ),
                                    ],
                                  )
                                : Column(
                                    children: const [
                                      Icon(
                                        Icons.close,
                                        color: Colors.white,
                                      ),
                                      Text(
                                        "Cancel",
                                        style: TextStyle(
                                            color: Colors.white, fontSize: 10),
                                      ),
                                    ],
                                  )),
                      ),
                    }
                  ],
                ),

                //===========================================================================Body Part
                body: Center(
                  child: Container(
                    color: backgroundColorShade,
                    child: GestureDetector(
                      onTap: () => FocusScope.of(context).unfocus(),
                      child: Form(
                        key: saleReturnController.formKey,
                        child: SingleChildScrollView(
                          child: Column(
                            children: [
                              Card(
                                elevation: 2,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 15),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      //===========================Bill No.
                                      Expanded(
                                        flex: 1,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "फिर्ता बिल न. ",
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            FormBuilderTextField(
                                              name: "bill_no",
                                              readOnly: saleReturnController
                                                  .readOnlyFlag,
                                              autocorrect: false,
                                              keyboardType: TextInputType.text,
                                              textInputAction:
                                                  TextInputAction.done,
                                              textAlign: TextAlign.right,
                                              style: formFieldTextStyle,
                                              decoration:
                                                  formFieldStyle.copyWith(
                                                      labelText:
                                                          "Return Bill No."),
                                              controller: saleReturnController
                                                  .billNoCtrl,
                                              onChanged: (value) {
                                                saleReturnController
                                                    .transaction
                                                    .value
                                                    .txnRefNumberChar = value;
                                                saleReturnController.transaction
                                                    .refresh();
                                              },
                                            ),
                                          ],
                                        ),
                                      ),

                                      const SizedBox(
                                        width: 20,
                                      ),

                                      //===========================Transaction Date
                                      Expanded(
                                        flex: 1,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "मिति",
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            CustomDatePickerTextField(
                                              readOnly: saleReturnController
                                                  .readOnlyFlag,
                                              maxBSDate: NepaliDateTime.now(),
                                              initialValue: saleReturnController
                                                  .transaction.value.txnDateBS,
                                              onChange: (selectedDate) {
                                                saleReturnController
                                                    .transaction
                                                    .value
                                                    .txnDateBS = selectedDate;
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),

                              Card(
                                elevation: 2,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 10),
                                  child: Column(
                                    children: [
                                      //===============================================Party Balance
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Row(
                                            children: [
                                              SizedBox(
                                                width: 20,
                                                height: 20,
                                                child: Checkbox(
                                                  activeColor: colorPrimary,
                                                  checkColor: Colors.white,
                                                  value: saleReturnController
                                                      .iscashSaleReturnSelected,
                                                  onChanged:
                                                      saleReturnController
                                                              .readOnlyFlag
                                                          ? null
                                                          : (value) {
                                                              saleReturnController
                                                                      .setiscashSaleReturnSelected =
                                                                  value!;
                                                              if (value) {
                                                                saleReturnController.onChangeParty(LedgerDetailModel(
                                                                    ledgerId:
                                                                        CASH_SALES_LEDGER_ID,
                                                                    ledgerTitle:
                                                                        "$CASH_SALES_LEDGER_NAME Return"));
                                                              } else {
                                                                saleReturnController.onChangeParty(
                                                                    LedgerDetailModel(
                                                                        ledgerId:
                                                                            null));
                                                              }
                                                            },
                                                ),
                                              ),
                                              Text(
                                                  "  खुद्रा बिक्री फिर्ता\n  (Cash Sale Return)",
                                                  style: labelStyle2)
                                            ],
                                          ),
                                          RichText(
                                            textAlign: TextAlign.right,
                                            text: TextSpan(
                                                text: "पुरानो बाँकी: ",
                                                style:
                                                    TextStyle(color: textColor),
                                                children: [
                                                  if (null !=
                                                      saleReturnController
                                                          .transaction
                                                          .value
                                                          .ledgerId) ...{
                                                    TextSpan(
                                                      text:
                                                          "${saleReturnController.selectedLedger.value.balanceAmount ?? 0}",
                                                      style: TextStyle(
                                                          color: ((saleReturnController
                                                                          .selectedLedger
                                                                          .value
                                                                          .balanceAmount ??
                                                                      0) >=
                                                                  0.0)
                                                              ? colorGreenDark
                                                              : colorRedLight),
                                                    )
                                                  }
                                                ]),
                                          )
                                        ],
                                      ),

                                      Container(
                                        height: 10,
                                      ),
                                      //===============================================Party Field
                                      if (!saleReturnController
                                          .iscashSaleReturnSelected)
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              'ग्राहकको नाम',
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            LedgerAutoCompleteTextFieldWithAdd(
                                                excludedIDS: const [
                                                  CASH_SALES_LEDGER_ID
                                                ],
                                                enableFlag:
                                                    !saleReturnController
                                                        .readOnlyFlag,
                                                labelText: "Customer Name",
                                                controller: saleReturnController
                                                    .partyNameCtrl,
                                                onChangedFn: (value) {
                                                  // Log.d("called i text change");
                                                },
                                                ledgetID: saleReturnController
                                                    .transaction.value.ledgerId,
                                                onSuggestionSelectedFn:
                                                    (LedgerDetailModel ledger) {
                                                  saleReturnController
                                                      .onChangeParty(ledger);
                                                })
                                          ],
                                        ),
                                      ...iscashSaleReturn
                                          ? [
                                              const SizedBox(
                                                height: 10,
                                              ),
                                              Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    "खुद्रा ग्राहकको नाम",
                                                    style: labelStyle2,
                                                  ),
                                                  const SizedBox(height: 5.0),
                                                  FormBuilderTextField(
                                                    name: "display_text",
                                                    // readOnly:
                                                    //     (null == state.selectedLedger.ledgerId)
                                                    //         ? false
                                                    //         : true,
                                                    readOnly:
                                                        saleReturnController
                                                            .readOnlyFlag,
                                                    autocorrect: false,
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            labelText:
                                                                "Billing Name"),
                                                    controller:
                                                        saleReturnController
                                                            .displayTextCtrl,
                                                    onChanged: (value) {
                                                      saleReturnController
                                                              .transaction
                                                              .value
                                                              .txnDisplayName =
                                                          value;
                                                      saleReturnController
                                                          .transaction
                                                          .refresh();
                                                    },
                                                  ),
                                                ],
                                              )
                                            ]
                                          : [],

                                      const SizedBox(
                                        height: 10,
                                      ),
                                      if (!iscashSaleReturn)
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            //===================================Mobile
                                            SizedBox(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.3,
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'फोन नम्बर',
                                                    style: labelStyle2,
                                                  ),
                                                  const SizedBox(height: 5.0),
                                                  FormBuilderTextField(
                                                      name: "mobile",
                                                      readOnly: true,
                                                      autocorrect: false,
                                                      keyboardType:
                                                          TextInputType.number,
                                                      textInputAction:
                                                          TextInputAction.done,
                                                      inputFormatters: [
                                                        FilteringTextInputFormatter
                                                            .digitsOnly
                                                      ],
                                                      style: formFieldTextStyle,
                                                      decoration: formFieldStyle
                                                          .copyWith(
                                                              labelText:
                                                                  "Contact no.",
                                                              hintText:
                                                                  "Contact no"),
                                                      controller:
                                                          saleReturnController
                                                              .mobileCtrl),
                                                ],
                                              ),
                                            ),

                                            //===================================Address
                                            SizedBox(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.5,
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Text(
                                                    'ठेगाना',
                                                    style: labelStyle2,
                                                  ),
                                                  const SizedBox(height: 5.0),
                                                  FormBuilderTextField(
                                                    name: "address",
                                                    readOnly: true,
                                                    autocorrect: false,
                                                    keyboardType:
                                                        TextInputType.text,
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            labelText:
                                                                "Address"),
                                                    controller:
                                                        saleReturnController
                                                            .addressCtrl,
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      if (!iscashSaleReturn)
                                        const SizedBox(height: 10.0),

                                      //=====================================PAN No Field
                                      if (!iscashSaleReturn)
                                        Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              "पान / मु. अ. कर नम्बर",
                                              style: labelStyle2,
                                            ),
                                            const SizedBox(height: 5.0),
                                            FormBuilderTextField(
                                                name: "pan_no",
                                                readOnly: true,
                                                autocorrect: false,
                                                keyboardType:
                                                    TextInputType.number,
                                                inputFormatters: [
                                                  FilteringTextInputFormatter
                                                      .digitsOnly
                                                ],
                                                textInputAction:
                                                    TextInputAction.done,
                                                style: formFieldTextStyle,
                                                decoration:
                                                    formFieldStyle.copyWith(
                                                        labelText:
                                                            "PAN/VAT No."),
                                                controller: saleReturnController
                                                    .panNoCtrl),
                                          ],
                                        ),
                                    ],
                                  ),
                                ),
                              ),

                              //================================================Item Container
                              Card(
                                child: Container(
                                    width: double.infinity,
                                    padding: const EdgeInsets.all(0.0),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          width: double.infinity,
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 15, vertical: 8),
                                          decoration: BoxDecoration(
                                              color: colorPrimaryLight,
                                              borderRadius:
                                                  const BorderRadius.only(
                                                      topLeft:
                                                          Radius.circular(4),
                                                      topRight:
                                                          Radius.circular(4))),
                                          child: DefaultTextStyle(
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 15,
                                            ),
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                const Text(
                                                  "बिक्री फिर्ता सामानहरु (Return Items)",
                                                ),
                                                Text(
                                                  "Total Item: ${saleReturnController.items.length}",
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        Container(
                                          constraints: const BoxConstraints(
                                              maxHeight: 300),
                                          child: getItemListView(
                                              context,
                                              saleReturnController.items,
                                              saleReturnController),
                                        ),
                                        // Container(
                                        //   margin: EdgeInsets.symmetric(
                                        //       vertical: 10),
                                        //   child: Center(
                                        //     child: RaisedButton(
                                        //       elevation: 10,
                                        //       shape: RoundedRectangleBorder(
                                        //         borderRadius:
                                        //             BorderRadius.circular(20.0),
                                        //       ),
                                        //       color: colorPrimary,
                                        //       splashColor: colorPrimaryLightest,
                                        //       child: Padding(
                                        //         padding:
                                        //             const EdgeInsets.symmetric(
                                        //                 vertical: 10,
                                        //                 horizontal: 10),
                                        //         child: Text(
                                        //           "बिक्री फिर्ता सामान थप्नुहोस्\n(Add Sale Return Item)",
                                        //           style: TextStyle(
                                        //             color: Colors.white,
                                        //             fontSize: 15,
                                        //           ),
                                        //         ),
                                        //       ),
                                        //       onPressed: saleReturnController
                                        //               .readOnlyFlag
                                        //           ? null
                                        //           : () async {
                                        //               var returnedData =
                                        //                   await showDialog(
                                        //                       context: context,
                                        //                       useRootNavigator:
                                        //                           true,
                                        //                       barrierDismissible:
                                        //                           false,
                                        //                       builder: (d_c) {
                                        //                         return AlertDialog(
                                        //                           insetPadding: EdgeInsets.symmetric(
                                        //                               horizontal:
                                        //                                   10,
                                        //                               vertical:
                                        //                                   10),
                                        //                           contentPadding:
                                        //                               EdgeInsets
                                        //                                   .zero,
                                        //                           clipBehavior:
                                        //                               Clip.hardEdge,
                                        //                           content: Container(
                                        //                               width: MediaQuery.of(context)
                                        //                                       .size
                                        //                                       .width -
                                        //                                   20,
                                        //                               child:
                                        //                                   AddEditSaleReturnBilledItemScreenView()),
                                        //                         );
                                        //                       });
                                        //               if (null !=
                                        //                   returnedData) {
                                        //                 if (null !=
                                        //                     returnedData
                                        //                         .billedItem) {
                                        //                   saleReturnController
                                        //                       .items
                                        //                       .add(returnedData
                                        //                           .billedItem);
                                        //                   saleReturnController
                                        //                       .items
                                        //                       .refresh();

                                        //                   saleReturnController
                                        //                       .recalculateForItems();
                                        //                   Log.d(
                                        //                       "got  billed item ${returnedData.billedItem.toJson()}");
                                        //                 }
                                        //               }
                                        //             },
                                        //     ),
                                        //   ),
                                        // ),
                                      ],
                                    )),
                              ),

                              //===============================================Total Amount
                              Card(
                                elevation: 2,
                                child: Column(
                                  children: [
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 4, horizontal: 8),
                                      decoration: BoxDecoration(
                                          color: colorPrimaryLight,
                                          borderRadius: const BorderRadius.only(
                                              topLeft: Radius.circular(4),
                                              topRight: Radius.circular(4))),
                                      child: const Center(
                                          child: Text(
                                        "Bill Totals (जम्मा बिल)",
                                        style: TextStyle(
                                            color: Colors.white, fontSize: 16),
                                      )),
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 10,
                                      ),
                                      child: Column(
                                        children: [
                                          // =============================================Sub Total
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                "उप कुल",
                                                style: labelStyle2,
                                              ),
                                              const SizedBox(height: 5.0),
                                              FormBuilderTextField(
                                                name: "txn_subtotal",
                                                readOnly: saleReturnController
                                                        .readOnlyFlag
                                                    ? true
                                                    : (saleReturnController
                                                            .items.isNotEmpty)
                                                        ? true
                                                        : false,
                                                autocorrect: false,
                                                keyboardType:
                                                    const TextInputType
                                                        .numberWithOptions(
                                                        decimal: true),
                                                textInputAction:
                                                    TextInputAction.done,
                                                style: formFieldTextStyle,
                                                inputFormatters: [
                                                  FilteringTextInputFormatter
                                                      .allow(RegExp(
                                                          r'^(\d+)?\.?\d{0,2}'))
                                                ],
                                                maxLength: 10,
                                                decoration:
                                                    formFieldStyle.copyWith(
                                                        hintText: "Sub Total",
                                                        counterText: ''),
                                                textAlign: TextAlign.end,
                                                controller: saleReturnController
                                                    .subTotalAmountCtrl,
                                                onChanged: (value) {
                                                  saleReturnController
                                                      .onSubTotalIndividualChange(
                                                          value ?? "",
                                                          editorTag:
                                                              'txn_subtotal');
                                                  saleReturnController
                                                      .transaction
                                                      .refresh();
                                                },
                                              ),
                                            ],
                                          ),
                                          const SizedBox(
                                            height: 20,
                                          ),

                                          // =============================================Discount
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                "छुट (Discount)",
                                                style: labelStyle2,
                                              ),
                                              const SizedBox(height: 5.0),
                                              Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  SizedBox(
                                                    width: 80,
                                                    child: FormBuilderTextField(
                                                      name:
                                                          "txn_discount_percent",
                                                      readOnly:
                                                          saleReturnController
                                                                  .readOnlyFlag ||
                                                              (!hasSubTotal),
                                                      autocorrect: false,
                                                      keyboardType:
                                                          const TextInputType
                                                              .numberWithOptions(
                                                              decimal: true),
                                                      textInputAction:
                                                          TextInputAction.done,
                                                      style: formFieldTextStyle,
                                                      decoration: formFieldStyle
                                                          .copyWith(
                                                              suffix:
                                                                  const Text(
                                                                      "%"),
                                                              labelText: "%"),
                                                      textAlign: TextAlign.end,
                                                      controller:
                                                          saleReturnController
                                                              .discountPercentageCtrl,
                                                      onChanged: (value) {
                                                        saleReturnController
                                                            .updateDiscountPercentage(
                                                                value ?? "",
                                                                editorTag:
                                                                    'txn_discount_percent');
                                                      },
                                                    ),
                                                  ),
                                                  const SizedBox(
                                                    width: 20,
                                                  ),
                                                  Expanded(
                                                    child: FormBuilderTextField(
                                                        name:
                                                            "txn_discount_amount",
                                                        readOnly:
                                                            saleReturnController
                                                                    .readOnlyFlag ||
                                                                (!hasSubTotal),
                                                        autocorrect: false,
                                                        keyboardType:
                                                            const TextInputType.numberWithOptions(
                                                                decimal: true),
                                                        textInputAction:
                                                            TextInputAction
                                                                .done,
                                                        style:
                                                            formFieldTextStyle,
                                                        decoration: formFieldStyle
                                                            .copyWith(
                                                                labelText:
                                                                    "छुट रकम (Dis. Amount)"),
                                                        textAlign:
                                                            TextAlign.end,
                                                        controller:
                                                            saleReturnController
                                                                .discountAmountCtrl,
                                                        onChanged: (value) {
                                                          saleReturnController
                                                              .updateDiscountAmount(
                                                                  value ?? "",
                                                                  editorTag:
                                                                      'txn_discount_amount');
                                                        }),
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),
                                          const SizedBox(
                                            height: 25,
                                          ),

                                          //====================================================VAT
                                          ...iscashSaleReturn
                                              ? []
                                              : [
                                                  Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Row(
                                                        children: [
                                                          SizedBox(
                                                            width: 20,
                                                            height: 20,
                                                            child: Checkbox(
                                                              activeColor:
                                                                  colorPrimary,
                                                              checkColor:
                                                                  Colors.white,
                                                              value: saleReturnController
                                                                  .isVatEnabled,
                                                              onChanged: (saleReturnController
                                                                          .readOnlyFlag ||
                                                                      (!hasSubTotal))
                                                                  ? null
                                                                  : (value) {
                                                                      saleReturnController
                                                                          .onToggleVat(
                                                                              value!);
                                                                    },
                                                            ),
                                                          ),
                                                          Text(
                                                              "  मु.अ. कर (VAT)",
                                                              style:
                                                                  labelStyle2)
                                                        ],
                                                      ),
                                                      const SizedBox(
                                                          height: 10.0),
                                                      Row(
                                                        mainAxisAlignment:
                                                            MainAxisAlignment
                                                                .spaceBetween,
                                                        children: [
                                                          SizedBox(
                                                            width: 80,
                                                            child:
                                                                FormBuilderTextField(
                                                              name:
                                                                  "txn_tax_percent",
                                                              readOnly: true,
                                                              autocorrect:
                                                                  false,
                                                              keyboardType:
                                                                  const TextInputType
                                                                      .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                              textInputAction:
                                                                  TextInputAction
                                                                      .done,
                                                              style:
                                                                  formFieldTextStyle,
                                                              decoration: formFieldStyle
                                                                  .copyWith(
                                                                      suffix:
                                                                          const Text(
                                                                              "%"),
                                                                      labelText:
                                                                          "%"),
                                                              textAlign:
                                                                  TextAlign.end,
                                                              controller:
                                                                  saleReturnController
                                                                      .vatPercentCtrl,
                                                              onChanged:
                                                                  (value) {
                                                                saleReturnController
                                                                    .onvatPercentChange(
                                                                        value ??
                                                                            "",
                                                                        editorTag:
                                                                            'txn_tax_percent');
                                                              },
                                                            ),
                                                          ),
                                                          const SizedBox(
                                                            width: 20,
                                                          ),
                                                          Expanded(
                                                            child:
                                                                FormBuilderTextField(
                                                              name:
                                                                  "txn_tax_amount",
                                                              readOnly: true,
                                                              autocorrect:
                                                                  false,
                                                              keyboardType:
                                                                  const TextInputType
                                                                      .numberWithOptions(
                                                                      decimal:
                                                                          true),
                                                              textInputAction:
                                                                  TextInputAction
                                                                      .done,
                                                              style:
                                                                  formFieldTextStyle,
                                                              decoration: formFieldStyle
                                                                  .copyWith(
                                                                      labelText:
                                                                          "मु.अ. कर रकम (VAT Amount) "),
                                                              textAlign:
                                                                  TextAlign.end,
                                                              controller:
                                                                  saleReturnController
                                                                      .vatAmountCtrl,
                                                              onChanged:
                                                                  (value) {
                                                                saleReturnController
                                                                    .onvatAmountChange(
                                                                        value ??
                                                                            "",
                                                                        editorTag:
                                                                            'txn_tax_amount');
                                                              },
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                  const SizedBox(
                                                    height: 5,
                                                  ),
                                                ],

                                          const Divider(
                                            height: 5,
                                          ),
                                          const Divider(
                                            height: 0,
                                          ),
                                          const SizedBox(
                                            height: 15,
                                          ),

                                          // =============================================Total Amount
                                          Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Text(
                                                "कुल रकम",
                                                style: labelStyle2,
                                              ),
                                              const SizedBox(
                                                height: 5,
                                              ),
                                              FormBuilderTextField(
                                                  name: "txn_total",
                                                  readOnly: true,
                                                  autocorrect: false,
                                                  keyboardType:
                                                      const TextInputType
                                                          .numberWithOptions(
                                                          decimal: true),
                                                  textInputAction:
                                                      TextInputAction.done,
                                                  style: formFieldTextStyle,
                                                  decoration:
                                                      formFieldStyle.copyWith(
                                                          labelText:
                                                              "Total Amount"),
                                                  textAlign: TextAlign.end,
                                                  controller:
                                                      saleReturnController
                                                          .totalAmountCtrl),
                                            ],
                                          ),
                                          const SizedBox(
                                            height: 25,
                                          ),

                                          // =============================================Received Amount
                                          Row(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.end,
                                              children: [
                                                Expanded(
                                                  child: Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Row(
                                                        children: [
                                                          SizedBox(
                                                            width: 20,
                                                            height: 20,
                                                            child: Checkbox(
                                                              activeColor:
                                                                  colorPrimary,
                                                              checkColor:
                                                                  Colors.white,
                                                              value: iscashSaleReturn
                                                                  ? true
                                                                  : (saleReturnController
                                                                      .isReceived),
                                                              onChanged: (saleReturnController
                                                                          .readOnlyFlag ||
                                                                      iscashSaleReturn ||
                                                                      (!hasSubTotal))
                                                                  ? null
                                                                  : (value) {
                                                                      saleReturnController
                                                                              .setIsReceived =
                                                                          value!;
                                                                      if (value) {
                                                                        saleReturnController
                                                                            .transaction
                                                                            .value
                                                                            .txnCashAmount = saleReturnController
                                                                                .transaction.value.txnTotalAmount ??
                                                                            0.0;
                                                                        saleReturnController
                                                                            .transaction
                                                                            .value
                                                                            .txnBalanceAmount = 0.00;
                                                                      } else {
                                                                        saleReturnController
                                                                            .transaction
                                                                            .value
                                                                            .txnCashAmount = 0.0;

                                                                        saleReturnController
                                                                            .transaction
                                                                            .value
                                                                            .txnBalanceAmount = saleReturnController
                                                                                .transaction.value.txnTotalAmount ??
                                                                            0.0;
                                                                      }
                                                                      saleReturnController
                                                                          .assignTransactionToTextFields();
                                                                    },
                                                            ),
                                                          ),
                                                          Text(" भुक्तानी रकम",
                                                              style:
                                                                  labelStyle2)
                                                        ],
                                                      ),
                                                      const SizedBox(
                                                        height: 10,
                                                      ),
                                                      PaymentModeSelector(
                                                          onChangedFn: (v) {
                                                            saleReturnController
                                                                .transaction
                                                                .value
                                                                .txnPaymentTypeId = v;

                                                            saleReturnController
                                                                    .transaction
                                                                    .value
                                                                    .txnPaymentReference =
                                                                null;
                                                            saleReturnController
                                                                    .transaction
                                                                    .value
                                                                    .chequeIssueDateBS =
                                                                null;
                                                            saleReturnController
                                                                .transaction
                                                                .refresh();
                                                          },
                                                          paymentModeID:
                                                              saleReturnController
                                                                  .transaction
                                                                  .value
                                                                  .txnPaymentTypeId,
                                                          enableFlag:
                                                              !(saleReturnController
                                                                      .readOnlyFlag ||
                                                                  (!hasSubTotal)))
                                                    ],
                                                  ),
                                                ),
                                                const SizedBox(
                                                  width: 10,
                                                ),
                                                Expanded(
                                                  child: FormBuilderTextField(
                                                    name: "txn_cash_amount",
                                                    readOnly:
                                                        (saleReturnController
                                                                .readOnlyFlag ||
                                                            !hasSubTotal ||
                                                            iscashSaleReturn),
                                                    autocorrect: false,
                                                    keyboardType:
                                                        const TextInputType
                                                            .numberWithOptions(
                                                            decimal: true),
                                                    textInputAction:
                                                        TextInputAction.done,
                                                    inputFormatters: [
                                                      FilteringTextInputFormatter
                                                          .digitsOnly
                                                    ],
                                                    style: formFieldTextStyle,
                                                    decoration:
                                                        formFieldStyle.copyWith(
                                                            labelText:
                                                                "Paid Amount"),
                                                    textAlign: TextAlign.end,
                                                    controller:
                                                        saleReturnController
                                                            .receivedAmountCtrl,
                                                    onChanged: (value) {
                                                      saleReturnController
                                                          .changeReceivedAmount(
                                                              value ?? "",
                                                              editorTag:
                                                                  'txn_cash_amount');
                                                    },
                                                  ),
                                                )
                                              ]),

                                          ...(saleReturnController.transaction
                                                      .value.txnPaymentTypeId !=
                                                  PAYMENT_MODE_CASH_ID)
                                              ? [
                                                  const SizedBox(
                                                    height: 25,
                                                  ),
                                                  Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text("चेक/भौचर न.",
                                                          style: labelStyle2),
                                                      const SizedBox(
                                                        height: 10,
                                                      ),
                                                      TextField(
                                                          autocorrect: false,
                                                          readOnly:
                                                              saleReturnController
                                                                  .readOnlyFlag,
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration: formFieldStyle
                                                              .copyWith(
                                                                  labelText:
                                                                      "Cheque/Voucher No."),
                                                          controller:
                                                              saleReturnController
                                                                  .paymentRefCtrl,
                                                          onChanged: (v) {
                                                            saleReturnController
                                                                .transaction
                                                                .value
                                                                .txnPaymentReference = v;
                                                            saleReturnController
                                                                .transaction
                                                                .refresh();
                                                          }),
                                                    ],
                                                  ),
                                                ]
                                              : [],
                                          if (saleReturnController.transaction
                                                  .value.txnPaymentTypeId ==
                                              PAYMENT_MODE_CHEQUE_ID) ...[
                                            const SizedBox(
                                              height: 25,
                                            ),
                                            Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                Text("चेक मिति",
                                                    style: labelStyle2),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                CustomDatePickerTextField(
                                                  labelText: "Cheque Date",
                                                  readOnly: saleReturnController
                                                      .readOnlyFlag,
                                                  // maxBSDate: NepaliDateTime.now(),
                                                  initialValue:
                                                      saleReturnController
                                                          .transaction
                                                          .value
                                                          .chequeIssueDateBS,
                                                  onChange: (selectedDate) {
                                                    saleReturnController
                                                            .transaction
                                                            .value
                                                            .chequeIssueDateBS =
                                                        selectedDate;
                                                  },
                                                ),
                                              ],
                                            )
                                          ],

                                          const SizedBox(
                                            height: 25,
                                          ),

                                          // =============================================Balance Amount
                                          ...iscashSaleReturn
                                              ? []
                                              : [
                                                  Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .start,
                                                    children: [
                                                      Text("बाँकी रहेको रकम",
                                                          style: labelStyle2),
                                                      const SizedBox(
                                                        height: 10,
                                                      ),
                                                      FormBuilderTextField(
                                                          name:
                                                              "txn_balance_amount",
                                                          readOnly: true,
                                                          autocorrect: false,
                                                          keyboardType:
                                                              const TextInputType.numberWithOptions(
                                                                  decimal:
                                                                      true),
                                                          textInputAction:
                                                              TextInputAction
                                                                  .done,
                                                          style:
                                                              formFieldTextStyle,
                                                          decoration: formFieldStyle
                                                              .copyWith(
                                                                  labelText:
                                                                      "Balance Due"),
                                                          textAlign:
                                                              TextAlign.end,
                                                          controller:
                                                              saleReturnController
                                                                  .dueAmountCtrl),
                                                    ],
                                                  ),
                                                  const SizedBox(
                                                    height: 20,
                                                  ),
                                                ],
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),

                              // ===============================================Image
                              Container(
                                width: double.infinity,
                                child: Card(
                                  elevation: 2,
                                  child: Container(
                                    child: Container(
                                        // color: Colors.red,
                                        height: 140,
                                        width: 100,
                                        // width: ,
                                        // child: (null==state.selectImage)?
                                        child: FormBuilderImagePicker(
                                            name: "image_picker",
                                            bottomSheetPadding:
                                                EdgeInsets.all(0),
                                            decoration: InputDecoration(
                                              border: InputBorder.none,
                                            ),
                                            maxImages: 2,
                                            iconColor: colorPrimaryLight,
                                            initialValue: saleReturnController
                                                .imageListFile,
                                            onChanged: (_fls) async {
                                              if (_fls != null &&
                                                  _fls.isNotEmpty) {
                                                _fls.forEach((element) {
                                                  saleReturnController.files
                                                      .add(
                                                    File(element.path),
                                                  );
                                                });
                                                saleReturnController.files
                                                    .refresh();
                                                bool isLargeFile =
                                                    await saleReturnController
                                                        .checkLargeImage(_fls);
                                                if (isLargeFile) {
                                                  showToastMessage(context,
                                                      message:
                                                          MAX_IMAGE_SIZE_MESSAGE,
                                                      alertType:
                                                          AlertType.Error);
                                                  return;
                                                }
                                              }
                                            })

                                        //     :customImageBox(
                                        //     state.selectImage,
                                        //     onCancel: (saleReturnController.readOnlyFlag)?  null : () => state.imageCancelButtonOnClickHandler()
                                        // ),
                                        ),
                                  ),
                                ),
                              ),
                              //===============================================Image Preview Grid
                              SizedBox(
                                width: double.infinity,
                                child: Card(
                                  elevation: 2,
                                  child: SizedBox(
                                      height: 140,
                                      width: 100,
                                      child: ImageGalleryGrid(
                                          images: saleReturnController.files)),
                                ),
                              ),

                              //===============================================Description
                              Card(
                                elevation: 2,
                                child: Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 10, vertical: 10),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        "कैफियत",
                                        style: labelStyle2,
                                      ),
                                      const SizedBox(height: 5.0),
                                      FormBuilderTextField(
                                        name: "description",
                                        readOnly:
                                            saleReturnController.readOnlyFlag,
                                        autocorrect: false,
                                        textAlign: TextAlign.start,
                                        textInputAction:
                                            TextInputAction.newline,
                                        style: formFieldTextStyle,
                                        decoration: formFieldStyle.copyWith(
                                            labelText: "Remarks"),
                                        minLines: 4,
                                        maxLines: 4,
                                        controller:
                                            saleReturnController.descCtrl,
                                        onChanged: (value) {
                                          saleReturnController.transaction.value
                                              .txnDescription = value;
                                          saleReturnController.transaction
                                              .refresh();
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),

                //=================================================Save button
                bottomNavigationBar: BottomSaveCancelButton(
                  shadow: false,
                  hasDelete: true,
                  onDeleteBtnPressedFn: () async {
                    showAlertDialog(context,
                        okText: "YES",
                        hasCancel: true,
                        cancelText: "NO",
                        alertType: AlertType.Error,
                        alertTitle: "Confirm Delete",
                        onCloseButtonPressed: () async {
                      // Navigator.of(context).pop();
                      ProgressDialog progressDialog = ProgressDialog(context,
                          type: ProgressDialogType.normal,
                          isDismissible: false);
                      progressDialog.update(
                          message: "Checking Permission. Please wait....");
                      await progressDialog.show();
                      Tuple2<bool, String> checkResp =
                          await PermissionWrapperController()
                              .requestForPermissionCheck(
                                  forPermission:
                                      PermissionManager.salesReturnDelete);
                      if (checkResp.item1) {
                        //has  permission
                        progressDialog.update(
                            message: "Deleting Data. Please wait....");
                        Tuple2<bool, String> deleteResp =
                            await TransactionRepository()
                                .delete(salesReturnID ?? "");
                        await progressDialog.hide();
                        if (deleteResp.item1) {
                          //  data deleted
                          TransactionHelper.refreshPreviousPages();
                          showAlertDialog(context,
                              barrierDismissible: false,
                              alertType: AlertType.Success,
                              alertTitle: "", onCloseButtonPressed: () {
                            // Navigator.of(_).pop();
                            //     Navigator.of(context).pop();
                            Navigator.of(context).pop();
                          }, message: deleteResp.item2);
                        } else {
                          //cannot  delete  data
                          showAlertDialog(context,
                              alertType: AlertType.Error,
                              alertTitle: "",
                              message: deleteResp.item2);
                        }
                      } else {
                        await progressDialog.hide();
                        showAlertDialog(context,
                            alertType: AlertType.Error,
                            alertTitle: "",
                            message: checkResp.item2);
                      }
                    },
                        message:
                            "Are you sure you  want to  delete this sales return record?");
                  },
                  saveText: "Share",
                  enableFlag: true,
                  onSaveBtnPressedFn: () {
                    TransactionHelper.goToPrintPage(
                        context, salesReturnID ?? "", TxnType.salesReturn);
                  },
                )));
      }
    });
  }
}

Widget getItemListView(
  BuildContext context,
  List<LineItemDetailModel> items,
  DetailSaleReturnController saleReturnController,
) {
  // return Container(height: 40, color: Colors.red);
  ScrollController scrollController = ScrollController();
  var listView = Scrollbar(
    controller: scrollController,
    thumbVisibility: true,
    child: ListView.builder(
        controller: scrollController,
        itemCount: items.length,
        shrinkWrap: true,
        itemBuilder: (context, int index) {
          LineItemDetailModel item = items[index];

          return Row(children: [
            Expanded(
                child: GestureDetector(
              onTap: saleReturnController.readOnlyFlag
                  ? null
                  : () async {
                      var returnedData = await showDialog(
                          context: context,
                          useRootNavigator: true,
                          barrierDismissible: false,
                          builder: (dC) {
                            return AlertDialog(
                                insetPadding: const EdgeInsets.symmetric(
                                    horizontal: 10, vertical: 10),
                                contentPadding: EdgeInsets.zero,
                                clipBehavior: Clip.hardEdge,
                                content: SizedBox(
                                  width: MediaQuery.of(context).size.width - 20,
                                  child: AddEditSaleReturnBilledItemScreenView(
                                    lineItemModel: item,
                                  ),
                                ));
                          });
                      if (null != returnedData) {
                        if (returnedData.deleteFlag) {
                          saleReturnController.items.removeAt(index);
                        } else if (null != returnedData.billedItem) {
                          saleReturnController.items.replaceRange(
                              index, 1, [returnedData.billedItem]);
                          saleReturnController.recalculateForItems();
                        }

                        saleReturnController.recalculateForItems();
                      }
                    },
              child: Card(
                elevation: 2,
                margin: EdgeInsets.only(
                    left: 10,
                    right: 10,
                    top: (0 == index) ? 15 : 8,
                    bottom: ((items.length - 1) == index) ? 20 : 8),
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                  decoration: const BoxDecoration(color: Colors.black12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      // =============================================item name
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              item.itemName ?? "",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: colorPrimaryDark),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 5,
                      ),

                      // =============================================gross amount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const SizedBox(
                            width: 75,
                            child: Text(
                              "Amount",
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  color: Colors.black54),
                            ),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              "${item.quantity} ${item.lineItemUnitName ?? ""} X ${formatCurrencyAmount(item.pricePerUnit!)} = ${formatCurrencyAmount(item.grossAmount!, false)}",
                              textAlign: TextAlign.right,
                              style: TextStyle(fontSize: 12, color: textColor),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 5,
                      ),

                      // =============================================Discount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            width: 100,
                            child: Text(
                              "Discount(%): ${item.discountPercent}",
                              style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 12,
                                  color: Colors.black54),
                            ),
                          ),
                          const SizedBox(
                            width: 10,
                          ),
                          Expanded(
                            child: Text(
                              " = ${formatCurrencyAmount(item.discountAmount!, false)}",
                              textAlign: TextAlign.right,
                              style: TextStyle(fontSize: 12, color: textColor),
                            ),
                          ),
                        ],
                      ),
                      const Divider(
                        height: 5,
                      ),
                      const Divider(
                        height: 0,
                      ),

                      // =============================================netAmount
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              formatCurrencyAmount(item.totalAmount!, false),
                              textAlign: TextAlign.right,
                              style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                  color: colorPrimary),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            )),
          ]);
        }),
  );

  return listView;
}
