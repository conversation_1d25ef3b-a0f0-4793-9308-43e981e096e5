import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class NotificationModel {
  int? notificationID;
  String? notificationType;
  String? date;
  String? dateBS;
  String? title;
  String? subtitle;
  String? reminderId;
  String? notificationLinkType;
  String? linkURL;
  Map<String, dynamic>? params;
  NotificationModel({
    this.notificationID,
    this.notificationType,
    this.date,
    this.dateBS,
    this.title,
    this.subtitle,
    this.reminderId,
    this.notificationLinkType,
    this.linkURL,
    this.params,
  });

  Map<String, dynamic> toJson() {
    return {
      'notification_type': notificationType,
      'notification_date': date,
      'title': title,
      'subtitle': subtitle,
      'reminder_id': reminderId,
      'link_type': notificationLinkType,
      'link_url': linkURL,
      'params': jsonEncode(params),
    };
  }

  factory NotificationModel.fromJson(Map<String, dynamic> map) {
    DateTime notificationDateTime = DateTime.parse(map["notification_date"]);
    String notificationDateBS = toDateBS(notificationDateTime);

    return NotificationModel(
        notificationType: map['notification_type'],
        date: map['notification_date'],
        dateBS: notificationDateBS,
        title: map['title'],
        subtitle: map['subtitle'],
        reminderId: map['reminder_id'],
        notificationLinkType: map['link_type'],
        linkURL: map['link_url'],
        params: jsonDecode(map['params'] ?? "{}"),
        notificationID: map['notification_id']);
  }
}
