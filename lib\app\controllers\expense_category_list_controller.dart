// ignore_for_file: use_build_context_synchronously

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/model/database/expanse_category_model.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/repository/expense_category_repository.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:tuple/tuple.dart';

class ExpensesCategoryListController extends GetxController {
  var _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  var _isSearching = true.obs;
  bool get isSearching => _isSearching.value;

  set isLoading(bool flag) => _isLoading.value = flag;

  var _categories = <ExpenseCategoryModel>[].obs;
  var _filteredCategories = <ExpenseCategoryModel>[].obs;

  ExpensesCategoryRepository _expenseCategoryRepository =
      ExpensesCategoryRepository();

  List<ExpenseCategoryModel> get categories => _categories;
  List<ExpenseCategoryModel> get filteredCategories => _filteredCategories;

  @override
  onInit() async {
    _isLoading(true);
    await fetchAll();
    _isLoading(false);
    super.onInit();
  }

  fetchAll() async {
    _isLoading(true);

    var categories = await _expenseCategoryRepository.getAllExpenseCategories();

    _categories.assignAll(categories);

    _filteredCategories.clear();
    _filteredCategories.addAll(_categories);

    _isLoading(false);
  }

  searchCategory(String searchString) {
    _isLoading(true);
    _filteredCategories.clear();

    for (var item in _categories) {
      _filteredCategories.addIf(
          item.expenseTitle!
              .toLowerCase()
              .contains(searchString.toString().toLowerCase()),
          item);
    }
    _isLoading(false);
  }

  deleteButtonOnPressedHandler(BuildContext context, String categoryID) async {
    showAlertDialog(context,
        okText: "YES",
        alertType: AlertType.Error,
        alertTitle: "Confirm Delete", onCloseButtonPressed: () async {
      // Navigator.of(context).pop();
      ProgressDialog progressDialog = ProgressDialog(context,
          type: ProgressDialogType.normal, isDismissible: false);
      progressDialog.update(message: "Checking Permission. Please wait....");
      await progressDialog.show();
      Tuple2<bool, String> checkResp = await PermissionWrapperController()
          .requestForPermissionCheck(
              forPermission: PermissionManager.expenseCategoryDelete);
      if (checkResp.item1) {
        //has  permission
        progressDialog.update(message: "Deleting Data. Please wait....");
        Tuple2<bool, String> deleteResp =
            await ExpensesCategoryRepository().delete(categoryID);
        await progressDialog.hide();
        if (deleteResp.item1) {
          //  data deleted
          TransactionHelper.refreshPreviousPages();
          fetchAll();
          showAlertDialog(context,
              // barrierDismissible: false,
              alertType: AlertType.Success,
              alertTitle: "",
              message: deleteResp.item2, onCloseButtonPressed: () {
            Navigator.of(context).pop();
          });
        } else {
          //cannot  delete  data
          showAlertDialog(context,
              alertType: AlertType.Error,
              alertTitle: "",
              message: deleteResp.item2);
        }
      } else {
        await progressDialog.hide();
        showAlertDialog(context,
            alertType: AlertType.Error,
            alertTitle: "",
            message: checkResp.item2);
      }
    }, message: "Are you sure you  want to  delete this category?");
  }
}
