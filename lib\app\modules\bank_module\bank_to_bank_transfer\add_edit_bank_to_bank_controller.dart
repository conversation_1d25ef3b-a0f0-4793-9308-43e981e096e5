import 'package:flutter/material.dart';

import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/model/database/bank_adjustment_modal.dart';
import 'package:mobile_khaata_v2/app/model/database/payment_type_model.dart';
import 'package:mobile_khaata_v2/app/repository/bank_adjustment_repository.dart';
import 'package:mobile_khaata_v2/app/repository/payment_type_repository.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

import 'package:nepali_utils/nepali_utils.dart';

class AddEditBankToBankTransferController extends GetxController {
  final String tag = "AddEditBankToBankTransferController";

  var _isLoading = true.obs;
  bool get isLoading => _isLoading.value;

  var _editFlag = false.obs;
  bool get editFlag => _editFlag.value;

  var _readOnlyFlag = false.obs;
  bool get readOnlyFlag => _readOnlyFlag.value;
  set readOnlyFlag(bool flag) {
    _readOnlyFlag.value = flag;
  }

  final _bankAdjustmentTxn = BankAdjustmentModel(
          bankAdjType: TxnType.bankToBankTransfer, bankAdjDateBS: currentDateBS)
      .obs;
  BankAdjustmentModel get bankAdjustmentTxn => _bankAdjustmentTxn.value;

  List<PaymentTypeModel> banks = [];
  PaymentTypeRepository paymentTypeRepository = PaymentTypeRepository();
  BankAdjustmentRepository bankAdjustmentRepository =
      BankAdjustmentRepository();
  final formKey = GlobalKey<FormState>();

  init() async {
    _isLoading(true);
    banks = await paymentTypeRepository.getAllBank();

    if (null != bankAdjustmentTxn.bankAdjId) {
      await initEdit();
    }
    _isLoading(false);
  }

  initEdit() async {
    _bankAdjustmentTxn.value = await bankAdjustmentRepository
        .getAdjustmentById(bankAdjustmentTxn.bankAdjId!);
    _editFlag.value = true;
    readOnlyFlag = true;
  }

  Future<bool> createAdjustment() async {
    bool status = false;
    try {
      BankAdjustmentModel newModel = BankAdjustmentModel();
      newModel = bankAdjustmentTxn;
      newModel.bankAdjDate =
          toDateAD(NepaliDateTime.parse(newModel.bankAdjDateBS!));

      String adjustmentId = await bankAdjustmentRepository.insert(newModel);

      status = true;
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }

  Future<bool> updateAdjustment() async {
    bool status = false;
    try {
      BankAdjustmentModel newModel = BankAdjustmentModel();
      newModel = bankAdjustmentTxn;
      newModel.bankAdjDate =
          toDateAD(NepaliDateTime.parse(newModel.bankAdjDateBS!));

      status = await bankAdjustmentRepository.update(newModel);
    } catch (e, trace) {
      // Log.e(tag, e.toString() + trace.toString());
    }
    return status;
  }
}
