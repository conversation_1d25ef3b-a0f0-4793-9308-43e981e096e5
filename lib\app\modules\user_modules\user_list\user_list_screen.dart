// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/model/others/user_create_model.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/add_edit_user/add_edit_user_screen.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/reset_password/user_reset_password_view.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/user_list/user_list_controller.dart';
import 'package:mobile_khaata_v2/app/modules/user_modules/user_permisson/user_permission_screen.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';
import 'package:tuple/tuple.dart';

class UserListPage extends StatelessWidget {
  final String tag = "UserListPage";

  final userListController =
      Get.put(UserListController(), tag: "UserListController");
  UserListPage({super.key}) {
    userListController.refreshUsers();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (userListController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      } else {
        return SafeArea(
            child: Scaffold(
          // resizeToAvoidBottomPadding: true,
          resizeToAvoidBottomInset: true,
          appBar: AppBar(
            titleSpacing: -5.0,
            title: const Text(
              "प्रयोगकर्ताहरू (User List)",
              style: TextStyle(
                  fontSize: 18,
                  color: Colors.white,
                  fontFamily: 'HelveticaRegular',
                  fontWeight: FontWeight.bold),
            ),
            backgroundColor: colorPrimary,
          ),
          body: userListController.isError
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(userListController.errorMessage),
                      const SizedBox(
                        height: 20,
                      ),
                      ElevatedButton(
                          onPressed: () {
                            userListController.refreshUsers();
                          },
                          child: const Text("Retry"))
                    ],
                  ),
                )
              : ListView(
                  padding: const EdgeInsets.all(10),
                  children: [
                    ...userListController.filteredUsers.map((user) {
                      return Container(
                        margin: const EdgeInsets.only(bottom: 10),
                        child: GestureDetector(
                          onTap: () {
                            Navigator.pushNamed(context, '/addEditUser',
                                arguments: AddEditUser(
                                  userData: UserCreateModel(
                                      userName: user.userName,
                                      userFullName: user.userFullName),
                                  userId: user.userId,
                                ));
                          },
                          child: Card(
                            elevation: 4,
                            margin: const EdgeInsets.only(
                              left: 5,
                              right: 5,
                            ),
                            child: Column(
                              children: <Widget>[
                                Container(
                                  margin: const EdgeInsets.only(
                                      left: 10, right: 10, top: 10),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            user.userFullName ?? "",
                                            style: TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 16,
                                                color: textColor),
                                          ),
                                          const SizedBox(
                                            height: 5,
                                          ),
                                          Text(
                                            "(UserName: ${user.userName})",
                                            style: const TextStyle(
                                                fontSize: 12,
                                                color: Colors.black54),
                                          ),
                                        ],
                                      ),
                                      Row(
                                        children: [
                                          //==============================Edit Button
                                          SizedBox(
                                            width: 35,
                                            height: 35,
                                            child: IconButton(
                                              icon: const Icon(
                                                Icons.vpn_key,
                                                size: 20,
                                                color: Colors.black54,
                                              ),
                                              onPressed: () {
                                                showModalBottomSheet(
                                                    isScrollControlled: true,
                                                    context: context,
                                                    builder: (_) =>
                                                        UserResetPasswordView(
                                                            _, user.userId!));
                                              },
                                            ),
                                          ),
                                          const SizedBox(
                                            width: 10,
                                          ),
                                          SizedBox(
                                            width: 35,
                                            height: 35,
                                            child: IconButton(
                                              icon: const Icon(
                                                Icons.security,
                                                size: 20,
                                                color: Colors.black54,
                                              ),
                                              onPressed: () {
                                                Navigator.pushNamed(
                                                    context, '/userPermission',
                                                    arguments:
                                                        UserPermissionScreen(
                                                      user: user,
                                                    ));
                                              },
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                ),
                                Container(
                                    margin: const EdgeInsets.only(
                                      left: 10,
                                      right: 10,
                                    ),
                                    child: const Divider(
                                      thickness: 0.5,
                                    )),
                                Container(
                                  padding: const EdgeInsets.all(6),
                                  child: Column(children: [
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        const Text("Last Activity At"),
                                        Text(user.lastActivityAtBS ?? "N/A")
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 4,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        const Text("Device Name"),
                                        Text(user.deviceName ?? "N/A")
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 4,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        const Text("Device Model"),
                                        Text(user.deviceModel ?? "N/A")
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 4,
                                    ),
                                    const Divider(
                                      thickness: 0.5,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          user.isActive == 1
                                              ? "Enabled"
                                              : "Disabled",
                                          style: labelStyle2.copyWith(
                                              color: user.isActive == 1
                                                  ? colorPrimaryDark
                                                  : colorRedDark),
                                        ),
                                        Switch(
                                          value: user.isActive == 1,
                                          onChanged: (value) async {
                                            ProgressDialog progressDialog =
                                                ProgressDialog(context,
                                                    type: ProgressDialogType
                                                        .normal,
                                                    isDismissible: false);
                                            progressDialog.update(
                                                message:
                                                    "Updating user. Please wait....");
                                            await progressDialog.show();

                                            Tuple2<bool, String> toggleResp =
                                                await userListController
                                                    .makeActiveInactive(
                                                        user.userId!,
                                                        value ? 1 : 0);
                                            await progressDialog.hide();
                                            if (toggleResp.item1) {
                                              showToastMessage(context,
                                                  message: toggleResp.item2,
                                                  duration: 2);
                                              userListController.refreshUsers();
                                            } else {
                                              showToastMessage(context,
                                                  alertType: AlertType.Error,
                                                  message: toggleResp.item2,
                                                  duration: 2);
                                            }
                                          },
                                          activeTrackColor: colorGreenLight,
                                          activeColor: colorGreen,
                                        ),
                                      ],
                                    ),
                                  ]),
                                )
                              ],
                            ),
                          ),
                        ),
                      );
                    }).toList()
                  ],
                ),
          floatingActionButton: SizedBox(
            width: 45,
            child: FloatingActionButton(
              onPressed: () {
                Navigator.pushNamed(context, '/addEditUser');
              },
              backgroundColor: colorPrimary,
              elevation: 6,
              child: const Icon(
                Icons.add,
                size: 25,
                color: Colors.white,
              ),
            ),
          ),
        ));
      }
    });
  }
}
