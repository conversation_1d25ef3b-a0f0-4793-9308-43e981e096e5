import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_custom_clippers/flutter_custom_clippers.dart';
import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/contact_us_dialog.dart';
import 'package:mobile_khaata_v2/app/model/others/login_model.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/reset_password/reset_password_screen.dart';
import 'package:mobile_khaata_v2/app/modules/auth_modules/select_company_module/select_company.dart';
import 'package:mobile_khaata_v2/http/api_base_helper.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/device_info_helper.dart';
import 'package:mobile_khaata_v2/utilities/logger.dart';
import 'package:mobile_khaata_v2/utilities/login_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import '../../model/others/company_model.dart';
import 'otp_verification.dart';

class LoginScreen extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;

    // Get keyboard height to adjust layout when keyboard is visible
    final bottomInset = MediaQuery.of(context).viewInsets.bottom;
    final isKeyboardVisible = bottomInset > 0;

    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        toolbarHeight: 60,
        elevation: 2,
        backgroundColor: colorPrimary,
        automaticallyImplyLeading: false,
        centerTitle: true,
        title: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Text(
              "mobile",
              style: TextStyle(
                  fontSize: 35, color: Colors.white, fontFamily: 'ArialBlack'),
            ),
            Text(
              " खाता",
              style: TextStyle(
                fontSize: 40,
                color: Colors.white,
                fontWeight: FontWeight.w800,
                fontFamily: 'ArialBlack',
              ),
            ),
          ],
        ),
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: SingleChildScrollView(
          child: ConstrainedBox(
            constraints: BoxConstraints(
              minHeight: screenHeight - bottomInset,
            ),
            child: Stack(
              children: <Widget>[
                // Main content container - adjust height based on keyboard visibility
                Container(
                  width: double.infinity,
                  // Reduce height when keyboard is visible
                  height: isKeyboardVisible
                      ? screenHeight - bottomInset - (screenHeight * 0.12)
                      : screenHeight * 0.7,
                  margin: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                  child: ClipPath(
                    clipper: DiagonalPathClipperTwo(),
                    child: Container(
                      padding: EdgeInsets.symmetric(vertical: 1, horizontal: 1),
                      decoration: BoxDecoration(
                          color: colorPrimary, boxShadow: downShadow),
                      child: ClipPath(
                        clipper: DiagonalPathClipperTwo(),
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 15),
                          decoration: BoxDecoration(
                            color: Colors.white,
                          ),
                          child: SingleChildScrollView(
                              physics: AlwaysScrollableScrollPhysics(),
                              // padding: EdgeInsets.only(bottom: 200),
                              child: LoginForm()),
                        ),
                      ),
                    ),
                  ),
                ),

                // Bottom logo - hide when keyboard is visible or position it at bottom
                if (!isKeyboardVisible)
                  Positioned(
                    bottom: 0,
                    left: 0,
                    right: 0,
                    child: RotatedBox(
                      quarterTurns: -2,
                      child: ClipPath(
                        clipper: DiagonalPathClipperTwo(),
                        child: Container(
                          padding: EdgeInsets.only(
                            bottom: 20,
                          ),
                          width: double.infinity,
                          height: screenHeight * 0.18,
                          decoration: BoxDecoration(
                              color: colorPrimary, boxShadow: upShadow),
                          child: Center(
                            child: RotatedBox(
                              quarterTurns: 2,
                              child: Image.asset(
                                'images/logo-bottom.png',
                                height: screenHeight * 0.1,
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  )
              ],
            ),
          ),
        ),
      ),
    ));
  }
}

class LoginForm extends StatefulWidget {
  @override
  _LoginFormState createState() => _LoginFormState();
}

class _LoginFormState extends State<LoginForm> {
  final String tag = "Login";

  bool _isAdmin = true;
  bool _passwordVisible = false;
  bool _enableLoginBtn = false;
  LoginHelper _loginHelper = new LoginHelper();

  GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  TextEditingController _usernameController = TextEditingController();
  TextEditingController _subusernameController = TextEditingController();
  TextEditingController _passwordController = TextEditingController();

  void checkLoginState() async {
    bool status = await _loginHelper.isLoggedIn;
    if (status) {
      await refreshGlobalVariables();
    }

    if (status) {
      Navigator.pushReplacementNamed(
        context,
        '/home',
      );
    }
  }

  void checkUsernamePassword() {
    if (_usernameController.text.isEmpty || _passwordController.text.isEmpty) {
      setState(() {
        _enableLoginBtn = false;
      });
    } else if ((!_isAdmin) && (_subusernameController.text.isEmpty)) {
      setState(() {
        _enableLoginBtn = false;
      });
    } else {
      setState(() {
        _enableLoginBtn = true;
      });
    }
  }

  Future<void> _doLogin() async {
    FocusScope.of(context).unfocus();

    DeviceInfoHelper deviceInfo = new DeviceInfoHelper();
    await deviceInfo.init();

    LoginModel _login = LoginModel();
    _login.username = _usernameController.text;
    _login.subusername = _subusernameController.text;
    _login.password = _passwordController.text;
    _login.deviceId = deviceInfo.deviceId;
    _login.deviceName = deviceInfo.name;
    _login.devicePlatform = deviceInfo.platform;
    _login.deviceModel = deviceInfo.model;
    _login.deviceManufacturer = deviceInfo.manufacturer;
    _login.isAdmin = _isAdmin;

    try {
      ProgressDialog _progressDialog = ProgressDialog(context,
          type: ProgressDialogType.normal, isDismissible: false);
      _progressDialog.update(message: "Please wait....");
      await _progressDialog.show();

      ApiBaseHelper apiBaseHelper = new ApiBaseHelper();
      ApiResponse apiResponse = await apiBaseHelper.post(
          apiBaseHelper.ACTION_AUTH_LOGIN, _login.toJson(),
          accessToken: false);

      await _progressDialog.hide();

      if (apiResponse.status) {
        Log.d("api login respose ${apiResponse.data} ");
        LoginApiResponseModel _loginApiResponse =
            LoginApiResponseModel.fromJson(apiResponse.data);
        if (_loginApiResponse.operation == 'validate-otp') {
          Navigator.pushReplacementNamed(context, "/otp_verify",
              arguments: OtpVerifyScreenArguments(_loginApiResponse.token!,
                  _loginApiResponse.registeredMobileNo!));
          showToastMessage(context, message: apiResponse.msg!, duration: 4);
        } else {
          Log.d("resp is ${_loginApiResponse.toString()}");
          bool status = await _loginHelper.login(
              accessToken: _loginApiResponse.accessToken!,
              userName: _loginApiResponse.username!,
              fullName: _loginApiResponse.fullName!,
              subdata: {
                'expiry_info': apiResponse.data['expiry_info'],
                'agent_detail': apiResponse.data['agent_detail']
              },
              permissions: (_loginApiResponse.permissions != null)
                  ? _loginApiResponse.permissions!.cast<String>()
                  : [],
              multiUserFlag: _loginApiResponse.multiUserFlag!,
              isExpired: _loginApiResponse.isAccountExpired!);

          List<CompanyModel> companyList =
              (apiResponse.data["child_company"] as List)
                  .map((company) => CompanyModel.fromJson(company))
                  .toList();

          bool company_status = await _loginHelper.storeCompanies(
            companyList,
          );

          if (status) {
            await refreshGlobalVariables();
            TransactionHelper.refreshPreviousPages();

            // //Reset/reload controller after login

            if (_loginApiResponse.isFirstLogin == 1) {
              showAlertDialog(
                context,
                alertType: AlertType.Success,
                alertTitle: "Welcome to Mobile खाता",
                barrierDismissible: false,
                onCloseButtonPressed: () {
                  Navigator.of(context).pop();
                  Navigator.of(context).pushNamedAndRemoveUntil(
                      '/home', (Route<dynamic> route) => false);
                },
                message: apiResponse.msg ?? "",
              );
            } else {
              print("Hello worls print print");
              print(_loginApiResponse.isAdmin!);
              print(apiResponse.data['is_admin']);

              if (_loginApiResponse.isAdmin!) {
                if (apiResponse.data["child_company"] == null ||
                    apiResponse.data["child_company"].length == 0) {
                  Navigator.pushReplacementNamed(context, "/home");
                } else {
                  Navigator.of(context).pushReplacement(MaterialPageRoute(
                      builder: (context) => SelectCompanyScreen(
                            loginModel: _login,
                            loginResponse: _loginApiResponse,
                          )));
                }
              } else {
                Navigator.pushReplacementNamed(context, "/home");
              }

              showToastMessage(context,
                  message: apiResponse.msg ?? "", duration: 2);
            }
          }
        }
      } else {
        print(apiResponse.data);

        showAlertDialog(context,
            alertType: AlertType.Error,
            alertTitle: "Error",
            message: getLastQuotedContent(apiResponse.msg!));
      }
    } catch (e, trace) {
      Log.e(tag, e.toString() + trace.toString());
    }
  }

  String getLastQuotedContent(String input) {
    final regex = RegExp(r'"([^"]*)"');
    final matches = regex.allMatches(input);

    if (matches.isEmpty) return '';

    return matches.last.group(1) ?? '';
  }

  @override
  void initState() {
    checkLoginState();

    super.initState();
    _usernameController.addListener(checkUsernamePassword);
    _subusernameController.addListener(checkUsernamePassword);
    _passwordController.addListener(checkUsernamePassword);
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _subusernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          SizedBox(
            height: 20,
          ),

          //===============================Username
          Container(
            width: screenWidth * 0.75,
            child: TextFormField(
              autocorrect: false,
              keyboardType: TextInputType.number,
              textInputAction: TextInputAction.next,
              controller: _usernameController,
              inputFormatters: [FilteringTextInputFormatter.digitsOnly],
              decoration: InputDecoration(
                filled: true,
                fillColor: Color(0xFFAFBCD4),
                contentPadding: EdgeInsets.all(14),
                hintText: "Registered Mobile No",
                hintStyle: kHintTextStyle,
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(6.0),
                    borderSide: BorderSide.none),
                prefixIcon: Icon(
                  Icons.person,
                  color: textColor,
                ),
              ),
              style: TextStyle(
                color: textColor,
              ),
            ),
          ),
          SizedBox(
            height: 15,
          ),
          // ResetPasswordDialogContent(context),

          //===============================Sub Username
          if (!_isAdmin) ...[
            Container(
              width: screenWidth * 0.75,
              child: TextFormField(
                autocorrect: false,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.next,
                controller: _subusernameController,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                decoration: InputDecoration(
                  filled: true,
                  fillColor: Color(0xFFAFBCD4),
                  contentPadding: EdgeInsets.all(14),
                  hintText: "Subuser Mobile No",
                  hintStyle: kHintTextStyle,
                  border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(6.0),
                      borderSide: BorderSide.none),
                  prefixIcon: Icon(
                    Icons.person,
                    color: textColor,
                  ),
                ),
                style: TextStyle(
                  color: textColor,
                ),
              ),
            ),
            SizedBox(
              height: 15,
            ),
          ],

          //===============================Password
          Container(
            width: screenWidth * 0.75,
            child: TextFormField(
              autocorrect: false,
              textInputAction: TextInputAction.done,
              obscureText: !_passwordVisible,
              controller: _passwordController,
              decoration: InputDecoration(
                filled: true,
                fillColor: Color(0xFFAFBCD4),
                contentPadding: const EdgeInsets.all(14),
                hintText: "Password",
                hintStyle: kHintTextStyle,
                border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(6.0),
                    borderSide: BorderSide.none),
                prefixIcon: Icon(
                  Icons.lock,
                  color: textColor,
                ),
                suffixIcon: IconButton(
                  icon: Icon(
                    // Based on passwordVisible state choose the icon
                    _passwordVisible ? Icons.visibility : Icons.visibility_off,
                    color: textColor,
                  ),
                  onPressed: () {
                    setState(() {
                      _passwordVisible = !_passwordVisible;
                    });
                  },
                ),
              ),
              style: TextStyle(
                color: textColor,
              ),
            ),
          ),
          SizedBox(
            height: 20,
          ),

          //===============================User Type
          Container(
            width: screenWidth * 0.75,
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  child: Checkbox(
                    activeColor: colorPrimary,
                    checkColor: Colors.white,
                    value: _isAdmin,
                    onChanged: (value) {
                      setState(() {
                        _isAdmin = value!;
                        // Log.d(_isAdmin.toString());
                      });
                    },
                  ),
                ),
                Text("  Admin User", style: labelStyle2)
              ],
            ),
          ),
          SizedBox(
            height: 25,
          ),

          //===============================Login ButtonÏ
          Container(
            child: Center(
              child: ElevatedButton(
                // color: colorPrimary,
                // disabledColor: Colors.black45,
                style: ElevatedButton.styleFrom(
                  disabledBackgroundColor: Colors.black45,
                  backgroundColor: colorPrimary,
                  foregroundColor: colorPrimaryLightest,
                ),
                child: Container(
                    padding: EdgeInsets.symmetric(horizontal: 80, vertical: 12),
                    child: Text(
                      "Login",
                      style: TextStyle(
                          color: (!_enableLoginBtn)
                              ? Colors.white38
                              : Colors.white,
                          fontSize: 16),
                    )),
                onPressed: (!_enableLoginBtn) ? null : _doLogin,
              ),
            ),
          ),
          SizedBox(
            height: 20,
          ),

          //===============================Forgot Password
          Center(
            child: GestureDetector(
              child: Text(
                "Forgot Password ?\n(आफ्नो Password बिर्सनुभो ?)",
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: colorPrimary,
                    decoration: TextDecoration.underline,
                    height: 1.5),
              ),
              onTap: () {
                // Navigator.pushNamed(context, "/forgot_password");
                showModalBottomSheet(
                    isScrollControlled: true,
                    context: context,
                    builder: (_) => ResetPasswordDialogContent(_));
              },
            ),
          ),
          SizedBox(
            height: 20,
          ),

          //===============================Forgot Password
          Container(
              child: Center(
            child: Image.asset(
              'images/or.png',
              height: 20.0,
              fit: BoxFit.cover,
            ),
          )),
          SizedBox(
            height: 10,
          ),

          //===============================New Account
          Container(
            child: Center(
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                    backgroundColor: colorPrimary,
                    foregroundColor: colorPrimaryLightest),
                child: Container(
                    padding: EdgeInsets.all(10),
                    child: Text(
                      "नयाँ खाता ? (New Account)",
                      style: TextStyle(color: Colors.white),
                    )),
                onPressed: () {
                  Navigator.pushNamed(context, '/registration');
                },
              ),
            ),
          ),
          SizedBox(
            height: 15,
          ),

          InkWell(
            onTap: () {
              displayContactUsDialog(context);
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "Need Help",
                  style: TextStyle(
                      color: colorPrimary,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      decoration: TextDecoration.underline),
                ),
                Icon(
                  Icons.help,
                  color: colorPrimary,
                  size: 30,
                ),
              ],
            ),
          ),

          SizedBox(
            height: 60,
          ),
        ],
      ),
    );
  }
}
