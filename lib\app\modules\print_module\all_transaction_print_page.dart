// ignore_for_file: unnecessary_this

import 'package:flutter/material.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/modules/print_module/all_transaction_print.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/constants.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:printing/printing.dart';

class AllTransactionPrintPage extends StatelessWidget {
  final String? pageTitle;
  final List<AllTransactionModel>? transactions;
  final String? partyText;
  final String? txnTypeText;
  final String? endDate;
  final String? startDate;
  final double? totalSale;
  final double? totalSaleReturn;
  final double? totalPurchase;
  final double? totalPurchaseReturn;
  final double? totalExpense;
  final double? totalPaymentIn;
  final double? totalPaymentOut;

  const AllTransactionPrintPage({
    super.key,
    this.pageTitle = "Print Preview",
    this.transactions,
    this.partyText = "All Party",
    this.txnTypeText = "All Transaction",
    this.endDate,
    this.startDate,
    this.totalSale,
    this.totalSaleReturn,
    this.totalPurchase,
    this.totalPurchaseReturn,
    this.totalExpense,
    this.totalPaymentIn,
    this.totalPaymentOut,
  });

  @override
  Widget build(BuildContext context) {
    String sDate = toDateBS(DateTime.parse(this.startDate ?? ""));
    String eDate = toDateBS(DateTime.parse(this.endDate ?? ""));
    return SafeArea(
      child: Scaffold(
        // resizeToAvoidBottomPadding: true,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          backgroundColor: colorPrimary,
          leading: BackButton(
            onPressed: () => Navigator.pop(context, false),
          ),
          centerTitle: false,
          titleSpacing: -5.0,
          title: Text(
            pageTitle ?? "",
            style: const TextStyle(
                fontSize: 18,
                color: Colors.white,
                fontFamily: 'HelveticaRegular',
                fontWeight: FontWeight.bold),
          ),
          actions: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12),
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(10)),
                  backgroundColor: colorPrimary,
                  foregroundColor: colorPrimaryLightest,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
                child: Column(
                  children: const [
                    Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                    Text(
                      "Cancel",
                      style: TextStyle(color: Colors.white, fontSize: 10),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        body: PdfPreview(
          useActions: true,
          canChangePageFormat: false,
          initialPageFormat: defaultPdfPageFormat,
          // allowPrinting: false,
          // allowSharing: false,
          pdfFileName: "${this.pageTitle} for "
              "${this.partyText} From $startDate To $endDate .pdf",
          maxPageWidth: 700,
          // actions: actions,
          build: (format) {
            return generateCustomReport(
              defaultPdfPageFormat,
              transactions: this.transactions,
              partyText: this.partyText ?? "",
              txnTypeText: this.txnTypeText ?? "",
              startDate: sDate,
              endDate: eDate,
              totalSale: this.totalSale,
              totalSaleReturn: this.totalSaleReturn,
              totalPurchase: this.totalPurchase,
              totalPurchaseReturn: this.totalPurchaseReturn,
              totalExpense: this.totalExpense,
              totalPaymentIn: this.totalPaymentIn,
              totalPaymentOut: this.totalPaymentOut,
            );
          },
        ),
      ),
    );
  }
}
