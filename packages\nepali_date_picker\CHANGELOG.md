## [5.3.3] - Nov 21, 2023
* Supports `flutter < 3.16.0`.

## [5.3.2] - Nov 21, 2023
* Bumps dependencies to latest version.

## [5.3.1] - Jan 29, 2023
* Made changes necessary for Flutter 3.7
* Added screenshots for pub.dev

## [5.3.0] - May 16, 2022
* Updated minimum flutter version to `v3.0.0`

## [5.2.0] - Sept 9, 2021
* Fixed issue where validation error was not being shown for the first time in Input Date Picker.
* Added day validation to Input Date Picker.
* Fixed issue where day 10 & 20 could not be entered in input date picker [#12](https://github.com/sarbagyastha/nepali_date_picker/pull/12). Thanks to [@AmritAcharya](https://github.com/Amritacharya).

## [5.1.0] - May 19, 2021
* Compatible with `flutter >= 2.1.0`

## [5.1.0-dev.0] - Feb 23, 2021
Temporary Fix to support for Flutter 2.1.0+

## [5.0.0] - Feb 23, 2021
**Breaking Change**
* Migrated to null safety
* Extended Support for BS Calendar from 1970 to 2100.

## [4.2.0+1] - Oct 4, 2020
* Added `CalendarDateRangePicker` widget.
* Added `showMaterialDateRangePicker` method.
* Added keyboard navigation support.
* Synced with latest material specs.

## [4.1.0+1] - Jun 22, 2020
* Added `CalendarDatePicker` widget with `dayBuilder`, `selectedDayDecoration` and `todayDecoration`.
* Added Calender picker demo to example app.

## [4.0.0+3] - May 3, 2020
* Updated material picker's design to match [Material Spec](https://material.io/components/pickers).
* Added web demo.

## [3.3.0-beta.1] - January 25, 2020
* Fixed shutter while changing months
* Compatible with `flutter >= 1.14.5-pre.24`

## [3.2.0+1] - January 25, 2020
* Updated `nepali_utils` to latest version.
* Memory Optimizations

## [3.2.0] - November 21, 2019
* **[Improvement]** Code Refactor.

## [3.1.0+1] - August 3, 2019
* Update to support up to 2099 B.S.
* Updated `nepali_utils` dependency.
* Minor layout related fixes.
* Example updated with time picker

## [3.0.1] -July 30, 2019
* **FIXED** Resolved selected date being overflowed.
* **FIXED** Added Nepali Language support to Year Picker.

## [3.0.0] - June 16, 2019
Version 3 introduces Cupertino style Date picker.

* **BREAKING CHANGES** In order to maintain consistency.
`showNepaliDatePicker` is now replace with `showMaterialDatePicker`.
* **[Feature Added]** `showCupertinoDatePicker` and `showAdaptiveDatePicker` added.

## [2.0.0] - May 19, 2019
This is a completely re-written version, so include **BREAKING
CHANGES**.
* **[Improved]** Works on any screen sizes without layout problems.
* **[Feature Added]** Includes semantics i.e ease of access.
* **[Improved]** Now backed by
  [nepali_utils](https://pub.dev/packages/nepali_utils).
* **[Feature Added]** Fuchsia Compatible.

## [1.2.2] - April 25, 2019

* Added buttons to adjust month.
* **Breaking Change** Dart constraint is now `>=2.1.0 <3.0.0`

## [1.2.1] - December 24, 2018

* Documentation adn Example Added. 

## [1.2.0] - December 23, 2018

* Changes in ReadMe

## [1.1.0] - December 23, 2018

* Initial Release

