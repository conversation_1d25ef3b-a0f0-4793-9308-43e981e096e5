import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/app/model/others/all_transaction_model.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/app/repository/report_repository.dart';
import 'package:mobile_khaata_v2/database/txn_type.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:nepali_utils/nepali_utils.dart';

/**
 * VAT REPORT CONTROLLER - CALCULATION FIXES AND EXPLANATIONS:
 *
 * This controller generates VAT (Value Added Tax) reports for regulatory compliance.
 * Key improvements made:
 * 1. Proper decimal precision handling for all VAT calculations
 * 2. Safe null handling for tax amounts and taxable totals
 * 3. Clear separation of sales vs purchase VAT calculations
 * 4. Accurate opening/closing balance calculations
 *
 * VAT CALCULATION PRINCIPLES:
 * - Taxable Total: Amount before VAT is applied
 * - Tax Total: VAT amount collected/paid
 * - Net Amount: Taxable Total + Tax Total
 * - Only transactions with VAT > 0 are included in VAT reports
 */

class VatReportController extends GetxController {
  var _txnLoading = false.obs;
  bool get txnLoading => _txnLoading.value;

  // PARTY INFORMATION OBSERVABLES:
  var partyName = "".obs;
  var partyAddress = "".obs;
  var tinNo = "".obs;
  var tinType = "".obs; // VAT OR PAN
  var fiscalYear = "".obs;

  // BALANCE OBSERVABLES:
  var openingBalance = "".obs;
  var closingBalance = "".obs;
  var showHeader = true.obs;

  // VAT CALCULATION OBSERVABLES WITH CLEAR NAMING:
  var salesTaxableTotal = "".obs; // Sales amount before VAT
  var salesTaxTotal = "".obs; // VAT collected on sales
  var purchaseTaxableTotal = "".obs; // Purchase amount before VAT
  var purchaseTaxTotal = "".obs; // VAT paid on purchases
  var salesReturnTaxableTotal = "".obs; // Sales return amount before VAT
  var salesReturnTaxTotal = "".obs; // VAT refunded on sales returns
  var purchaseReturnTaxableTotal = "".obs; // Purchase return amount before VAT
  var purchaseReturnTaxTotal = "".obs; // VAT recovered on purchase returns

  LedgerRepository ledgerRepository = LedgerRepository();
  ReportRepository _reportRepository = ReportRepository();

  Key? vatPanDropdownKey;

  /**
   * VAT REPORT GENERATION:
   * Generates comprehensive VAT report with all transaction types
   *
   * VAT CALCULATION BREAKDOWN:
   * 1. Sales VAT: VAT collected from customers
   * 2. Purchase VAT: VAT paid to suppliers
   * 3. Sales Return VAT: VAT refunded to customers
   * 4. Purchase Return VAT: VAT recovered from suppliers
   *
   * BALANCE CALCULATION:
   * - Opening Balance: Party balance at start date
   * - Closing Balance: Party balance at end date + 1 day
   */
  generateReport({
    required String startDate,
    required String endDate,
    String? ledgerID,
  }) async {
    _txnLoading(true);

    // STEP 1: CALCULATE CLOSING DATE (END DATE + 1 DAY)
    String closingDate = DateFormat("y-MM-dd")
        .format(DateTime.parse(endDate).add(Duration(days: 1)));

    // STEP 2: CONVERT TO NEPALI DATE FOR FISCAL YEAR CALCULATION
    String nepaliDate =
        NepaliDateTime.parse(toDateBS(DateTime.parse(startDate)))
            .format("y-MM-dd");

    // STEP 3: GET LEDGER DETAILS
    LedgerDetailModel? ledger =
        await ledgerRepository.getLedgerWithBalanceById(ledgerID ?? "");

    // STEP 4: INITIALIZE VAT CALCULATION VARIABLES WITH PROPER TYPE
    double _salesTaxableTotal = 0.0;
    double _salesTaxTotal = 0.0;
    double _purchaseTaxableTotal = 0.0;
    double _purchaseTaxTotal = 0.0;
    double _salesReturnTaxableTotal = 0.0;
    double _salesReturnTaxTotal = 0.0;
    double _purchaseReturnTaxableTotal = 0.0;
    double _purchaseReturnTaxTotal = 0.0;

    // STEP 5: FETCH VAT-RELATED TRANSACTIONS
    Map<String, dynamic> alltxns = await _reportRepository.getAllTransactions(
        startDate: startDate,
        endDate: endDate,
        types: [
          TxnType.sales,
          TxnType.purchase,
          TxnType.salesReturn,
          TxnType.purchaseReturn
        ],
        ledgerID: ledgerID);

    // STEP 6: PROCESS EACH TRANSACTION FOR VAT CALCULATIONS
    for (AllTransactionModel txn in alltxns['txnList']) {
      // ONLY PROCESS TRANSACTIONS WITH VAT (Tax Amount > 0)
      if ((txn.txnTaxAmount ?? 0.00) > 0) {
        if (TxnType.sales == txn.txnType) {
          // SALES VAT CALCULATION:
          // Taxable Total = Amount before VAT
          // Tax Total = VAT collected from customer
          _salesTaxableTotal = parseDouble(
                  (_salesTaxableTotal + (txn.txnTaxableTotalAmount ?? 0.00))
                      .toStringAsFixed(2)) ??
              0.0;
          _salesTaxTotal = parseDouble(
                  (_salesTaxTotal + (txn.txnTaxAmount ?? 0.00))
                      .toStringAsFixed(2)) ??
              0.0;
        } else if (TxnType.purchase == txn.txnType) {
          // PURCHASE VAT CALCULATION:
          // Taxable Total = Amount before VAT
          // Tax Total = VAT paid to supplier
          _purchaseTaxableTotal = parseDouble(
                  (_purchaseTaxableTotal + (txn.txnTaxableTotalAmount ?? 0.00))
                      .toStringAsFixed(2)) ??
              0.0;
          _purchaseTaxTotal = parseDouble(
                  (_purchaseTaxTotal + (txn.txnTaxAmount ?? 0.00))
                      .toStringAsFixed(2)) ??
              0.0;
        } else if (TxnType.salesReturn == txn.txnType) {
          // SALES RETURN VAT CALCULATION:
          // Taxable Total = Return amount before VAT
          // Tax Total = VAT refunded to customer
          _salesReturnTaxableTotal = parseDouble((_salesReturnTaxableTotal +
                      (txn.txnTaxableTotalAmount ?? 0.00))
                  .toStringAsFixed(2)) ??
              0.0;
          _salesReturnTaxTotal = parseDouble(
                  (_salesReturnTaxTotal + (txn.txnTaxAmount ?? 0.00))
                      .toStringAsFixed(2)) ??
              0.0;
        } else if (TxnType.purchaseReturn == txn.txnType) {
          // PURCHASE RETURN VAT CALCULATION:
          // Taxable Total = Return amount before VAT
          // Tax Total = VAT recovered from supplier
          _purchaseReturnTaxableTotal = parseDouble(
                  (_purchaseReturnTaxableTotal +
                          (txn.txnTaxableTotalAmount ?? 0.00))
                      .toStringAsFixed(2)) ??
              0.0;
          _purchaseReturnTaxTotal = parseDouble(
                  (_purchaseReturnTaxTotal + (txn.txnTaxAmount ?? 0.00))
                      .toStringAsFixed(2)) ??
              0.0;
        }
      }
    }

    // STEP 7: CALCULATE OPENING AND CLOSING BALANCES
    double openingBal = await LedgerRepository()
        .getOpeningBalanceForPartyForDate(startDate, ledgerID ?? "");
    double closingBal = await LedgerRepository()
        .getOpeningBalanceForPartyForDate(closingDate, ledgerID ?? "");

    // STEP 8: UPDATE OBSERVABLE VALUES WITH PROPER FORMATTING
    vatPanDropdownKey = UniqueKey();

    // Balance information
    openingBalance.value = openingBal.toStringAsFixed(2);
    closingBalance.value = closingBal.toStringAsFixed(2);

    // Party information
    fiscalYear.value = getFiscalYear(nepaliDate);
    partyName.value = ledger?.ledgerTitle ?? "";
    partyAddress.value = ledger?.address ?? "";
    tinNo.value = ledger?.tinNo ?? "";
    tinType.value = ledger?.tinFlag ?? "";

    // VAT calculation results with 2 decimal precision
    salesTaxableTotal.value = _salesTaxableTotal.toStringAsFixed(2);
    salesTaxTotal.value = _salesTaxTotal.toStringAsFixed(2);
    purchaseTaxableTotal.value = _purchaseTaxableTotal.toStringAsFixed(2);
    purchaseTaxTotal.value = _purchaseTaxTotal.toStringAsFixed(2);
    salesReturnTaxableTotal.value = _salesReturnTaxableTotal.toStringAsFixed(2);
    salesReturnTaxTotal.value = _salesReturnTaxTotal.toStringAsFixed(2);
    purchaseReturnTaxableTotal.value =
        _purchaseReturnTaxableTotal.toStringAsFixed(2);
    purchaseReturnTaxTotal.value = _purchaseReturnTaxTotal.toStringAsFixed(2);

    _txnLoading(false);

    print("Fiscal Year: ${fiscalYear.value}");
  }
}
