import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_custom_clippers/flutter_custom_clippers.dart';
import 'package:mobile_khaata_v2/utilities/network_utils.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class SalesIntro extends StatelessWidget {
  const SalesIntro({super.key});

  @override
  Widget build(BuildContext context) {
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;

    NetworkUtil netUtil = NetworkUtil();

    kera() async {
      final keee = await netUtil.checkInternetConnection();
      print(keee);
    }

    kera();

    return SafeArea(
      child: Container(
          decoration: const BoxDecoration(color: Color(0xFFf5f8ff)),
          child: Stack(
            fit: StackFit.expand,
            children: [
              Positioned(
                left: 0,
                right: 0,
                top: screenWidth * 0.04,
                child: Text(
                  "बिक्री",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: screenWidth * 0.11,
                      color: colorPrimary,
                      fontWeight: FontWeight.w800,
                      fontFamily: "ArialBlack"),
                ),
              ),
              Positioned(
                top: screenHeight * 0.09,
                left: 0,
                right: 0,
                bottom: screenHeight * 0.21,
                child: ClipPath(
                  clipper: DiagonalPathClipperTwo(),
                  child: Container(
                    width: double.infinity,
                    margin: EdgeInsets.symmetric(horizontal: 10),
                    padding: EdgeInsets.symmetric(horizontal: 15, vertical: 15),
                    decoration: BoxDecoration(
                        color: Colors.white, boxShadow: downShadow),
                    child: SingleChildScrollView(
                        physics: AlwaysScrollableScrollPhysics(),
                        child: Column(
                          children: [
                            Text(
                              "जती धेरै बिक्री\nत्यती धेरै कमाइ",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                  fontSize: screenWidth * 0.06,
                                  fontWeight: FontWeight.w800,
                                  fontFamily: "ArialBlack"),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Image.asset(
                              'images/sales-small.png',
                              height: screenWidth * 0.3,
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Text(
                              "के कती बिक्री भयो ? नगद वा उधारो बिक्री भयो ? थाहा पाउन तथा बिक्री गरिएका सम्पूर्ण सामाग्रीहरुको VAT/PAN सहित् को मुल्य तथा परीमाणको रेकर्ड राख्न सजिलो तथा भर्पर्दो माध्यम",
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: screenWidth * 0.045,
                                fontWeight: FontWeight.normal,
                              ),
                            ),
                          ],
                        )),
                  ),
                ),
              ),
            ],
          )),
    );
  }
}
