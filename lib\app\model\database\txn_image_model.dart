import 'dart:convert';

class TxnImageModel {
  TxnImageModel({
    this.txnId,
    this.sno,
    this.imageBitmap,
    this.imageExt,
  });

  String? txnId;
  int? sno;
  List<int>? imageBitmap;
  String? imageExt;

  factory TxnImageModel.fromJson(Map<String, dynamic> json) {
    return TxnImageModel(
      txnId: json["txn_id"],
      sno: json["sno"],
      imageBitmap: jsonDecode(json["image_bitmap"]).cast<int>(),
      imageExt: json["image_ext"],
    );
  }

  Map<String, dynamic> toJson() => {
        "txn_id": txnId,
        "sno": sno,
        "image_bitmap": jsonEncode(imageBitmap),
        "image_ext": imageExt,
      };
}
