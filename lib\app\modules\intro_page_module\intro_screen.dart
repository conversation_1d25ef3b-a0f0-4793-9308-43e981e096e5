// ignore_for_file: library_private_types_in_public_api

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_custom_clippers/flutter_custom_clippers.dart';
import 'package:mobile_khaata_v2/app/modules/intro_page_module/credit_intro.dart';
import 'package:mobile_khaata_v2/app/modules/intro_page_module/expense_intro.dart';
import 'package:mobile_khaata_v2/app/modules/intro_page_module/get_started_intro.dart';
import 'package:mobile_khaata_v2/app/modules/intro_page_module/purchase_intro.dart';
import 'package:mobile_khaata_v2/app/modules/intro_page_module/reminder_intro.dart';
import 'package:mobile_khaata_v2/app/modules/intro_page_module/report_intro.dart';
import 'package:mobile_khaata_v2/app/modules/intro_page_module/sales_intro.dart';

import 'package:mobile_khaata_v2/utilities/styles.dart';

import 'package:shared_preferences/shared_preferences.dart';

class IntroScreen extends StatefulWidget {
  const IntroScreen({super.key});

  @override
  _IntroScreenState createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen> {
  final PageController _pageController = PageController();
  SharedPreferences? prefs;
  final List _introPageList = [
    const SalesIntro(),
    PurchaseIntro(),
    CreditIntro(),
    ExpenseIntro(),
    ReportIntro(),
    ReminderIntro(),
    GetStartedIntro(),
  ];

  @override
  void initState() {
    _initSharedPrefs();
    super.initState();
  }

  void _initSharedPrefs() async {
    prefs = await SharedPreferences.getInstance();
    // bool introStatus = (prefs!.getBool(FreshInstallStatus) ?? true);

    // if (!introStatus) {
    //   Navigator.pushReplacementNamed(context, "/login");
    // }
  }

  @override
  Widget build(BuildContext context) {
    int totalPageCount = _introPageList.length;
    double screenHeight = MediaQuery.of(context).size.height;
    double screenWidth = MediaQuery.of(context).size.width;

    return SafeArea(
        child: Scaffold(
            body: PageView.builder(
      controller: _pageController,
      itemCount: totalPageCount,
      itemBuilder: (context, index) {
        return Stack(
          children: [
            _introPageList[index],

            //Footer
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: RotatedBox(
                quarterTurns: -2,
                child: ClipPath(
                  clipper: DiagonalPathClipperTwo(),
                  child: RotatedBox(
                    quarterTurns: 2,
                    child: Container(
                      padding: EdgeInsets.only(
                          top: screenHeight * 0.06, left: 15, right: 15),
                      width: double.infinity,
                      height: screenHeight * 0.28,
                      decoration: BoxDecoration(
                          color: colorPrimary, boxShadow: downShadow),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Image.asset(
                            'images/logo-bottom.png',
                            height: screenWidth * 0.23,
                            fit: BoxFit.cover,
                          ),
                          SizedBox(
                            height: screenHeight * 0.01,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              //======================================Skip Button
                              index != totalPageCount - 1
                                  ? SizedBox(
                                      width: MediaQuery.of(context).size.width *
                                          .20,
                                      child: ElevatedButton(
                                        // padding: EdgeInsets.symmetric(
                                        //     horizontal: 5, vertical: 0),
                                        // elevation: 0,
                                        child: const Text(
                                          "Skip",
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.w800,
                                          ),
                                        ),
                                        // color: colorPrimary,
                                        // textColor: Colors.white,
                                        onPressed: () {
                                          _pageController
                                              .jumpToPage(totalPageCount - 1);
                                        },
                                      ),
                                    )
                                  : const InkWell(
                                      child: Text(""),
                                    ),

                              //======================================Center Indicator
                              Container(
                                alignment: Alignment.center,
                                height: 15,
                                // width: MediaQuery.of(context).size.width * 0.35,
                                child: ListView.builder(
                                  shrinkWrap: true,
                                  scrollDirection: Axis.horizontal,
                                  itemCount: totalPageCount,
                                  itemBuilder: (BuildContext context, int i) {
                                    return Padding(
                                      padding: const EdgeInsets.all(2.0),
                                      child: Container(
                                        width: 15,
                                        decoration: BoxDecoration(
                                          color: (i == index)
                                              ? colorOrangeDark
                                              : Colors.white,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),

                              //======================================Next Button
                              index != totalPageCount - 1
                                  ? SizedBox(
                                      width: MediaQuery.of(context).size.width *
                                          .20,
                                      child: ElevatedButton(
                                        // elevation: 0,
                                        child: const Text(
                                          "Next",
                                          style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.w800,
                                          ),
                                        ),
                                        // color: colorPrimary,
                                        // textColor: Colors.white,
                                        onPressed: () {
                                          _pageController.jumpToPage(index + 1);
                                        },
                                      ),
                                    )
                                  : const InkWell(
                                      child: Text(""),
                                    ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            )
          ],
        );
      },
    )));
  }
}
