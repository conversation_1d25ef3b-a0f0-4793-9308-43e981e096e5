import 'package:flutter/material.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/components/pdf_card_view.dart';
import 'package:mobile_khaata_v2/app/model/others/ledger_detail_model.dart';
import 'package:mobile_khaata_v2/app/modules/credit_list_module/credit_list_controller.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';

class CreditListPage extends StatefulWidget {
  final int? initialPage;
  const CreditListPage({super.key, this.initialPage});
  @override
  // ignore: library_private_types_in_public_api
  _CreditListPageState createState() => _CreditListPageState();
}

class _CreditListPageState extends State<CreditListPage>
    with SingleTickerProviderStateMixin {
  final String tag = "Credit List Screen";

  bool showSearchBar = false;
  final FocusNode searchBoxFocus = FocusNode();
  String searchBoxPlaceholder = "ग्राहक खोज्नुहोस् (Party Search)";

  TabController? tabController;

  @override
  void initState() {
    tabController = TabController(
        length: 2, vsync: this, initialIndex: widget.initialPage ?? 0);
    super.initState();
  }

  @override
  void dispose() {
    tabController!.dispose();
    super.dispose();
  }

  searchButtonOnPressedHandler() {
    showSearchBar = !showSearchBar;

    if (showSearchBar) searchBoxFocus.requestFocus();

    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final creditListController =
        Get.put(CreditListController(), tag: "CreditListController");

    searchButtonOnPressedHandler() {
      showSearchBar = !showSearchBar;

      if (showSearchBar) searchBoxFocus.requestFocus();

      creditListController.search("");

      setState(() {});
    }

    searchBoxOnChangeHandler(String searchString) {
      creditListController.search(searchString);
    }

    creditListController.init();

    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        toolbarHeight: 60,
        elevation: 0,
        leading: BackButton(
          onPressed: () => Navigator.pop(context, false),
        ),
        titleSpacing: -5.0,
        centerTitle: false,
        backgroundColor: colorPrimary,
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            (showSearchBar)
                ? SizedBox(
                    width: MediaQuery.of(context).size.width * 0.56,
                    height: 50,
                    child: FormBuilderTextField(
                      name: "searchBox",
                      autocorrect: false,
                      keyboardType: TextInputType.text,
                      textInputAction: TextInputAction.done,
                      focusNode: searchBoxFocus,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                      ),
                      decoration: InputDecoration(
                          hintText: searchBoxPlaceholder,
                          contentPadding: EdgeInsets.zero,
                          prefixStyle: formFieldTextStyle,
                          hintStyle: const TextStyle(
                              color: Colors.white60, fontSize: 18),
                          border: InputBorder.none),
                      onChanged: (searchString) {
                        searchBoxOnChangeHandler(searchString ?? "");
                      },
                    ),
                  )
                : const Text(
                    "उधारो (Credit) ",
                    style: TextStyle(
                        fontSize: 18,
                        color: Colors.white,
                        fontFamily: 'HelveticaRegular',
                        fontWeight: FontWeight.bold),
                  ),
          ],
        ),
        actions: <Widget>[
          IconButton(
            icon: (showSearchBar)
                ? const Icon(
                    Icons.cancel,
                    color: Colors.white,
                  )
                : const Icon(
                    Icons.search,
                    color: Colors.white,
                  ),
            onPressed: () => searchButtonOnPressedHandler(),
          )
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Container(
              height: MediaQuery.of(context).size.height,
              decoration: BoxDecoration(
                color: backgroundColorShade,
              ),
              child: (!showSearchBar)
                  ? Column(
                      children: [
                        Container(
                          height: 50,
                          decoration: BoxDecoration(color: colorPrimary),
                          child: TabBar(
                            controller: tabController,
                            indicatorColor: Colors.white,
                            labelStyle: const TextStyle(
                              fontSize: 14,
                            ),
                            unselectedLabelColor: Colors.white54,
                            indicator: const BoxDecoration(
                                border: Border(
                                    bottom: BorderSide(
                                        color: Colors.white, width: 5))),
                            tabs: const [
                              Tab(
                                  child: Text(
                                "लिनुपर्ने\n(Receivable)",
                                textAlign: TextAlign.center,
                              )),
                              Tab(
                                  child: Text("तिर्नुपर्ने\n(Payable)",
                                      textAlign: TextAlign.center)),
                            ],
                          ),
                        ),
                        Expanded(
                            child: TabBarView(
                          controller: tabController,
                          children: [
                            _ReceivableListView(creditListController),
                            _PayableListView(creditListController),
                          ],
                        )),
                      ],
                    )
                  : showSearchBar
                      ? Obx(() {
                          return Column(
                            children: creditListController.filtered.map((e) {
                              return PartyCardView(
                                ledger: e,
                                showBothPayReceiveBtn: false,
                              );
                            }).toList(),
                          );
                        })
                      : Container(),
            ),
          ),
        ],
      ),
    ));
  }
}

class _PayableListView extends StatelessWidget {
  final CreditListController creditListController;

  const _PayableListView(this.creditListController);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (creditListController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }
      if (creditListController.payable.isEmpty) {
        return const SizedBox(
            width: double.infinity,
            child: Center(
                child: Text(
              "No Records ",
              style: TextStyle(color: Colors.black54),
            )));
      } else {
        return ListView.builder(
          itemCount: creditListController.payable.length,
          shrinkWrap: true,
          itemBuilder: (context, int index) {
            LedgerDetailModel ledger = creditListController.payable[index];

            return Container(
              margin: EdgeInsets.only(
                  left: 5,
                  right: 5,
                  top: (0 == index) ? 10 : 0,
                  bottom: ((creditListController.payable.length - 1) == index)
                      ? 20
                      : 5),
              child: PartyCardView(
                ledger: ledger,
                showBothPayReceiveBtn: false,
              ),
            );
          },
        );
      }
    });
  }
}

class _ReceivableListView extends StatelessWidget {
  final CreditListController creditListController;

  const _ReceivableListView(this.creditListController);

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (creditListController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }
      if (creditListController.receivable.isEmpty) {
        return const SizedBox(
            width: double.infinity,
            child: Center(
                child: Text(
              "No Records",
              style: TextStyle(color: Colors.black54),
            )));
      } else {
        return ListView.builder(
          itemCount: creditListController.receivable.length,
          shrinkWrap: true,
          itemBuilder: (context, int index) {
            LedgerDetailModel ledger = creditListController.receivable[index];

            return Container(
              margin: EdgeInsets.only(
                  left: 5,
                  right: 5,
                  top: (0 == index) ? 10 : 0,
                  bottom:
                      ((creditListController.receivable.length - 1) == index)
                          ? 20
                          : 5),
              child: PartyCardView(
                ledger: ledger,
                showBothPayReceiveBtn: false,
              ),
            );
          },
        );
      }
    });
  }
}
