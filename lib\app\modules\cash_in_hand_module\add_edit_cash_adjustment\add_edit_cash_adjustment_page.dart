// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_form_builder/flutter_form_builder.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';

import 'package:mobile_khaata_v2/app/common_widgets/alerts.dart';
import 'package:mobile_khaata_v2/app/components/cancel_save_button.dart';
import 'package:mobile_khaata_v2/app/components/custom_datepicker_textfield.dart';
import 'package:mobile_khaata_v2/app/modules/cash_in_hand_module/add_edit_cash_adjustment/add_edit_cash_adjustment_controller.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/repository/cash_adjustment_repository.dart';
import 'package:mobile_khaata_v2/database/cash_adjust_type.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/styles.dart';
import 'package:mobile_khaata_v2/utilities/transaction_helper.dart';
import 'package:nepali_date_picker/nepali_date_picker.dart';
import 'package:progress_dialog_null_safe/progress_dialog_null_safe.dart';

import 'package:tuple/tuple.dart';

class AddEditCashAdjustmentPage extends StatelessWidget {
  final String tag = "AddEditCashAdjustmentPage";

  final String? adjustmentId;
  final addEditCashAdjController = AddEditCashAdjustmentController();

  AddEditCashAdjustmentPage({super.key, this.adjustmentId}) {
    addEditCashAdjController.cashAdjustmentTxn.cashAdjId = adjustmentId;
    addEditCashAdjController.init();
  }

  final _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (addEditCashAdjController.isLoading) {
        return Container(
            color: Colors.white,
            child: const Center(child: CircularProgressIndicator()));
      }

      return SafeArea(
          child: Scaffold(
        // resizeToAvoidBottomPadding: true,
        resizeToAvoidBottomInset: true,
        appBar: AppBar(
          toolbarHeight: 60,
          elevation: 4,
          backgroundColor: colorPrimary,
          leading: BackButton(
            onPressed: () => Navigator.pop(context, false),
          ),
          centerTitle: false,
          titleSpacing: -5.0,
          title: Text(
            (!addEditCashAdjController.editFlag)
                ? "Cash in hand Adjustment"
                : "Edit Cash in hand Adjustment",
            style: const TextStyle(
                fontSize: 16,
                color: Colors.white,
                fontFamily: 'HelveticaRegular',
                fontWeight: FontWeight.bold),
          ),
          actions: [
            if (addEditCashAdjController.editFlag) ...{
              InkWell(
                  onTap: () => addEditCashAdjController.readOnlyFlag =
                      !addEditCashAdjController.readOnlyFlag,
                  splashColor: colorPrimaryLighter,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 10, horizontal: 15),
                    child: (addEditCashAdjController.readOnlyFlag)
                        ? Column(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(
                                Icons.mode_edit,
                                color: Colors.white,
                              ),
                              Text(
                                "Edit",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          )
                        : Column(
                            mainAxisSize: MainAxisSize.min,
                            children: const [
                              Icon(
                                Icons.close,
                                color: Colors.white,
                              ),
                              Text(
                                "Cancel",
                                style: TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ],
                          ),
                  )),
            }
          ],
        ),
        body: GestureDetector(
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: Container(
            decoration: const BoxDecoration(color: Colors.white),
            padding: const EdgeInsets.only(
              left: 15,
              right: 15,
            ),
            height: MediaQuery.of(context).size.height,
            child: Form(
              key: addEditCashAdjController.formKey,
              child: SingleChildScrollView(
                controller: _scrollController,
                child: Column(
                  children: [
                    const SizedBox(
                      height: 15,
                    ),

                    //===================================Adjustment Date Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "मिति",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        CustomDatePickerTextField(
                          readOnly: addEditCashAdjController.readOnlyFlag,
                          initialValue: addEditCashAdjController
                              .cashAdjustmentTxn.cashAdjDateBS,
                          maxBSDate: NepaliDateTime.now(),
                          onChange: (selectedDate) {
                            addEditCashAdjController
                                .cashAdjustmentTxn.cashAdjDateBS = selectedDate;
                          },
                        ),
                      ],
                    ),

                    const SizedBox(
                      height: 20,
                    ),

                    //===============================================Adjustment Type Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "समायोजनको किसिम",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderDropdown(
                          name: 'adjustment_type',
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              labelText: "Adjustment Type"),
                          items: CashAdjType.cashAdjTypeList.map((row) {
                            return DropdownMenuItem(
                                value: row["value"], child: Text(row["text"]));
                          }).toList(),
                          // readOnly: addEditCashAdjController.readOnlyFlag,
                          initialValue: addEditCashAdjController
                              .cashAdjustmentTxn.cashAdjType,
                          onChanged: (value) {
                            addEditCashAdjController
                                .cashAdjustmentTxn.cashAdjType = value as int?;
                          },
                          validator: (value) {
                            if (value == null || value == 0) {
                              return "समायोजनको किसिम छनौट गर्नुहोस् (Please select Adjustment Type)";
                            }
                            return null;
                          },
                        ),
                      ],
                    ),

                    const SizedBox(
                      height: 20,
                    ),

                    //===============================================Amount Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "रकम",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "amount",
                          autocorrect: false,
                          textInputAction: TextInputAction.done,
                          keyboardType: const TextInputType.numberWithOptions(
                              decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'^(\d+)?\.?\d{0,2}'))
                          ],
                          maxLength: 10,
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              labelText: "Amount", counterText: ''),
                          readOnly: addEditCashAdjController.readOnlyFlag,
                          initialValue: addEditCashAdjController
                              .cashAdjustmentTxn.cashAdjAmount
                              ?.toString(),
                          onChanged: (value) {
                            addEditCashAdjController.cashAdjustmentTxn
                                .cashAdjAmount = parseDouble(value);
                          },
                          validator: (value) {
                            if (value == null || parseDouble(value)! <= 0) {
                              return 'रकम खाली वा शून्य राख्नमिल्दैन (Amount cannot be empty or zero)';
                            }
                            return null;
                          },
                        ),
                      ],
                    ),

                    const SizedBox(
                      height: 20,
                    ),

                    //========================================Description Field
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "विवरण",
                          style: labelStyle2,
                        ),
                        const SizedBox(height: 5.0),
                        FormBuilderTextField(
                          name: "adjustment_desc",
                          autocorrect: false,
                          textInputAction: TextInputAction.done,
                          keyboardType: TextInputType.text,
                          style: formFieldTextStyle,
                          decoration: formFieldStyle.copyWith(
                              labelText: "Adjustment Description"),
                          readOnly: addEditCashAdjController.readOnlyFlag,
                          initialValue: addEditCashAdjController
                              .cashAdjustmentTxn.cashAdjDescription,
                          onChanged: (value) {
                            addEditCashAdjController.cashAdjustmentTxn
                                .cashAdjDescription = strTrim(value!);
                          },
                        ),
                      ],
                    ),

                    const SizedBox(
                      height: 150,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),

        //=================================================Save button
        bottomNavigationBar: BottomSaveCancelButton(
          shadow: false,
          hasDelete: (addEditCashAdjController.editFlag &&
                  !addEditCashAdjController.readOnlyFlag)
              ? true
              : false,
          onDeleteBtnPressedFn: () async {
            showAlertDialog(context,
                okText: "YES",
                hasCancel: true,
                cancelText: "NO",
                alertType: AlertType.Error,
                alertTitle: "Confirm Delete", onCloseButtonPressed: () async {
              // Navigator.of(_).pop();
              ProgressDialog progressDialog = ProgressDialog(context,
                  type: ProgressDialogType.normal, isDismissible: false);
              progressDialog.update(
                  message: "Checking Permission. Please wait....");
              await progressDialog.show();
              Tuple2<bool, String> checkResp =
                  await PermissionWrapperController().requestForPermissionCheck(
                      forPermission: PermissionManager.cashAdjDelete);
              if (checkResp.item1) {
                //has  permission
                progressDialog.update(
                    message: "Deleting Data. Please wait....");
                Tuple2<bool, String> deleteResp =
                    await CashAdjustmentRepository().delete(adjustmentId ?? "");
                await progressDialog.hide();
                if (deleteResp.item1) {
                  //  data deleted
                  TransactionHelper.refreshPreviousPages();
                  showAlertDialog(context,
                      barrierDismissible: false,
                      alertType: AlertType.Success,
                      alertTitle: "", onCloseButtonPressed: () {
                    // Navigator.of(_).pop();
                    Navigator.of(context).pop();
                  }, message: deleteResp.item2);
                } else {
                  //cannot  delete  data
                  showAlertDialog(context,
                      alertType: AlertType.Error,
                      alertTitle: "",
                      message: deleteResp.item2);
                }
              } else {
                await progressDialog.hide();
                showAlertDialog(context,
                    alertType: AlertType.Error,
                    alertTitle: "",
                    message: checkResp.item2);
              }
            },
                message:
                    "Are you sure you  want to  delete this adjustment record?");
          },
          enableFlag: !addEditCashAdjController.readOnlyFlag,
          onSaveBtnPressedFn: (addEditCashAdjController.readOnlyFlag)
              ? null
              : () async {
                  FocusScope.of(context).unfocus();
                  if (addEditCashAdjController.formKey.currentState!
                      .validate()) {
                    if (null ==
                        addEditCashAdjController
                            .cashAdjustmentTxn.cashAdjDateBS) {
                      showAlertDialog(context,
                          alertType: AlertType.Error,
                          alertTitle: "Error",
                          message: "मिति भर्नुहोस्\n(Fill Date)");
                      return;
                    }

                    ProgressDialog progressDialog = ProgressDialog(context,
                        type: ProgressDialogType.normal, isDismissible: false);
                    progressDialog.update(
                        message: "Saving data. Please wait....");
                    await progressDialog.show();

                    bool status = false;
                    try {
                      if (!addEditCashAdjController.editFlag) {
                        status =
                            await addEditCashAdjController.createAdjustment();
                      } else {
                        status =
                            await addEditCashAdjController.updateAdjustment();
                      }
                    } catch (e, trace) {
                      // Log.e(tag, e.toString() + trace.toString());
                    }
                    await progressDialog.hide();

                    if (status) {
                      Navigator.pop(context, true);

                      TransactionHelper.refreshPreviousPages();

                      String message = (addEditCashAdjController.editFlag)
                          ? "Adjustment Updated Successfully."
                          : "Adjustment Created Successfully.";
                      showToastMessage(context, message: message, duration: 2);
                    } else {
                      showToastMessage(context,
                          alertType: AlertType.Error,
                          message: "Failed to process operation",
                          duration: 2);
                    }
                  }
                },
        ),
      ));
    });
  }
}
