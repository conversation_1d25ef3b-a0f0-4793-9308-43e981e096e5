import 'package:get/get.dart';
import 'package:mobile_khaata_v2/app/modules/permission_module/permission_wrapper_controller.dart';
import 'package:mobile_khaata_v2/app/repository/cash_adjustment_repository.dart';
import 'package:mobile_khaata_v2/app/repository/ledger_repository.dart';
import 'package:mobile_khaata_v2/app/repository/transaction_repository.dart';
import 'package:mobile_khaata_v2/database/permission_manager.dart';
import 'package:mobile_khaata_v2/main.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';
import 'package:mobile_khaata_v2/utilities/nepali_date.dart';
import 'package:nepali_utils/nepali_utils.dart';
import 'package:tuple/tuple.dart';

import '../../utilities/logger.dart';

class DashboardItemController extends GetxController {
  final String tag = 'DashboardItemController';

  var _totalToReceive = 0.00.obs;
  var _totalToPay = 0.00.obs;
  var _totalSales = 0.00.obs;
  var _salesMonth = "".obs;
  var _totalPurchase = 0.00.obs;
  var _purchaseMonth = "".obs;
  var _totalExpenses = 0.00.obs;
  var _expenseMonth = "".obs;
  var _totalCashInHand = 0.00.obs;

  var _isLoading = true.obs;

  var hasPermission = false.obs;

  bool get isLoading => _isLoading.value;
  double get totalToReceive => _totalToReceive.value;
  double get totalToPay => _totalToPay.value;
  double get totalSales => _totalSales.value;
  String get salesMonth => _salesMonth.value;
  double get totalPurchase => _totalPurchase.value;
  String get purchaseMonth => _purchaseMonth.value;
  double get totalExpenses => _totalExpenses.value;
  String get expenseMonth => _expenseMonth.value;

  double get totalCashInHand => _totalCashInHand.value;

  LedgerRepository ledgerRepository = new LedgerRepository();
  CashAdjustmentRepository cashAdjustmentRepository =
      new CashAdjustmentRepository();
  TransactionRepository transactionRepository = TransactionRepository();

  String? startDate;
  String? endDate;

  @override
  onInit() {
    init();
  }

  init() async {
    Log.d("init dashboard list controller");
    _isLoading(true);

    Tuple2<bool, String> checkResponse = await PermissionWrapperController()
        .requestforPermissionOnly(
            forPermission: PermissionManager.dashboardWidget);

    hasPermission.value = checkResponse.item1;

    List<String> currentDate = currentDateBS.split('-');

    String days = NepaliDate.daysInMonth(
            year: int.tryParse(currentDate[0]) ?? 2000,
            month: int.tryParse(currentDate[1]) ?? 1)
        .toString();

    currentDate[2] = '01';
    startDate = toDateAD(NepaliDateTime.parse(currentDate.join("-")));
    currentDate[2] = days;
    endDate = toDateAD(NepaliDateTime.parse(currentDate.join("-")));

    _totalToReceive(await ledgerRepository.getTotalReceivable());

    _totalToPay(await ledgerRepository.getTotalPayable());
    _totalCashInHand(await cashAdjustmentRepository.getTotalCashInHand());

    Tuple2<String, double> sales =
        await transactionRepository.getTotalSalesCurrentMonth();
    _totalSales(sales.item2);
    _salesMonth(sales.item1);

    Tuple2<String, double> purchase =
        await transactionRepository.getTotalPurchaseCurrentMonth();
    _totalPurchase(purchase.item2);
    _purchaseMonth(purchase.item1);

    Tuple2<String, double> expenses =
        await transactionRepository.getTotalExpensesCurrentMonth();
    _totalExpenses(expenses.item2);
    _expenseMonth(expenses.item1);

    _totalToPay.refresh();
    // Log.d("new total to pay" + totalToPay.toString());

    print("this is has permisson ${hasPermission.value} ");

    _isLoading(false);
  }
}
