import 'package:intl/intl.dart';
import 'package:mobile_khaata_v2/utilities/common_helper.dart';

class ItemAdjustmentModel {
  ItemAdjustmentModel(
      {this.itemAdjId,
      this.itemAdjDate,
      this.itemAdjDateBS,
      this.itemAdjTime,
      this.itemAdjItemId,
      this.itemAdjType,
      this.itemAdjQuantity,
      this.itemAdjDescription,
      this.itemAdjAtprice,
      this.lastActivityType,
      this.lastActivityAt,
      this.lastActivityBy});

  String? itemAdjId;
  String? itemAdjDate;
  String? itemAdjDateBS;
  String? itemAdjTime;
  String? itemAdjItemId;
  int? itemAdjType;
  double? itemAdjQuantity;
  String? itemAdjDescription;
  double? itemAdjAtprice;

  int? lastActivityType;
  String? lastActivityAt;
  String? lastActivityBy;

  factory ItemAdjustmentModel.fromJson(Map<String, dynamic> json) {
    DateTime itemAdjDateTime = DateTime.parse(json["item_adj_date"]);
    String itemAdjTime = DateFormat('HH:mm:ss').format(itemAdjDateTime);
    String itemAdjDate = DateFormat('y-MM-dd').format(itemAdjDateTime);
    String itemAdjDateBS = toDateBS(itemAdjDateTime);

    return ItemAdjustmentModel(
        itemAdjId: json["item_adj_id"],
        itemAdjDate: itemAdjDate,
        itemAdjDateBS: itemAdjDateBS,
        itemAdjTime: itemAdjTime,
        itemAdjItemId: json["item_adj_item_id"],
        itemAdjType: json["item_adj_type"],
        itemAdjQuantity: json["item_adj_quantity"],
        itemAdjDescription: json["item_adj_description"],
        itemAdjAtprice: json["item_adj_atprice"],
        lastActivityType: json["last_activity_type"],
        lastActivityAt: json["last_activity_at"],
        lastActivityBy: json['last_activity_by']);
  }

  Map<String, dynamic> toJson() => {
        "item_adj_id": itemAdjId,
        "item_adj_date": itemAdjDate,
        "item_adj_item_id": itemAdjItemId,
        "item_adj_type": itemAdjType,
        "item_adj_quantity": itemAdjQuantity,
        "item_adj_description": itemAdjDescription,
        "item_adj_atprice": itemAdjAtprice,
        "last_activity_type": lastActivityType,
        "last_activity_at": lastActivityAt,
        "last_activity_by": lastActivityBy
      };
}
